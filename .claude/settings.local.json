{"permissions": {"allow": ["Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /t:Restore)", "Bash(\"../../.nuget/NuGet.exe\" restore packages.config -PackagesDirectory ../../packages)", "Bash(dotnet --version)", "<PERSON><PERSON>(mkdir:*)", "Bash(dotnet restore:*)", "<PERSON><PERSON>(powershell:*)", "Bash(\"/mnt/c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe\" -Command \"Install-Package -Source https://api.nuget.org/v3/index.json -Name Afterman.nRepo -RequiredVersion 2021.9.15.2 -Destination ./packages -Force\")", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /p:RestorePackages=true /p:Configuration=Debug)", "Bash(./.nuget/NuGet.exe help:*)", "Bash(./.nuget/NuGet.exe install:*)", "Ba<PERSON>(unzip:*)", "Bash(\"/mnt/c/Windows/System32/tar.exe\" -xf Newtonsoft.Json.13.0.3.nupkg -C Newtonsoft.Json.13.0.3)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug /p:RestorePackages=false)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.Domain/Jitb.Employment.Domain.csproj\" /p:Configuration=Debug /p:RestorePackages=false /p:RequireRestoreConsent=false /p:EnableNuGetPackageRestore=false)", "Bash(grep:*)", "Bash(dotnet build:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /t:Restore)", "<PERSON><PERSON>(mv:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.HarriCompare.csproj /p:Configuration=Debug /p:OutputPath=bin\\Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/NuGet/nuget.exe\" restore Jitb.Employment.sln)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /t:Restore /p:RestoreConfigFile=NuGet.Config)", "mcp__serena__check_onboarding_performed", "mcp__serena__list_dir", "mcp__serena__search_for_pattern", "mcp__serena__find_file", "Bash(find:*)", "mcp__serena__find_symbol", "Bash(dotnet test:*)", "mcp__serena__think_about_collected_information", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment/Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenantTests/Jitb.Employment.HarriValidateTenantTests.csproj\")", "<PERSON><PERSON>(cat:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant.Tests/Jitb.Employment.HarriValidateTenant.Tests.csproj\")", "<PERSON><PERSON>(msbuild:*)", "mcp__serena__get_symbols_overview", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__read_memory", "mcp__serena__think_about_task_adherence", "Bash(rm:*)", "mcp__mem0-mcp__search_memory", "mcp__serena__replace_symbol_body", "mcp__serena__insert_after_symbol", "mcp__serena__list_memories", "mcp__mem0-mcp__add_memory", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant.Tests.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"../Jitb.Employment.HarriValidateTenant.Tests/Jitb.Employment.HarriValidateTenant.Tests.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"./Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompare/Jitb.Employment/Jitb.Employment/Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompare\\Jitb.Employment\\Jitb.Employment\\Jitb.Employment.HarriValidateTenant\\Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "mcp__serena__replace_regex", "Bash(/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "Bash(/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe \"Jitb.Employment.HarriValidateTenant.Tests/Jitb.Employment.HarriValidateTenant.Tests.csproj\" /p:Configuration=Debug)", "Bash(/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/NuGet/nuget.exe restore \"Jitb.Employment.HarriValidateTenant.Tests/packages.config\" -PackagesDirectory packages)", "Bash(/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"./Jitb.Employment/Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /v:minimal)", "mcp__serena__find_referencing_symbols", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment/Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant.Tests/Jitb.Employment.HarriValidateTenant.Tests.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment/Jitb.Employment.HarriValidateTenant.Tests/Jitb.Employment.HarriValidateTenant.Tests.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"./Jitb.Employment.HarriValidateTenant.Tests/Jitb.Employment.HarriValidateTenant.Tests.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(dotnet --list-runtimes)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant.Tests.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompare/Jitb.Employment/Jitb.Employment/src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompare\\Jitb.Employment\\Jitb.Employment\\src\\Jitb.Employment.HarriCompare\\Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/NuGet/nuget.exe\" restore packages.config -PackagesDirectory ../../packages)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(./Jitb.Employment.HarriCompare.exe)"], "deny": []}}