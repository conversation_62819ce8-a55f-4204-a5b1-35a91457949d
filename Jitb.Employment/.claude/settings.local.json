{"permissions": {"allow": ["<PERSON><PERSON>(msbuild *)", "Bash(find:*)", "Bash(for:*)", "Bash(do echo \"=== $file ===\")", "<PERSON><PERSON>(tail:*)", "Bash(done)", "<PERSON><PERSON>(claude chat)", "Bash(ls:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.Employment/Jitb.Employment/Jitb.Employment.sln\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.sln\" /p:Configuration=Debug)", "<PERSON><PERSON>(mkdir:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCaller.Domain/Jitb.Employment.HarriCaller.Domain.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCaller.Domain.Tests/Jitb.Employment.HarriCaller.Domain.Tests.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCaller.Domain.Tests/Jitb.Employment.HarriCaller.Domain.Tests.csproj)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCaller.Domain.Tests/Jitb.Employment.HarriCaller.Domain.Tests.csproj)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCaller.Domain.Tests/Jitb.Employment.HarriCaller.Domain.Tests.csproj --filter \"FullyQualifiedName~ConfigurationContextTests\" --no-build)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCaller.Domain.Tests/Jitb.Employment.HarriCaller.Domain.Tests.csproj --filter \"FullyQualifiedName~RateLimitTypesTests\" --no-build)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCaller.Domain.Tests/Jitb.Employment.HarriCaller.Domain.Tests.csproj --no-build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCaller.Domain.Tests/Jitb.Employment.HarriCaller.Domain.Tests.csproj --no-build --verbosity normal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /mnt/c/dev/master3/Jitb.Employment/Jitb.Employment/Jitb.Employment.sln)", "Bash(\"/mnt/c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe\" -c \"[System.Reflection.Assembly]::LoadFile(''C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\src\\Jitb.Employment.HarriInbound.Endpoint\\bin\\Debug\\Microsoft.Extensions.Logging.Abstractions.dll'').GetName().Version\")", "Bash(nuget restore:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /t:Restore Jitb.Employment.sln)", "Bash(rm:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /t:Clean Jitb.Employment.sln)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /p:Configuration=Debug /p:Platform=\"Any CPU\" Jitb.Employment.sln)", "Bash(cp:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\jitb.employment\\jitb.employment\\src\\Jitb.Employment.HarriInbound.Endpoint\\Jitb.Employment.HarriInbound.Endpoint.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\jitb.employment\\jitb.employment\\src\\Jitb.Employment.HarriCaller.Domain\\Jitb.Employment.HarriCaller.Domain.csproj\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" vstest Jitb.Employment.HarriCaller.Domain.Tests.dll --Tests:Constructor_WithEmptyRateLimiters_ShouldThrowException)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.Employment/Jitb.Employment/src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\src\\Jitb.Employment.HarriCompare\\Jitb.Employment.HarriCompare.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\Jitb.Employment.HarriCompare.Tests.csproj\")", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git config:*)", "Bash(git checkout:*)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" vstest \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --Tests:EmployeeDataTests)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --logger:console)", "Bash(verbosity=normal --Tests:EmployeeComparisonAdapterTests.FromEmployee_WithValidEmployee_ShouldCreateEmployeeComparisonData)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug /t:Build)", "Bash(verbosity=minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.Employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" /logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug /t:Rebuild)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --filter \"Category=Task3\" --logger \"console;verbosity=minimal\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --filter \"Category=Task3\" --no-build)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --filter \"FullyQualifiedName~ConfigurationLoaderTests\" --verbosity quiet)", "Bash(grep:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /p:Configuration=Debug /v:quiet)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --filter \"Category=Task3\" --verbosity minimal)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity quiet --filter \"Category=Task1\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity quiet --filter \"Category=Task2\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity quiet --filter \"Category!=Task1&Category!=Task2&Category!=Task3\")", "Bash(dotnet test:*)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity quiet --filter \"Category=Task3\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity normal --filter \"Category=Task3\" --logger \"trx\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity normal --filter \"Category=Task3\" --logger \"console;verbosity=detailed\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity normal --filter \"FullyQualifiedName=Jitb.Employment.HarriCompare.Tests.Configuration.ConfigurationValidatorTests.ValidateConnectionString_WithMissingDataSource_ShouldReturnFalse\" --logger \"console;verbosity=detailed\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity normal --filter \"Category=Task3\" --logger \"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --Tests:ConfigurationLoaderTests.LoadConfiguration_WithValidAppConfig_ShouldReturnComparisonConfiguration --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/MSTest.exe\" /testcontainer:\"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\")", "Bash(\"/mnt/c/dev/master3/Jitb.Employment/Jitb.Employment/packages/xunit.runner.console.2.9.3/tools/net462/xunit.console.exe\" \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" -trait \"Category=Task3\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"TestCategory=Task3\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"Category=Task3\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"TestCategory=Task3\" --logger:console --verbosity:normal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\Jitb.Employment.sln\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"Category=Task3&DisplayName~CommandLineOptions\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" /TestCaseFilter:\"Category=Task3\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" /TestCaseFilter:\"Category=Task3 & TestCategory!=AppConfig\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" /TestCaseFilter:\"TestCategory=Task3 & DisplayName~ToString_ShouldIncludeExitCode\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" /TestCaseFilter:\"Category=Task3 & FullyQualifiedName~ToString_ShouldIncludeExitCode\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" /TestCaseFilter:\"Category=Task3 & FullyQualifiedName~MergeConfigurationWithCommandLine_WithNullFileConfig_ShouldThrowArgumentNullException\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" /TestCaseFilter:\"Category=Task3 & FullyQualifiedName~ValidateConfigurationSections_WithMissingRequiredSections_ShouldReturnFalse\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity normal --filter \"FullyQualifiedName=Jitb.Employment.HarriCompare.Tests.Configuration.ExitCodeMappingTests.GetExitCodeDescription_WithValidExitCode_ShouldReturnDescription\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity minimal)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --verbosity minimal --filter \"Category=Task4\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /mnt/c/dev/master3/Jitb.Employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --filter \"Category=Task4\" --logger \"console;verbosity=detailed\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --filter \"FullyQualifiedName~IHarriEmployeeRepository_ShouldHaveCurrentToken_Property|FullyQualifiedName~EisDatabaseConnectionResult_ShouldHaveRequiredProperties\" --logger \"console;verbosity=detailed\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --filter \"FullyQualifiedName~CurrentToken_Property\" --logger \"console;verbosity=detailed\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --filter \"Category=Task4\" --logger \"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --filter \"Category=Task5\" --logger \"console;verbosity=detailed\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /mnt/c/dev/master3/jitb.employment/Jitb.Employment/Jitb.Employment.sln)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\jitb.employment\\Jitb.Employment\\Jitb.Employment.sln\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:RunTests=true /p:Configuration=Debug)", "Bash(./bin/Debug/Jitb.Employment.HarriCompare.exe)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/jitb.employment/Jitb.Employment/src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\jitb.employment\\Jitb.Employment\\src\\Jitb.Employment.HarriCompare\\Jitb.Employment.HarriCompare.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\jitb.employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\Jitb.Employment.HarriCompare.Tests.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /t:Test)", "Bash(./Jitb.Employment.HarriCompare.exe)", "Bash(./xunit.console.exe Jitb.Employment.HarriCompare.Tests.dll -trait \"Category=Task40\")", "Bash(dotnet xunit.runner.console.dll:*)", "Bash(dotnet vstest:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" /TestCaseFilter:\"TestCategory=Task50\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" /TestCaseFilter:\"Category=Task50\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /mnt/c/dev/master3/jitb.employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"FullyQualifiedName~ConfigurationLoaderTests\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"FullyQualifiedName~ComparisonServiceTests.RunComparisonAsync OR FullyQualifiedName~ProgramTests.Main_WithNoArguments_ShouldExecuteSuccessfully\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"FullyQualifiedName~ComparisonServiceTests.RunComparisonAsync\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"FullyQualifiedName~ProgramTests.Main_WithNoArguments_ShouldExecuteSuccessfully\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"Category=Task10 OR Category=Task20\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"FullyQualifiedName~Main_WithNoArguments_ShouldExecuteSuccessfully\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"Category=Task10\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"Category=Task20\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /TestCaseFilter:\"Category=\"\"HarriCompare Skipped\"\"\" /logger:trx)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /TestCaseFilter:\"Category=HarriCompare\\ Skipped\" /logger:trx)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /logger:trx)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /TestCaseFilter:\"TestCategory=Task20\" /logger:trx)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /TestCaseFilter:\"Category=Task20\" /logger:trx)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /TestCaseFilter:\"FullyQualifiedName=Jitb.Employment.HarriCompare.Tests.ProgramTests.Main_WithNoArguments_ShouldDisplayCompletionMessage\" /logger:trx)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj /p:Configuration=Debug /t:Build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --configuration Debug --filter \"Category=Task60\" --logger \"console;verbosity=detailed\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.sln\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.Employment\\Jitb.Employment\\Jitb.Employment.sln\")", "Bash(\"./Jitb.Employment.HarriCompare.exe\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.Employment/Jitb.Employment/Jitb.Employment.sln\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.sln\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/p:Configuration=Debug\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /t:restore)", "Bash(\"./bin/Debug/Jitb.Employment.HarriCompare.exe\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/jitb.employment/Jitb.Employment/src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\jitb.employment\\Jitb.Employment\\src\\Jitb.Employment.HarriCompare\\Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /t:Restore)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/VSTest.Console.exe\" Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"TestCategory=Task80\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/VSTest.Console.exe\" Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"Category=Task80\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/VSTest.Console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --logger:trx)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/VSTest.Console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"Category=Task80\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/VSTest.Console.exe\" \"/mnt/c/dev/master3/jitb.employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"Category=Task80\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/VSTest.Console.exe\" \"C:\\dev\\master3\\jitb.employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"Category=Task80\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /p:Configuration=Debug /t:Build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /p:Configuration=Debug /t:Jitb_Employment_HarriCompare:Build)", "Bash(./Jitb.Employment.HarriCompare.exe --help)", "<PERSON>sh(./Jitb.Employment.HarriCompare.exe --verbose)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 23 --verbose)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 99999 --verbose)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 10101 --verbose)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /t:Build)", "<PERSON><PERSON>(mv:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"Jitb.Employment.HarriCompare.Tests.dll\" /TestCaseFilter:\"TestCategory=Task70\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"Jitb.Employment.HarriCompare.Tests.dll\" /ListTests)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"Jitb.Employment.HarriCompare.Tests.dll\" /TestCaseFilter:\"FullyQualifiedName~TenantNotFoundForLocationException\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"Jitb.Employment.HarriCompare.Tests.dll\" /TestCaseFilter:\"Category=Task70\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.sln /p:Configuration=Debug /t:Test)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test \"tests/Jitb.Employment.HarriCompare.Tests/\" --logger:console)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test \"tests/Jitb.Employment.HarriCompare.Tests/\" --verbosity normal)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test \"tests/Jitb.Employment.HarriCompare.Tests/\" --filter \"GetEmployeesByTenantAsync_WithInvalidTenant_ShouldReturnEmptyCollection\" --logger:console --verbosity detailed)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test \"tests/Jitb.Employment.HarriCompare.Tests/\" --filter \"Main_WithNoArguments_ShouldExecuteSuccessfully\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /p:VSTestConsoleExt=\"C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\Common7\\IDE\\Extensions\\TestPlatform\\vstest.console.exe\" /p:VSTestFramework=\"MSTest\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --Tests:ProgramTests.Main_WithNoArguments_ShouldExecuteSuccessfully)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --Tests:ProgramTests.Main_WithNoArguments_ShouldExecuteSuccessfully)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --Tests:ProgramTests.Main_WithNoArguments_ShouldExecuteSuccessfully)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --Tests:ProgramTests.Main_WithNoArguments_ShouldExecuteSuccessfully)", "<PERSON>sh(./Jitb.Employment.HarriCompare.Tests.exe)", "<PERSON><PERSON>(echo:*)", "Bash(powershell.exe:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug /t:Clean,Build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug /t:VSTest)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" Jitb.Employment.HarriCompare.Tests.dll --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:console)", "Bash(verbosity=normal)", "Bash(verbosity=quiet)", "Bash(verbosity=detailed)", "Bash(\"/mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/src/Jitb.Employment.HarriCompare/bin/Debug/Jitb.Employment.HarriCompare.exe\" --help)", "Bash(\"/mnt/c/Program Files/Common Files/Microsoft Shared/VSTest/17.0/vstest.console.exe\" bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --tests:Jitb.Employment.HarriCompare.Tests.ProgramTests.Main_WithNoArguments_ShouldExecuteSuccessfully --logger:console)", "Bash(./Jitb.Employment.HarriCompare.exe --store 1)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 1)", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:console --Tests:Main_WithNoArguments_ShouldExecuteSuccessfully)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:console --testcasefilter:\"FullyQualifiedName=Jitb.Employment.HarriCompare.Tests.ProgramTests.Main_WithNoArguments_ShouldExecuteSuccessfully\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\src\\Jitb.Employment.HarriCompare\\Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.HarriCompare.csproj)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/Jitb.Employment.sln)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\Jitb.Employment.sln\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\src\\Jitb.Employment.HarriCompare\\Jitb.Employment.HarriCompare.csproj\")", "<PERSON><PERSON>(timeout:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\src\\Jitb.Employment.HarriCompare\\Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug /t:Clean,Build)", "Bash(\"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe\" /reference:Jitb.Employment.HarriCompare.exe TestEmployeeId.cs)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/Roslyn/csc.exe\" TestEmployeeId.cs /r:\"Jitb.Employment.HarriCompare.exe\" /r:\"Jitb.Employment.Domain.dll\" /target:exe /out:TestEmployeeId.exe)", "Bash(./Jitb.Employment.HarriCompare.exe --location 3081)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 3081)", "Bash(\"packages/xunit.runner.console.2.9.3/tools/net452/xunit.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" -trait \"Category=Task8\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/MSTest.exe\" /testcontainer:\"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" /category:\"Task8\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj)", "Bash(./Jitb.Employment.HarriCompare.exe --test)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\Jitb.Employment.HarriCompare.Tests.csproj\")", "<PERSON>sh(./Jitb.Employment.HarriCompare.exe 202020)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 202020)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 101010)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 74)", "<PERSON>sh(./Jitb.Employment.HarriCompare.exe 101010)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 34)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 303030)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 34,74)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 34,74 --output-path /mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/TestReports --verbose)", "Bash(./Jitb.Employment.HarriCompare.exe --locations 34 --output-path \"C:\\Temp\\TestReports\" --verbose)", "<PERSON><PERSON>(true)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test --logger \"console;verbosity=normal\" --no-build)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test --logger \"console;verbosity=normal\" --filter \"FullyQualifiedName~EmployeeComparisonAdapterTests\" --no-build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test --logger \"console;verbosity=normal\" --filter \"FullyQualifiedName~EmployeeTestDataBuilderTests.CreateActiveEmployee_ShouldReturnEmployeeWithActiveStatus\" --no-build)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test --logger \"console;verbosity=normal\" --filter \"FullyQualifiedName~ProgramTests\" --no-build)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" --logger \"console;verbosity=normal\" --filter \"FullyQualifiedName~ProgramTests\" --no-build)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test --logger \"console;verbosity=minimal\" --no-build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /Tests:Main_WithNoArguments_ShouldExecuteSuccessfully)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /Tests:Main_WithTestArguments_ShouldExecuteSuccessfully)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug /t:Rebuild)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug /verbosity:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug /verbosity:normal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"TestCategory=Task10\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"Category=Task10\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"Category=Task10\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"Category=Task10\" --logger:console --ListFullyQualifiedTests)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"Category=Task10\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"Category=Task10\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"FullyQualifiedName~LoadConfiguration_WithEmptyLocationNumbers_ShouldThrowConfigurationException\" --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug /t:Rebuild)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"./tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug /t:Rebuild)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"Category=Task50\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll)", "Bash(dotnet:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /Logger:console;verbosity=minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /ListFullyQualifiedTests)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /t:VSTest /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" /logger:console /settings:\"tests/Jitb.Employment.HarriCompare.Tests/test.runsettings\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:trx --logger:console)", "Bash(\"C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/Common7/IDE/MSTest.exe\" /testcontainer:\"bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:trx --resultsDirectory:TestResults)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:\"console;verbosity=minimal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"TestCategory=Task20|TestCategory=Task80\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"FullyQualifiedName~ProgramDependencyInjection\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"FullyQualifiedName~ProgramDependencyInjection\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"FullyQualifiedName~ProgramConfigurationIntegration\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"FullyQualifiedName~GetEmployeesByTenantAsync_WithInvalidTenant\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --testcasefilter:\"(FullyQualifiedName~Program_MainAsync_ShouldUseComparisonService)|(FullyQualifiedName~Program_MainAsync_ShouldExecuteWithServiceProvider)|(FullyQualifiedName~MainAsync_WithInvalidConfigFile_ShouldReturnConfigurationErrorExitCode)|(FullyQualifiedName~MainAsync_WithVerboseFlag_ShouldDisplayDetailedProgress)|(FullyQualifiedName~MainAsync_WithCommandLineLocationArguments_ShouldOverrideAppConfig)|(FullyQualifiedName~MainAsync_WithAppConfigLocationNumbers_ShouldUseConfigurationLoader)|(FullyQualifiedName~GetEmployeesByTenantAsync_WithInvalidTenant_ShouldReturnEmptyCollection)\" --logger:\"console;verbosity=minimal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:\"console;verbosity=minimal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --logger:\"trx;LogFileName=TestResults.trx\" --resultsDirectory:TestResults)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj /p:Configuration=Debug /nologo)", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/TestAgent/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --Tests:Program_MainAsync_ShouldUseComparisonService --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --Tests:Program_MainAsync_ShouldUseComparisonService --logger:console)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --Tests:Program_MainAsync_ShouldUseComparisonService,Program_MainAsync_ShouldExecuteWithServiceProvider,MainAsync_WithInvalidConfigFile_ShouldReturnConfigurationErrorExitCode,MainAsync_WithVerboseFlag_ShouldDisplayDetailedProgress,MainAsync_WithCommandLineLocationArguments_ShouldOverrideAppConfig,MainAsync_WithAppConfigLocationNumbers_ShouldUseConfigurationLoader,GetEmployeesByTenantAsync_WithInvalidTenant_ShouldReturnEmptyCollection --logger:console)", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --logger \"console;verbosity=detailed\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --logger \"console;verbosity=detailed\" --filter \"FullyQualifiedName=Jitb.Employment.HarriCompare.Tests.Services.ErrorScenarioIntegrationTests.LocationNotFoundInHarriTenantByLocation_ShouldGenerateNoTenantForLocationError\")", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj --logger \"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug /t:Build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" /logger:console)", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/Common7/IDE/MSTest.exe\" /testcontainer:bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /test:Program_MainAsync_ShouldUseComparisonService)", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/Common7/IDE/MSTest.exe\" /testcontainer:bin/Debug/Jitb.Employment.HarriCompare.Tests.dll /test:ProgramDependencyInjectionTests)", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/Common7/IDE/MSTest.exe\" /testcontainer:\"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" /test:Program_MainAsync_ShouldUseComparisonService)", "Bash(\"packages/xunit.runner.console.2.4.1/tools/net461/xunit.console.exe\" bin/Debug/Jitb.Employment.HarriCompare.Tests.dll -method Jitb.Employment.HarriCompare.Tests.ProgramDependencyInjectionTests.Program_MainAsync_ShouldUseComparisonService)", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll --TestCaseFilter:\"FullyQualifiedName~Program_MainAsync_ShouldUseComparisonService\")", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"FullyQualifiedName~Program_MainAsync_ShouldUseComparisonService\")", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompareSave/Jitb.Employment/Jitb.Employment/tests/Jitb.Employment.HarriCompare.Tests/bin/Debug/Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"FullyQualifiedName~Program_MainAsync_ShouldUseComparisonService\")", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"FullyQualifiedName~Program_MainAsync_ShouldUseComparisonService\")", "Bash(\"/mnt/c/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/Common7/IDE/CommonExtensions/Microsoft/TestWindow/vstest.console.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompareSave\\Jitb.Employment\\Jitb.Employment\\tests\\Jitb.Employment.HarriCompare.Tests\\bin\\Debug\\Jitb.Employment.HarriCompare.Tests.dll\" --TestCaseFilter:\"FullyQualifiedName~ProgramDependencyInjectionTests\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug /t:Rebuild)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug /t:VSTest /p:VSTestLogger=console)", "mcp__mem0-mcp__search_memory", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug /p:Platform=\"Any CPU\" /t:Build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriInbound.Endpoint.XUnit.Tests/Jitb.Employment.HarriInbound.Endpoint.XUnit.Tests.csproj\" /p:Configuration=Debug /t:Build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriInbound.Endpoint.XUnit.Tests/Jitb.Employment.HarriInbound.Endpoint.Xunit.Tests.csproj\")", "mcp__mem0-mcp__add_memory", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/NuGet.exe\" restore packages.config -PackagesDirectory ../packages)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj)", "Bash(\"./Jitb.Employment.HarriValidateTenant.exe\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" /mnt/c/dev/master3/Jitb.EmploymentHarriCompare/Jitb.Employment/Jitb.Employment/Jitb.Employment.sln)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "Bash(./Jitb.Employment.HarriValidateTenant.exe 34)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /t:Rebuild)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompare/Jitb.Employment/Jitb.Employment/Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /t:Rebuild)", "<PERSON><PERSON>(touch:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompare\\Jitb.Employment\\Jitb.Employment\\Jitb.Employment.HarriValidateTenant\\Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"/mnt/c/dev/master3/Jitb.EmploymentHarriCompare/Jitb.Employment/Jitb.Employment/Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"C:\\dev\\master3\\Jitb.EmploymentHarriCompare\\Jitb.Employment\\Jitb.Employment\\Jitb.Employment.HarriValidateTenant\\Jitb.Employment.HarriValidateTenant.csproj\" /p:Configuration=Debug /t:Clean,Build)", "Bash(sqlcmd:*)", "Bash(\"/mnt/c/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/SQLCMD.EXE\" -S . -d dbNsbLogging -Q \"SELECT TOP 10 Date, Level, Logger, Message FROM EmploymentHarriValidateTenant WHERE Logger LIKE ''%LocationValidationService%'' ORDER BY Date DESC\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant/Jitb.Employment.HarriValidateTenant.csproj\")", "mcp__serena__get_symbols_overview", "mcp__refactor-mcp__code_search", "mcp__refactor-mcp__code_refactor", "Bash(\"/mnt/c/Program Files/dotnet/dotnet.exe\" test \"tests/Jitb.Employment.Domain.Tests/Jitb.Employment.Domain.Tests.csproj\" --logger \"console;verbosity=minimal\" --no-build)", "Bash(./Jitb.Employment.HarriValidateTenant.exe --help)", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenantTests/Jitb.Employment.HarriValidateTenantTests.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenantTests/Jitb.Employment.HarriValidateTenantTests.csproj\" /t:Restore)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"Jitb.Employment.HarriValidateTenantTests/bin/Debug/Jitb.Employment.HarriValidateTenantTests.dll\" --TestCaseFilter:\"Category=RefactorSafety\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenant.Tests/Jitb.Employment.HarriValidateTenant.Tests.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.sln\" /t:Restore)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"Jitb.Employment.HarriValidateTenantTests/bin/Debug/Jitb.Employment.HarriValidateTenantTests.dll\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenantTests/Jitb.Employment.HarriValidateTenantTests.csproj\" /t:Restore /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenantTests/Jitb.Employment.HarriValidateTenantTests.csproj\" /p:Configuration=Debug /v:minimal)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"Jitb.Employment.DatabaseMonitor.Xunit.Tests/bin/Debug/Jitb.Employment.DatabaseMonitor.Xunit.Tests.dll\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/Extensions/TestPlatform/vstest.console.exe\" \"tests/Jitb.Employment.HarriInbound.Endpoint.XUnit.Tests/bin/Debug/Jitb.Employment.HarriInbound.Endpoint.XUnit.Tests.dll\" --logger:\"console;verbosity=normal\")", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"Jitb.Employment.HarriValidateTenantTests/Jitb.Employment.HarriValidateTenantTests.csproj\" /p:Configuration=Debug /t:Clean,Build)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/NuGet.exe\" restore Jitb.Employment.HarriValidateTenantTests/packages.config -PackagesDirectory packages)", "mcp__serena__list_dir"], "deny": []}}