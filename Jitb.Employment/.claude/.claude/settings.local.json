{"permissions": {"allow": ["mcp__mem0__search-memories", "mcp__mem0__add-memory", "Bash(rm:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"tests/Jitb.Employment.HarriCompare.Tests/Jitb.Employment.HarriCompare.Tests.csproj\" /p:Configuration=Debug)", "Bash(dotnet test:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /p:Configuration=Debug)", "Bash(\"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe\" \"src/Jitb.Employment.HarriCompare/Jitb.Employment.HarriCompare.csproj\" /t:Clean)"], "deny": []}}