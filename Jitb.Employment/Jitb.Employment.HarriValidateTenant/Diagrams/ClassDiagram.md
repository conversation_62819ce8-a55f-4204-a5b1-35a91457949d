# HarriValidateTenant Class Diagram

```mermaid
classDiagram
    class Program {
        +Main(string[]) void
        -SetupStructureMap() IContainer
        -ConfigureLogging() void
        -HandleException(Exception) void
    }
    
    class Constants {
        +DefaultApiVersion string
        +HireApiVersion string
        +ValidPositionCodes string[]
    }
    
    class TenantValidationResult {
        +string TenantId
        +string TenantName
        +int ProcessedLocation
        +List~ValidationComponentResult~ ComponentResults
        +string FinalResult
        +DateTime StartTime
        +DateTime EndTime
        +TimeSpan ProcessingDuration
        +List~int~ UntestedLocations
        +GetDisplayName() string
    }
    
    class ValidationComponentResult {
        +string ComponentName
        +string Status
        +string Details
        +DateTime Timestamp
        +Exception Exception
        +ValidationComponentResult(string, string)
        +ValidationComponentResult(string, string, string, Exception)
    }
    
    class TenantProcessingResult {
        +string TenantId
        +int ProcessedLocation
        +string Status
        +List~int~ UntestedLocations
        +Exception Exception
    }
    
    class ITenantValidationService {
        <<interface>>
        +ValidateLocationForTenantAsync(string, int) Task
    }
    
    class ITenantExtractionService {
        <<interface>>
        +ExtractTenantsFromLocations(List~int~) Dictionary~string_List~int~~
        +SelectFirstLocationPerTenant(Dictionary~string_List~int~~) Dictionary~string_int~
    }
    
    class ITenantValidationResultCapture {
        <<interface>>
        +StartTenantValidation(string, string, int) void
        +RecordComponentResult(string, string, string, string) void
        +CompleteTenantValidation(string, string) void
        +GetResults() List~TenantValidationResult~
        +ClearResults() void
    }
    
    class ISummaryReportService {
        <<interface>>
        +GenerateSummaryReport(List~TenantValidationResult~) void
    }
    
    class ITenantProcessingWorkflowService {
        <<interface>>
        +ProcessTenantsAsync(Dictionary~string_int~) Task~List~TenantProcessingResult~~
    }
    
    class ILocationValidationService {
        <<interface>>
        +ValidateLocationAsync(int) Task
    }
    
    class IConfigurationService {
        <<interface>>
        +GetProcessLocations() List~int~
        +IsTenantValidationEnabled() bool
        +GetMaxConcurrentTenants() int
        +SkipEmployeeLoadWhenPreexistingFound() bool
        +MinimumExistingEmployeeCountToSkip() int
    }
    
    class TenantValidationService {
        -IHarriTenantByLocationRepository _harriTenantByLocationRepository
        -IHarriTenantRepository _harriTenantRepository
        -ILocationValidationService _locationValidationService
        -IConfigurationService _configurationService
        -ITenantValidationResultCapture _resultCapture
        -ICallHarriWebServiceProvider _callHarriWebServiceProvider
        -ILogger _logger
        +TenantValidationService(dependencies)
        +ValidateLocationForTenantAsync(string, int) Task
        -ValidateLocationComponentsAsync(string, int, HarriTenantByLocation) Task
        -ValidateGetLocationsAsync(string, int, HarriTenantByLocation) Task
        -ValidateLoadEmployeeAsync(string, int, HarriTenantByLocation) Task
        -ValidateReadEmployeeAsync(string, int, HarriTenantByLocation) Task
        -ValidatePositionMappingAsync(string, int, HarriTenantByLocation) Task
        -ValidateEventReceivedAsync(string, int, HarriTenantByLocation) Task
        -GetEmployeeCountInHarriAsync(Guid, int) Task~int~
    }
    
    class TenantExtractionService {
        -IHarriTenantByLocationRepository _repository
        -ILogger _logger
        +TenantExtractionService(dependencies)
        +ExtractTenantsFromLocations(List~int~) Dictionary~string_List~int~~
        +SelectFirstLocationPerTenant(Dictionary~string_List~int~~) Dictionary~string_int~
    }
    
    class TenantValidationResultCapture {
        -ConcurrentDictionary~string_TenantValidationResult~ _results
        -object _lock
        +StartTenantValidation(string, string, int) void
        +RecordComponentResult(string, string, string, string) void
        +CompleteTenantValidation(string, string) void
        +GetResults() List~TenantValidationResult~
        +ClearResults() void
    }
    
    class SummaryReportService {
        -ILogger _logger
        +SummaryReportService(ILogger)
        +GenerateSummaryReport(List~TenantValidationResult~) void
        -FormatResultsAsTable(List~TenantValidationResult~) string
        -WriteReportToFile(string) void
        -DisplayReportToConsole(string) void
    }
    
    class TenantProcessingWorkflowService {
        -ITenantValidationService _tenantValidationService
        -IConfigurationService _configurationService
        -ILogger _logger
        +TenantProcessingWorkflowService(dependencies)
        +ProcessTenantsAsync(Dictionary~string_int~) Task~List~TenantProcessingResult~~
        -ProcessSingleTenantAsync(string, int, SemaphoreSlim) Task~TenantProcessingResult~
    }
    
    class LocationValidationService {
        -ILocationValidationRepository _repository
        -ICallHarriWebServiceProvider _callHarriWebServiceProvider
        -IConfigurationService _configurationService
        -ILogger _logger
        +LocationValidationService(dependencies)
        +ValidateLocationAsync(int) Task
        -ValidateLocationInTenantAsync(Guid, int) Task~bool~
        -LoadEmployeeIntoHarri(Guid, Employee, int) Task~bool~
        -CheckForNewHireEvent(Guid, int, int) Task~bool~
    }
    
    class ConfigurationService {
        -List~int~ _processLocations
        -bool _isTenantValidationEnabled
        -int _maxConcurrentTenants
        -bool _skipEmployeeLoadWhenPreexistingFound
        -int _minimumExistingEmployeeCountToSkip
        +ConfigurationService()
        +GetProcessLocations() List~int~
        +IsTenantValidationEnabled() bool
        +GetMaxConcurrentTenants() int
        +SkipEmployeeLoadWhenPreexistingFound() bool
        +MinimumExistingEmployeeCountToSkip() int
    }
    
    class HarriConfigurationService {
        +GetHarriTenants() List~ConfigTenant~
        +GetTenantByName(string) ConfigTenant
    }
    
    class ConfigTenant {
        +string Name
        +string TenantId
        +string BaseUrl
        +Dictionary~string_string~ Headers
    }
    
    class IHarriTenantByLocationRepository {
        <<interface>>
        +GetTenantByLocation(int) HarriTenantByLocation
    }
    
    class IHarriTenantRepository {
        <<interface>>
        +FirstOrDefault(Expression~Func~HarriTenant_bool~~) HarriTenant
    }
    
    class ICallHarriWebServiceProvider {
        <<interface>>
        +Call(Guid, Method, string, string, bool, string) Task~RestResponse~
    }
    
    Program --> ITenantExtractionService
    Program --> ITenantProcessingWorkflowService
    Program --> ISummaryReportService
    Program --> ITenantValidationResultCapture
    Program --> IConfigurationService
    
    TenantValidationService ..|> ITenantValidationService
    TenantExtractionService ..|> ITenantExtractionService
    TenantValidationResultCapture ..|> ITenantValidationResultCapture
    SummaryReportService ..|> ISummaryReportService
    TenantProcessingWorkflowService ..|> ITenantProcessingWorkflowService
    LocationValidationService ..|> ILocationValidationService
    ConfigurationService ..|> IConfigurationService
    
    TenantValidationService --> ILocationValidationService
    TenantValidationService --> IConfigurationService
    TenantValidationService --> ITenantValidationResultCapture
    TenantValidationService --> IHarriTenantByLocationRepository
    TenantValidationService --> IHarriTenantRepository
    TenantValidationService --> ICallHarriWebServiceProvider
    
    TenantProcessingWorkflowService --> ITenantValidationService
    TenantProcessingWorkflowService --> IConfigurationService
    
    SummaryReportService --> TenantValidationResult
    TenantValidationResultCapture --> TenantValidationResult
    TenantValidationResult --> ValidationComponentResult
    
    TenantProcessingWorkflowService --> TenantProcessingResult
    TenantExtractionService --> IHarriTenantByLocationRepository
    LocationValidationService --> ICallHarriWebServiceProvider
```

## Architecture Overview

This class diagram represents the **HarriValidateTenant** application architecture, which follows a **tenant-focused validation approach** with the following key patterns:

### **Core Components:**

1. **Entry Point**: `Program` class orchestrates the entire workflow
2. **Service Layer**: Multiple specialized services implementing single-responsibility principle
3. **Models**: Data transfer objects for validation results
4. **Configuration**: Centralized configuration management

### **Key Architectural Patterns:**

- **Dependency Injection**: All services use constructor injection
- **Interface Segregation**: Each service has a focused interface
- **Repository Pattern**: External data access through repository interfaces
- **Concurrent Processing**: Thread-safe result capture and parallel tenant processing
- **Separation of Concerns**: Clear boundaries between validation, reporting, and workflow management

### **Workflow:**
1. **Extract** unique tenants from location lists
2. **Process** tenants concurrently with configurable limits  
3. **Validate** each tenant's components (locations, employees, positions, events)
4. **Capture** results in thread-safe collections
5. **Generate** comprehensive summary reports

### **External Dependencies:**
- **Domain Layer**: Repository interfaces for data access
- **Harri API**: Web service provider for external API calls
- **Logging**: NLog integration throughout all services

### **Recent Enhancements:**
- **ID-based Employee Validation**: Both `GetEmployeeCountInHarriAsync` and `ValidateReadEmployeeAsync` now test reading employees by specific IDs rather than just generic listing
- **Tenant-wide Location Reading**: Methods now read from any location in the tenant rather than being restricted to specific locations
- **Enhanced Logging**: Comprehensive logging tracks the ID-based read testing process