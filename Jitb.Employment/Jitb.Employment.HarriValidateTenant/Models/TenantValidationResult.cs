using System;
using System.Collections.Generic;

namespace Jitb.Employment.HarriValidateTenant.Models
{
    public class TenantValidationResult
    {
        public string TenantId { get; set; }
        public string TenantName { get; set; }
        public int ProcessedLocation { get; set; }
        public List<int> UntestedLocations { get; set; }
        public List<ValidationComponentResult> ComponentResults { get; set; }
        public string FinalResult { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan ProcessingDuration => EndTime - StartTime;

        public TenantValidationResult()
        {
            UntestedLocations = new List<int>();
            ComponentResults = new List<ValidationComponentResult>();
            StartTime = DateTime.Now;
        }

        /// <summary>
        /// Returns the tenant name if available, otherwise falls back to tenant ID.
        /// Returns "(Unknown)" if both are null or empty.
        /// </summary>
        /// <returns>Display name for the tenant</returns>
        public string GetDisplayName()
        {
            // Prefer tenant name if it's not null, empty, or whitespace
            if (!string.IsNullOrWhiteSpace(TenantName))
            {
                return TenantName.Trim();
            }

            // Fall back to tenant ID if tenant name is not available
            if (!string.IsNullOrWhiteSpace(TenantId))
            {
                return TenantId.Trim();
            }

            // Return default if both are null or empty
            return "(Unknown)";
        }
    }
}