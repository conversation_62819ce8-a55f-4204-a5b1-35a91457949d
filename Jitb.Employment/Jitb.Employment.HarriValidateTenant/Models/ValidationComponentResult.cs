using System;

namespace Jitb.Employment.HarriValidateTenant.Models
{
    public class ValidationComponentResult
    {
        public string ComponentName { get; set; }
        public string Status { get; set; }
        public string Details { get; set; }
        public DateTime Timestamp { get; set; }
        public Exception Exception { get; set; }

        public ValidationComponentResult()
        {
            Timestamp = DateTime.Now;
        }

        public ValidationComponentResult(string componentName, string status, string details = null)
        {
            ComponentName = componentName;
            Status = status;
            Details = details;
            Timestamp = DateTime.Now;
        }
    }
}