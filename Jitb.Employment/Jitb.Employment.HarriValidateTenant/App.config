﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <!-- Custom configuration section for Harri tenants -->
    <configSections>
        <section name="harriTenants" type="Jitb.Employment.HarriValidateTenant.Configuration.HarriTenantsConfigurationSection, Jitb.Employment.HarriValidateTenant" />
    </configSections>
    
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    <appSettings>
        <add key="ProcessLocations" value="10002,34,10001, 3058, 3080" />
       <!-- Configuration setting to control whether to skip loading employees when preexisting employees are found -->
        <add key="SkipEmployeeLoadWhenPreexistingFound" value="true" />
        <!-- Minimum number of existing employees required to trigger skipping employee load -->
        <add key="MinimumExistingEmployeeCountToSkip" value="1" />
        <!-- Maximum number of locations that can be processed concurrently (default: 5) -->
        <add key="MaxConcurrentLocations" value="5" />
        <!-- Directory path where validation reports will be saved -->
        <add key="ReportLocation" value="C:\temp\HarriValidation" />
    </appSettings>
    
    <harriTenants>  
        <tenants>
	        <add name="Corp Test Lab2 config"
	             clientId="5qho03eb2gc8v4vbhctgitvqil"
	             secret="93p81fjth2t59438faefrj9k3cn6lv9jhal0hqp8le59hjqpss"
	             tokenUrl="https://oauth.harri.com/oauth2/token" />
        </tenants>
    </harriTenants>
    <connectionStrings>
	    <add name="Default" connectionString="data source=dbnservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNserviceBus;integrated security=SSPI;enlist=false;" />
	    <add name="Config" connectionString="data source=dbnservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus_Employment_Config;integrated security=true;enlist=false;" />
	    <!--<add name="Default" connectionString="data source=.;initial catalog=dbNserviceBus;integrated security=SSPI;enlist=false;" />
        <add name="Config" connectionString="data source=.;initial catalog=dbNServiceBus_Employment_Config;integrated security=true;enlist=false;" />-->
    </connectionStrings>
</configuration>