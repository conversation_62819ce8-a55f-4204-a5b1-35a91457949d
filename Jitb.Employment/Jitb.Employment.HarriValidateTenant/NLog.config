<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      throwExceptions="false"
      internalLogLevel="Error"
      internalLogFile="c:\temp\nlog-internal.log">
  <variable name="endpointName" value="EmploymentHarriValidateTenant"/>
  <targets>
    <target name="console"
            xsi:type="ColoredConsole"
            layout="${date:format=yyyy-MM-dd HH\:mm\:ss.fff} ${level:uppercase=true:padding=5} ${logger:shortName=true} ${message} ${exception:format=tostring}"/>
    <target name="file"
            xsi:type="File"
            fileName="C:\JIB Logs\Employment\${var:endpointName}_${shortdate}.txt"
            archiveFileName="C:\JIB Logs\Employment\${var:endpointName}_{#}.txt"
            archiveNumbering="DateAndSequence"
            archiveAboveSize="1000000"
            archiveDateFormat="yyyy-MM-dd"
            maxArchiveFiles="10"
            createDirs="true"
            layout="${date:format=yyyy-MM-dd HH\:mm\:ss.fff} ${level:uppercase=true:padding=5} ${logger} ${message} ${exception:format=tostring}"/>
    <target name="database"
            xsi:type="Database"
            connectionString="Data Source=.;Initial catalog=dbNsbLogging;enlist=false;Integrated Security=SSPI;Connection Timeout=3;"
            commandTimeout="3"
            keepConnection="false">
      <commandText>
        INSERT INTO [dbo].[${var:endpointName}] ([Date],[Level],[Machine],[Identity],[Process],[ProcessId],[ThreadId],[Logger],[CallSite],[Line],[Message],[StackTrace],[Exception])
        VALUES (@Date, @Level, @Machine, @Identity, @Process, @ProcessId, @ThreadId, @Logger, @CallSite, @Line, @Message, @StackTrace, @Exception);
      </commandText>
      <parameter name="@Date" layout="${date}" />
      <parameter name="@Level" layout="${level:uppercase=true}" />
      <parameter name="@Machine" layout="${machinename}" />
      <parameter name="@Identity" layout="${windows-identity}" />
      <parameter name="@Process" layout="${processname}" />
      <parameter name="@ProcessId" layout="${processid}" />
      <parameter name="@ThreadId" layout="${threadid}" />
      <parameter name="@Logger" layout="${logger}" />
      <parameter name="@CallSite" layout="${callsite:className=true:includeNamespace=true:fileName=true:includeSourcePath=true:methodName=true:cleanNamesOfAnonymousDelegates=true:skipFrames=0}" />
      <parameter name="@Line" layout="${callsite-linenumber:skipFrames=0}" />
      <parameter name="@Message" layout="${message}" />
      <parameter name="@StackTrace" layout="${stacktrace:topFrames=10}" />
      <parameter name="@Exception" layout="${exception:format=toString,Data:maxInnerExceptionLevel=10}" />
      <install-command ignoreFailures="true">
        <text>
          IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = N'${var:endpointName}')
          BEGIN
          CREATE TABLE [dbo].[${var:endpointName}](
          [Id] [int] IDENTITY(1,1) NOT NULL,
          [Date] [datetime] NOT NULL,
          [Level] [varchar](50) NOT NULL,
          [Machine] [varchar](255) NOT NULL,
          [Identity] [varchar](255) NOT NULL,
          [Process] [varchar](255) NOT NULL,
          [ProcessId] [varchar](50) NOT NULL,
          [ThreadId] [varchar](50) NOT NULL,
          [Logger] [varchar](255) NOT NULL,
          [CallSite] [varchar](1000) NOT NULL,
          [Line] [varchar](50) NOT NULL,
          [Message] [varchar](max) NOT NULL,
          [StackTrace] [varchar](max) NOT NULL,
          [Exception] [varchar](max) NULL
          ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
          END
        </text>
      </install-command>
    </target>
  </targets>
  <rules>
    <!-- Primary application logging to console and file -->
    <logger name="Jitb.*" minlevel="Debug" writeTo="console" />
    <logger name="Jitb.*" minlevel="Debug" writeTo="file" />
    
    <!-- Specific loggers MUST come before general patterns for correct precedence -->
    
    <!-- Specific logger for Program class (final=true prevents duplicate logging) -->
    <logger name="Jitb.Employment.HarriValidateTenant.Program" minlevel="Info" writeTo="database" final="true" />
    
    <!-- Specific logger for LocationValidationService class (final=true prevents duplicate logging) -->
    <logger name="Jitb.Employment.HarriValidateTenant.Services.LocationValidationService" minlevel="Debug" writeTo="database" final="true" />
    
    <!-- General database logging with filters (MUST come after specific loggers) -->
    <logger name="Jitb.*" minlevel="Info" writeTo="database">
      <filters>
        <when condition="length('${exception}') > 0" action="Log" />
        <when condition="level >= LogLevel.Debug" action="Log" />
        <when condition="true" action="Ignore" />
      </filters>
    </logger>

    <!-- NServiceBus logging -->
    <logger name="NServiceBus*" minlevel="Info" writeTo="console" />
    <logger name="NServiceBus*" minlevel="Info" writeTo="file" />

    <!-- Suppress verbose NHibernate logging -->
    <logger name="NHibernate.cfg*" minlevel="Off" writeTo="" />
    <logger name="NHibernate.Dialect*" minlevel="Off" writeTo="" />
    <logger name="NHibernate.SQL" minlevel="Debug" writeTo="file" />
  </rules>
</nlog>