# Location Validation Implementation

## Overview
This document describes the implementation of location validation functionality that validates all configured locations exist in the tenant's Harri location list.

## Requirements Implemented
After performing `GetLocationsResult`, the system now:
1. Loops through all locations configured in app.config (`ProcessLocations` setting)
2. Checks if each location exists in the retrieved Harri locations list
3. Records each location in the report with either 'Pass' or 'Missing' status
4. Sets the overall result to 'Fail' if any location is missing

## Key Changes Made

### 1. Enhanced GetLocationsForTenantAsync Method
- **File**: `RefactoredTenantValidationService.cs`
- **Change**: Modified return type to include the actual locations list
- **Before**: `Task<(string result, string details)>`
- **After**: `Task<GetLocationsResult>` where `GetLocationsResult` contains:
  - `Result`: Pass/Fail status
  - `Details`: Description of the result
  - `Locations`: The actual `HarriLocations2List` retrieved from Harri API

### 2. New ValidateConfiguredLocationsAsync Method
- **File**: `RefactoredTenantValidationService.cs`
- **Purpose**: Validates that all configured locations exist in tenant's location list
- **Logic**:
  ```csharp
  // Get configured locations from app.config
  var configuredLocations = _configurationService.GetProcessLocations().ToList();
  
  // Check each configured location against tenant locations
  foreach (var configuredLocationId in configuredLocations)
  {
      var locationExists = tenantLocations.Any(loc => loc.Id == configuredLocationId);
      
      if (locationExists)
      {
          // Record as "Pass" in report
          _resultCapture.RecordComponentResult(tenantId, $"Location_{configuredLocationId}", "Pass", ...);
      }
      else
      {
          // Record as "Missing" in report
          _resultCapture.RecordComponentResult(tenantId, $"Location_{configuredLocationId}", "Missing", ...);
      }
  }
  ```

### 3. Updated ValidateSingleLocationForTenant Method
- **File**: `RefactoredTenantValidationService.cs`
- **Addition**: Added location validation step after getting locations:
  ```csharp
  // Step 1: Get all locations for tenant to validate read access
  var getLocationsResult = await GetLocationsForTenantAsync(tenantId);
  _resultCapture.RecordComponentResult(tenantId, "GetLocations", getLocationsResult.Result, getLocationsResult.Details);

  // Step 1.1: Validate that all configured locations exist in the tenant's location list
  var validateLocationsResult = await ValidateConfiguredLocationsAsync(tenantId, getLocationsResult.Locations);
  _resultCapture.RecordComponentResult(tenantId, "ValidateConfiguredLocations", validateLocationsResult.Result, validateLocationsResult.Details);
  ```

### 4. Updated DetermineOverallResult Method
- **File**: `RefactoredTenantValidationService.cs`
- **Change**: Added `validateLocationsResult` parameter to include location validation in overall result calculation
- **Logic**: If any location validation fails, the overall result becomes "Fail"

### 5. New Result Classes
- **File**: `RefactoredTenantValidationService.cs`
- **Added**:
  - `GetLocationsResult`: Contains result, details, and locations list
  - `ValidateLocationsResult`: Contains result and details for location validation

## Configuration
The system reads locations to validate from the `ProcessLocations` setting in app.config:
```xml
<add key="ProcessLocations" value="10002,34,10001,3058,3080" />
```

## Report Output
For each configured location, the system now records individual component results:
- Component Name: `Location_{locationId}` (e.g., "Location_10002")
- Status: "Pass" or "Missing"
- Details: Descriptive message about whether the location was found

## Example Scenarios

### Scenario 1: All Locations Found (Pass)
- Configured: [10002, 34, 10001]
- Tenant has: [10002, 34, 10001, 5000]
- Result: Pass
- Individual results: Location_10002=Pass, Location_34=Pass, Location_10001=Pass

### Scenario 2: Some Locations Missing (Fail)
- Configured: [10002, 34, 10001, 3058, 3080]
- Tenant has: [10002, 34, 5000]
- Result: Fail
- Individual results: Location_10002=Pass, Location_34=Pass, Location_10001=Missing, Location_3058=Missing, Location_3080=Missing
- Overall validation: Fail (due to missing locations)

### Scenario 3: No Tenant Locations (Fail)
- Configured: [10002, 34]
- Tenant has: []
- Result: Fail
- Details: "No locations retrieved from tenant"

## Integration Points
- **Configuration Service**: Reads `ProcessLocations` from app.config
- **Result Capture Service**: Records individual location validation results
- **Harri API**: Retrieves tenant locations via GET /locations endpoint
- **Overall Result Logic**: Includes location validation in final Pass/Fail determination

## Interface Updates
The original error `CS0738` was resolved by updating the interface definition:

**File**: `IRefactoredTenantValidationService.cs`
- **Updated**: `GetLocationsForTenantAsync` method signature to return `Task<GetLocationsResult>` instead of `Task<(string result, string details)>`
- **Added**: `ValidateConfiguredLocationsAsync` method to the interface
- **Added**: Result classes `GetLocationsResult` and `ValidateLocationsResult` for .NET Framework compatibility

## Compilation Notes
The project appears to have some basic .NET Framework reference issues (missing System types), but the core location validation logic is sound. The interface mismatch error has been resolved.

## Files Modified/Created
1. `RefactoredTenantValidationService.cs` - Main implementation with location validation
2. `IRefactoredTenantValidationService.cs` - Updated interface to match implementation
3. `LocationValidationExample.cs` - Example/demonstration code (new file)
4. `LocationValidationTest.cs` - Test class with core validation logic (new file)

## Testing
- `LocationValidationExample.cs` provides a demonstration of the validation logic with sample data
- `LocationValidationTest.cs` contains isolated test logic that can be run independently
- Both files show different scenarios: all found, some missing, none found, no configuration
