<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{595262BE-C346-43E7-9DC3-DFE78E2D7ECE}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>Jitb.Employment.HarriValidateTenant</RootNamespace>
    <AssemblyName>Jitb.Employment.HarriValidateTenant</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Afterman.nRepo, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Afterman.nRepo.2021.9.15.2\lib\net461\Afterman.nRepo.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.10\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=110.2.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.110.2.0\lib\net48\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap, Version=4.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\StructureMap.4.7.1\lib\net45\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=7.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.7.0.2\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Configuration\ConfigTenant.cs" />
    <Compile Include="Configuration\HarriConfigurationService.cs" />
    <Compile Include="Configuration\HarriTenantsConfigurationSection.cs" />
    <Compile Include="Configuration\IHarriConfigurationService.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\ConfigurationService.cs" />
    <Compile Include="Services\ConfigurationServiceTenantExtensions.cs" />
    <Compile Include="Services\IConfigurationService.cs" />
    <Compile Include="Services\ILocationValidationService.cs" />
    <Compile Include="Services\ITenantExtractionService.cs" />
    <Compile Include="Services\ITenantProcessingWorkflowService.cs" />
    <Compile Include="Services\ITenantValidationService.cs" />
    <Compile Include="Services\LocationValidationService.cs" />
    <Compile Include="Services\TenantExtractionService.cs" />
    <Compile Include="Services\TenantProcessingResult.cs" />
    <Compile Include="Services\TenantProcessingWorkflowService.cs" />
    <Compile Include="Services\TenantValidationService.cs" />
    <Compile Include="Models\TenantValidationResult.cs" />
    <Compile Include="Models\ValidationComponentResult.cs" />
    <Compile Include="Services\ISummaryReportService.cs" />
    <Compile Include="Services\SummaryReportService.cs" />
    <Compile Include="Services\ITenantValidationResultCapture.cs" />
    <Compile Include="Services\TenantValidationResultCapture.cs" />
    <Compile Include="Services\IRefactoredTenantValidationService.cs" />
    <Compile Include="Services\RefactoredTenantValidationService.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="NLog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="Configuration-README.md" />
    <None Include="Diagrams\ClassDiagram.md" />
    <None Include="Harri-501-Error-Fixes.md" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\src\Jitb.Employment.Domain\Jitb.Employment.Domain.csproj">
      <Project>{ac419d99-e346-4b40-b49f-e0bc6fa0d3ca}</Project>
      <Name>Jitb.Employment.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\src\Jitb.Employment.HarriCaller.Domain\Jitb.Employment.HarriCaller.Domain.csproj">
      <Project>{1A286B7F-19CA-4B84-A204-8DF96F4E9160}</Project>
      <Name>Jitb.Employment.HarriCaller.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\src\Jitb.Employment.HarriOutbound.Endpoint\Jitb.Employment.HarriOutbound.Endpoint.csproj">
      <Project>{F0196BC6-3A6E-4434-AC4A-D2440A2B987C}</Project>
      <Name>Jitb.Employment.HarriOutbound.Endpoint</Name>
    </ProjectReference>
    <ProjectReference Include="..\src\Jitb.Employment.Contracts\Jitb.Employment.Contracts.csproj">
      <Project>{CEE04B09-0C80-488A-82D4-33B625CCD064}</Project>
      <Name>Jitb.Employment.Contracts</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="NewFolder1\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>