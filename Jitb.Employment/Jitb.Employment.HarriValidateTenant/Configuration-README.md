# Harri Validate Tenant Configuration

This document describes the configuration options available for the Harri Validate Tenant application.

## Configuration Settings

The application uses App.config to configure its behavior. The following settings are available:

## Custom Harri Tenant Configuration

In addition to database tenants, you can configure custom Harri tenants directly in App.config:

### Harri Tenants Section

Add the following configuration section to define custom tenants:

```xml
<configSections>
    <section name="harriTenants" type="Jitb.Employment.HarriValidateTenant.Configuration.HarriTenantsConfigurationSection, Jitb.Employment.HarriValidateTenant" />
</configSections>

<harriTenants>
    <tenants>
        <add name="TestTenant1" 
             clientId="your-client-id-1" 
             secret="your-secret-1" 
             tokenUrl="https://api.harri.com/oauth/token" />
        <add name="TestTenant2" 
             clientId="your-client-id-2" 
             secret="your-secret-2" 
             tokenUrl="https://api.harri.com/oauth/token" />
    </tenants>
</harriTenants>
```

### Required Fields for Custom Tenants

- **name**: Unique identifier for the tenant
- **clientId**: Harri OAuth client ID  
- **secret**: Harri OAuth client secret
- **tokenUrl**: Harri OAuth token endpoint URL

### Usage in Code

```csharp
var configService = new HarriConfigurationService();

// Get all configured tenants
var allTenants = configService.GetConfiguredTenants();

// Get specific tenant by name
var tenant = configService.GetTenantByName("TestTenant1");
```

### ProcessLocations
**Required:** Yes  
**Type:** Comma-separated list of integers  
**Example:** `604,3,34`

Specifies the list of location IDs that should be validated by the application.

### SkipEmployeeLoadWhenPreexistingFound
**Required:** No  
**Type:** Boolean (true/false)  
**Default:** true  
**Example:** `true`

Controls whether the application should skip the employee loading step when a sufficient number of employees already exist in the Harri tenant for a location.

When set to `true`:
- The application will check the existing employee count in Harri for each location
- If the count meets or exceeds the `MinimumExistingEmployeeCountToSkip` threshold, validation is considered successful without attempting to load a new employee
- This improves performance and reduces unnecessary API calls when tenants are already properly populated

When set to `false`:
- The application will always attempt to load an employee regardless of how many employees already exist
- This ensures the complete validation process is performed for every location

### MinimumExistingEmployeeCountToSkip
**Required:** No  
**Type:** Non-negative integer  
**Default:** 10  
**Example:** `10`

Specifies the minimum number of existing employees that must be present in a Harri tenant to trigger the skip behavior (when `SkipEmployeeLoadWhenPreexistingFound` is `true`).

- A value of `0` means any existing employees will trigger the skip behavior
- Higher values require more employees to be present before skipping occurs
- The default value of `10` maintains backward compatibility with the previous hardcoded threshold

## Configuration Examples

### Default Configuration (Backward Compatible)
```xml
<appSettings>
    <add key="ProcessLocations" value="604,3,34" />
    <!-- The following settings will use their default values if not specified -->
    <!-- SkipEmployeeLoadWhenPreexistingFound defaults to true -->
    <!-- MinimumExistingEmployeeCountToSkip defaults to 10 -->
</appSettings>
```

### Always Skip When Any Employees Exist
```xml
<appSettings>
    <add key="ProcessLocations" value="604,3,34" />
    <add key="SkipEmployeeLoadWhenPreexistingFound" value="true" />
    <add key="MinimumExistingEmployeeCountToSkip" value="1" />
</appSettings>
```

### Never Skip Employee Loading
```xml
<appSettings>
    <add key="ProcessLocations" value="604,3,34" />
    <add key="SkipEmployeeLoadWhenPreexistingFound" value="false" />
    <!-- MinimumExistingEmployeeCountToSkip is ignored when skipping is disabled -->
</appSettings>
```

### Custom Threshold
```xml
<appSettings>
    <add key="ProcessLocations" value="604,3,34" />
    <add key="SkipEmployeeLoadWhenPreexistingFound" value="true" />
    <add key="MinimumExistingEmployeeCountToSkip" value="5" />
</appSettings>
```

## Validation Behavior

The validation process follows this logic:

1. **Location Validation**: Verify the location exists in the HarriTenantByLocation repository
2. **Tenant Validation**: Verify the location exists in the tenant's location list in Harri
3. **Employee Count Check**: Get the count of existing employees in Harri for the location
4. **Skip Decision**: If `SkipEmployeeLoadWhenPreexistingFound` is `true` AND the existing employee count is >= `MinimumExistingEmployeeCountToSkip`, mark validation as successful and skip to step 7
5. **Employee Selection**: Find an active employee with job code RORM20 in the local database
6. **Employee Loading**: Attempt to load the selected employee into Harri and verify success
7. **Event Verification**: Wait 2 minutes and check for the 'New Hire' event in Harri (when employee was loaded)
8. **Completion**: Log the final validation result

## Error Handling

The application will throw `ConfigurationErrorsException` in the following cases:
- `ProcessLocations` is missing or empty
- `ProcessLocations` contains non-numeric values
- `SkipEmployeeLoadWhenPreexistingFound` contains a value other than "true" or "false"
- `MinimumExistingEmployeeCountToSkip` contains a negative number or non-numeric value

## Logging

The application provides detailed logging throughout the validation process, including:
- Configuration values being used
- Skip decisions and their reasoning
- Employee counts and threshold comparisons
- Validation success/failure results

Both console output and file logging are provided for monitoring and debugging purposes.