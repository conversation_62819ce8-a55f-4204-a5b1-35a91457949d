using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.HarriValidateTenant.Services;
using NLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriValidateTenant
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            // Initialize NLog configuration first
            var logger = LogManager.GetCurrentClassLogger();

            try
            {
                Console.WriteLine("Starting Harri Tenant Validation application...");

                // Explicitly load NLog configuration with better error handling
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "NLog.config");
                if (File.Exists(configPath))
                {
                    LogManager.LoadConfiguration(configPath);
                    logger.Debug("NLog configuration loaded successfully from: " + configPath);
                    Console.WriteLine($"NLog configuration loaded from: {configPath}");
                }
                else
                {
                    Console.WriteLine($"Warning: NLog.config not found at {configPath}");
                    // Fallback to default configuration
                    LogManager.Configuration = new NLog.Config.LoggingConfiguration();
                }

                // Test logging before DI container creation
                logger.Debug("Testing logger before DI container setup");
                Console.WriteLine("Logger test message sent before DI container setup");

                var container = new StructureMap.Container();
                container.Configure(x =>
                {
                    x.AddRegistry<Jitb.Employment.Domain.DomainRegistry>();
                    x.AddRegistry<Jitb.Employment.HarriCaller.Domain.DomainRegistry>();

                    // Include Harri Outbound Endpoint registrations (IHireEmployeeOutboundProvider, etc.)
                    x.AddRegistry<Jitb.Employment.HarriOutbound.Endpoint.EndpointRegistry>();

                    // Common infrastructure
                    x.For<Afterman.nRepo.IMasterUnitOfWork>().Use<Afterman.nRepo.MasterUnitOfWork>();

                    // Logging for components that require NLog.ILogger
                    x.For<NLog.ILogger>().Use(() => NLog.LogManager.GetCurrentClassLogger());

                    // Application services
                    x.For<IConfigurationService>().Use<ConfigurationService>();
                    x.For<ILocationValidationService>().Use<LocationValidationService>();
                    x.For<IHarriTransformsProvider>().Use<HarriTransformsProvider>();
                    x.For<ICallHarriWebServiceProvider>().Use<CallHarriWebServiceProvider>();

                    // New tenant services registration
                    x.For<ITenantExtractionService>().Use<TenantExtractionService>();
                    x.For<ITenantValidationService>().Use<TenantValidationService>();
                    x.For<IRefactoredTenantValidationService>().Use<RefactoredTenantValidationService>();
                    x.For<ITenantProcessingWorkflowService>().Use<TenantProcessingWorkflowService>();
                    x.For<ISummaryReportService>().Use<SummaryReportService>();
                    x.For<ITenantValidationResultCapture>().Use<TenantValidationResultCapture>().Singleton();
                });

                Console.WriteLine("DI container configured without logger dependency.");

                // Get the new refactored validation service
                var refactoredValidationService = container.GetInstance<IRefactoredTenantValidationService>();

                Console.WriteLine("Using new ValidationRoutine structure following pseudo code...");
                logger.Debug("Starting new ValidationRoutine process");

                // Execute the new ValidationRoutine that follows the exact pseudo code structure
                await refactoredValidationService.ValidationRoutine();

                Console.WriteLine("ValidationRoutine completed successfully.");

                logger.Debug("Harri Tenant Validation application completed successfully");
                Console.WriteLine("Application completed successfully.");
            }
            catch (Exception ex)
            {
                logger.Fatal(ex, "Harri Tenant Validation application failed");
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                Environment.ExitCode = 1;
            }
            finally
            {
                Console.WriteLine("Shutting down logging...");
                try
                {
                    // Attempt to flush with a short timeout
                    LogManager.Flush(TimeSpan.FromSeconds(Constants.LogFlushTimeoutSeconds));
                    Console.WriteLine("Log flush completed.");
                }
                catch (Exception flushEx)
                {
                    Console.WriteLine($"Warning: Log flush had issues: {flushEx.Message}");
                }

                try
                {
                    // Give async operations a moment to complete
                    await Task.Delay(Constants.AsyncOperationDelayMs);

                    // Safely shutdown NLog
                    LogManager.Configuration = null;
                    Console.WriteLine("NLog shutdown completed.");
                }
                catch (Exception shutdownEx)
                {
                    Console.WriteLine($"Warning: NLog shutdown had issues: {shutdownEx.Message}");
                }
            }
        }

    }

    internal static class Constants
    {
        // Logging timeouts
        public const int LogFlushTimeoutSeconds = 2;
        public const int AsyncOperationDelayMs = 100;

        // Default concurrency settings
        public const int DefaultMaxConcurrency = 5;
        public const int DefaultMinimumExistingEmployeeCount = 10;

        // Validation timing
        public const int NewHireEventWaitMinutes = 2;
        public const int EventLookbackMinutes = 5;

        // API settings
        public const string DefaultApiVersion = "V1";
        public const string HireApiVersion = "V3";

        // Job codes
        public const string PrimaryManagerJobCode = "RORM20";
        public static readonly string[] ManagerJobCodes = { PrimaryManagerJobCode, "RORM24", "RORM30", "RORM32", "RORM36" };
        public static readonly string[] ValidPositionCodes = { "RORH03", "RORH00", "RORH05", "RORH08", "RORH10", "RORH13", PrimaryManagerJobCode, "RORM24", "RORM30", "RORM32", "RORM36" };
        public const string ActiveEmployeeStatus = "01";

        // Default values
        public const string DefaultPhoneNumber = "************";
    }
}
