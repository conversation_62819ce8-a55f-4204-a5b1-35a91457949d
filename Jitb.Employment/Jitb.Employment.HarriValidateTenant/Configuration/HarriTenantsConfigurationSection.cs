using System.Configuration;

namespace Jitb.Employment.HarriValidateTenant.Configuration
{
    public class HarriTenantsConfigurationSection : ConfigurationSection
    {
        [ConfigurationProperty("tenants")]
        public HarriTenantElementCollection Tenants
        {
            get { return (HarriTenantElementCollection)this["tenants"]; }
        }
    }

    [ConfigurationCollection(typeof(HarriTenantElement))]
    public class HarriTenantElementCollection : ConfigurationElementCollection
    {
        protected override ConfigurationElement CreateNewElement()
        {
            return new HarriTenantElement();
        }

        protected override object GetElementKey(ConfigurationElement element)
        {
            return ((HarriTenantElement)element).Name;
        }

        public HarriTenantElement this[int index]
        {
            get { return (HarriTenantElement)BaseGet(index); }
        }

        public HarriTenantElement this[string key]
        {
            get { return (HarriTenantElement)BaseGet(key); }
        }
    }

    public class HarriTenantElement : ConfigurationElement
    {
        [ConfigurationProperty("name", IsRequired = true, IsKey = true)]
        public string Name
        {
            get { return (string)this["name"]; }
            set { this["name"] = value; }
        }

        [ConfigurationProperty("clientId", IsRequired = true)]
        public string ClientId
        {
            get { return (string)this["clientId"]; }
            set { this["clientId"] = value; }
        }

        [ConfigurationProperty("secret", IsRequired = true)]
        public string Secret
        {
            get { return (string)this["secret"]; }
            set { this["secret"] = value; }
        }

        [ConfigurationProperty("tokenUrl", IsRequired = true)]
        public string TokenUrl
        {
            get { return (string)this["tokenUrl"]; }
            set { this["tokenUrl"] = value; }
        }
    }
}