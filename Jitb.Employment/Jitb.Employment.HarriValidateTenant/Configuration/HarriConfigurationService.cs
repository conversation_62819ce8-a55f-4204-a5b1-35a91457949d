using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace Jitb.Employment.HarriValidateTenant.Configuration
{
    public class HarriConfigurationService : IHarriConfigurationService
    {
        private readonly List<ConfigTenant> _configuredTenants;

        public HarriConfigurationService()
        {
            _configuredTenants = LoadTenantsFromConfig();
        }

        public List<ConfigTenant> GetConfiguredTenants()
        {
            return _configuredTenants;
        }

        public ConfigTenant GetTenantByName(string name)
        {
            return _configuredTenants.FirstOrDefault(t => t.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        private List<ConfigTenant> LoadTenantsFromConfig()
        {
            var tenants = new List<ConfigTenant>();
            
            try
            {
                var section = ConfigurationManager.GetSection("harriTenants") as HarriTenantsConfigurationSection;
                
                if (section?.Tenants != null)
                {
                    foreach (HarriTenantElement element in section.Tenants)
                    {
                        tenants.Add(new ConfigTenant
                        {
                            Name = element.Name,
                            ClientId = element.ClientId,
                            Secret = element.Secret,
                            TokenUrl = element.TokenUrl
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't fail - fall back to empty list
                Console.WriteLine($"Error loading Harri tenants from config: {ex.Message}");
            }

            return tenants;
        }
    }
}