using System.Threading.Tasks;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Interface for tenant-focused validation service.
    /// Provides validation capabilities with tenant context, delegating to existing location validation logic.
    /// Follows SOLID principles with single responsibility for tenant-scoped validation operations.
    /// </summary>
    public interface ITenantValidationService
    {
        /// <summary>
        /// Validates a location within the context of a specific tenant.
        /// Delegates to existing location validation logic while providing tenant context for logging and error handling.
        /// </summary>
        /// <param name="tenantId">The tenant identifier for context (can be "(null)" for locations without tenants)</param>
        /// <param name="locationId">The location ID to validate</param>
        /// <returns>Task representing the asynchronous validation operation</returns>
        /// <exception cref="System.ArgumentNullException">Thrown when tenantId is null</exception>
        /// <exception cref="System.ArgumentException">Thrown when locationId is invalid (less than or equal to 0)</exception>
        Task ValidateLocationForTenantAsync(string tenantId, int locationId);
    }
}