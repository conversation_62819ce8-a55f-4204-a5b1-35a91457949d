using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Concepts;
using Jitb.Employment.HarriCaller.Domain.Enums;
using Jitb.Employment.HarriCaller.Domain.Exceptions;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.HarriValidateTenant.Configuration;
using Newtonsoft.Json;
using NLog;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    public class LocationValidationService : ILocationValidationService
    {
        private readonly IHarriTenantByLocationRepository _harriTenantByLocationRepository;
        private readonly IEmployeeRepository _employeeRepository;
        private readonly ICallHarriWebServiceProvider _callHarriWebServiceProvider;
        private readonly IGenderDictionary _genderDictionary;
        private readonly IHarriTransformsProvider _harriTransformsProvider;
        private readonly IConfigurationService _configurationService;
        private readonly IHarriConfigurationService _harriConfigurationService;

        // Hardcode the logger instead of using DI
        private static readonly ILogger _logger = LogManager.GetCurrentClassLogger();

        public LocationValidationService(
            IHarriTenantByLocationRepository harriTenantByLocationRepository,
            IEmployeeRepository employeeRepository,
            ICallHarriWebServiceProvider callHarriWebServiceProvider,
            IGenderDictionary genderDictionary,
            IHarriTransformsProvider harriTransformsProvider,
            IConfigurationService configurationService)
        {
            _harriTenantByLocationRepository = harriTenantByLocationRepository ?? throw new ArgumentNullException(nameof(harriTenantByLocationRepository));
            _employeeRepository = employeeRepository ?? throw new ArgumentNullException(nameof(employeeRepository));
            _callHarriWebServiceProvider = callHarriWebServiceProvider ?? throw new ArgumentNullException(nameof(callHarriWebServiceProvider));
            _genderDictionary = genderDictionary ?? throw new ArgumentNullException(nameof(genderDictionary));
            _harriTransformsProvider = harriTransformsProvider ?? throw new ArgumentNullException(nameof(harriTransformsProvider));
            _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
            _harriConfigurationService = new HarriConfigurationService();

            // Debug logging to verify logger is working
            Console.WriteLine($"LocationValidationService constructor: Hardcoded logger type = {_logger.GetType().FullName}");
            _logger.Debug("LocationValidationService constructor completed with hardcoded logger");

            // Test the logger immediately to ensure it's working
            _logger.Debug("LocationValidationService: Testing hardcoded logger functionality");
            _logger.Warn("LocationValidationService: Hardcoded logger warning test");
        }

        public async Task ValidateLocationAsync(int location)
        {
            // Add console output to debug logging issues
            Console.WriteLine($"[CONSOLE] [Location {location}]: Validation Start");
            _logger.Info($"[Location {location}]: Validation Start");

            bool validationSuccessful = false;

            try
            {
                // Step 1: Resolve tenant (database → app.config fallback)
                var tenantId = await ResolveTenantAsync(location);
                if (tenantId == null)
                {
                    // ValidateLocationWithConfigTenants handles fallback logging and processing
                    await ValidateLocationWithConfigTenants(location);
                    return;
                }

                // Step 2: Validate location exists in tenant
                bool locationValidInTenant = await ValidateLocationInTenantAsync(tenantId.Value, location);
                if (!locationValidInTenant)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Location validation failed in tenant's location list");
                    _logger.Error($"[Location {location}]: Validation failed - Location not found in tenant's location list or improper naming convention");
                    return;
                }

                // Step 3: Validate positions
                Console.WriteLine($"[CONSOLE] [Location {location}]: Location validated in tenant's location list, validating positions");
                _logger.Debug($"[Location {location}]: Location validated in tenant's location list");
                await ValidatePositionsAsync(tenantId.Value, location);

                // Step 4: Check if employee loading should be skipped
                Console.WriteLine($"[CONSOLE] [Location {location}]: Position validation complete, checking for existing employees in Harri");
                _logger.Debug($"[Location {location}]: Position validation complete");

                int existingEmployeeCount = await GetEmployeeCountInHarri(tenantId.Value, location);
                if (ShouldSkipEmployeeLoad(existingEmployeeCount))
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Skipping bulk employee load due to sufficient existing employees ({existingEmployeeCount}), but validating employee read capability");
                    _logger.Info($"[Location {location}]: Skipping bulk employee load due to sufficient existing employees ({existingEmployeeCount}), but validating employee read capability");

                    // Even when skipping bulk load, we need to verify we can read an employee from Harri
                    await ValidateEmployeeReadFromHarri(tenantId.Value, location);
                    validationSuccessful = true;
                    return;
                }

                // Step 5: Find and load manager employee
                Console.WriteLine($"[CONSOLE] [Location {location}]: Found {existingEmployeeCount} existing employees in Harri - continuing with employee load validation per configuration");
                _logger.Info($"[Location {location}]: Found {existingEmployeeCount} existing employees in Harri - continuing with employee load validation per configuration");

                var manager = FindManagerEmployee(location);
                if (manager != null)
                {
                    bool loadSuccessful = await LoadManagerIntoHarriAsync(tenantId.Value, manager, location);
                    if (loadSuccessful)
                    {
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Validation successful");
                        _logger.Info($"[Location {location}]: Validation successful");
                        validationSuccessful = true;
                    }
                    else
                    {
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Validation failed - Employee could not be loaded into Harri");
                        _logger.Error($"[Location {location}]: Validation failed - Employee could not be loaded into Harri");
                    }
                }
                else
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: No employee found with any of the specified job codes (RORM20, RORM23, RORM24, RORM30, RORM32)");
                    _logger.Warn($"[Location {location}]: No employee found with any of the specified job codes (RORM20, RORM23, RORM24, RORM30, RORM32)");
                    _logger.Error($"[Location {location}]: Validation failed - No employee found with any of the specified job codes (RORM20, RORM23, RORM24, RORM30, RORM32)");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Exception occurred: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Validation failed with exception");
                throw;
            }
            finally
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Validation complete");
                _logger.Info($"[Location {location}]: Validation complete");
            }

            await Task.CompletedTask;
        }

        private async Task<Guid?> ResolveTenantAsync(int location)
        {
            Console.WriteLine($"[CONSOLE] [Location {location}]: Checking if location exists in HarriTenantByLocation");

            var harriTenant = _harriTenantByLocationRepository.GetTenantByLocation(location);

            if (harriTenant == null)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Location not found in HarriTenantByLocation, checking app.config tenants");
                _logger.Warn($"[Location {location}]: Location not found in HarriTenantByLocation, checking app.config tenants");
                return null;
            }

            Console.WriteLine($"[CONSOLE] [Location {location}]: Found tenant {harriTenant.TenantId}, validating location in tenant's location list");
            _logger.Debug($"[Location {location}]: Found tenant {harriTenant.TenantId}");

            return harriTenant.TenantId;
        }

        private async Task<bool> ValidateLocationInTenantAsync(Guid tenantId, int location)
        {
            return await ValidateLocationInTenant(tenantId, location);
        }

        private bool ShouldSkipEmployeeLoad(int existingCount)
        {
            bool skipWhenPreexistingFound = _configurationService.SkipEmployeeLoadWhenPreexistingFound();
            int minimumCountToSkip = _configurationService.MinimumExistingEmployeeCountToSkip();

            Console.WriteLine($"[CONSOLE] Configuration - SkipWhenPreexistingFound: {skipWhenPreexistingFound}, MinimumCountToSkip: {minimumCountToSkip}");
            _logger.Debug($"Configuration - SkipWhenPreexistingFound: {skipWhenPreexistingFound}, MinimumCountToSkip: {minimumCountToSkip}");

            if (skipWhenPreexistingFound && existingCount >= minimumCountToSkip)
            {
                Console.WriteLine($"[CONSOLE] Found {existingCount} existing employees in Harri (>= {minimumCountToSkip}) - validation successful, skipping employee load per configuration");
                _logger.Info($"'Loading employee test' skipped because employees are already loaded into tenant");
                _logger.Debug($"Found {existingCount} existing employees in Harri (>= {minimumCountToSkip}) - validation successful, skipping employee load per configuration");
                return true;
            }

            return false;
        }

        private Domain.Concepts.Employee FindManagerEmployee(int location)
        {
            // Get primary employees in the store, active status (01)
            var employees = _employeeRepository.GetListByLocationId(location, includeBorrowed: false, includeTerminated: false);

            Console.WriteLine($"[CONSOLE] [Location {location}]: Found {employees?.Count() ?? 0} employees");
            _logger.Info($"[Location {location}]: Found {employees?.Count() ?? 0} employees");

            // Look for employees with specified job codes in priority order: RORM20, RORM23, RORM24, RORM30, RORM32
            var jobCodesToSearch = Constants.ManagerJobCodes;
            var manager = employees
                .Where(e => jobCodesToSearch.Contains(e.CurrJobCodeCode) && e.CurrentStatus == "01")
                .OrderBy(e => Array.IndexOf(jobCodesToSearch, e.CurrJobCodeCode))
                .FirstOrDefault();

            if (manager != null)
            {
                var displayName = !string.IsNullOrEmpty(manager.NickName) ? manager.NickName : manager.FirstName;
                Console.WriteLine($"[CONSOLE] [Location {location}]: Found employee with {manager.CurrJobCodeCode} job code - EmployeeId: {manager.EmployeeId}, BadgeId: {manager.BadgeId}, Name: {displayName} {manager.LastName}");
                _logger.Info($"[Location {location}]: Found employee with {manager.CurrJobCodeCode} job code - EmployeeId: {manager.EmployeeId}, BadgeId: {manager.BadgeId}, Name: {displayName} {manager.LastName}");
            }

            return manager;
        }

        private async Task<bool> LoadManagerIntoHarriAsync(Guid tenantId, Domain.Concepts.Employee manager, int location)
        {
            return await LoadEmployeeIntoHarri(tenantId, manager, location);
        }

        private async Task<int> GetEmployeeCountInHarri(Guid tenantId, int location)
        {
            try
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Getting employee count from Harri tenant {tenantId}");
                _logger.Debug($"[Location {location}]: Getting employee count from Harri tenant {tenantId}");

                // FIXED: Use correct endpoint format and API version
                // The original code was using a constants endpoint which may not exist
                // Use a more standard employees endpoint with location filtering
                var employeesUrl = $"employees?location_id={location}&page=1&limit=1";

                Console.WriteLine($"[CONSOLE] [Location {location}]: Calling Harri employees endpoint: {employeesUrl}");
                _logger.Debug($"[Location {location}]: Calling Harri employees endpoint: {employeesUrl}");

                // FIXED: Use V1 instead of V2 - V2 may not be implemented for employees endpoint
                var response = await _callHarriWebServiceProvider.Call(tenantId, Method.Get, employeesUrl, null, false, Constants.DefaultApiVersion);

                if (!response.IsSuccessful)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to get employees from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    _logger.Error($"[Location {location}]: Failed to get employees from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    return 0; // Return 0 if call fails - will continue with employee loading validation
                }

                Console.WriteLine($"[CONSOLE] [Location {location}]: Successfully retrieved employees from Harri");
                _logger.Debug($"[Location {location}]: Harri employees response: {response.Content}");

                // Deserialize the response
                dynamic content = JsonConvert.DeserializeObject(response.Content);

                // Handle different possible response structures
                int employeeCount = 0;

                // Try different possible response structures
                if (content?.data?.employees != null)
                {
                    employeeCount = ((Newtonsoft.Json.Linq.JArray)content.data.employees).Count;
                }
                else if (content?.employees != null)
                {
                    employeeCount = ((Newtonsoft.Json.Linq.JArray)content.employees).Count;
                }
                else if (content?.total_count != null)
                {
                    employeeCount = (int)content.total_count;
                }
                else if (content?.count != null)
                {
                    employeeCount = (int)content.count;
                }
                else
                {
                    // Fallback: try to parse as HarriInboundEmployeeData
                    try
                    {
                        var employees = JsonConvert.DeserializeObject<HarriInboundEmployeeData>(content.ToString());
                        employeeCount = employees?.Employees?.Count ?? 0;
                    }
                    catch (Exception parseEx)
                    {
                        _logger.Warn($"[Location {location}]: Could not parse employee response: {parseEx.Message}");
                        employeeCount = 0;
                    }
                }

                Console.WriteLine($"[CONSOLE] [Location {location}]: Found {employeeCount} employees in Harri");
                _logger.Debug($"[Location {location}]: Found {employeeCount} employees in Harri");

                return employeeCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Error getting employee count from Harri: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Error getting employee count from Harri");
                return 0; // Return 0 if error occurs - will continue with employee loading validation
            }
        }

        private async Task ValidatePositionsAsync(Guid tenantId, int location)
        {
            try
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Validating positions in Harri tenant {tenantId}");
                _logger.Debug($"[Location {location}]: Validating positions in Harri tenant {tenantId}");

                // Required positions for validation
                var requiredPositions = new HashSet<string>(Constants.ValidPositionCodes);

                // Call Harri API to get positions
                var response = await _callHarriWebServiceProvider.Call(tenantId, Method.Get, "positions", null, false, Constants.DefaultApiVersion);

                if (!response.IsSuccessful)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to get positions from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    _logger.Error($"[Location {location}]: Failed to get positions from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    return;
                }

                _logger.Debug($"[Location {location}]: Harri positions response: {response.Content}");

                // Parse the response to get actual positions
                dynamic content = JsonConvert.DeserializeObject(response.Content);
                var actualPositions = new HashSet<string>();
                var extraPositionDetails = new List<(string code, string name)>();

                if (content != null)
                {
                    // Handle both array and object with positions array
                    var positions = content is Newtonsoft.Json.Linq.JArray ? content : content.positions ?? content;

                    if (positions != null)
                    {
                        foreach (var position in positions)
                        {
                            string positionCode = position?.code ?? position?.Code ?? position?.name ?? position?.Name;
                            string positionName = position?.name ?? position?.Name ?? position?.title ?? position?.Title ?? "";

                            // Add to actual positions if code exists
                            if (!string.IsNullOrEmpty(positionCode))
                            {
                                actualPositions.Add(positionCode);
                            }

                            // Track all positions (including null codes) for extra position checking
                            if (string.IsNullOrEmpty(positionCode) || !requiredPositions.Contains(positionCode))
                            {
                                extraPositionDetails.Add((positionCode ?? "[NULL]", positionName));
                            }
                        }
                    }
                }

                // Check for missing positions
                var missingPositions = requiredPositions.Except(actualPositions).ToList();

                // Extra positions are already captured in extraPositionDetails

                bool hasDiscrepancies = missingPositions.Any() || extraPositionDetails.Any();

                if (!hasDiscrepancies)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Position test passed");
                    _logger.Info($"[Location {location}]: Position test passed");
                }
                else
                {
                    if (missingPositions.Any())
                    {
                        string missingList = string.Join(", ", missingPositions);
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Missing positions in Harri: {missingList}");
                        _logger.Warn($"[Location {location}]: Missing positions in Harri: {missingList}");
                    }

                    if (extraPositionDetails.Any())
                    {
                        var extraList = extraPositionDetails.Select(ep =>
                            string.IsNullOrEmpty(ep.name)
                                ? ep.code
                                : $"{ep.code} ({ep.name})"
                        );
                        string extraPositionsText = string.Join(", ", extraList);
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Extra positions found in Harri: {extraPositionsText}");
                        _logger.Warn($"[Location {location}]: Extra positions found in Harri: {extraPositionsText}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Error validating positions: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Error validating positions");
                // Don't throw - continue with validation process
            }
        }

        private async Task<bool> ValidateLocationInTenant(Guid tenantId, int location)
        {
            try
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Getting locations from tenant {tenantId}");
                _logger.Debug($"[Location {location}]: Getting locations from tenant {tenantId}");

                // Create URL generator for locations endpoint
                var urlGenerator = new HarriUrlGeneratorFactory("").CreateGenerator(UrlType.LocationList);
                var locationsUrl = urlGenerator.Generate(null);

                Console.WriteLine($"[CONSOLE] [Location {location}]: Calling Harri locations endpoint: {locationsUrl}");
                _logger.Debug($"[Location {location}]: Calling Harri locations endpoint: {locationsUrl}");

                // Call Harri locations endpoint
                var response = await _callHarriWebServiceProvider.Call(tenantId, Method.Get, locationsUrl, null, false, Constants.DefaultApiVersion);

                if (!response.IsSuccessful)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to get locations from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    _logger.Error($"[Location {location}]: Failed to get locations from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    return false;
                }

                Console.WriteLine($"[CONSOLE] [Location {location}]: Successfully retrieved locations from Harri");
                _logger.Debug($"[Location {location}]: Harri locations response: {response.Content}");

                // Deserialize the response
                var locations = JsonConvert.DeserializeObject<HarriLocations2List>(response.Content);

                if (locations == null || !locations.Any())
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: No locations found in tenant");
                    _logger.Warn($"[Location {location}]: No locations found in tenant");
                    return false;
                }

                Console.WriteLine($"[CONSOLE] [Location {location}]: Found {locations.Count} locations in tenant, searching for location {location}");
                _logger.Debug($"[Location {location}]: Found {locations.Count} locations in tenant");

                // Find the location in the list
                var targetLocation = locations.FirstOrDefault(l => l.Id == location);

                if (targetLocation == null)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Location {location} not found in tenant's location list");
                    _logger.Error($"[Location {location}]: Location {location} not found in tenant's location list");
                    return false;
                }

                Console.WriteLine($"[CONSOLE] [Location {location}]: Found location in tenant - Id: {targetLocation.Id}, Name: '{targetLocation.Name}', Type: '{targetLocation.Type}'");
                _logger.Debug($"[Location {location}]: Found location in tenant - Id: {targetLocation.Id}, Name: '{targetLocation.Name}', Type: '{targetLocation.Type}'");

                // Validate naming convention: name should start with 'J{6-digit location number}' - WARNING ONLY
                var expectedNamePrefix = $"J{location:D6}";

                if (string.IsNullOrEmpty(targetLocation.Name) || !targetLocation.Name.StartsWith(expectedNamePrefix))
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: WARNING - Location name '{targetLocation.Name}' does not match expected pattern '{expectedNamePrefix}*' (continuing validation)");
                    _logger.Warn($"[Location {location}]: WARNING - Location name '{targetLocation.Name}' does not match expected pattern '{expectedNamePrefix}*' (continuing validation)");
                    // Don't return false - this is just a warning, continue with validation
                }
                else
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Location name validation successful - '{targetLocation.Name}' matches expected pattern");
                    _logger.Info($"[Location {location}]: Location name validation successful - '{targetLocation.Name}' matches expected pattern");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Error validating location in tenant: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Error validating location in tenant");
                return false;
            }
        }

        private async Task<bool> LoadEmployeeIntoHarri(Guid tenantId, Domain.Concepts.Employee employee, int location)
        {
            try
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Loading employee {employee.EmployeeId} (Badge: {employee.BadgeId}) into Harri");
                _logger.Info($"[Location {location}]: Loading employee {employee.EmployeeId} (Badge: {employee.BadgeId}) into Harri");

                var newHirePayload = CreateNewHirePayload(employee);
                var serializerSettings = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore };
                var payload = JsonConvert.SerializeObject(newHirePayload, serializerSettings);

                _logger.Debug($"[Location {location}]: Harri payload: {payload}");

                var response = await _callHarriWebServiceProvider.Call(tenantId, Method.Post, "employees", payload, false, Constants.HireApiVersion);

                if (response.IsSuccessful)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Successfully loaded employee {employee.EmployeeId} into Harri - Status: {response.StatusCode}");
                    _logger.Info($"[Location {location}]: Successfully loaded employee {employee.EmployeeId} into Harri - Status: {response.StatusCode}");

                    // Add to schedule
                    _logger.Debug($"[Location {location}]: Adding employee {employee.EmployeeId} to schedule");
                    var schedulePayload = CreateSchedulePayload();
                    var scheduleJson = JsonConvert.SerializeObject(schedulePayload, serializerSettings);

                    var scheduleResponse = await _callHarriWebServiceProvider
                        .Call(tenantId, Method.Put, $@"employees/{employee.BadgeId}/include_in_schedule", scheduleJson, false, Constants.DefaultApiVersion);

                    if (scheduleResponse.IsSuccessful)
                    {
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Successfully updated employee {employee.EmployeeId} to include_in_schedule - Status: {scheduleResponse.StatusCode}");
                        _logger.Info($"[Location {location}]: Successfully updated employee {employee.EmployeeId} to  include_in_schedule - Status: {scheduleResponse.StatusCode}");

                        // Wait 2 minutes and check for 'New Hire' event
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Waiting 2 minutes before checking for 'New Hire' event...");
                        _logger.Info($"[Location {location}]: Waiting 2 minutes before checking for 'New Hire' event for employee {employee.BadgeId}");

                        await Task.Delay(TimeSpan.FromMinutes(2));

                        Console.WriteLine($"[CONSOLE] [Location {location}]: 2 minutes elapsed, checking for 'New Hire' event");
                        _logger.Info($"[Location {location}]: 2 minutes elapsed, checking for 'New Hire' event for employee {employee.BadgeId}");

                        bool newHireEventFound = await CheckForNewHireEvent(tenantId, employee.BadgeId, location);

                        if (newHireEventFound)
                        {
                            Console.WriteLine($"[CONSOLE] [Location {location}]: 'New Hire' event successfully found for employee {employee.BadgeId}");
                            _logger.Info($"[Location {location}]: 'New Hire' event successfully found for employee {employee.BadgeId} - validation fully successful");
                        }
                        else
                        {
                            Console.WriteLine($"[CONSOLE] [Location {location}]: 'New Hire' event NOT found for employee {employee.BadgeId}");
                            _logger.Warn($"[Location {location}]: 'New Hire' event NOT found for employee {employee.BadgeId} - employee loaded but event may not have been generated");
                        }

                        return true; // Employee creation and schedule addition successful (event check is informational)
                    }
                    else
                    {
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to update employee {employee.EmployeeId} to include_in_schedule - Status: {scheduleResponse.StatusCode}, Content: {scheduleResponse.Content}");
                        _logger.Warn($"[Location {location}]: Failed to update employee {employee.EmployeeId} to include_in_schedule - Status: {scheduleResponse.StatusCode}, Content: {scheduleResponse.Content}");
                        return false; // Employee created but schedule addition failed
                    }
                }
                else
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to load employee {employee.EmployeeId} into Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    _logger.Error($"[Location {location}]: Failed to load employee {employee.EmployeeId} into Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    return false; // Employee creation failed
                }
            }
            catch (InvalidCallToHarriException ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Harri API error loading employee {employee.EmployeeId}: {ex.Message}");
                _logger.Error($"[Location {location}]: Harri API error loading employee {employee.EmployeeId}: {ex.Message}");
                return false; // Exception occurred
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Unexpected error loading employee {employee.EmployeeId} into Harri: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Unexpected error loading employee {employee.EmployeeId} into Harri");
                return false; // Exception occurred
            }
        }

        private async Task<bool> CheckForNewHireEvent(Guid tenantId, int badgeId, int location)
        {
            try
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Checking for 'New Hire' event for employee {badgeId}");
                _logger.Info($"[Location {location}]: Checking for 'New Hire' event for employee {badgeId} in tenant {tenantId}");

                // FIXED: Use a more standard events endpoint format
                // The constants endpoint may not exist or be implemented correctly
                var eventsUrl = $"events?employee_id={badgeId}&limit=50";

                Console.WriteLine($"[CONSOLE] [Location {location}]: Calling Harri events endpoint: {eventsUrl}");
                _logger.Debug($"[Location {location}]: Calling Harri events endpoint: {eventsUrl}");

                // FIXED: Use V1 API version as events endpoint typically uses V1
                var response = await _callHarriWebServiceProvider.Call(tenantId, Method.Get, eventsUrl, null, false, Constants.DefaultApiVersion);

                if (!response.IsSuccessful)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to get events from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    _logger.Error($"[Location {location}]: Failed to get events from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    return false;
                }

                Console.WriteLine($"[CONSOLE] [Location {location}]: Successfully retrieved events from Harri");
                _logger.Debug($"[Location {location}]: Harri events response: {response.Content}");

                // Parse the events response
                dynamic eventsData = JsonConvert.DeserializeObject(response.Content);

                if (eventsData == null)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: No events data found in response");
                    _logger.Warn($"[Location {location}]: No events data found in response for employee {badgeId}");
                    return false;
                }

                // Look for events in the response - handle different possible structures
                var events = eventsData.events ?? eventsData.data?.events ?? eventsData.data ?? eventsData;

                if (events == null)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: No events array found in response");
                    _logger.Warn($"[Location {location}]: No events array found in response for employee {badgeId}");
                    return false;
                }

                int totalEventsCount = 0;
                int newHireEventsCount = 0;
                bool foundNewHireEvent = false;
                DateTime cutoffTime = DateTime.Now.AddMinutes(-5); // Look for events within the last 5 minutes

                foreach (var eventItem in events)
                {
                    totalEventsCount++;

                    try
                    {
                        // Check if this is a new hire event for our employee
                        string eventType = eventItem.type ?? eventItem.eventType ?? eventItem.event_type ?? "";
                        string employeeId = eventItem.employeeId ?? eventItem.employee_id ?? eventItem.badgeId ?? eventItem.badge_id ?? "";
                        DateTime eventTime = DateTime.MinValue;

                        // Try to parse event time from various possible fields
                        if (eventItem.time != null)
                            DateTime.TryParse(eventItem.time.ToString(), out eventTime);
                        else if (eventItem.timestamp != null)
                            DateTime.TryParse(eventItem.timestamp.ToString(), out eventTime);
                        else if (eventItem.created_at != null)
                            DateTime.TryParse(eventItem.created_at.ToString(), out eventTime);

                        // Check if this is a "New Hire" or "EMPLOYEE.HIRED" event
                        bool isNewHireEvent = eventType.Equals("EMPLOYEE.HIRED", StringComparison.OrdinalIgnoreCase) ||
                                            eventType.Equals("NEW_HIRE", StringComparison.OrdinalIgnoreCase) ||
                                            eventType.Equals("NEWHIRE", StringComparison.OrdinalIgnoreCase) ||
                                            eventType.ToUpperInvariant().Contains("HIRE");

                        if (isNewHireEvent)
                        {
                            newHireEventsCount++;
                            _logger.Debug($"[Location {location}]: Found hire event - Type: {eventType}, EmployeeId: {employeeId}, Time: {eventTime}");
                        }

                        // Check if this event matches our employee and is recent
                        if (isNewHireEvent &&
                            (employeeId == badgeId.ToString() || employeeId == badgeId.ToString()) &&
                            eventTime > cutoffTime)
                        {
                            foundNewHireEvent = true;
                            Console.WriteLine($"[CONSOLE] [Location {location}]: Found matching 'New Hire' event for employee {badgeId} - Type: {eventType}, Time: {eventTime}");
                            _logger.Info($"[Location {location}]: Found matching 'New Hire' event for employee {badgeId} - Type: {eventType}, Time: {eventTime}");
                            break;
                        }
                    }
                    catch (Exception eventParseEx)
                    {
                        _logger.Debug($"[Location {location}]: Error parsing individual event: {eventParseEx.Message}");
                        // Continue processing other events
                    }
                }

                Console.WriteLine($"[CONSOLE] [Location {location}]: Event search complete - Total events: {totalEventsCount}, New hire events: {newHireEventsCount}, Found matching event: {foundNewHireEvent}");
                _logger.Info($"[Location {location}]: Event search complete for employee {badgeId} - Total events: {totalEventsCount}, New hire events: {newHireEventsCount}, Found matching event: {foundNewHireEvent}");

                return foundNewHireEvent;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Error checking for 'New Hire' event for employee {badgeId}: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Error checking for 'New Hire' event for employee {badgeId}");
                return false;
            }
        }

        private HarriNewHirePayload CreateNewHirePayload(Domain.Concepts.Employee employee)
        {
            try
            {
                return new HarriNewHirePayload
                {
                    Id = employee.BadgeId.ToString(),
                    Email = _harriTransformsProvider.GetEmployeeEmail(employee),
                    FirstName = _harriTransformsProvider.GetFirstName(employee),
                    LastName = employee.LastName,
                    MiddleName = employee.MiddleInitial,
                    KnownAs = employee.NickName ?? employee.FirstName,
                    BirthDate = employee.BirthDate,
                    Phone = employee.CellPhoneNumber ?? employee.HomePhoneNumber ?? Constants.DefaultPhoneNumber,
                    HomePhone = employee.HomePhoneNumber,
                    Gender = _genderDictionary.Translate(employee.Sex),
                    SocialSecurityNumber = employee.IsFranchiseEmployee() ? employee.Ssn : null,

                    Address = new Address
                    {
                        CountryCode = "US",
                        StateCode = employee.StateCode,
                        City = employee.City,
                        AddressLine1 = employee.Address1,
                        AddressLine2 = employee.Address2,
                        PostalCode = employee.ZipCode
                    },
                    HireDate = employee.HireDate ?? DateTime.Today,
                    PayrollId = (employee.IsCorporateEmployee()) ? $"{employee.HREmployeeId:d8}" : null,
                    LocationId = $"{employee.LastHomeLocationNumber}",
                    Position = new Position(employee.CurrJobCodeCode),
                    PayType = new PayType(employee.IsExempt(), employee.CurrPayRate?.PayRate ?? 0m),
                    Geid = $"{employee.BadgeId}"
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] Error occurred while creating the new hire payload: {ex.Message}");
                _logger.Error(ex, "An error occurred while creating the new hire payload.");
                throw;
            }
        }

        private HarriSchedulePayload CreateSchedulePayload()
        {
            return new HarriSchedulePayload
            {
                IncludeInSchedule = "REGULAR"
            };
        }

        private async Task ValidateLocationWithConfigTenants(int location)
        {
            var configTenants = _harriConfigurationService.GetConfiguredTenants();

            if (configTenants == null || !configTenants.Any())
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: No tenants configured in app.config");
                _logger.Error($"[Location {location}]: Validation failed - No tenants found in database or app.config");
                return;
            }

            Console.WriteLine($"[CONSOLE] [Location {location}]: Found {configTenants.Count} tenants in app.config, testing each one");
            _logger.Info($"[Location {location}]: Found {configTenants.Count} tenants in app.config");

            foreach (var configTenant in configTenants)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Testing config tenant '{configTenant.Name}'");
                _logger.Info($"[Location {location}]: Testing config tenant '{configTenant.Name}'");

                try
                {
                    // Create a mock HarriTenant from the config tenant
                    var mockTenant = new HarriTenant
                    {
                        Name = configTenant.Name,
                        Client = configTenant.ClientId,
                        Secret = configTenant.Secret,
                        TokenUrl = configTenant.TokenUrl
                    };

                    // Test if this tenant has the location
                    bool locationExists = await ValidateLocationInConfigTenant(mockTenant, location);

                    if (locationExists)
                    {
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Location found in config tenant '{configTenant.Name}', getting employees");
                        _logger.Info($"[Location {location}]: Location found in config tenant '{configTenant.Name}', proceeding with validation");

                        // Get employee count for this tenant/location
                        int existingEmployeeCount = await GetEmployeeCountInHarriForConfigTenant(mockTenant, location);

                        bool skipEmployeeLoad = _configurationService.SkipEmployeeLoadWhenPreexistingFound() &&
                                               existingEmployeeCount >= _configurationService.MinimumExistingEmployeeCountToSkip();

                        if (skipEmployeeLoad)
                        {
                            Console.WriteLine($"[CONSOLE] [Location {location}]: Skipping employee load - found {existingEmployeeCount} existing employees (>= {_configurationService.MinimumExistingEmployeeCountToSkip()})");
                            _logger.Info($"[Location {location}]: Validation successful - skipped employee load due to {existingEmployeeCount} existing employees");
                        }
                        else
                        {
                            Console.WriteLine($"[CONSOLE] [Location {location}]: Found {existingEmployeeCount} existing employees, proceeding with employee load test");
                            _logger.Info($"[Location {location}]: Found {existingEmployeeCount} existing employees, proceeding with employee load test");

                            // Get an employee to test loading
                            Console.WriteLine($"[CONSOLE] [Location {location}]: Getting employee from location {location}");
                            var employees = _employeeRepository.GetListByLocationId(location, includeBorrowed: false, includeTerminated: false);
                            var eligibleEmployee = employees?.FirstOrDefault(e => e.JobCode?.Code == Constants.PrimaryManagerJobCode && e.CurrentStatus == "Active");

                            if (eligibleEmployee == null)
                            {
                                Console.WriteLine($"[CONSOLE] [Location {location}]: No active RORM20 employees found for location {location}");
                                _logger.Error($"[Location {location}]: Validation failed - No active RORM20 employees found for location {location}");
                                continue;
                            }

                            // Attempt to load the employee
                            bool loadSuccessful = await LoadEmployeeIntoHarriForConfigTenant(mockTenant, eligibleEmployee, location);

                            if (loadSuccessful)
                            {
                                Console.WriteLine($"[CONSOLE] [Location {location}]: Successfully loaded employee into config tenant '{configTenant.Name}'");
                                _logger.Info($"[Location {location}]: Validation successful - employee loaded into config tenant '{configTenant.Name}'");
                            }
                            else
                            {
                                Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to load employee into config tenant '{configTenant.Name}'");
                                _logger.Error($"[Location {location}]: Validation failed - could not load employee into config tenant '{configTenant.Name}'");
                            }
                        }

                        return; // Found a working tenant, stop testing others
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Error testing config tenant '{configTenant.Name}': {ex.Message}");
                    _logger.Error(ex, $"[Location {location}]: Error testing config tenant '{configTenant.Name}'");
                    continue; // Try next tenant
                }
            }

            Console.WriteLine($"[CONSOLE] [Location {location}]: Location not found in any configured tenants");
            _logger.Error($"[Location {location}]: Validation failed - Location not found in any database or app.config tenants");
        }

        private async Task<bool> ValidateLocationInConfigTenant(HarriTenant tenant, int location)
        {
            try
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Getting locations from config tenant '{tenant.Name}'");
                _logger.Info($"[Location {location}]: Getting locations from config tenant '{tenant.Name}'");

                var urlGenerator = new HarriUrlGeneratorFactory("").CreateGenerator(UrlType.LocationList);
                var locationsUrl = urlGenerator.Generate(null);

                var response = await _callHarriWebServiceProvider.Call(tenant, Method.Get, locationsUrl, null, false, Constants.DefaultApiVersion);

                if (!response.IsSuccessful)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to get locations from config tenant - Status: {response.StatusCode}");
                    _logger.Error($"[Location {location}]: Failed to get locations from config tenant - Status: {response.StatusCode}");
                    return false;
                }

                var locations = JsonConvert.DeserializeObject<HarriLocations2List>(response.Content);
                var targetLocation = locations?.FirstOrDefault(l => l.Id == location);

                return targetLocation != null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Error validating location in config tenant: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Error validating location in config tenant");
                return false;
            }
        }

        private async Task<int> GetEmployeeCountInHarriForConfigTenant(HarriTenant tenant, int location)
        {
            try
            {
                // FIXED: Use the same improved approach as the main method
                var employeesUrl = $"employees?location_id={location}&page=1&limit=1";
                var response = await _callHarriWebServiceProvider.Call(tenant, Method.Get, employeesUrl, null, false, Constants.DefaultApiVersion);

                if (!response.IsSuccessful)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to get employees from config tenant - Status: {response.StatusCode}");
                    return 0;
                }

                dynamic content = JsonConvert.DeserializeObject(response.Content);

                // Handle different possible response structures
                int employeeCount = 0;

                if (content?.data?.employees != null)
                {
                    employeeCount = ((Newtonsoft.Json.Linq.JArray)content.data.employees).Count;
                }
                else if (content?.employees != null)
                {
                    employeeCount = ((Newtonsoft.Json.Linq.JArray)content.employees).Count;
                }
                else if (content?.total_count != null)
                {
                    employeeCount = (int)content.total_count;
                }
                else if (content?.count != null)
                {
                    employeeCount = (int)content.count;
                }
                else
                {
                    try
                    {
                        var employees = JsonConvert.DeserializeObject<HarriInboundEmployeeData>(content.ToString());
                        employeeCount = employees?.Employees?.Count ?? 0;
                    }
                    catch
                    {
                        employeeCount = 0;
                    }
                }

                return employeeCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Error getting employee count from config tenant: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Error getting employee count from config tenant");
                return 0;
            }
        }

        private async Task<bool> LoadEmployeeIntoHarriForConfigTenant(HarriTenant tenant, Domain.Concepts.Employee employee, int location)
        {
            try
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Loading employee {employee.EmployeeId} (Badge: {employee.BadgeId}) into config tenant");
                _logger.Info($"[Location {location}]: Loading employee {employee.EmployeeId} (Badge: {employee.BadgeId}) into config tenant");

                // Use the same approach as the existing method
                var newHirePayload = CreateNewHirePayload(employee);
                var serializerSettings = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore };
                var payload = JsonConvert.SerializeObject(newHirePayload, serializerSettings);

                Console.WriteLine($"[CONSOLE] [Location {location}]: Harri payload: {payload}");
                _logger.Debug($"[Location {location}]: Harri payload: {payload}");

                var response = await _callHarriWebServiceProvider.Call(tenant, Method.Post, "employees", payload, false, Constants.HireApiVersion);

                if (response.IsSuccessful)
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Successfully loaded employee into config tenant");
                    _logger.Info($"[Location {location}]: Successfully loaded employee into config tenant");
                    return true;
                }
                else
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Failed to load employee into config tenant - Status: {response.StatusCode}, Content: {response.Content}");
                    _logger.Error($"[Location {location}]: Failed to load employee into config tenant - Status: {response.StatusCode}, Content: {response.Content}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Error loading employee into config tenant: {ex.Message}");
                _logger.Error(ex, $"[Location {location}]: Error loading employee into config tenant");
                return false;
            }
        }

        private async Task ValidateEmployeeReadFromHarri(Guid tenantId, int location)
        {
            Console.WriteLine($"[CONSOLE] [Location {location}]: Validating employee read capability from Harri");
            _logger.Debug($"[Location {location}]: Validating employee read capability from Harri");

            try
            {
                // Get the first page of employees from Harri with actual employee data
                var employeesUrl = $"employees?location_id={location}&page=1&limit=1";
                var response = await _callHarriWebServiceProvider.Call(tenantId, Method.Get, employeesUrl, null, false, Constants.DefaultApiVersion);

                if (response.IsSuccessful && !string.IsNullOrEmpty(response.Content))
                {
                    // Parse the response to ensure we can read at least one employee
                    dynamic content = JsonConvert.DeserializeObject(response.Content);

                    // Check for employees in various response formats
                    bool hasEmployees = false;
                    string employeeId = null;

                    if (content?.data?.employees != null && ((Newtonsoft.Json.Linq.JArray)content.data.employees).Count > 0)
                    {
                        hasEmployees = true;
                        employeeId = content.data.employees[0]?.id?.ToString();
                    }
                    else if (content?.employees != null && ((Newtonsoft.Json.Linq.JArray)content.employees).Count > 0)
                    {
                        hasEmployees = true;
                        employeeId = content.employees[0]?.id?.ToString();
                    }

                    if (hasEmployees && !string.IsNullOrEmpty(employeeId))
                    {
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Successfully read employee {employeeId} from Harri - validation complete");
                        _logger.Info($"[Location {location}]: Successfully read employee {employeeId} from Harri - validation complete");
                    }
                    else
                    {
                        Console.WriteLine($"[CONSOLE] [Location {location}]: Warning - API returned success but no employee data found in Harri");
                        _logger.Warn($"[Location {location}]: Warning - API returned success but no employee data found in Harri");
                        throw new InvalidOperationException($"No employee data returned from Harri for location {location}");
                    }
                }
                else
                {
                    Console.WriteLine($"[CONSOLE] [Location {location}]: Error reading employees from Harri: {response.StatusCode} - {response.ErrorMessage}");
                    _logger.Error($"[Location {location}]: Error reading employees from Harri: {response.StatusCode} - {response.ErrorMessage}");
                    throw new InvalidOperationException($"Failed to read employees from Harri for location {location}: {response.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CONSOLE] [Location {location}]: Error validating employee read from Harri: {ex.Message}");
                _logger.Error($"[Location {location}]: Error validating employee read from Harri", ex);
                throw new InvalidOperationException($"Failed to validate employee read capability from Harri for location {location}: {ex.Message}", ex);
            }
        }
    }
}