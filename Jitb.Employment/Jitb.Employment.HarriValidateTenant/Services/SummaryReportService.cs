using Jitb.Employment.HarriValidateTenant.Models;
using NLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    public class SummaryReportService : ISummaryReportService
    {
        private static readonly ILogger _logger = LogManager.GetCurrentClassLogger();

        private const int TenantColumnWidth = 15;
        private const int LocationColumnWidth = 8;
        private const int ComponentColumnWidth = 20;
        private const int ResultColumnWidth = 20;
        private const int DetailsColumnWidth = 40;

        public string GenerateSummaryReport(List<TenantValidationResult> results)
        {
            try
            {
                _logger.Debug("Generating summary report for {0} tenant results", results?.Count ?? 0);

                if (results == null || !results.Any())
                {
                    return "No tenant validation results to report.";
                }

                var report = new StringBuilder();

                // Generate header
                AppendHeader(report);

                // Generate data rows - sort with null tenants first, then by tenant display name
                var sortedResults = results.OrderBy(r =>
                {
                    var displayName = r.GetDisplayName();
                    // Put null tenants at the top by returning "0" for them, "1" + displayName for others
                    return displayName == "(null)" ? "0" : "1" + displayName;
                });

                var isFirstResult = true;
                foreach (var result in sortedResults)
                {
                    // Add empty line between tenants (but not before the first tenant)
                    if (!isFirstResult)
                    {
                        report.AppendLine();
                    }
                    AppendTenantResult(report, result);
                    isFirstResult = false;
                }

                _logger.Debug("Summary report generated successfully");
                return report.ToString();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating summary report");
                throw new InvalidOperationException("Failed to generate summary report", ex);
            }
        }

        public async Task<string> GenerateSummaryReportAsync(List<TenantValidationResult> results)
        {
            return await Task.Run(() => GenerateSummaryReport(results));
        }

        public void SaveReportToFile(string report, string filePath)
        {
            try
            {
                _logger.Debug("Saving report to file: {0}", filePath);

                if (string.IsNullOrEmpty(report))
                    throw new ArgumentException("Report content cannot be null or empty", nameof(report));

                if (string.IsNullOrEmpty(filePath))
                    throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(filePath, report, Encoding.UTF8);
                _logger.Info("Report saved successfully to: {0}", filePath);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error saving report to file: {0}", filePath);
                throw new InvalidOperationException($"Failed to save report to {filePath}", ex);
            }
        }

        public string FormatReportForConsole(List<TenantValidationResult> results)
        {
            return GenerateSummaryReport(results);
        }

        private void AppendHeader(StringBuilder report)
        {
            // Header row
            report.AppendLine(
                PadRight("Tenant", TenantColumnWidth) +
                PadRight("Location", LocationColumnWidth) +
                PadRight("Test Component", ComponentColumnWidth) +
                PadRight("Result", ResultColumnWidth) +
                "Details/Notes");

            // Separator row
            report.AppendLine(
                new string('-', TenantColumnWidth - 1) + "   " +
                new string('-', LocationColumnWidth - 1) + "    " +
                new string('-', ComponentColumnWidth - 1) + "       " +
                new string('-', ResultColumnWidth - 1) + "        " +
                new string('-', DetailsColumnWidth - 1));
        }

        private void AppendTenantResult(StringBuilder report, TenantValidationResult result)
        {
            var isFirstRow = true;

            // If no component results, still show the tenant row
            if (!result.ComponentResults.Any())
            {
                var tenantDisplay = result.GetDisplayName();
                AppendDataRow(report, tenantDisplay, result.ProcessedLocation.ToString(), "", "", "");
                return;
            }

            foreach (var component in result.ComponentResults.OrderBy(GetComponentDisplayOrder))
            {
                var tenantDisplay = isFirstRow ? result.GetDisplayName() : "";
                var locationDisplay = isFirstRow ? result.ProcessedLocation.ToString() : "";

                AppendDataRow(report, tenantDisplay, locationDisplay, component.ComponentName, component.Status, component.Details ?? "");
                isFirstRow = false;
            }

            // Add untested locations row if any exist
            if (result.UntestedLocations.Any())
            {
                var untestedLocationsList = string.Join(", ", result.UntestedLocations);
                AppendDataRow(report, "", "", "Additional Locations", "Not Tested", untestedLocationsList);
            }

            // Add final result row
            AppendDataRow(report, "", "", "Final Result", result.FinalResult ?? "Unknown", "");
        }

        private void AppendDataRow(StringBuilder report, string tenant, string location, string component, string result, string details)
        {
            report.AppendLine(
                PadRight(tenant, TenantColumnWidth) +
                PadRight(location, LocationColumnWidth) +
                PadRight(component, ComponentColumnWidth) +
                PadRight(result, ResultColumnWidth) +
                (details ?? ""));
        }

        private string PadRight(string value, int totalWidth)
        {
            if (string.IsNullOrEmpty(value))
                return new string(' ', totalWidth);

            return value.Length >= totalWidth
                ? value.Substring(0, totalWidth - 1) + " "
                : value.PadRight(totalWidth);
        }

        private int GetComponentDisplayOrder(ValidationComponentResult component)
        {
            // Define display order for components
            switch (component.ComponentName)
            {
                case "Tenant Lookup":
                    return 1;
                case "GetLocations":
                    return 2;
                case "Load Employee":
                    return 3;
                case "Read Employee":
                    return 4;
                case "Position Mapping":
                    return 5;
                case "Event Received":
                    return 6;
                case "Additional Locations":
                    return 7;
                case "Final Result":
                    return 8;
                default:
                    return 999;
            }
        }
    }
}