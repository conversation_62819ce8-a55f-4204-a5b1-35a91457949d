using System;
using System.Collections.Generic;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Result class for tenant processing workflow operations.
    /// Provides comprehensive tracking of tenant processing outcomes, untested locations, and summary statistics.
    /// Follows SOLID principles with clear separation of concerns for result data.
    /// </summary>
    public class TenantProcessingResult
    {
        /// <summary>
        /// Gets or sets the dictionary of processed tenants with their success status.
        /// Key: Tenant identifier, Value: true if processed (successfully or not), false if not attempted.
        /// </summary>
        public Dictionary<string, bool> ProcessedTenants { get; set; } = new Dictionary<string, bool>();

        /// <summary>
        /// Gets or sets the dictionary of successfully processed tenants.
        /// Key: Tenant identifier, Value: true if validation completed without exceptions.
        /// </summary>
        public Dictionary<string, bool> SuccessfulTenants { get; set; } = new Dictionary<string, bool>();

        /// <summary>
        /// Gets or sets the dictionary of failed tenants with their associated exceptions.
        /// Key: Tenant identifier, Value: the exception that caused the failure.
        /// </summary>
        public Dictionary<string, Exception> FailedTenants { get; set; } = new Dictionary<string, Exception>();

        /// <summary>
        /// Gets or sets the collection of location IDs that were not tested.
        /// These are locations that were not selected as the first location for their respective tenants.
        /// </summary>
        public IEnumerable<int> UntestedLocations { get; set; } = new List<int>();

        /// <summary>
        /// Gets or sets the dictionary mapping tenant identifiers to their untested location IDs.
        /// Key: Tenant identifier, Value: collection of location IDs not tested for that tenant.
        /// </summary>
        public Dictionary<string, IEnumerable<int>> UntestedLocationsByTenant { get; set; } = new Dictionary<string, IEnumerable<int>>();

        /// <summary>
        /// Gets or sets the total number of input locations that were considered for processing.
        /// </summary>
        public int TotalLocations { get; set; }

        /// <summary>
        /// Gets or sets the number of locations that were actually tested.
        /// This should equal the number of unique tenants processed.
        /// </summary>
        public int TestedLocations { get; set; }

        /// <summary>
        /// Gets or sets whether the workflow was skipped due to configuration settings.
        /// True if tenant workflow was disabled and processing was skipped entirely.
        /// </summary>
        public bool WorkflowSkipped { get; set; }
    }
}