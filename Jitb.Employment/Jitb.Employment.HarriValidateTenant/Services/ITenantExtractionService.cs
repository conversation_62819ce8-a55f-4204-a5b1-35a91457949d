using System.Collections.Generic;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Service for extracting tenant information from location data and selecting appropriate locations per tenant.
    /// Follows SOLID principles with single responsibility for tenant extraction operations.
    /// </summary>
    public interface ITenantExtractionService
    {
        /// <summary>
        /// Extracts and groups locations by their associated tenants.
        /// </summary>
        /// <param name="locations">Collection of location IDs to process</param>
        /// <returns>Dictionary mapping tenant identifiers to lists of their associated location IDs</returns>
        /// <exception cref="System.ArgumentNullException">Thrown when locations parameter is null</exception>
        Dictionary<string, List<int>> ExtractTenantsFromLocations(IEnumerable<int> locations);

        /// <summary>
        /// Selects the first location for each tenant from tenant-location mappings.
        /// </summary>
        /// <param name="tenantLocationMappings">Dictionary mapping tenant identifiers to lists of location IDs</param>
        /// <returns>Dictionary mapping tenant identifiers to their first location ID</returns>
        /// <exception cref="System.ArgumentNullException">Thrown when tenantLocationMappings parameter is null</exception>
        Dictionary<string, int> SelectFirstLocationPerTenant(Dictionary<string, List<int>> tenantLocationMappings);
    }
}