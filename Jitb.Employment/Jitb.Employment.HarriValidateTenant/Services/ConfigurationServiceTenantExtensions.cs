using System.Configuration;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Extension methods for IConfigurationService to provide tenant-specific configuration settings.
    /// Follows SOLID principles by extending functionality without modifying existing interfaces.
    /// </summary>
    public static class ConfigurationServiceTenantExtensions
    {
        /// <summary>
        /// Gets whether tenant validation is enabled.
        /// </summary>
        /// <param name="configService">The configuration service instance</param>
        /// <returns>
        /// True if tenant validation should be performed; false to skip tenant validation.
        /// Defaults to true when setting is not present.
        /// </returns>
        /// <exception cref="ConfigurationErrorsException">Thrown when the configuration value is not a valid boolean</exception>
        public static bool IsTenantValidationEnabled(this IConfigurationService configService)
        {
            var tenantValidationValue = ConfigurationManager.AppSettings["IsTenantValidationEnabled"];
            
            if (string.IsNullOrWhiteSpace(tenantValidationValue))
            {
                // Default to true - tenant validation is enabled by default
                return true;
            }

            if (bool.TryParse(tenantValidationValue, out bool isEnabled))
            {
                return isEnabled;
            }
            else
            {
                throw new ConfigurationErrorsException($"Invalid IsTenantValidationEnabled value: {tenantValidationValue}. Must be true or false.");
            }
        }

        /// <summary>
        /// Gets whether tenant workflow is enabled.
        /// </summary>
        /// <param name="configService">The configuration service instance</param>
        /// <returns>
        /// True if tenant-focused workflow should be used; false to fall back to location-focused processing.
        /// Defaults to true when setting is not present.
        /// </returns>
        /// <exception cref="ConfigurationErrorsException">Thrown when the configuration value is not a valid boolean</exception>
        public static bool IsTenantWorkflowEnabled(this IConfigurationService configService)
        {
            var tenantWorkflowValue = ConfigurationManager.AppSettings["IsTenantWorkflowEnabled"];
            
            if (string.IsNullOrWhiteSpace(tenantWorkflowValue))
            {
                // Default to true - tenant workflow is enabled by default
                return true;
            }

            if (bool.TryParse(tenantWorkflowValue, out bool isEnabled))
            {
                return isEnabled;
            }
            else
            {
                throw new ConfigurationErrorsException($"Invalid IsTenantWorkflowEnabled value: {tenantWorkflowValue}. Must be true or false.");
            }
        }

        /// <summary>
        /// Gets the maximum number of tenants that can be processed concurrently.
        /// </summary>
        /// <param name="configService">The configuration service instance</param>
        /// <returns>
        /// The maximum number of tenant validations that can run in parallel.
        /// Defaults to 5 to balance performance with API rate limiting.
        /// </returns>
        /// <exception cref="ConfigurationErrorsException">Thrown when the configuration value is not a valid positive integer</exception>
        /// <remarks>
        /// This setting controls how many tenants can be processed simultaneously to improve performance
        /// while preventing overwhelming the Harri API with too many concurrent requests.
        /// Higher values may improve performance but could hit API rate limits.
        /// Lower values are more conservative but may take longer to complete.
        /// A value of 1 effectively disables parallel processing (sequential mode).
        /// </remarks>
        public static int GetMaxConcurrentTenants(this IConfigurationService configService)
        {
            var maxConcurrencyValue = ConfigurationManager.AppSettings["MaxConcurrentTenants"];
            
            if (string.IsNullOrWhiteSpace(maxConcurrencyValue))
            {
                // Default to 5 for balanced performance without overwhelming API
                return 5;
            }

            if (int.TryParse(maxConcurrencyValue, out int maxConcurrency) && maxConcurrency > 0)
            {
                return maxConcurrency;
            }
            else
            {
                throw new ConfigurationErrorsException($"Invalid MaxConcurrentTenants value: {maxConcurrencyValue}. Must be a positive integer.");
            }
        }
    }
}