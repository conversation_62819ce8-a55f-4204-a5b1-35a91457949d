using System.Collections.Generic;
using Jitb.Employment.HarriValidateTenant.Models;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    public interface ITenantValidationResultCapture
    {
        void StartTenantValidation(string tenantId, int location);
        void StartTenantValidation(string tenantId, string tenantName, int location);
        void RecordComponentResult(string tenantId, string componentName, string status, string details = null);
        void AddUntestedLocation(string tenantId, int location);
        void CompleteTenantValidation(string tenantId, string finalResult);
        TenantValidationResult GetResult(string tenantId);
        List<TenantValidationResult> GetAllResults();
        void ClearResults();
    }
}