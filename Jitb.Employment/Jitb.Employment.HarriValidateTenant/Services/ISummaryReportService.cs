using System.Collections.Generic;
using System.Threading.Tasks;
using Jitb.Employment.HarriValidateTenant.Models;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    public interface ISummaryReportService
    {
        string GenerateSummaryReport(List<TenantValidationResult> results);
        Task<string> GenerateSummaryReportAsync(List<TenantValidationResult> results);
        void SaveReportToFile(string report, string filePath);
        string FormatReportForConsole(List<TenantValidationResult> results);
    }
}