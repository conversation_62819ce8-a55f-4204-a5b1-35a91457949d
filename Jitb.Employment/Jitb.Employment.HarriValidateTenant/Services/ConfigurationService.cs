using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Provides configuration settings for the Harri Validate Tenant application
    /// </summary>
    public class ConfigurationService : IConfigurationService
    {
        /// <summary>
        /// Gets the list of location IDs to process from the ProcessLocations configuration setting
        /// </summary>
        /// <returns>Collection of location IDs to validate</returns>
        /// <exception cref="ConfigurationErrorsException">Thrown when ProcessLocations setting is missing or contains invalid values</exception>
        public IEnumerable<int> GetProcessLocations()
        {
            var processLocationsValue = ConfigurationManager.AppSettings["ProcessLocations"];
            
            if (string.IsNullOrWhiteSpace(processLocationsValue))
            {
                throw new ConfigurationErrorsException("ProcessLocations setting is missing or empty in App.config");
            }

            var locationStrings = processLocationsValue.Split(',');
            var locations = new List<int>();

            foreach (var locationString in locationStrings)
            {
                if (int.TryParse(locationString.Trim(), out int location))
                {
                    locations.Add(location);
                }
                else
                {
                    throw new ConfigurationErrorsException($"Invalid location value: {locationString.Trim()}");
                }
            }

            return locations;
        }

        /// <summary>
        /// Gets whether to skip employee loading when preexisting employees are found in a tenant
        /// </summary>
        /// <returns>
        /// True if employee loading should be skipped when preexisting employees meet the minimum threshold;
        /// False if employee loading should always be attempted regardless of preexisting employees.
        /// Defaults to true for backward compatibility when setting is not present.
        /// </returns>
        /// <exception cref="ConfigurationErrorsException">Thrown when the configuration value is not a valid boolean</exception>
        /// <remarks>
        /// This setting controls whether the validation process will skip the employee loading step
        /// when a sufficient number of employees already exist in the Harri tenant for the location.
        /// When set to true, if the existing employee count meets or exceeds the MinimumExistingEmployeeCountToSkip
        /// threshold, the validation will be considered successful without attempting to load a new employee.
        /// This can improve performance and reduce unnecessary API calls when a tenant is already properly populated.
        /// </remarks>
        public bool SkipEmployeeLoadWhenPreexistingFound()
        {
            var skipSettingValue = ConfigurationManager.AppSettings["SkipEmployeeLoadWhenPreexistingFound"];
            
            if (string.IsNullOrWhiteSpace(skipSettingValue))
            {
                // Default to true for backward compatibility (existing behavior)
                return true;
            }

            if (bool.TryParse(skipSettingValue, out bool skipValue))
            {
                return skipValue;
            }
            else
            {
                throw new ConfigurationErrorsException($"Invalid SkipEmployeeLoadWhenPreexistingFound value: {skipSettingValue}. Must be true or false.");
            }
        }

        /// <summary>
        /// Gets the minimum number of existing employees required to trigger skipping employee load
        /// </summary>
        /// <returns>
        /// The minimum count of existing employees that must be present in a tenant
        /// to trigger skipping the employee loading process (when SkipEmployeeLoadWhenPreexistingFound is true).
        /// Defaults to 10 for backward compatibility when setting is not present.
        /// </returns>
        /// <exception cref="ConfigurationErrorsException">Thrown when the configuration value is not a valid non-negative integer</exception>
        /// <remarks>
        /// This setting works in conjunction with SkipEmployeeLoadWhenPreexistingFound to determine
        /// when the validation process should skip attempting to load a new employee.
        /// A value of 0 means any existing employees will trigger the skip behavior.
        /// Higher values require more employees to be present before skipping occurs.
        /// The default value of 10 maintains backward compatibility with the previous hardcoded threshold.
        /// </remarks>
        public int MinimumExistingEmployeeCountToSkip()
        {
            var minimumCountValue = ConfigurationManager.AppSettings["MinimumExistingEmployeeCountToSkip"];
            
            if (string.IsNullOrWhiteSpace(minimumCountValue))
            {
                // Default to 10 for backward compatibility (existing behavior)
                return 10;
            }

            if (int.TryParse(minimumCountValue, out int minimumCount) && minimumCount >= 0)
            {
                return minimumCount;
            }
            else
            {
                throw new ConfigurationErrorsException($"Invalid MinimumExistingEmployeeCountToSkip value: {minimumCountValue}. Must be a non-negative integer.");
            }
        }

        /// <summary>
        /// Gets the maximum number of locations that can be processed concurrently
        /// </summary>
        /// <returns>
        /// The maximum number of location validations that can run in parallel.
        /// Defaults to 5 to balance performance with API rate limiting.
        /// </returns>
        /// <exception cref="ConfigurationErrorsException">Thrown when the configuration value is not a valid positive integer</exception>
        /// <remarks>
        /// This setting controls how many locations can be processed simultaneously to improve performance
        /// while preventing overwhelming the Harri API with too many concurrent requests.
        /// Higher values may improve performance but could hit API rate limits.
        /// Lower values are more conservative but may take longer to complete.
        /// A value of 1 effectively disables parallel processing (sequential mode).
        /// </remarks>
        public int GetMaxConcurrentLocations()
        {
            var maxConcurrencyValue = ConfigurationManager.AppSettings["MaxConcurrentLocations"];
            
            if (string.IsNullOrWhiteSpace(maxConcurrencyValue))
            {
                // Default to 5 for balanced performance without overwhelming API
                return 5;
            }

            if (int.TryParse(maxConcurrencyValue, out int maxConcurrency) && maxConcurrency > 0)
            {
                return maxConcurrency;
            }
            else
            {
                throw new ConfigurationErrorsException($"Invalid MaxConcurrentLocations value: {maxConcurrencyValue}. Must be a positive integer.");
            }
        }

        /// <summary>
        /// Gets the directory path where validation reports will be saved
        /// </summary>
        /// <returns>
        /// The directory path for report output.
        /// Defaults to the application base directory if not configured.
        /// </returns>
        /// <remarks>
        /// This setting allows configuring a specific directory for report output.
        /// If the directory doesn't exist, it will be created automatically when reports are saved.
        /// If the setting is not present, reports will be saved to the application's base directory.
        /// </remarks>
        public string GetReportLocation()
        {
            var reportLocationValue = ConfigurationManager.AppSettings["ReportLocation"];
            
            if (string.IsNullOrWhiteSpace(reportLocationValue))
            {
                // Default to application base directory for backward compatibility
                return AppDomain.CurrentDomain.BaseDirectory;
            }

            return reportLocationValue;
        }

        /// <summary>
        /// Gets the comma-separated list of company email domains for email preference logic
        /// </summary>
        /// <returns>
        /// The company email domains string used for determining email preference.
        /// Defaults to "jackinthebox.com,deltaco.com" if not configured.
        /// </returns>
        /// <remarks>
        /// This setting is used by the email selection logic to determine which email address
        /// to prefer when an employee has both primary and alternate email addresses.
        /// Following UC1 from HarriOutboundUseCases.md: if the primary email contains a company domain,
        /// the alternate email is preferred; otherwise the primary email is used.
        /// </remarks>
        public string GetCompanyEmailDomains()
        {
            var companyDomainsValue = ConfigurationManager.AppSettings["CompanyEmailDomains"];
            
            if (string.IsNullOrWhiteSpace(companyDomainsValue))
            {
                // Default to jack and deltaco domains for backward compatibility
                return "jackinthebox.com,deltaco.com";
            }

            return companyDomainsValue;
        }
    }
}