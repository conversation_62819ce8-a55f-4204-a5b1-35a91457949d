using System.Collections.Generic;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Interface for configuration services providing settings for the Harri Validate Tenant application
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// Gets the list of location IDs to process
        /// </summary>
        /// <returns>Collection of location IDs to validate</returns>
        IEnumerable<int> GetProcessLocations();
        
        /// <summary>
        /// Gets whether to skip employee loading when preexisting employees are found
        /// </summary>
        /// <returns>True to skip employee loading when threshold is met, false to always attempt loading</returns>
        bool SkipEmployeeLoadWhenPreexistingFound();
        
        /// <summary>
        /// Gets the minimum number of existing employees required to trigger skipping employee load
        /// </summary>
        /// <returns>Minimum count of existing employees to trigger skip behavior</returns>
        int MinimumExistingEmployeeCountToSkip();
        
        /// <summary>
        /// Gets the maximum number of locations that can be processed concurrently
        /// </summary>
        /// <returns>Maximum number of concurrent location validations (defaults to 5)</returns>
        int GetMaxConcurrentLocations();
        
        /// <summary>
        /// Gets the directory path where validation reports will be saved
        /// </summary>
        /// <returns>Directory path for report output (defaults to application base directory)</returns>
        string GetReportLocation();
        
        /// <summary>
        /// Gets the comma-separated list of company email domains for email preference logic
        /// </summary>
        /// <returns>Company email domains string (defaults to "jackinthebox.com,deltaco.com")</returns>
        string GetCompanyEmailDomains();
    }
}