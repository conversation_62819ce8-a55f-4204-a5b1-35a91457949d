using System;
using System.Collections.Generic;
using System.Linq;
using Jitb.Employment.Domain.Repositories.Config;
using NLog;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Service implementation for extracting tenant information from location data.
    /// Follows SOLID principles with dependency injection and single responsibility.
    /// </summary>
    public class TenantExtractionService : ITenantExtractionService
    {
        private readonly IHarriTenantByLocationRepository _harriTenantByLocationRepository;
        private static readonly ILogger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Initializes a new instance of TenantExtractionService with required dependencies.
        /// </summary>
        /// <param name="harriTenantByLocationRepository">Repository for tenant-location data access</param>
        /// <exception cref="ArgumentNullException">Thrown when harriTenantByLocationRepository is null</exception>
        public TenantExtractionService(IHarriTenantByLocationRepository harriTenantByLocationRepository)
        {
            _harriTenantByLocationRepository = harriTenantByLocationRepository ?? 
                throw new ArgumentNullException(nameof(harriTenantByLocationRepository));
            
            _logger.Debug("TenantExtractionService initialized successfully");
        }

        /// <summary>
        /// Extracts and groups locations by their associated tenants.
        /// Locations without tenants are grouped under "(null)" key.
        /// </summary>
        /// <param name="locations">Collection of location IDs to process</param>
        /// <returns>Dictionary mapping tenant identifiers to lists of their associated location IDs</returns>
        /// <exception cref="ArgumentNullException">Thrown when locations parameter is null</exception>
        public Dictionary<string, List<int>> ExtractTenantsFromLocations(IEnumerable<int> locations)
        {
            if (locations == null)
            {
                throw new ArgumentNullException(nameof(locations), "Locations parameter cannot be null");
            }

            var result = new Dictionary<string, List<int>>();
            
            // Handle empty collections gracefully
            var locationList = locations.ToList();
            if (!locationList.Any())
            {
                _logger.Debug("ExtractTenantsFromLocations: Empty location list provided, returning empty dictionary");
                return result;
            }

            _logger.Debug($"ExtractTenantsFromLocations: Processing {locationList.Count} locations");

            foreach (var location in locationList)
            {
                try
                {
                    var tenantData = _harriTenantByLocationRepository.GetTenantByLocation(location);
                    string tenantKey;
                    
                    if (tenantData == null)
                    {
                        tenantKey = "(null)";
                        _logger.Debug($"Location {location}: No tenant found, grouping under '(null)'");
                    }
                    else
                    {
                        tenantKey = tenantData.TenantId.ToString();
                        _logger.Debug($"Location {location}: Found tenant {tenantKey}");
                    }

                    // Initialize list if tenant key doesn't exist
                    if (!result.ContainsKey(tenantKey))
                    {
                        result[tenantKey] = new List<int>();
                    }

                    // Add location to the appropriate tenant group
                    result[tenantKey].Add(location);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"Error processing location {location} for tenant extraction");
                    // Continue processing other locations even if one fails
                }
            }

            _logger.Debug($"ExtractTenantsFromLocations: Completed processing, found {result.Count} unique tenants");
            return result;
        }

        /// <summary>
        /// Selects the first location for each tenant from tenant-location mappings.
        /// </summary>
        /// <param name="tenantLocationMappings">Dictionary mapping tenant identifiers to lists of location IDs</param>
        /// <returns>Dictionary mapping tenant identifiers to their first location ID</returns>
        /// <exception cref="ArgumentNullException">Thrown when tenantLocationMappings parameter is null</exception>
        public Dictionary<string, int> SelectFirstLocationPerTenant(Dictionary<string, List<int>> tenantLocationMappings)
        {
            if (tenantLocationMappings == null)
            {
                throw new ArgumentNullException(nameof(tenantLocationMappings), 
                    "TenantLocationMappings parameter cannot be null");
            }

            var result = new Dictionary<string, int>();

            // Handle empty collections gracefully
            if (!tenantLocationMappings.Any())
            {
                _logger.Debug("SelectFirstLocationPerTenant: Empty tenant mappings provided, returning empty dictionary");
                return result;
            }

            _logger.Debug($"SelectFirstLocationPerTenant: Processing {tenantLocationMappings.Count} tenant mappings");

            foreach (var tenantMapping in tenantLocationMappings)
            {
                try
                {
                    var tenant = tenantMapping.Key;
                    var locations = tenantMapping.Value;

                    // Skip tenants with no locations or null/empty location lists
                    if (locations == null || !locations.Any())
                    {
                        _logger.Warn($"Tenant {tenant}: No locations found, skipping");
                        continue;
                    }

                    // Select the first location for this tenant
                    var firstLocation = locations.First();
                    result[tenant] = firstLocation;

                    _logger.Debug($"Tenant {tenant}: Selected first location {firstLocation} from {locations.Count} locations");
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"Error processing tenant {tenantMapping.Key} for first location selection");
                    // Continue processing other tenants even if one fails
                }
            }

            _logger.Debug($"SelectFirstLocationPerTenant: Completed processing, selected locations for {result.Count} tenants");
            return result;
        }
    }
}