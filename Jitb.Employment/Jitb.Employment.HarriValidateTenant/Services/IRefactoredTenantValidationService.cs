using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Expanded interface for the refactored HarriValidateTenant system.
    /// This interface defines the new pseudo code structure with ValueTuple return types.
    /// 
    /// CRITICAL: This interface does NOT exist yet - these are TDD Red phase definitions.
    /// The failing tests will drive the implementation of these methods.
    /// 
    /// Key design principles:
    /// - ValidationRoutine() orchestrates the overall validation process
    /// - All result methods return ValueTuples with (result text, additional details)
    /// - Tenant list methods return ValueTuples with (tenantId, tenantName, List&lt;int&gt; locations)
    /// - Skip logic is built into LoadManagerAsync based on existing employee counts
    /// - All methods are async and mockable for unit testing
    /// </summary>
    public interface IRefactoredTenantValidationService
    {
        /// <summary>
        /// Main validation routine that orchestrates the entire validation process.
        /// Follows the pseudo code structure:
        /// 1. Get tenants for all locations
        /// 2. For null tenants: add to report with 'Fail' result
        /// 3. For valid tenants: call ValidateTenantsAsync
        /// 4. Finalize and print the report
        /// </summary>
        /// <returns>Task representing the asynchronous validation operation</returns>
        Task ValidationRoutine();

        /// <summary>
        /// Validates tenants with the specified tenant information.
        /// Implements the core validation logic with skip conditions:
        /// 1. Get employee count for location
        /// 2. If skip conditions met: skip LoadManager, else load manager
        /// 3. Read employee (first employee if skipped, loaded manager if not)
        /// 4. Validate positions
        /// 5. Build report for tenant
        /// </summary>
        /// <param name="tenantId">The tenant identifier</param>
        /// <param name="tenantName">The tenant display name</param>
        /// <param name="locations">List of location IDs for this tenant</param>
        /// <returns>Task representing the asynchronous validation operation</returns>
        Task ValidateTenantsAsync(string tenantId, string tenantName, List<int> locations);

        /// <summary>
        /// Gets all tenants organized by their locations.
        /// Returns ValueTuple containing tenantId, tenantName, and list of locations.
        /// Groups locations by tenant and handles null tenants appropriately.
        /// </summary>
        /// <returns>Array of ValueTuples containing (tenantId, tenantName, List&lt;int&gt; locations)</returns>
        Task<(string tenantId, string tenantName, List<int> locations)[]> GetTenantsForAllLocationsAsync();

        /// <summary>
        /// Gets employees for a specific tenant and location.
        /// Returns ValueTuple containing employee count and first employee object.
        /// Limits employee retrieval to maximum of 50 employees for performance.
        /// </summary>
        /// <param name="tenantId">The tenant identifier</param>
        /// <param name="locationId">The location identifier</param>
        /// <returns>ValueTuple containing (employee count, first employee object)</returns>
        Task<(int count, object firstEmployee)> GetEmployeesAsync(string tenantId, int locationId);

        /// <summary>
        /// Gets all locations for a tenant to validate read access to locations API.
        /// Returns ValueTuple containing result text and details.
        /// </summary>
        /// <param name="tenantId">The tenant identifier</param>
        /// <returns>ValueTuple containing (result text, details)</returns>
        Task<(string result, string details)> GetLocationsForTenantAsync(string tenantId);

        /// <summary>
        /// Loads a manager for the specified location with skip logic.
        /// Returns ValueTuple containing result text, details, and loaded employee data.
        /// 
        /// Skip logic: If skipWhenPreexistingFound is true AND existingCount >= minimumCountToSkip,
        /// returns ("Skipped", details, null) without loading. Otherwise attempts to load manager.
        /// </summary>
        /// <param name="tenantId">The tenant identifier</param>
        /// <param name="locationId">The location identifier</param>
        /// <param name="existingCount">Number of existing employees from GetEmployeesAsync</param>
        /// <param name="skipWhenPreexistingFound">Configuration flag for skip behavior</param>
        /// <param name="minimumCountToSkip">Minimum employee count threshold for skipping</param>
        /// <returns>ValueTuple containing (result text, details, loaded employee data)</returns>
        Task<(string result, string details, object loadedEmployee)> LoadManagerAsync(string tenantId, int locationId, 
            int existingCount, bool skipWhenPreexistingFound, int minimumCountToSkip);

        /// <summary>
        /// Reads an employee based on whether the load manager step was skipped.
        /// Returns ValueTuple containing result text and details.
        /// 
        /// If wasSkipped is true: reads the first employee from GetEmployeesAsync
        /// If wasSkipped is false: reads the manager loaded in LoadManagerAsync
        /// </summary>
        /// <param name="tenantId">The tenant identifier</param>
        /// <param name="locationId">The location identifier</param>
        /// <param name="wasSkipped">Whether the LoadManagerAsync step was skipped</param>
        /// <param name="employeeToRead">The employee object to read (first employee or loaded manager)</param>
        /// <returns>ValueTuple containing (result text, details)</returns>
        Task<(string result, string details)> ReadEmployeeAsync(string tenantId, int locationId, 
            bool wasSkipped, object employeeToRead);

        /// <summary>
        /// Validates positions for the specified tenant and location.
        /// Returns ValueTuple containing result text and details.
        /// Performs position validation through Harri API calls.
        /// </summary>
        /// <param name="tenantId">The tenant identifier</param>
        /// <param name="locationId">The location identifier</param>
        /// <returns>ValueTuple containing (result text, details)</returns>
        Task<(string result, string details)> ValidatePositionsAsync(string tenantId, int locationId);

        /// <summary>
        /// Temporary test method to compare database vs App.config tenant authentication.
        /// This helps isolate the 501 error cause by testing both approaches side-by-side.
        /// </summary>
        /// <returns>String containing detailed test results</returns>
        Task<string> TestTenantAuthentication();

    }
}