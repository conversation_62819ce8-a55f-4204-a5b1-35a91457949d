using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using NLog;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Implementation of tenant processing workflow service.
    /// Orchestrates end-to-end tenant-focused processing including extraction, selection, and validation.
    /// Follows SOLID principles with dependency injection and comprehensive error handling.
    /// </summary>
    public class TenantProcessingWorkflowService : ITenantProcessingWorkflowService
    {
        private readonly ITenantExtractionService _tenantExtractionService;
        private readonly ITenantValidationService _tenantValidationService;
        private readonly IConfigurationService _configurationService;
        private static readonly ILogger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Initializes a new instance of TenantProcessingWorkflowService with required dependencies.
        /// </summary>
        /// <param name="tenantExtractionService">Service for tenant extraction operations</param>
        /// <param name="tenantValidationService">Service for tenant validation operations</param>
        /// <param name="configurationService">Service for accessing configuration settings</param>
        /// <exception cref="ArgumentNullException">Thrown when any parameter is null</exception>
        public TenantProcessingWorkflowService(
            ITenantExtractionService tenantExtractionService,
            ITenantValidationService tenantValidationService,
            IConfigurationService configurationService)
        {
            _tenantExtractionService = tenantExtractionService ?? 
                throw new ArgumentNullException(nameof(tenantExtractionService));
            _tenantValidationService = tenantValidationService ?? 
                throw new ArgumentNullException(nameof(tenantValidationService));
            _configurationService = configurationService ?? 
                throw new ArgumentNullException(nameof(configurationService));

            _logger.Debug("TenantProcessingWorkflowService initialized successfully");
        }

        /// <summary>
        /// Processes tenants using provided tenant-to-first-location mappings.
        /// Validates each tenant's first location and tracks processing results with concurrency control.
        /// </summary>
        /// <param name="tenantFirstLocations">Dictionary mapping tenant identifiers to their first location IDs</param>
        /// <returns>Task returning TenantProcessingResult with processing outcomes and statistics</returns>
        public async Task<TenantProcessingResult> ProcessTenantsAsync(Dictionary<string, int> tenantFirstLocations)
        {
            var result = new TenantProcessingResult();

            // Check if tenant workflow is enabled
            if (!_configurationService.IsTenantWorkflowEnabled())
            {
                _logger.Info("Tenant workflow is disabled, skipping tenant processing");
                result.WorkflowSkipped = true;
                return result;
            }

            if (tenantFirstLocations == null || !tenantFirstLocations.Any())
            {
                _logger.Debug("No tenants provided for processing");
                return result;
            }

            _logger.Info($"Starting tenant processing for {tenantFirstLocations.Count} tenants");

            // Get concurrency setting
            int maxConcurrency = _configurationService.GetMaxConcurrentTenants();
            _logger.Debug($"Processing tenants with max concurrency: {maxConcurrency}");

            // Process tenants with concurrency control
            using (var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency))
            {
                var tenantTasks = tenantFirstLocations.Select(async tenantLocation =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        var tenantId = tenantLocation.Key;
                        var locationId = tenantLocation.Value;
                        
                        _logger.Debug($"Starting processing for tenant: {tenantId} with location: {locationId}");
                        
                        // Mark tenant as processed
                        result.ProcessedTenants[tenantId] = true;

                        // Attempt validation
                        await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);
                        
                        // Mark as successful
                        result.SuccessfulTenants[tenantId] = true;
                        _logger.Debug($"Completed processing for tenant: {tenantId} with location: {locationId}");
                    }
                    catch (Exception ex)
                    {
                        var tenantId = tenantLocation.Key;
                        _logger.Error(ex, $"Error processing tenant {tenantId} with location {tenantLocation.Value}");
                        result.FailedTenants[tenantId] = ex;
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(tenantTasks);
            }

            _logger.Info($"Tenant processing completed. Successful: {result.SuccessfulTenants.Count}, Failed: {result.FailedTenants.Count}");
            return result;
        }

        /// <summary>
        /// Performs full end-to-end workflow from location list to tenant processing.
        /// Extracts tenants, selects first locations, processes tenants, and tracks untested locations.
        /// </summary>
        /// <param name="allLocations">Collection of all location IDs to process</param>
        /// <returns>Task returning TenantProcessingResult with comprehensive processing outcomes</returns>
        public async Task<TenantProcessingResult> ProcessFullWorkflowAsync(IEnumerable<int> allLocations)
        {
            var result = new TenantProcessingResult();
            var locationList = allLocations?.ToList() ?? new List<int>();
            
            result.TotalLocations = locationList.Count;
            
            if (!locationList.Any())
            {
                _logger.Debug("No locations provided for full workflow processing");
                return result;
            }

            _logger.Info($"Starting full tenant workflow for {locationList.Count} locations");

            try
            {
                // Step 1: Extract tenant mappings from locations
                var tenantLocationMappings = _tenantExtractionService.ExtractTenantsFromLocations(locationList);
                _logger.Debug($"Extracted {tenantLocationMappings.Count} tenant mappings");

                // Step 2: Select first location per tenant
                var tenantFirstLocations = _tenantExtractionService.SelectFirstLocationPerTenant(tenantLocationMappings);
                _logger.Debug($"Selected first locations for {tenantFirstLocations.Count} tenants");

                result.TestedLocations = tenantFirstLocations.Count;

                // Step 3: Calculate untested locations
                var testedLocationIds = tenantFirstLocations.Values.ToHashSet();
                var untestedLocationIds = locationList.Where(loc => !testedLocationIds.Contains(loc)).ToList();
                result.UntestedLocations = untestedLocationIds;

                // Step 4: Group untested locations by tenant
                result.UntestedLocationsByTenant = new Dictionary<string, IEnumerable<int>>();
                foreach (var tenantMapping in tenantLocationMappings)
                {
                    var tenantId = tenantMapping.Key;
                    var allTenantLocations = tenantMapping.Value;
                    var firstLocation = tenantFirstLocations.ContainsKey(tenantId) ? tenantFirstLocations[tenantId] : -1;
                    var untestedForTenant = allTenantLocations.Where(loc => loc != firstLocation).ToList();
                    
                    if (untestedForTenant.Any())
                    {
                        result.UntestedLocationsByTenant[tenantId] = untestedForTenant;
                    }
                }

                _logger.Debug($"Identified {untestedLocationIds.Count} untested locations across {result.UntestedLocationsByTenant.Count} tenants");

                // Step 5: Process tenants
                var processingResult = await ProcessTenantsAsync(tenantFirstLocations);

                // Merge processing results
                result.ProcessedTenants = processingResult.ProcessedTenants;
                result.SuccessfulTenants = processingResult.SuccessfulTenants;
                result.FailedTenants = processingResult.FailedTenants;
                result.WorkflowSkipped = processingResult.WorkflowSkipped;

                _logger.Info($"Full workflow completed. Processed {result.ProcessedTenants.Count} tenants from {locationList.Count} locations");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error in full tenant processing workflow");
                throw;
            }

            return result;
        }
    }
}