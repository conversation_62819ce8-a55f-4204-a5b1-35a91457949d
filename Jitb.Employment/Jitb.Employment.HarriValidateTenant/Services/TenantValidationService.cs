using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Newtonsoft.Json;
using NLog;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Implementation of tenant-focused validation service.
    /// Provides tenant-scoped validation by delegating to existing location validation logic
    /// while adding tenant context for logging and error handling.
    /// Follows SOLID principles with dependency injection and delegation pattern.
    /// </summary>
    public class TenantValidationService : ITenantValidationService
    {
        private readonly IHarriTenantByLocationRepository _harriTenantByLocationRepository;
        private readonly IHarriTenantRepository _harriTenantRepository;
        private readonly ILocationValidationService _locationValidationService;
        private readonly IConfigurationService _configurationService;
        private readonly ITenantValidationResultCapture _resultCapture;
        private readonly ICallHarriWebServiceProvider _callHarriWebServiceProvider;
        private static readonly ILogger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Initializes a new instance of TenantValidationService with required dependencies.
        /// </summary>
        /// <param name="harriTenantByLocationRepository">Repository for tenant-location data access</param>
        /// <param name="harriTenantRepository">Repository for tenant data access</param>
        /// <param name="locationValidationService">Service for performing core location validation</param>
        /// <param name="configurationService">Service for accessing configuration settings</param>
        /// <param name="resultCapture">Service for capturing validation results</param>
        /// <param name="callHarriWebServiceProvider">Provider for calling Harri web services</param>
        /// <exception cref="ArgumentNullException">Thrown when any parameter is null</exception>
        public TenantValidationService(
            IHarriTenantByLocationRepository harriTenantByLocationRepository,
            IHarriTenantRepository harriTenantRepository,
            ILocationValidationService locationValidationService,
            IConfigurationService configurationService,
            ITenantValidationResultCapture resultCapture,
            ICallHarriWebServiceProvider callHarriWebServiceProvider)
        {
            _harriTenantByLocationRepository = harriTenantByLocationRepository ??
                throw new ArgumentNullException(nameof(harriTenantByLocationRepository));
            _harriTenantRepository = harriTenantRepository ??
                throw new ArgumentNullException(nameof(harriTenantRepository));
            _locationValidationService = locationValidationService ??
                throw new ArgumentNullException(nameof(locationValidationService));
            _configurationService = configurationService ??
                throw new ArgumentNullException(nameof(configurationService));
            _resultCapture = resultCapture ??
                throw new ArgumentNullException(nameof(resultCapture));
            _callHarriWebServiceProvider = callHarriWebServiceProvider ??
                throw new ArgumentNullException(nameof(callHarriWebServiceProvider));

            _logger.Debug("TenantValidationService initialized successfully");
        }

        /// <summary>
        /// Validates a location within the context of a specific tenant.
        /// Performs parameter validation, checks configuration, looks up tenant information,
        /// and delegates to existing location validation logic.
        /// </summary>
        /// <param name="tenantId">The tenant identifier for context (can be "(null)" for locations without tenants)</param>
        /// <param name="locationId">The location ID to validate</param>
        /// <returns>Task representing the asynchronous validation operation</returns>
        /// <exception cref="ArgumentNullException">Thrown when tenantId is null</exception>
        /// <exception cref="ArgumentException">Thrown when locationId is invalid (less than or equal to 0)</exception>
        public async Task ValidateLocationForTenantAsync(string tenantId, int locationId)
        {
            // Parameter validation
            if (tenantId == null)
            {
                throw new ArgumentNullException(nameof(tenantId), "Tenant ID cannot be null");
            }

            if (locationId <= 0)
            {
                throw new ArgumentException("Location ID must be greater than 0", nameof(locationId));
            }

            // Check if tenant validation is enabled via configuration
            if (!_configurationService.IsTenantValidationEnabled())
            {
                _logger.Info($"[Tenant {tenantId}] [Location {locationId}]: Tenant validation is disabled, skipping validation");
                return;
            }

            _logger.Info($"[Tenant {tenantId}] [Location {locationId}]: Starting tenant-focused validation");

            // Look up tenant information first to get tenant details
            var harriTenantByLocation = _harriTenantByLocationRepository.GetTenantByLocation(locationId);
            string tenantName = null;

            if (harriTenantByLocation != null)
            {
                // Look up the actual tenant to get the display name
                var harriTenant = _harriTenantRepository.FirstOrDefault(t => t.TenantId == harriTenantByLocation.TenantId);
                tenantName = harriTenant?.Name ?? harriTenantByLocation.TenantId.ToString();

                if (harriTenant?.Name != null)
                {
                    _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Found tenant name for display: {tenantName}");
                }
                else
                {
                    _logger.Warn($"[Tenant {tenantId}] [Location {locationId}]: Tenant {harriTenantByLocation.TenantId} found in location mapping but not in HarriTenant table, using GUID for display");
                }
            }
            else
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: No tenant mapping found, will use provided tenantId for display");
            }

            // Initialize result capture with tenant name
            _resultCapture.StartTenantValidation(tenantId, tenantName, locationId);

            try
            {
                if (harriTenantByLocation == null)
                {
                    if (tenantId != "(null)")
                    {
                        _logger.Warn($"[Tenant {tenantId}] [Location {locationId}]: Expected tenant '{tenantId}' but location has no tenant mapping");
                        _resultCapture.RecordComponentResult(tenantId, "Tenant Lookup", "Tenant Not Found", $"Expected tenant '{tenantId}' but location has no tenant mapping");
                    }
                    else
                    {
                        _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Location has no tenant mapping as expected");
                        _resultCapture.RecordComponentResult(tenantId, "Tenant Lookup", "Tenant Not Found");
                    }
                }
                else
                {
                    var actualTenantId = harriTenantByLocation.TenantId.ToString();
                    if (tenantId != "(null)" && tenantId != actualTenantId)
                    {
                        _logger.Warn($"[Tenant {tenantId}] [Location {locationId}]: Tenant mismatch - expected '{tenantId}', found '{actualTenantId}'");
                        _resultCapture.RecordComponentResult(tenantId, "Tenant Lookup", "Pass", $"Tenant mismatch - expected '{tenantId}', found '{actualTenantId}'");
                    }
                    else
                    {
                        _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Tenant mapping verified successfully");
                        _resultCapture.RecordComponentResult(tenantId, "Tenant Lookup", "Pass");
                    }
                }

                // Perform detailed validation to capture component results accurately
                try
                {
                    await ValidateLocationComponentsAsync(tenantId, locationId, harriTenantByLocation);
                    _resultCapture.CompleteTenantValidation(tenantId, "Pass");
                    _logger.Info($"[Tenant {tenantId}] [Location {locationId}]: Tenant-focused validation completed successfully");
                }
                catch (Exception validationEx)
                {
                    _logger.Warn(validationEx, $"[Tenant {tenantId}] [Location {locationId}]: Validation failed");
                    _resultCapture.RecordComponentResult(tenantId, "Validation", "Fail", validationEx.Message);
                    _resultCapture.CompleteTenantValidation(tenantId, "Fail");
                    throw; // Re-throw to be handled by outer catch
                }
            }
            catch (Exception ex)
            {
                _resultCapture.RecordComponentResult(tenantId, "Validation", "Fail", ex.Message);
                _resultCapture.CompleteTenantValidation(tenantId, "Fail");
                _logger.Error(ex, $"[Tenant {tenantId}] [Location {locationId}]: Tenant-focused validation failed");
                throw; // Let caller handle tenant-level exceptions
            }
        }

        /// <summary>
        /// Validates individual location components and captures detailed results.
        /// Determines whether employee loading should be skipped and records appropriate status.
        /// </summary>
        /// <param name="tenantId">The tenant identifier for context</param>
        /// <param name="locationId">The location ID being validated</param>
        /// <param name="harriTenantByLocation">The HarriTenantByLocation object if found, null otherwise</param>
        /// <returns>Task representing the asynchronous validation operation</returns>
        private async Task ValidateLocationComponentsAsync(string tenantId, int locationId, Domain.Concepts.Config.HarriTenantByLocation harriTenantByLocation)
        {
            try
            {
                // Step 1: GetLocations validation
                await ValidateGetLocationsAsync(tenantId, locationId, harriTenantByLocation);
                _resultCapture.RecordComponentResult(tenantId, "GetLocations", "Pass");

                // Step 2: Load Employee validation with skip logic
                await ValidateLoadEmployeeAsync(tenantId, locationId, harriTenantByLocation);

                // Step 3: Read Employee validation
                await ValidateReadEmployeeAsync(tenantId, locationId, harriTenantByLocation);
                _resultCapture.RecordComponentResult(tenantId, "Read Employee", "Pass");

                // Step 4: Position Mapping validation - result recorded internally
                await ValidatePositionMappingAsync(tenantId, locationId, harriTenantByLocation);

                // Step 5: Event Received validation
                await ValidateEventReceivedAsync(tenantId, locationId, harriTenantByLocation);
                _resultCapture.RecordComponentResult(tenantId, "Event Received", "Pass");
            }
            catch (Exception ex)
            {
                // If any component fails, record failures for remaining components
                _logger.Warn(ex, $"[Tenant {tenantId}] [Location {locationId}]: Component validation failed");

                // Record failures for components that likely failed
                _resultCapture.RecordComponentResult(tenantId, "GetLocations", "Fail", ex.Message);
                _resultCapture.RecordComponentResult(tenantId, "Load Employee", "Fail");
                _resultCapture.RecordComponentResult(tenantId, "Read Employee", "Fail");
                _resultCapture.RecordComponentResult(tenantId, "Position Mapping", "Fail");
                _resultCapture.RecordComponentResult(tenantId, "Event Received", "Fail");

                throw; // Re-throw to be handled by caller
            }
        }

        /// <summary>
        /// Validates the GetLocations component by verifying location exists in tenant.
        /// Calls the actual Harri locations API to ensure the location is present and properly configured.
        /// </summary>
        private async Task ValidateGetLocationsAsync(string tenantId, int locationId, Domain.Concepts.Config.HarriTenantByLocation harriTenantByLocation)
        {
            if (harriTenantByLocation == null)
            {
                // For locations without tenants, we can't validate GetLocations
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Skipping GetLocations validation - no tenant found");
                return;
            }

            try
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Validating location exists in tenant {harriTenantByLocation.TenantId}");

                // Call actual Harri locations API to validate location exists and is accessible
                var response = await _callHarriWebServiceProvider.Call(
                    harriTenantByLocation.TenantId,
                    Method.Get,
                    "locations",
                    null,
                    false,
                    "V1");

                if (!response.IsSuccessful)
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: GetLocations validation failed - API call failed with status {response.StatusCode}");
                    throw new InvalidOperationException($"Failed to call Harri locations API - Status: {response.StatusCode}, Content: {response.Content}");
                }

                // Parse response and verify location exists
                dynamic content = JsonConvert.DeserializeObject(response.Content);
                bool locationFound = false;

                if (content != null)
                {
                    var locations = content is Newtonsoft.Json.Linq.JArray ? content : content.locations ?? content;

                    if (locations != null)
                    {
                        foreach (var location in locations)
                        {
                            int locationIdFromApi = location?.id ?? location?.Id ?? 0;
                            if (locationIdFromApi == locationId)
                            {
                                locationFound = true;
                                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Location found in tenant's location list");
                                break;
                            }
                        }
                    }
                }

                if (!locationFound)
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Location {locationId} not found in tenant's location list");
                    throw new InvalidOperationException($"Location {locationId} not found in tenant {harriTenantByLocation.TenantId} location list");
                }

                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: GetLocations validation passed - location found and accessible");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[Tenant {tenantId}] [Location {locationId}]: GetLocations validation failed");
                throw; // Re-throw to be handled by caller
            }
        }

        /// <summary>
        /// Validates the Load Employee component and determines if loading should be skipped.
        /// This is where we capture the actual skip/pass status.
        /// </summary>
        private async Task ValidateLoadEmployeeAsync(string tenantId, int locationId, Domain.Concepts.Config.HarriTenantByLocation harriTenantByLocation)
        {
            if (harriTenantByLocation == null)
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Load Employee skipped - no tenant found");
                _resultCapture.RecordComponentResult(tenantId, "Load Employee", "Skipped", "No tenant found");
                return;
            }

            try
            {
                // Get employee count from Harri to determine if we should skip
                var existingEmployeeCount = await GetEmployeeCountInHarriAsync(harriTenantByLocation.TenantId, locationId);

                // Check if employee loading should be skipped
                bool skipWhenPreexistingFound = _configurationService.SkipEmployeeLoadWhenPreexistingFound();
                int minimumCountToSkip = _configurationService.MinimumExistingEmployeeCountToSkip();

                if (skipWhenPreexistingFound && existingEmployeeCount >= minimumCountToSkip)
                {
                    _logger.Info($"[Tenant {tenantId}] [Location {locationId}]: Load Employee skipped - found {existingEmployeeCount} existing employees (>= {minimumCountToSkip})");
                    _resultCapture.RecordComponentResult(tenantId, "Load Employee", "Skipped", $"Found {existingEmployeeCount} existing employees >= {minimumCountToSkip} threshold");
                    return;
                }

                // If not skipping, perform actual employee load validation
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Load Employee proceeding - found {existingEmployeeCount} existing employees (< {minimumCountToSkip})");

                // Delegate to existing LocationValidationService for actual loading logic
                await _locationValidationService.ValidateLocationAsync(locationId);

                _resultCapture.RecordComponentResult(tenantId, "Load Employee", "Pass", $"Successfully loaded employee with {existingEmployeeCount} existing employees");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[Tenant {tenantId}] [Location {locationId}]: Load Employee validation failed");
                _resultCapture.RecordComponentResult(tenantId, "Load Employee", "Fail", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Validates the Read Employee component by actually calling the Harri employees API
        /// and verifying we can successfully read employee data.
        /// </summary>
        private async Task ValidateReadEmployeeAsync(string tenantId, int locationId, Domain.Concepts.Config.HarriTenantByLocation harriTenantByLocation)
        {
            if (harriTenantByLocation == null)
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Read Employee skipped - no tenant found");
                return;
            }

            try
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Validating employee read capability from Harri");

                // Call Harri employees API to verify we can read employee data
                var employeesUrl = $"employees?location_id={locationId}&page=1&limit=1";
                var response = await _callHarriWebServiceProvider.Call(
                    harriTenantByLocation.TenantId,
                    Method.Get,
                    employeesUrl,
                    null,
                    false,
                    "V1");

                if (!response.IsSuccessful)
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Read Employee validation failed - API call failed with status {response.StatusCode}");
                    throw new InvalidOperationException($"Failed to read employees from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                }

                if (string.IsNullOrEmpty(response.Content))
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Read Employee validation failed - empty response from API");
                    throw new InvalidOperationException("Empty response received from Harri employees API");
                }

                // Parse response to verify we can read the data structure
                dynamic content = JsonConvert.DeserializeObject(response.Content);

                if (content == null)
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Read Employee validation failed - unable to parse response");
                    throw new InvalidOperationException("Unable to parse employee data from Harri API response");
                }

                // Verify the response has the expected structure and extract employee ID for specific read test
                string externalId = null;
                bool hasEmployeeStructure = false;

                if (content?.data?.employees != null)
                {
                    var employeesArray = (Newtonsoft.Json.Linq.JArray)content.data.employees;
                    hasEmployeeStructure = true;

                    // Get external ID from first employee if available
                    if (employeesArray.Count > 0)
                    {
                        var firstEmployee = employeesArray[0];
                        externalId = firstEmployee["external_id"]?.ToString() ?? firstEmployee["id"]?.ToString() ?? firstEmployee["badge_id"]?.ToString();
                    }
                }
                else if (content?.employees != null)
                {
                    var employeesArray = (Newtonsoft.Json.Linq.JArray)content.employees;
                    hasEmployeeStructure = true;

                    // Get external ID from first employee if available
                    if (employeesArray.Count > 0)
                    {
                        var firstEmployee = employeesArray[0];
                        externalId = firstEmployee["external_id"]?.ToString() ?? firstEmployee["id"]?.ToString() ?? firstEmployee["badge_id"]?.ToString();
                    }
                }
                else if ((content?.total_count != null) || (content?.count != null))
                {
                    hasEmployeeStructure = true;
                }

                if (!hasEmployeeStructure)
                {
                    _logger.Warn($"[Tenant {tenantId}] [Location {locationId}]: Read Employee validation warning - unexpected response structure but API accessible");
                    // Don't fail - the API is working even if structure is unexpected
                }

                // Step 2: Test reading a specific employee by ID (this is the key addition for the test to pass)
                if (!string.IsNullOrEmpty(externalId))
                {
                    _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Testing employee read by ID using external_id: {externalId}");

                    var specificEmployeeUrl = $"employees/{externalId}";
                    var specificEmployeeResponse = await _callHarriWebServiceProvider.Call(
                        harriTenantByLocation.TenantId,
                        Method.Get,
                        specificEmployeeUrl,
                        null,
                        false,
                        "V1");

                    if (specificEmployeeResponse.IsSuccessful)
                    {
                        _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Successfully read employee by ID {externalId} - ID-based read capability confirmed");
                    }
                    else
                    {
                        _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Failed to read employee by ID {externalId} - Status: {specificEmployeeResponse.StatusCode}, Content: {specificEmployeeResponse.Content}");
                        throw new InvalidOperationException($"Failed to read specific employee by ID {externalId} - Status: {specificEmployeeResponse.StatusCode}");
                    }
                }
                else
                {
                    _logger.Warn($"[Tenant {tenantId}] [Location {locationId}]: No external ID available to test ID-based employee read - validation passes but ID-based read not tested");
                }

                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Read Employee validation passed - successfully read employee data from Harri and confirmed ID-based read capability");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[Tenant {tenantId}] [Location {locationId}]: Read Employee validation failed");
                throw; // Re-throw to be handled by caller
            }
        }

        /// <summary>
        /// Validates the Position Mapping component by checking if all required positions are present in Harri.
        /// Uses Constants.ValidPositionCodes for validation and implements Pass/Warning/Fail logic based on
        /// missing positions (Fail) and extra positions with/without codes (Fail/Warning).
        /// </summary>
        private async Task ValidatePositionMappingAsync(string tenantId, int locationId, Domain.Concepts.Config.HarriTenantByLocation harriTenantByLocation)
        {
            if (harriTenantByLocation == null)
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Position Mapping skipped - no tenant found");
                return;
            }

            try
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Validating positions in Harri tenant {harriTenantByLocation.TenantId}");

                // Use Constants.ValidPositionCodes for validation as directed by user
                var requiredPositions = new HashSet<string>(Constants.ValidPositionCodes);

                // Call Harri API to get positions
                var response = await _callHarriWebServiceProvider.Call(harriTenantByLocation.TenantId, Method.Get, "positions", null, false, "V1");

                if (!response.IsSuccessful)
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Failed to get positions from Harri - Status: {response.StatusCode}, Content: {response.Content}");
                    _resultCapture.RecordComponentResult(tenantId, "Position Mapping", "Fail", $"Failed to call Harri positions API - Status: {response.StatusCode}");
                    return;
                }

                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Harri positions response: {response.Content}");

                // Parse the response to get actual positions
                dynamic content = JsonConvert.DeserializeObject(response.Content);
                var actualPositions = new HashSet<string>();
                var extraPositionDetails = new List<(string code, string name)>();

                if (content != null)
                {
                    // Handle both array and object with positions array
                    var positions = content is Newtonsoft.Json.Linq.JArray ? content : content.positions ?? content;

                    if (positions != null)
                    {
                        foreach (var position in positions)
                        {
                            string originalCode = position?.code ?? position?.Code;
                            string positionName = position?.name ?? position?.Name ?? position?.title ?? position?.Title ?? "";
                            string categoryCode = position?.category?.code ?? position?.Category?.code ?? "";

                            // Always track positions with null codes as extra positions for reporting
                            if (string.IsNullOrEmpty(originalCode))
                            {
                                extraPositionDetails.Add(("(null)", positionName));
                                
                                // Try to map for validation purposes
                                string mappedCode = MapPositionNameToCode(positionName, categoryCode);
                                if (!string.IsNullOrEmpty(mappedCode))
                                {
                                    actualPositions.Add(mappedCode);
                                }
                            }
                            else
                            {
                                // Position has a valid code
                                actualPositions.Add(originalCode);
                                
                                // Track as extra if not in required positions
                                if (!requiredPositions.Contains(originalCode))
                                {
                                    extraPositionDetails.Add((originalCode, positionName));
                                }
                            }
                        }
                    }
                }

                // Check for missing positions
                var missingPositions = requiredPositions.Except(actualPositions).ToList();

                // Determine validation status based on user requirements:
                // - Missing positions = FAIL
                // - Extra positions with null codes = FAIL  
                // - Extra positions with codes = WARNING
                // - No discrepancies = PASS

                // Build status message components
                var messageComponents = new List<string>();
                var hasNullCodePositions = extraPositionDetails.Any(ep => ep.code == "(null)");
                var hasExtraCodedPositions = extraPositionDetails.Any(ep => ep.code != "(null)");

                if (missingPositions.Any())
                {
                    string missingList = string.Join(", ", missingPositions);
                    messageComponents.Add($"Missing positions: {missingList}");
                }

                if (extraPositionDetails.Any())
                {
                    var extraList = extraPositionDetails.Select(ep =>
                        string.IsNullOrEmpty(ep.name)
                            ? ep.code
                            : $"{ep.code} - {ep.name}"
                    );
                    string extraPositionsText = string.Join(", ", extraList);
                    messageComponents.Add($"Extra positions: {extraPositionsText}");
                }

                // Determine final status
                string finalMessage = string.Join("; ", messageComponents);

                if (missingPositions.Any() || hasNullCodePositions)
                {
                    // FAIL if missing positions OR positions with null codes
                    _logger.Warn($"[Tenant {tenantId}] [Location {locationId}]: Position Mapping FAILED - {finalMessage}");
                    _resultCapture.RecordComponentResult(tenantId, "Position Mapping", "Fail", finalMessage);
                }
                else if (hasExtraCodedPositions)
                {
                    // WARNING if only extra positions with valid codes
                    _logger.Warn($"[Tenant {tenantId}] [Location {locationId}]: Position Mapping WARNING - {finalMessage}");
                    _resultCapture.RecordComponentResult(tenantId, "Position Mapping", "Warning", finalMessage);
                }
                else
                {
                    // PASS if all required positions present and no extra positions
                    _logger.Info($"[Tenant {tenantId}] [Location {locationId}]: Position Mapping validation passed");
                    _resultCapture.RecordComponentResult(tenantId, "Position Mapping", "Pass");
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[Tenant {tenantId}] [Location {locationId}]: Error validating positions");
                _resultCapture.RecordComponentResult(tenantId, "Position Mapping", "Fail", $"Error validating positions: {ex.Message}");
                // Don't throw - continue with validation process
            }
        }

        /// <summary>
        /// Maps position names to expected position codes for positions that have null codes.
        /// </summary>
        private string MapPositionNameToCode(string positionName, string categoryCode)
        {
            if (string.IsNullOrEmpty(positionName))
                return null;

            // Normalize for case-insensitive comparison
            var name = positionName.Trim().ToLowerInvariant();
            var category = categoryCode?.Trim().ToUpperInvariant() ?? "";

            // Map management positions
            if (category == "MANAGE" || name.Contains("manager"))
            {
                if (name.Contains("restaurant") || name.Contains("general"))
                    return Constants.PrimaryManagerJobCode; // RORM20
                
                // Default to primary manager for other management positions
                return Constants.PrimaryManagerJobCode; // RORM20
            }

            // Map hourly positions based on common names
            switch (name)
            {
                case var n when n.Contains("crew") || n.Contains("team member"):
                    return "RORH03";
                case var n when n.Contains("cashier"):
                    return "RORH00";
                case var n when n.Contains("cook") || n.Contains("kitchen"):
                    return "RORH05";
                case var n when n.Contains("shift") && n.Contains("lead"):
                    return "RORH08";
                case var n when n.Contains("trainer"):
                    return "RORH10";
                case var n when n.Contains("maintenance"):
                    return "RORH13";
                default:
                    // Return null for unmappable positions
                    return null;
            }
        }

        /// <summary>
        /// Validates the Event Received component by actually calling the Harri events API
        /// and verifying the events system is operational.
        /// </summary>
        private async Task ValidateEventReceivedAsync(string tenantId, int locationId, Domain.Concepts.Config.HarriTenantByLocation harriTenantByLocation)
        {
            if (harriTenantByLocation == null)
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Event Received skipped - no tenant found");
                return;
            }

            try
            {
                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Validating event system accessibility");

                // Call Harri events API to verify the events system is working
                // We'll check for any recent events to validate the API is operational
                var eventsUrl = $"events?limit=1";
                var response = await _callHarriWebServiceProvider.Call(
                    harriTenantByLocation.TenantId,
                    Method.Get,
                    eventsUrl,
                    null,
                    false,
                    "V1");

                if (!response.IsSuccessful)
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Event Received validation failed - API call failed with status {response.StatusCode}");
                    throw new InvalidOperationException($"Failed to access Harri events API - Status: {response.StatusCode}, Content: {response.Content}");
                }

                if (string.IsNullOrEmpty(response.Content))
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Event Received validation failed - empty response from events API");
                    throw new InvalidOperationException("Empty response received from Harri events API");
                }

                // Parse response to verify we can access the events structure
                dynamic content = JsonConvert.DeserializeObject(response.Content);

                if (content == null)
                {
                    _logger.Error($"[Tenant {tenantId}] [Location {locationId}]: Event Received validation failed - unable to parse events response");
                    throw new InvalidOperationException("Unable to parse events data from Harri API response");
                }

                // Verify the response has the expected events structure
                dynamic events = null;

                if (content is Newtonsoft.Json.Linq.JArray)
                {
                    // If the content itself is an array, it contains the events
                    events = content;
                }
                else if (content is Newtonsoft.Json.Linq.JObject)
                {
                    // If it's an object, look for events property
                    events = content.events ?? content.data?.events ?? content.data ?? null;
                }

                if (events == null)
                {
                    _logger.Warn($"[Tenant {tenantId}] [Location {locationId}]: Event Received validation warning - no events structure found but API accessible");
                    // Don't fail - the API is working even if no events are present
                }
                else
                {
                    _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Events structure found in response - events system operational");
                }

                _logger.Debug($"[Tenant {tenantId}] [Location {locationId}]: Event Received validation passed - events API is accessible");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[Tenant {tenantId}] [Location {locationId}]: Event Received validation failed");
                throw; // Re-throw to be handled by caller
            }
        }

        /// <summary>
        /// Gets the employee count from Harri for a specific tenant and location.
        /// </summary>
        private async Task<int> GetEmployeeCountInHarriAsync(Guid tenantId, int locationId)
        {
            try
            {
                _logger.Debug($"[Location {locationId}]: Getting locations and testing employee read by ID from Harri tenant {tenantId}");

                // Step 1: Get locations from tenant (not filtering by specific location)
                var locationsUrl = "locations";
                var locationsResponse = await _callHarriWebServiceProvider.Call(tenantId, Method.Get, locationsUrl, null, false, "V1");

                if (!locationsResponse.IsSuccessful)
                {
                    _logger.Error($"[Location {locationId}]: Failed to get locations from Harri - Status: {locationsResponse.StatusCode}, Content: {locationsResponse.Content}");
                    return 0; // Return 0 if call fails - will continue with employee loading validation
                }

                // Parse locations to get any available location
                dynamic locationsContent = JsonConvert.DeserializeObject(locationsResponse.Content);
                int? availableLocationId = null;

                if (locationsContent != null)
                {
                    var locations = locationsContent is Newtonsoft.Json.Linq.JArray ? locationsContent : locationsContent.locations ?? locationsContent;

                    if (locations != null && ((Newtonsoft.Json.Linq.JArray)locations).Count > 0)
                    {
                        var firstLocation = locations[0];
                        availableLocationId = firstLocation?.id ?? firstLocation?.Id;
                        _logger.Debug($"[Location {locationId}]: Using location {availableLocationId} from tenant's location list for employee read test");
                    }
                }

                if (!availableLocationId.HasValue)
                {
                    _logger.Warn($"[Location {locationId}]: No locations found in tenant, cannot test employee read by ID");
                    return 0;
                }

                // Step 2: Get employees from any location to obtain external IDs
                var employeesUrl = $"employees?location_id={availableLocationId}&page=1&limit=50";
                var employeesResponse = await _callHarriWebServiceProvider.Call(tenantId, Method.Get, employeesUrl, null, false, "V1");

                if (!employeesResponse.IsSuccessful)
                {
                    _logger.Error($"[Location {locationId}]: Failed to get employees from Harri - Status: {employeesResponse.StatusCode}, Content: {employeesResponse.Content}");
                    return 0; // Return 0 if call fails - will continue with employee loading validation
                }

                // Parse employees response to get external ID and count
                dynamic employeesContent = JsonConvert.DeserializeObject(employeesResponse.Content);
                int employeeCount = 0;
                string externalId = null;

                // Handle different possible response structures and extract count
                if (employeesContent?.data?.employees != null)
                {
                    var employeesArray = (Newtonsoft.Json.Linq.JArray)employeesContent.data.employees;
                    employeeCount = employeesArray.Count;

                    // Get external ID from first employee if available
                    if (employeesArray.Count > 0)
                    {
                        var firstEmployee = employeesArray[0];
                        externalId = firstEmployee["external_id"]?.ToString() ?? firstEmployee["id"]?.ToString() ?? firstEmployee["badge_id"]?.ToString();
                    }
                }
                else if (employeesContent?.employees != null)
                {
                    var employeesArray = (Newtonsoft.Json.Linq.JArray)employeesContent.employees;
                    employeeCount = employeesArray.Count;

                    // Get external ID from first employee if available
                    if (employeesArray.Count > 0)
                    {
                        var firstEmployee = employeesArray[0];
                        externalId = firstEmployee["external_id"]?.ToString() ?? firstEmployee["id"]?.ToString() ?? firstEmployee["badge_id"]?.ToString();
                    }
                }
                else if (employeesContent?.total_count != null)
                {
                    employeeCount = (int)employeesContent.total_count;
                }
                else if (employeesContent?.count != null)
                {
                    employeeCount = (int)employeesContent.count;
                }

                //_logger.Debug($"[Location {locationId}]: Found {employeeCount} employees in Harri from location {availableLocationId}");

                //// Step 3: Test reading a specific employee by external ID (if we have one)
                //if (!string.IsNullOrEmpty(externalId))
                //{
                //    _logger.Debug($"[Location {locationId}]: Testing employee read by ID using external_id: {externalId}");

                //    var specificEmployeeUrl = $"employees/{externalId}";
                //    var specificEmployeeResponse = await _callHarriWebServiceProvider.Call(tenantId, Method.Get, specificEmployeeUrl, null, false, "V1");

                //    if (specificEmployeeResponse.IsSuccessful)
                //    {
                //        _logger.Debug($"[Location {locationId}]: Successfully read employee by ID {externalId} - ID-based read capability confirmed");
                //    }
                //    else
                //    {
                //        _logger.Warn($"[Location {locationId}]: Failed to read employee by ID {externalId} - Status: {specificEmployeeResponse.StatusCode}, but generic read works");
                //    }
                //}
                //else
                //{
                //    _logger.Debug($"[Location {locationId}]: No external ID available to test ID-based employee read");
                //}

                return employeeCount;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[Location {locationId}]: Error getting employee count and testing ID-based read from Harri");
                return 0; // Return 0 if error occurs - will continue with employee loading validation
            }
        }
    }
}