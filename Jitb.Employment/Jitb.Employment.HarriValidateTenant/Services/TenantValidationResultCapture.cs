using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using Jitb.Employment.HarriValidateTenant.Models;
using NLog;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    public class TenantValidationResultCapture : ITenantValidationResultCapture
    {
        private static readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly ConcurrentDictionary<string, TenantValidationResult> _results;
        private readonly ConcurrentDictionary<string, int> _nullTenantLocations; // Track locations for null tenants

        public TenantValidationResultCapture()
        {
            _results = new ConcurrentDictionary<string, TenantValidationResult>();
            _nullTenantLocations = new ConcurrentDictionary<string, int>();
        }

        /// <summary>
        /// Generate the appropriate key for storing results. For null tenants, includes location to make it unique.
        /// </summary>
        private string GetResultKey(string tenantId, int location = 0)
        {
            if (tenantId == "(null)")
            {
                return $"{tenantId}_{location}";
            }
            return tenantId;
        }

        /// <summary>
        /// Find the correct key for a tenant ID by checking stored null tenant locations
        /// </summary>
        private string FindResultKey(string tenantId)
        {
            if (tenantId == "(null)")
            {
                // For null tenants, we need to find which location-specific key to use
                // This is called from RecordComponentResult and CompleteTenantValidation
                // We'll try to find the matching key from stored null tenant mappings
                var matchingKey = _nullTenantLocations.Keys.FirstOrDefault(k => k.StartsWith(tenantId + "_"));
                if (!string.IsNullOrEmpty(matchingKey))
                {
                    return matchingKey;
                }
                // Fallback to the last added null tenant result
                var nullKeys = _results.Keys.Where(k => k.StartsWith("(null)_")).ToList();
                if (nullKeys.Any())
                {
                    return nullKeys.Last();
                }
            }
            return tenantId;
        }

        public void StartTenantValidation(string tenantId, int location)
        {
            StartTenantValidation(tenantId, null, location);
        }

        public void StartTenantValidation(string tenantId, string tenantName, int location)
        {
            try
            {
                _logger.Debug("Starting tenant validation capture for tenant: {0} ({1}), location: {2}", tenantId, tenantName ?? "Unknown", location);
                
                if (string.IsNullOrEmpty(tenantId))
                    throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));

                string resultKey = GetResultKey(tenantId, location);
                
                // Track null tenant location mappings
                if (tenantId == "(null)")
                {
                    _nullTenantLocations.TryAdd(resultKey, location);
                }

                var result = new TenantValidationResult
                {
                    TenantId = tenantId,
                    TenantName = tenantName,
                    ProcessedLocation = location,
                    StartTime = DateTime.Now
                };

                _results.AddOrUpdate(resultKey, result, (key, existing) => 
                {
                    _logger.Warn("Overwriting existing validation result for key: {0}", key);
                    return result;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error starting tenant validation capture for tenant: {0}", tenantId);
                throw;
            }
        }

        public void RecordComponentResult(string tenantId, string componentName, string status, string details = null)
        {
            try
            {
                _logger.Debug("Recording component result for tenant: {0}, component: {1}, status: {2}", tenantId, componentName, status);
                
                if (string.IsNullOrEmpty(tenantId))
                    throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));
                
                if (string.IsNullOrEmpty(componentName))
                    throw new ArgumentException("Component name cannot be null or empty", nameof(componentName));
                
                if (string.IsNullOrEmpty(status))
                    throw new ArgumentException("Status cannot be null or empty", nameof(status));

                string resultKey = FindResultKey(tenantId);
                
                if (_results.TryGetValue(resultKey, out var result))
                {
                    var componentResult = new ValidationComponentResult(componentName, status, details);
                    result.ComponentResults.Add(componentResult);
                }
                else
                {
                    _logger.Warn("Attempted to record component result for non-existent tenant key: {0} (original tenant: {1})", resultKey, tenantId);
                    // Create a new result if it doesn't exist
                    StartTenantValidation(tenantId, 0);
                    RecordComponentResult(tenantId, componentName, status, details);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error recording component result for tenant: {0}, component: {1}", tenantId, componentName);
                throw;
            }
        }

        public void AddUntestedLocation(string tenantId, int location)
        {
            try
            {
                _logger.Debug("Adding untested location for tenant: {0}, location: {1}", tenantId, location);
                
                if (string.IsNullOrEmpty(tenantId))
                    throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));

                string resultKey = FindResultKey(tenantId);
                
                if (_results.TryGetValue(resultKey, out var result))
                {
                    if (!result.UntestedLocations.Contains(location))
                    {
                        result.UntestedLocations.Add(location);
                    }
                }
                else
                {
                    _logger.Warn("Attempted to add untested location for non-existent tenant: {0}", tenantId);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error adding untested location for tenant: {0}, location: {1}", tenantId, location);
                throw;
            }
        }

        public void CompleteTenantValidation(string tenantId, string finalResult)
        {
            try
            {
                _logger.Debug("Completing tenant validation for tenant: {0}, final result: {1}", tenantId, finalResult);
                
                if (string.IsNullOrEmpty(tenantId))
                    throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));
                
                if (string.IsNullOrEmpty(finalResult))
                    throw new ArgumentException("Final result cannot be null or empty", nameof(finalResult));

                string resultKey = FindResultKey(tenantId);
                
                if (_results.TryGetValue(resultKey, out var result))
                {
                    result.FinalResult = finalResult;
                    result.EndTime = DateTime.Now;
                }
                else
                {
                    _logger.Warn("Attempted to complete validation for non-existent tenant: {0}", tenantId);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error completing tenant validation for tenant: {0}", tenantId);
                throw;
            }
        }

        public TenantValidationResult GetResult(string tenantId)
        {
            try
            {
                if (string.IsNullOrEmpty(tenantId))
                    throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));

                string resultKey = FindResultKey(tenantId);
                _results.TryGetValue(resultKey, out var result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting result for tenant: {0}", tenantId);
                throw;
            }
        }

        public List<TenantValidationResult> GetAllResults()
        {
            try
            {
                return _results.Values.OrderBy(r => r.GetDisplayName()).ToList();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting all results");
                throw;
            }
        }

        public void ClearResults()
        {
            try
            {
                _logger.Debug("Clearing all tenant validation results");
                _results.Clear();
                _nullTenantLocations.Clear();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error clearing results");
                throw;
            }
        }
    }
}