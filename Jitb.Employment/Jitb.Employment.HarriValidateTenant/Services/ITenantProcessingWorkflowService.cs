using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriValidateTenant.Services
{
    /// <summary>
    /// Interface for tenant processing workflow service.
    /// Provides end-to-end tenant-focused processing workflows including extraction, selection, and validation.
    /// Follows SOLID principles with single responsibility for tenant workflow orchestration.
    /// </summary>
    public interface ITenantProcessingWorkflowService
    {
        /// <summary>
        /// Processes tenants using provided tenant-to-first-location mappings.
        /// Validates each tenant's first location and tracks processing results.
        /// </summary>
        /// <param name="tenantFirstLocations">Dictionary mapping tenant identifiers to their first location IDs</param>
        /// <returns>Task returning TenantProcessingResult with processing outcomes and statistics</returns>
        Task<TenantProcessingResult> ProcessTenantsAsync(Dictionary<string, int> tenantFirstLocations);

        /// <summary>
        /// Performs full end-to-end workflow from location list to tenant processing.
        /// Extracts tenants from locations, selects first location per tenant, and processes each tenant.
        /// </summary>
        /// <param name="allLocations">Collection of all location IDs to process</param>
        /// <returns>Task returning TenantProcessingResult with comprehensive processing outcomes</returns>
        Task<TenantProcessingResult> ProcessFullWorkflowAsync(IEnumerable<int> allLocations);
    }
}