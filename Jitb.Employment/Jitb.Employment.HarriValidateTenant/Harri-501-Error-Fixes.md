# Harri 501 Error Fixes - LocationValidationService

## Issues Identified and Fixed

### 1. **GetEmployeeCountInHarri Method**
**Problem**: The method was using `Constants.PayloadMappingEndpoints.GetEmployeesByLocation` endpoint with V2 API version, which likely doesn't exist or isn't implemented on the Harri server.

**Fix**: 
- Changed to use standard REST endpoint format: `employees?location_id={location}&page=1&limit=1`
- Switched from V2 to V1 API version (more likely to be implemented)
- Added better response parsing to handle different possible JSON structures

### 2. **CheckForNewHireEvent Method**
**Problem**: The method was using `Constants.PayloadMappingEndpoints.GetEvents` endpoint, which may not be correctly implemented.

**Fix**:
- Changed to use standard REST endpoint format: `events?employee_id={badgeId}&limit=50`
- Uses V1 API version consistently
- Improved JSON response parsing to handle different structures

### 3. **GetEmployeeCountInHarriForConfigTenant Method**
**Problem**: Was using a simplistic approach that might not handle different response formats correctly.

**Fix**:
- Applied the same improvements as the main GetEmployeeCountInHarri method
- Consistent endpoint format and error handling

## Root Causes of 501 Errors

The 501 "Not Implemented" errors were likely caused by:

1. **Invalid Endpoint Paths**: Using constant-based endpoints that don't map to actual Harri API endpoints
2. **Unsupported API Versions**: V2 and V3 versions may not be implemented for all endpoints
3. **Incorrect URL Construction**: The constants may have created malformed URLs

## Key Changes Made

### Endpoint Format Changes
- **Before**: `Constants.PayloadMappingEndpoints.GetEmployeesByLocation` (V2)
- **After**: `employees?location_id={location}&page=1&limit=1` (V1)

- **Before**: `Constants.PayloadMappingEndpoints.GetEvents` (V1)
- **After**: `events?employee_id={badgeId}&limit=50` (V1)

### Response Parsing Improvements
Added robust parsing that handles multiple possible JSON response structures:
- `content.data.employees`
- `content.employees` 
- `content.total_count`
- `content.count`
- Fallback to `HarriInboundEmployeeData` deserialization

### API Version Consistency
- Standardized on V1 for employees and events endpoints
- Only use V3 for the proven employee creation endpoint

## Testing Recommendations

1. **Test with Known Working Location**: Try a location that definitely exists in Harri
2. **Check Logs**: Look for the detailed URL construction logs to verify correct endpoints
3. **Verify API Versions**: Confirm with Harri documentation which versions are supported
4. **Gradual Testing**: Test each method individually before running full validation

## Next Steps

If 501 errors persist:

1. **Check Harri API Documentation**: Verify the exact endpoint paths and supported versions
2. **Contact Harri Support**: Provide the full URL construction logs for troubleshooting
3. **Test Individual Endpoints**: Use a tool like Postman to test the endpoints directly
4. **Review Authentication**: Ensure the OAuth tokens have the correct scope permissions

The enhanced logging in `CallHarriWebServiceProvider` will now provide detailed diagnostic information for any remaining 501 errors.