﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JIBUtilities
{
    public static class JibUtilities
    {
        public static int Age(DateTime startDate)
        {
            int i = 0;
            var endDate = DateTime.Now;
            while (startDate <= endDate)
            {
                i++;
                startDate = startDate.AddYears(1);
            }
            return i;
        }
    }
}
