select

e.employeeId,
e.<PERSON>,
e.<PERSON>,
l.locationnumber,
l.begindate,
l.enddate,
l.activeflag,
l.primaryflag,
e.jobcode,
j.jobcodedescription,
e.jobcodeeffectivedate,
e.salaryclass,
e.HREmployeeIdentityID,
e.CompanyId, 
(select top 1 payrate from dbnservicebus..employeepayrate r where r.employeeid = e.employeeid order by datetimecreated desc) as Payrate
 from dbnservicebus..employee e
join dbnservicebus..employeelocation l
on l.employeeid = e.employeeid
join dbnservicebus..jobcodes j
on j.jobcode = e.jobcode
join dbnservicebus..ErestaurantMigration m
on m.locationnumber = l.locationnumber and m.asofdate <= getdate()
inner join dbnservicebus..location loc on l.locationnumber = loc.locationnumber and  e.companyid = loc.entitynumber
where e.badgeid = 911339





