﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using ValueOf;

namespace ValueObjects
{
    public class SocialSecurityNumber : ValueOf<long, SocialSecurityNumber>
    {
        public static SocialSecurityNumber From(string value)
        {
            var origValue = value;
            if (value.Length == 11)
            {
                if ((value[3] == '-') && (value[6] == '-'))
                {
                    value = value.Remove(6, 1);
                    value = value.Remove(3, 1);
                }
                else throw new FormatException($"Invalid format for Social Security Number ({origValue}).");
            }

            try
            {
                return SocialSecurityNumber.From(long.Parse(value));
            }
            catch (SsnOutOfRangeException e)
            {
                throw new SsnOutOfRangeException(origValue,e);
            }
            catch (FormatException e)
            {
                throw new FormatException($"Invalid format for Social Security Number ({origValue}).",e);
            }

        }


        private const long MaxSsn = 999_99_9999;
        private const long MinSsn = 000_00_0000;
        public virtual string Formatted => $"{Value:***********}";

        public virtual string Masked => $"XXX-XX-{Formatted.Substring(7, 4)}";

        public static object Get { get; private set; }

        protected override void Validate()
        {
    // Removing MinSsn test 
            if (Value < MinSsn)
                throw new SsnOutOfRangeException(Value);
            if (Value > MaxSsn)
                throw new SsnOutOfRangeException(Value);
        }
    }



    public class SsnOutOfRangeException : Exception
    {
        public SsnOutOfRangeException (long ssn)
        :base ($"Social Security Number ({ssn}) is out of range.")
    {}
        public SsnOutOfRangeException(string ssn)
            : base($"Social Security Number ({ssn}) is out of range.")
        { }

        public SsnOutOfRangeException(string ssn,Exception e)
            : base($"Social Security Number ({ssn}) is out of range.",e)
        { }
    }
}
