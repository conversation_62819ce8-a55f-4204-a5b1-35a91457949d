﻿using System;
using ValueOf;

namespace ValueObjects
{
    public class PhoneNumber : ValueOf<(string CountryCode, long? Number), PhoneNumber>
    {
        public static PhoneNumber From(string value)
        {
            try
            {

                if (string.IsNullOrWhiteSpace(value))
                    return From((null, (long?)null));

                var origValue = value;

                value = value.Replace("-", "")
                    .Replace("(", "")
                    .Replace(")", "")
                    .Replace(" ", "");

                if (value.Contains(","))
                {
                    var parts = value.Split(',');
                    if (parts.Length == 2 && long.TryParse(parts[1], out var number))
                        return From((parts[0], number));
                    else
                        throw new PhoneNumberOutOfRangeException(origValue);
                }

                if (!value.StartsWith("+1") && !value.StartsWith("+52") && !char.IsDigit(value[0]))
                    throw new PhoneNumberOutOfRangeException(origValue);

                if (value.StartsWith("+1"))
                    return FromUsNumber(value.Substring(2), origValue);
                else if (value.StartsWith("+52")) return FromMexicanNumber(value.Substring(3), origValue);

                return FromUsNumber(value, origValue, true);
            }


            catch (PhoneNumberOutOfRangeException e)
            {
                return From((null, (long?)null));
            }
        }


        public virtual string Formatted
        {
            get
            {
                if (Value == (null, null))
                    return null;

                if (Value.CountryCode == "+52")
                {
                    var numberStr = Value.Number.ToString();
                    switch (numberStr.Length)
                    {
                        case 10: // Format: +52 55 5555 5555 (local number)  
                            var temp = $"+52 {numberStr.Substring(0, 2)} {numberStr.Substring(2, 4)} {numberStr.Substring(6, 4)}";
                            return temp;
                        case 11: // Format: +52 1 55 5555 5555 (mobile number)  
                            return $"+52 {numberStr.Substring(0, 1)} {numberStr.Substring(1, 2)} {numberStr.Substring(3, 4)} {numberStr.Substring(7, 4)}";
                        default:
                            return $"+52 {Value.Number}";
                    }
                }
                else if (Value.CountryCode == "+1" || Value.CountryCode == null)
                {
                    var numberStr = Value.Number.ToString();
                    return $"{numberStr.Substring(0, 3)}-{numberStr.Substring(3, 3)}-{numberStr.Substring(6, 4)}";
                }
                return $"{Value.CountryCode} {Value.Number}";
            }
        }

        private static PhoneNumber FromUsNumber(string value, string origValue, bool isWithoutCountryCode = false)
        {
            if (value.Length != 10)
                throw new PhoneNumberOutOfRangeException(origValue);

            if (!long.TryParse(value, out long numberValue))
                throw new PhoneNumberOutOfRangeException(origValue);

            return PhoneNumber.From(("+1", numberValue));
        }

        private static PhoneNumber FromMexicanNumber(string value, string origValue)
        {
            // Mexican numbers can be 10 or 11 digits
            if (value.Length != 10 && value.Length != 11)
                throw new PhoneNumberOutOfRangeException(origValue);

            if (!long.TryParse(value, out long numberValue))
                throw new PhoneNumberOutOfRangeException(origValue);

            // Return with clean country code (no space)
            return PhoneNumber.From(("+52", numberValue));
        }
    }
}

public class PhoneNumberOutOfRangeException : Exception
{
    public PhoneNumberOutOfRangeException(string phoneNumber)
        : base($"Phone Number ({phoneNumber}) is out of range.")
    {
    }
}
