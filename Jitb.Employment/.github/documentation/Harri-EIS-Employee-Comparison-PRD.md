# Product Requirements Document (PRD)
## Harri-EIS Employee Comparison System

---

### Document Information
- **Title:** Harri-EIS Employee Comparison System
- **Version:** 1.0
- **Date:** 2025-07-05
- **Author:** <PERSON> 4 (Business Analyst)
- **Stakeholders:** IT Department
- **Status:** Final

### Change History
| Date | Author | Version | Changes |
|------|--------|---------|---------|
| 2025-07-05 | <PERSON> 4 | 1.0 | Initial PRD creation based on requirements gathering |

---

## 1. Executive Summary

### 1.1 Project Overview
The Harri-EIS Employee Comparison System is a console-based data integrity validation tool designed to compare active employee records between two systems: Harri (employee management system) and EIS (Employee Information System). The primary purpose is to identify discrepancies between the systems, enabling IT staff to discover bugs and maintain data integrity.

### 1.2 Business Objective
- **Primary Goal:** Data integrity validation and system bug discovery
- **Target Users:** IT technical staff
- **Timeline:** One week implementation
- **Scope:** Process approximately 50,000 employee records
- **Deliverable:** CSV discrepancy reports with configurable filtering options

---

## 2. Product Description

### 2.1 Product Purpose
This system validates data consistency between Harri and EIS by comparing active employee records and generating detailed discrepancy reports. The tool serves as a quality assurance mechanism to ensure both systems maintain synchronized employee data.

### 2.2 Success Metrics
- **Data Processing:** Successfully process 50,000+ employee records
- **Accuracy:** Identify all discrepancies between systems
- **Reliability:** Handle system unavailability gracefully
- **Usability:** Generate actionable CSV reports for IT staff analysis

### 2.3 Target Audience
- **Primary Users:** IT technical staff
- **Skill Level:** Technical personnel comfortable with console applications
- **Usage Pattern:** On-demand execution for data validation

---

## 3. Functional Requirements

### 3.1 Core Functionality

#### 3.1.1 Data Retrieval
- **FR-001:** Retrieve active employees from Harri system
  - **Criteria:** Status = 'ACTIVE'
  - **Method:** Use existing API methods from main project
  - **Authentication:** Existing Harri authentication methods

- **FR-002:** Retrieve active employees from EIS system
  - **Criteria:** CurrentStatus ≠ '11'
  - **Method:** Use existing database methods from main project
  - **Authentication:** Connection strings via configuration file

#### 3.1.2 Data Comparison
- **FR-003:** Compare employee records between systems
  - **Key Fields:** BadgeId (EIS) vs GEID (Harri)
  - **Scope:** Identify employees missing or with different status in either system
  - **Include:** Terminated employees appearing as active in one system

- **FR-008:** Employee identity validation
  - **Requirement:** Validate that BadgeId and GEID represent the same person
  - **Method:** Cross-reference employee identifying information

#### 3.1.3 Report Generation
- **FR-007:** Generate CSV discrepancy reports
  - **Format:** CSV file format
  - **Fields:** EIS EmployeeId, BadgeId/GEID, Name, Status, Home Location
  - **Naming:** Append date and time for unique filenames
  - **Statistics:** No summary statistics required

#### 3.1.4 Error Handling
- **FR-006:** System unavailability handling
  - **Behavior:** Throw exception and terminate if either system unavailable
  - **Logging:** Error details logged to configured file location

- **FR-009:** Duplicate ID handling
  - **Behavior:** Document duplicate BadgeIds or GEIDs in the report
  - **Tracking:** Identify and report data quality issues

- **FR-010:** Different identifying information handling
  - **Behavior:** Handle cases where employees exist in both systems with different identifiers
  - **Processing:** Attempt to match and document discrepancies

#### 3.1.5 Execution Model
- **FR-005:** On-demand execution
  - **Trigger:** Manual execution by IT staff
  - **Scheduling:** No automated scheduling required
  - **Frequency:** As needed for data validation

---

## 4. Technical Requirements

### 4.1 Architecture

#### 4.1.1 Framework and Platform
- **Target Framework:** .NET 8.0
- **Application Type:** Console application
- **Deployment:** Standalone executable
- **Integration:** Reference main project methods when possible

#### 4.1.2 Project Structure
- **Main Project:** `/mnt/c/dev/jitb.employment/jitb.employment/` (.NET Framework 4.8)
- **This Project:** `/mnt/c/dev/Demo/Jitb.Employment.HarriCompare/` (.NET 8.0)
- **Shared Library:** .NET Standard 2.0 library for cross-framework compatibility

### 4.2 Configuration

#### 4.2.1 Configuration Format
- **Format:** JSON configuration file
- **Parameters:**
  - EIS connection strings
  - Output file location
  - Log file location
  - Filtering options (all/selected stores/selected tenants)

#### 4.2.2 Sample Configuration Structure
```json
{
  "ConnectionStrings": {
    "EIS": "Data Source=server;Initial Catalog=database;..."
  },
  "OutputSettings": {
    "ReportPath": "C:\\Reports\\",
    "LogPath": "C:\\Logs\\"
  },
  "FilterOptions": {
    "Mode": "all", // "all", "stores", "tenants"
    "SelectedStores": [],
    "SelectedTenants": []
  }
}
```

### 4.3 Data Processing

#### 4.3.1 Performance Requirements
- **Processing Method:** In-memory comparison
- **Volume:** Handle 50,000+ employee records
- **Memory Management:** Standard .NET memory handling
- **No specific performance requirements**

#### 4.3.2 Retry Mechanisms
- **Harri API:** Implement retry logic for API calls
- **EIS Database:** Standard database connection retry
- **Configuration:** Configurable retry attempts and delays

### 4.4 Logging and Monitoring

#### 4.4.1 Logging Framework
- **Framework:** NLog (consistent with main project)
- **Configuration:** Follow NLog.config standards from main project
- **Location:** Configured log file location (not executable directory)

#### 4.4.2 Error Codes
- **0:** Success
- **1:** General failure
- **2:** Configuration error
- **3:** Harri system unavailable
- **4:** EIS system unavailable
- **5:** File I/O error

---

## 5. User Experience Requirements

### 5.1 Interface Design
- **Type:** Console application with no graphical user interface
- **Interaction:** Execute and complete without user intervention
- **Progress:** No progress indicators required
- **Output:** Generate CSV file and exit

### 5.2 User Workflow
1. **Setup:** Configure JSON settings file
2. **Execution:** Run console application
3. **Processing:** System retrieves data and performs comparison
4. **Output:** CSV report generated to configured location
5. **Completion:** Application exits with appropriate error code

### 5.3 Error Handling
- **Method:** Log errors to file and return error codes
- **Display:** No console error display required
- **Recovery:** No automatic recovery mechanisms
- **Cancellation:** No user cancellation capability

---

## 6. Business Requirements

### 6.1 Business Context
- **Primary Purpose:** Data integrity validation and bug discovery
- **Regulatory:** No compliance requirements
- **Frequency:** Unknown occurrence rate of discrepancies
- **Criticality:** Non-critical system operation

### 6.2 Operational Requirements
- **Users:** IT technical staff only
- **Availability:** No high availability requirements
- **Hours:** No time restrictions for execution
- **Resources:** No budget or resource constraints

### 6.3 Timeline
- **Implementation:** One week delivery requirement
- **Testing:** Include basic validation testing
- **Deployment:** Standalone executable deployment

---

## 7. Integration Requirements

### 7.1 System Dependencies
- **Harri System:** Existing API integration from main project
- **EIS System:** Database connectivity via existing methods
- **Main Project:** .NET Framework 4.8 project with existing methods

### 7.2 Data Flow
1. **Harri:** API calls using existing authentication
2. **EIS:** Database queries using connection strings
3. **Processing:** In-memory comparison logic
4. **Output:** CSV file generation

### 7.3 Security Requirements
- **Authentication:** Leverage existing system authentication
- **Data Handling:** Secure processing of employee information
- **File Security:** Standard file system permissions for output

---

## 8. Quality Assurance

### 8.1 Testing Strategy
- **Framework:** xUnit (per project standards)
- **Coverage:** Focus on comparison logic and error handling
- **Data Validation:** Test with sample employee datasets
- **Integration:** Verify connection to both systems

### 8.2 Acceptance Criteria
- **Functional:** Successfully compare employee records
- **Performance:** Process 50,000 records without failure
- **Reliability:** Handle system unavailability gracefully
- **Output:** Generate accurate CSV reports

---

## 9. Constraints and Assumptions

### 9.1 Technical Constraints
- **Framework Compatibility:** .NET 8.0 and .NET Framework 4.8 integration
- **Memory Usage:** Standard in-memory processing limitations
- **System Dependencies:** Reliance on existing main project methods

### 9.2 Business Constraints
- **Timeline:** One week implementation window
- **Resources:** Single developer resource
- **Scope:** Limited to current requirements only

### 9.3 Assumptions
- **Data Volume:** Approximately 50,000 active employees
- **System Availability:** Harri and EIS systems generally available
- **Data Quality:** Existing data quality standards maintained

---

## 10. Risks and Mitigation

### 10.1 Technical Risks
- **Cross-Framework Integration:** Potential compatibility issues
  - **Mitigation:** Use .NET Standard 2.0 shared library
- **Memory Usage:** Large dataset processing
  - **Mitigation:** Monitor memory usage during testing

### 10.2 Business Risks
- **Data Accuracy:** Incorrect discrepancy identification
  - **Mitigation:** Thorough testing with known data sets
- **System Unavailability:** Cannot complete comparison
  - **Mitigation:** Graceful error handling and logging

---

## 11. Traceability Matrix

| Requirement ID | Description | Memory Reference | PRD Section | Status |
|---------------|-------------|------------------|-------------|---------|
| FR-001 | Retrieve Active Employees from Harri | FR-001 | 3.1.1 | Confirmed |
| FR-002 | Retrieve Active Employees from EIS | FR-002 | 3.1.1 | Confirmed |
| FR-003 | Compare Employee Data Between Systems | FR-003 | 3.1.2 | Confirmed |
| FR-005 | On-Demand Execution | FR-005 | 3.1.5 | Confirmed |
| FR-006 | Exception Handling for System Unavailability | FR-006 | 3.1.4 | Confirmed |
| FR-007 | CSV Report Generation | FR-007 | 3.1.3 | Confirmed |
| FR-008 | Employee Identity Validation | FR-008 | 3.1.2 | Confirmed |
| FR-009 | Duplicate ID Handling | FR-009 | 3.1.4 | Confirmed |
| FR-010 | Handle Different Identifying Information | FR-010 | 3.1.4 | Confirmed |
| BR-001 | Data Integrity Validation | BR-001 | 6.1 | Confirmed |
| BR-002 | IT Stakeholder Focus | BR-002 | 5.1 | Confirmed |
| BR-003 | One Week Implementation Timeline | BR-003 | 6.3 | Confirmed |
| BR-004 | Technical Staff Operation | BR-004 | 5.2 | Confirmed |
| BR-005 | High Volume Processing | BR-005 | 4.3.1 | Confirmed |
| BR-006 | Non-Critical System | BR-006 | 6.2 | Confirmed |
| TECH-001 | Multi-Target Framework Architecture | TECH-001 | 4.1.1 | Confirmed |
| TECH-007 | JSON Configuration | TECH-007 | 4.2.1 | Confirmed |
| TECH-008 | Standalone Deployment | TECH-008 | 4.1.1 | Confirmed |
| TECH-009 | Error Code Standards | TECH-009 | 4.4.2 | Confirmed |
| TECH-010 | NLog Configuration Standards | TECH-010 | 4.4.1 | Confirmed |

---

## 12. Appendices

### 12.1 Glossary
- **Harri:** Employee management system
- **EIS:** Employee Information System
- **GEID:** Global Employee ID (Harri system identifier)
- **BadgeId:** Employee badge identifier (EIS system identifier)
- **Active Employee:** Employee with active status in respective system

### 12.2 References
- **Main Project:** `/mnt/c/dev/jitb.employment/jitb.employment/`
- **Project Guidelines:** CLAUDE.md project standards
- **Configuration Standards:** NLog.config from main project

### 12.3 Document Status
- **Requirements Gathering:** Complete
- **Technical Review:** Pending
- **Stakeholder Approval:** Pending
- **Implementation:** Ready to begin

---

**Document prepared by:** Claude Sonnet 4 (Senior Business Analyst)  
**Date:** 2025-07-05  
**Next Review:** Upon implementation completion