---
mode: agent
---


- **Persona or Role:** Defines who the AI should act as (e.g., a tutor, salesperson, or writer) to tailor the response style.

- **Task or Directive:** Clearly states what the AI should do, using specific action verbs like “write,” “summarize,” or “list”.

- **Context:** Provides background information or relevant details to guide the AI’s output and ensure relevance.

- **Format or Constraints:** Specifies the desired structure, tone, length, or other parameters for the output (e.g., “in a table” or “under 200 words”).

- **Examples (Optional):** Includes sample inputs or outputs to clarify expectations, which can help the AI better understand the desired result.