---
title: "Persistent Project Memory"
description: "Evolving context, requirements, and rationale for AI continuity and PRD generation."
type: "context"
tags: ["ai", "memory", "requirements", "traceability"]
created: 2025-06-19
updated: 2025-06-19
version: 1.0
author: "Simeon Brett"
status: "active"

ai_usage: true
agent_scope: "project"
agent_priority: "high"
auto_update: true

agent_notes: |
  This file must be detailed enough to:
  1. Resume a requirement gathering session in a new conversation or tool without reintroducing prior context.
  2. Generate a complete PRD based solely on this file, even without access to previous transcripts.
  Update this file after every significant prompt or decision. Include finalized requirements, clarified assumptions, constraints, rationale, and other persistent context.

---

> ⚠️ NOTE: All content below is illustrative unless marked as "Status: Confirmed".  
> Replace example entries with finalized data as it becomes available.

# 🧠 Project Memory Context

---

## 🧭 Project Summary

- **Purpose:** [Insert high-level goal or business objective]
- **Stakeholders:** [List of key names and roles]
- **Project Phase:** [e.g., Discovery / Design / Development / Review]
- **Initial Requirements Source:** [e.g., User brief, stakeholder meeting, bug reports]
- **Reference PRDs:** [List, if any]
- **Related Systems or Dependencies:** [Internal APIs, 3rd-party services, etc.]

---

## ✅ Functional Requirements

### FR-001 (Example Only): Users can reset their password via email
- **Status:** Example
- **Confirmed On:** —
- **Rationale:** Standard account recovery flow
- **Linked Transcript Entries:** N/A

---

## 📊 Business Requirements & Decisions

### DEC-001 (Example Only): Enforce SSO for all internal tools
- **Status:** Example
- **Date:** 2025-06-19
- **Confirmed By:** —
- **Rationale:** Follows org-wide InfoSec policy
- **Impacted Areas:** Authentication, Access Control

---

## 💡 Design Evolution

### Design Note (Example Only): Transition from REST to GraphQL for internal APIs
- **Status:** Example
- **Date:** 2025-06-19
- **Why:** Simplifies nested data retrieval for frontend
- **Replaces:** REST-based `/v1/*` endpoints

---

## 📁 Project Structure

- `/auth`: Handles login, registration, and session logic
- `/jobs`: Background task definitions (e.g., emails, reports)
- `/common`: Shared utilities, enums, constants
- `/config`: Environment variables and deployment configs
- `/mcp`: Model Context Protocol handlers and formatters

---

## 🔐 Authentication

- **Method (Example):** JWT for public APIs, OAuth2 for internal tools
- **Token Policy (Example):** Expiration after 15 minutes, renewable with refresh token
- **Session Tracking (Example):** Store user roles in access claims

---

## 🔎 Logging Strategy

- **C#:** NLog with async targets
- **Python:** loguru with structured log formatting
- **Log Fields (Example):** request_id, user_id, elapsed_ms, endpoint

---

## 💾 Data Storage & DB Info

- **Primary DB (Example):** SQL Server (C# side), MongoDB (Python), PostgreSQL (TS)
- **Backup Schedule (Example):** Nightly @ 02:00 UTC via Azure BACPAC export
- **Indexing Notes (Example):** Add compound index on (org_id, updated_at)

---

## 🧪 Testing Strategy

- **Frameworks:**
  - C#: xUnit
  - Python: pytest
  - TypeScript: Jest
- **Folder Convention:** All tests live in `/tests`
- **Coverage Goals (Example):**
  - Unit: ≥ 85%
  - Integration: ≥ 70%

---

## 🧱 MCP Notes

- **Compliance (Example):** All public API responses must follow MCP envelope format
- **Validator (Example):** `ValidateMcpEnvelope()`
- **Known Issues (Example):** Forgetting to set `mcp.version` leads to client deserialization failure

---

## 📛 Naming Conventions

- **Services:** End with `Service` (e.g., `PaymentService`)
- **Controllers or Routes:** End with `Controller` or `Routes`
- **Async Methods:** Must use `Async` suffix
- **React Components:** PascalCase only

---

## ⚠️ Gotchas & Caveats

- **Startup (Example):** `ConfigureLogging()` must be called before `Build()` to ensure DI tracing
- **Mongo (Example):** ObjectIDs must be serialized to string in JSON payloads

---

## 🔗 Traceability Matrix (Optional but Recommended)

| ID       | Requirement/Decision                        | Memory Ref             | PRD Section |
|----------|---------------------------------------------|-------------------------|-------------|
| FR-001   | Password reset via email                    | FR-001                  | 2.1.1       |
| DEC-001  | Use of SSO across internal services          | DEC-001                 | 3.2.1       |

---

## 📅 Revision Log

| Date       | Author         | Summary of Changes                       |
|------------|----------------|------------------------------------------|
| 2025-06-19 | Simeon Brett   | Created project memory template          |
