---
title: "Harri-EIS Employee Comparison Project - Persistent Memory"
description: "Evolving context, requirements, and rationale for AI continuity and PRD generation."
type: "context"
tags: ["ai", "memory", "requirements", "traceability", "harri", "eis", "employee-comparison"]
created: 2025-07-05
updated: 2025-07-05
version: 1.0
author: "Claude Sonnet 4"
status: "active"

ai_usage: true
agent_scope: "project"
agent_priority: "high"
auto_update: true

agent_notes: |
  This file must be detailed enough to:
  1. Resume a requirement gathering session in a new conversation or tool without reintroducing prior context.
  2. Generate a complete PRD based solely on this file, even without access to previous transcripts.
  Update this file after every significant prompt or decision. Include finalized requirements, clarified assumptions, constraints, rationale, and other persistent context.

---

# 🧠 Project Memory Context

---

## 🧭 Project Summary

- **Purpose:** Compare active employees between Harri system and EIS (Employee Information System) to identify and report discrepancies
- **Stakeholders:** [To be determined]
- **Project Phase:** Discovery / Requirements Gathering
- **Initial Requirements Source:** User brief - July 5, 2025
- **Reference PRDs:** [None specified]
- **Related Systems or Dependencies:** 
  - Harri system (employee management system)
  - EIS (Employee Information System)
  - Report generation system

---

## ✅ Functional Requirements

### FR-001: Retrieve Active Employees from Harri
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** Need to get current list of active employees from Harri system (Status = 'ACTIVE') using existing API method in MAIN project
- **Linked Transcript Entries:** 1, 3
- **Details:** Use existing API method in MAIN project, includes Harri authentication

### FR-002: Retrieve Active Employees from EIS
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** Need to get current list of active employees from EIS (CurrentStatus not = '11') using existing database method in MAIN project
- **Linked Transcript Entries:** 1, 3
- **Details:** Use existing database method in MAIN project, ConnectionStrings in config file

### FR-003: Compare Employee Data Between Systems
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** Core functionality to identify employees missing or with different status in either system
- **Linked Transcript Entries:** 1, 3
- **Details:** 
  - Key fields: BadgeId (EIS) vs GEID (Harri)
  - Focus on employees missing or with different status in either system
  - Include terminated employees who might still appear as active in one system

### FR-004: Generate Discrepancy Report
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** End deliverable - report showing all identified discrepancies
- **Linked Transcript Entries:** 1, 3

### FR-005: On-Demand Execution
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** System should run comparison on-demand rather than scheduled
- **Linked Transcript Entries:** 3

### FR-006: Exception Handling for System Unavailability
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** If either system is unavailable, throw exception and end processing
- **Linked Transcript Entries:** 3

### FR-007: CSV Report Generation
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** Generate discrepancy report in CSV format for easy data manipulation
- **Linked Transcript Entries:** 4
- **Details:** 
  - Format: CSV file
  - Fields: EIS EmployeeId, BadgeId/GEID, Name, Status, Home Location
  - Filename: Append date and time to make it unique
  - No summary statistics required

### FR-008: Employee Identity Validation
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** System should validate that BadgeId and GEID represent the same person
- **Linked Transcript Entries:** 4

### FR-009: Duplicate ID Handling
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** Document duplicate BadgeIds or GEIDs in the report
- **Linked Transcript Entries:** 4

### FR-010: Handle Different Identifying Information
- **Status:** Confirmed
- **Confirmed On:** 2025-07-05
- **Rationale:** System should handle cases where employee exists in both systems but with different identifying information
- **Linked Transcript Entries:** 4

---

## 📊 Business Requirements & Decisions

### BR-001: Data Integrity Validation
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Confirmed By:** User
- **Rationale:** Primary business purpose is data integrity validation and system bug discovery
- **Impacted Areas:** Comparison logic, report generation

### BR-002: IT Stakeholder Focus
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Confirmed By:** User
- **Rationale:** Primary stakeholder is IT staff, not business users
- **Impacted Areas:** User interface complexity, technical documentation

### BR-003: One Week Implementation Timeline
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Confirmed By:** User
- **Rationale:** Expected delivery within one week
- **Impacted Areas:** Project scope, complexity limitations

### BR-004: Technical Staff Operation
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Confirmed By:** User
- **Rationale:** Tool will be operated by technical staff only
- **Impacted Areas:** User interface design, error handling

### BR-005: High Volume Processing
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Confirmed By:** User
- **Rationale:** System needs to handle approximately 50,000 active employees
- **Impacted Areas:** Performance requirements, memory management

### BR-006: Non-Critical System
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Confirmed By:** User
- **Rationale:** System is not critical - no high availability requirements
- **Impacted Areas:** Error handling, recovery mechanisms

---

## 💡 Design Evolution

### UX-001: Console Application Interface
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Why:** Technical tool for IT staff - console application with no UI required
- **Replaces:** Any GUI requirements

### UX-002: Configuration-Based Filtering
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Why:** Support for all employees, selected stores (locations), or selected tenants via config file
- **Replaces:** Runtime parameter options

### UX-003: Log File Error Handling
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Why:** Errors logged to file with error codes returned for technical staff
- **Replaces:** Console-based error display

### UX-004: Configuration-Based Output Location
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Why:** Output file location specified in config file, not runtime parameters
- **Replaces:** Command-line output parameters

---

## 📁 Project Structure

### TECH-001: Multi-Target Framework Architecture
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** Main project (.NET Framework 4.8) with separate .NET 8.0 console application
- **Rationale:** Integration desired but framework version mismatch requires separate projects
- **Implementation:** 
  - Main project: `/mnt/c/dev/jitb.employment/jitb.employment/` (.NET Framework 4.8)
  - This project: `/mnt/c/dev/Demo/Jitb.Employment.HarriCompare/` (.NET 8.0)
  - Shared library: Create .NET Standard 2.0 library for cross-framework compatibility

### TECH-002: Shared Library Strategy
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** Create shared library project for common functionality
- **Rationale:** Enable code reuse between .NET Framework 4.8 and .NET 8.0 projects
- **Implementation:** Target .NET Standard 2.0 for maximum compatibility

### TECH-003: Reference Architecture
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** .NET 8.0 console app will reference main project methods if possible
- **Rationale:** Reuse existing Harri and EIS integration code
- **Challenge:** Cross-framework referencing may require wrapper implementations

### TECH-004: Logging Framework
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** Use NLog for consistent logging across projects
- **Rationale:** Follows project guidelines and maintains consistency

### TECH-005: Retry Mechanisms
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** Implement retry logic for Harri API calls
- **Rationale:** Improve reliability for external API dependencies

### TECH-006: In-Memory Data Processing
- **Status:** Confirmed (Technical Decision)
- **Date:** 2025-07-05
- **Decision:** Use in-memory comparison for 50,000 employee records
- **Rationale:** Simpler implementation, adequate for volume, no specific performance requirements

### TECH-007: JSON Configuration
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** Use JSON format for configuration file
- **Rationale:** Modern, readable configuration format
- **Parameters:** Connection strings, output location, filtering options (all/selected stores/selected tenants)

### TECH-008: Standalone Deployment
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** Deploy as standalone executable
- **Rationale:** Simplifies deployment and reduces dependencies

### TECH-009: Error Code Standards
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** 
  - 0: Success
  - 1: General failure
  - 2: Configuration error
  - 3: Harri system unavailable
  - 4: EIS system unavailable
  - 5: File I/O error
- **Rationale:** Standard exit codes for console applications

### TECH-010: NLog Configuration Standards
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Decision:** Follow NLog.config standards from main project
- **Rationale:** Consistency with existing project standards
- **Implementation:** Log files in configured location, not executable directory

### TECH-011: Visual Studio Solution Structure
- **Status:** Confirmed
- **Date:** 2025-07-11
- **Decision:** Always include .github folder as solution folder when present
- **Rationale:** Ensures project documentation, templates, and configuration files are accessible through Visual Studio
- **Implementation:** 
  - Add .github as root solution folder
  - Include all subfolders (documentation, instructions, prompts, templates, workfolder) as nested solution folders
  - Include all files in their respective folders within the solution structure
  - Update nested project relationships in GlobalSection(NestedProjects)

---

## 🔐 Authentication

### AUTH-001: Harri Authentication
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Method:** Use existing Harri authentication methods from main project
- **Details:** Authentication handled by existing API methods in main project

### AUTH-002: EIS Authentication
- **Status:** Confirmed
- **Date:** 2025-07-05
- **Method:** Connection strings in configuration file
- **Details:** Database connection authentication via config file

---

## 🔎 Logging Strategy

- **C#:** NLog with async targets (per project standards)
- **Log Fields:** request_id, user_id, elapsed_ms, system_source (Harri/EIS)

---

## 💾 Data Storage & DB Info

[To be determined - may need temporary storage for comparison processing]

---

## 🧪 Testing Strategy

- **Frameworks:** xUnit (per project standards)
- **Folder Convention:** All tests live in `/tests`
- **Coverage Goals:** TBD

---

## 📛 Naming Conventions

- **Services:** End with `Service` (e.g., `HarriService`, `EisService`, `ComparisonService`)
- **Controllers or Routes:** End with `Controller` or `Routes`
- **Async Methods:** Must use `Async` suffix
- **Target Framework:** .NET 8.0

---

## ⚠️ Gotchas & Caveats

[To be populated as discovered during requirements gathering]

---

## 🔗 Traceability Matrix

| ID       | Requirement/Decision                        | Memory Ref             | PRD Section |
|----------|---------------------------------------------|-------------------------|-------------|
| FR-001   | Retrieve Active Employees from Harri        | FR-001                  | TBD         |
| FR-002   | Retrieve Active Employees from EIS          | FR-002                  | TBD         |
| FR-003   | Compare Employee Data Between Systems       | FR-003                  | TBD         |
| FR-004   | Generate Discrepancy Report                 | FR-004                  | TBD         |

---

## 📅 Revision Log

| Date       | Author         | Summary of Changes                       |
|------------|----------------|------------------------------------------|
| 2025-07-05 | Claude Sonnet 4| Created initial project memory for Harri-EIS employee comparison |