# Task 5.2 - EIS Data Access Methods Analysis

**Date:** 2025-07-16  
**Task:** ANALYSIS ONLY - Identify existing EIS data access methods  
**Status:** Completed

## Executive Summary

This analysis documents the existing EIS (Employment Information System) data access methods that can be directly leveraged for the HarriCompare project. The analysis confirms that comprehensive employee data access infrastructure already exists and can be reused with minimal adaptation.

---

## Task 5.2.1 - Employee Entity Mappings and Repositories

### ✅ **Existing Employee Repository Interface: `IEmployeeRepository`**

**Location:** `/src/Jitb.Employment.Domain/Repositories/Employment/IEmployeeRepository.cs`

**48 Specialized Query Methods Available:**

#### **Employee Lookup Methods:**
```csharp
Employee GetByBadgeId(int badgeId);
Employee GetByNetworkId(string networkId);
Employee GetBySsn(string ssn);
Employee GetByHrEmployeeId(long hrEmployeeId);
Employee GetByEmployeeId(long employeeId);
Employee GetByErestaurantEmployeeNumber(string employeeNumber);
Employee GetByHrEmployeeIdentityId(string hrEmployeeIdentityId);
Employee GetByLawsonEmployeeIdAndCompanyId(int? lawsonEmployeeId, int? lawsonCompanyId);
```

#### **Location-Based Queries:**
```csharp
IList<Employee> GetListByLocationId(int locationNumber);
IEnumerable<Employee> GetAllByLocationNumber(int locationNumber);
IEnumerable<Employee> GetByPrimaryLocation(int locationNumber);
```

#### **Status-Based Filtering:**
```csharp
IEnumerable<Employee> GetActiveByBadgeId(int badgeId);
Employee GetByLawsonCompanyIdAndStatusAndSSN(int? lawsonCompanyId, string currentStatus, string ssn);
```

#### **Complex Business Queries:**
```csharp
Employee GetForNewHire(long? employeeId, long? ultiproEmployeeId, string ultiproEmployeeIdentityId, 
                      string ssn, DateTime? dateOfBirth, int? lawsonCompanyId, int? lawsonEmployeeId, 
                      string erEmployeeId, int? badgeId);
IEnumerable<Employee> GetEmployeesToConvertToErestaurantByLocation(Location location);
IEnumerable<Employee> GetSubordinates(long employeeId);
```

### ✅ **Repository Implementation: `EmployeeRepository`**

**Location:** `/src/Jitb.Employment.Domain/Repositories/Employment/EmployeeRepository.cs`

**Architecture:**
- **Inherits from:** `RepositoryBase<Employee>`
- **Database Connection:** Uses `DbAlias.Employment` constant
- **Logging:** Built-in `ILog Log = LogManager.GetLogger(typeof(EmployeeRepository))`
- **Constructor Pattern:** `EmployeeRepository(IMasterUnitOfWork masterUnitOfWork)`
- **Transaction Support:** Inherited from `RepositoryBase<Employee>`

### ✅ **Employee Entity Structure: `Employee`**

**Location:** `/src/Jitb.Employment.Domain/Concepts/Employee.cs`

**1,200+ lines of comprehensive domain logic including:**

#### **Core Identity Properties:**
```csharp
public virtual long EmployeeId { get; set; }
public virtual int BadgeId { get; set; }
public virtual string NetworkId { get; set; }
public virtual string eRestaurantEmployeeId { get; set; }
public virtual long? HREmployeeId { get; set; }
public virtual string HREmployeeIdentityId { get; set; }
```

#### **Personal Information:**
```csharp
public virtual string FirstName { get; set; }
public virtual string LastName { get; set; }
public virtual string MiddleName { get; set; }
public virtual string Ssn { get; set; }
public virtual DateTime? BirthDate { get; set; }
public virtual string EmailAddress { get; set; }
```

#### **Employment Status:**
```csharp
public virtual string CurrentStatus { get; set; }
public virtual DateTime? HireDate { get; set; }
public virtual DateTime? TerminationDate { get; set; }
public virtual bool IsEligibleForRehire { get; set; }
```

#### **Calculated Properties:**
```csharp
public virtual bool IsActive => CurrentStatus == CoreEmployeeStatusCodes.Active;
public virtual bool IsLoa => CurrentStatus == CoreEmployeeStatusCodes.LeaveOfAbsence;
public virtual bool IsTerminated => CurrentStatus == CoreEmployeeStatusCodes.Terminated || 
                                    CurrentStatus == CoreEmployeeStatusCodes.TerminatedInvoluntary;
```

---

## Task 5.2.2 - Active Employee Retrieval Methods

### ✅ **Direct Active Employee Methods:**

#### **Individual Employee Lookup:**
```csharp
// Get active employees by badge ID
IEnumerable<Employee> GetActiveByBadgeId(int badgeId);

// Get active employee by SSN (corporate only)
Employee GetBySsn(string ssn, bool isCorporate = false);
```

#### **Location-Based Active Employee Retrieval:**
```csharp
// Get all employees at a location (includes active, LOA, etc.)
IEnumerable<Employee> GetAllByLocationNumber(int locationNumber);

// Get employees with primary location assignment
IEnumerable<Employee> GetByPrimaryLocation(int locationNumber);

// Get location-specific employees
IList<Employee> GetListByLocationId(int locationNumber);
```

### ✅ **Status-Based Filtering Logic:**

#### **Core Status Codes:**
```csharp
public static class CoreEmployeeStatusCodes
{
    public const string Active = "01";
    public const string PartTimeActive = "02";
    public const string LeaveOfAbsence = "03";
    public const string LeaveOfAbsenceWithoutPay = "04";
    public const string TemporaryActive = "05";
    public const string PendingHire = "06";
    public const string ReHirePending = "07";
    public const string Terminated = "11";
    public const string TerminatedInvoluntary = "12";
}
```

#### **Status Classification Methods:**
```csharp
// Helper methods for status classification
public static bool IsActive(string statusCode);      // "01", "02", "05"
public static bool IsLoa(string statusCode);         // "03", "04"
public static bool IsTerminated(string statusCode);  // "11", "12"
```

### ✅ **Employee Entity Status Properties:**
```csharp
// Built-in status checking
public virtual bool IsActive => CurrentStatus == CoreEmployeeStatusCodes.Active;
public virtual bool IsLoa => CurrentStatus == CoreEmployeeStatusCodes.LeaveOfAbsence;
public virtual bool IsTerminated => CurrentStatus == CoreEmployeeStatusCodes.Terminated ||
                                    CurrentStatus == CoreEmployeeStatusCodes.TerminatedInvoluntary;
```

---

## Task 5.2.3 - Store-Based Filtering Capabilities

### ✅ **Location Repository: `LocationRepository`**

**Location:** `/src/Jitb.Employment.Domain/Repositories/Employment/LocationRepository.cs`

#### **Location Lookup Methods:**
```csharp
Location GetByLocationNumber(int number);
IEnumerable<Location> GetByLocationName(string locationName);
IEnumerable<Location> GetRestaurants();
bool IsCorporateLocation(int locationNumber);
bool IsFranchiseLocation(int locationNumber);
bool IsJackIntheBoxRestaurant(int? number);
```

### ✅ **Location Entity Structure:**
```csharp
public class Location
{
    public virtual int LocationNumber { get; set; }
    public virtual string LocationName { get; set; }
    public virtual string Address1 { get; set; }
    public virtual string BrandConceptCode { get; set; }
    public virtual int EntityNumber { get; set; }
    public virtual bool StoreOpen { get; set; }
    
    // Business Logic
    public virtual bool IsCorporateLocation() => EntityNumber == 1 || EntityNumber == 2;
    public virtual Employee GetRestaurantManager(IEmployeeRepository employeeRepository);
}
```

### ✅ **Employee Location Relationships:**

#### **Employee Location Collections:**
```csharp
// Available on Employee entity
public virtual IReadOnlyList<EmployeeLocation> Locations { get; }
public virtual IList<EmployeeLocation2> EmployeeLocations2 { get; }
public virtual EmployeeLocation HomeLocation { get; }
public virtual List<EmployeeLocation2> AllHomeLocations { get; }
public virtual IList<EmployeeLocation2> ActiveBorrowedLocations2 { get; }
```

#### **Location-Based Employee Methods:**
```csharp
// Employee entity methods
public virtual bool IsActiveInLocation(int locationNumber);
public virtual IList<EmployeeLocation> GetAllActiveLocations();
public virtual EmployeeLocation GetActiveLocation();
```

### ✅ **Employee Location Repository:**

**Location:** `/src/Jitb.Employment.Domain/Repositories/Employment/EmployeeLocation2Repository.cs`

#### **Location-Based Filtering Methods:**
```csharp
IList<EmployeeLocation2> GetByLocation(int location);
IList<EmployeeLocation2> GetActivePrimaryByLocation(int location, DateTime effDate);
IList<EmployeeLocation2> GetActivePrimaryByLocation(int location);
```

---

## Task 5.2.4 - Connection String and Transaction Patterns

### ✅ **Database Connection Configuration:**

#### **Database Alias Pattern:**
```csharp
// Location: /src/Jitb.Employment.Domain/Repositories/DbAlias.cs
public static class DbAlias
{
    public const string Employment = "Default";           // Main EIS database
    public const string JacksTimekeeping = "dbJTK";
    public const string ErestaurantIntegration = "ErestaurantIntegration";
    public const string LocationDb = "LocationDb";
    public const string Sitecenter = "Sitecenter";
    public const string Config = "Config";
}
```

#### **Repository Constructor Pattern:**
```csharp
public EmployeeRepository(IMasterUnitOfWork masterUnitOfWork)
    : base(masterUnitOfWork, DbAlias.Employment)
{
}
```

### ✅ **Transaction Management:**

#### **Unit of Work Pattern:**
```csharp
// Available through base repository
public abstract class RepositoryBase<T> : IRepository<T>
{
    // Transaction methods
    void BeginTransaction();
    void CommitTransaction();
    void RollbackTransaction();
    
    // Core CRUD operations
    void Add(T entity);
    void Remove(T entity);
    T Get(object key);
    IList<T> GetAll();
    IQueryable<T> CreateQuery();
}
```

#### **Connection String Configuration:**
```xml
<!-- ConnectionStrings.config -->
<connectionStrings>
    <add name="Default" 
         connectionString="data source=.;initial catalog=dbNserviceBus;integrated security=SSPI;enlist=false;" />
    <add name="ErestaurantIntegration" 
         connectionString="Data Source=.;Initial Catalog=dbNServiceBus;Integrated Security=SSPI;enlist=false;" />
    <add name="LocationDb" 
         connectionString="Data Source=.;Initial Catalog=dbNServiceBus;Integrated Security=SSPI;enlist=false;" />
</connectionStrings>
```

### ✅ **Error Handling and Logging:**

#### **Standard Logging Pattern:**
```csharp
// Used throughout repositories
private static readonly ILog Log = LogManager.GetLogger(typeof(EmployeeRepository));

// Usage examples
Log.Debug($"GetForNewHireUltiPro {ultiproEmployeeId} {ssn}");
Log.Info("Executing ExecuteLocationTableRefresh");
```

#### **Exception Handling Pattern:**
```csharp
try
{
    // Repository operations
    _employeeRepository.GetAll().Take(1).ToList();
    return true;
}
catch (Exception ex)
{
    Log.Error($"Connection validation failed: {ex.Message}");
    return false;
}
```

---

## 🎯 **Summary for HarriCompare Integration**

### ✅ **Directly Reusable Methods:**

1. **Employee Retrieval:**
   - `GetAllByLocationNumber(int locationNumber)` - Get all employees at a location
   - `GetActiveByBadgeId(int badgeId)` - Get active employees by badge ID
   - `GetByBadgeId(int badgeId)` - Get employees by badge ID (all statuses)

2. **Status Filtering:**
   - `CoreEmployeeStatusCodes.IsActive(string statusCode)` - Check if status is active
   - `CoreEmployeeStatusCodes.IsLoa(string statusCode)` - Check if status is LOA
   - `CoreEmployeeStatusCodes.IsTerminated(string statusCode)` - Check if status is terminated

3. **Location Management:**
   - `GetByLocationNumber(int number)` - Get location details
   - `GetRestaurants()` - Get all restaurant locations
   - `IsCorporateLocation(int locationNumber)` - Check if location is corporate

### ✅ **Connection and Transaction Patterns:**

1. **Database Connection:**
   - Uses `DbAlias.Employment` constant
   - Constructor injection with `IMasterUnitOfWork`
   - Connection string management via `ConnectionStrings.config`

2. **Transaction Management:**
   - Unit of Work pattern through `RepositoryBase<Employee>`
   - Built-in transaction support (Begin/Commit/Rollback)
   - Automatic connection management

3. **Error Handling:**
   - Standard logging via `ILog` with `LogManager`
   - Try/catch patterns with detailed logging
   - Connection validation through simple queries

### ✅ **Ready for HarriCompare Implementation:**

The existing EIS infrastructure provides:
- **Complete Employee entity** with 50+ properties and business methods
- **48 specialized query methods** for various employee lookup scenarios
- **Robust location-based filtering** with multiple relationship types
- **Comprehensive status management** with helper methods
- **Enterprise-grade transaction support** with Unit of Work pattern
- **Proven error handling** and logging patterns

**Recommendation:** The existing `EisEmployeeRepository` adapter class already implemented for HarriCompare successfully leverages all these patterns and can be used as-is for the comparison functionality.

---

**Analysis Completed:** 2025-07-16  
**Next Task:** Task 5.3 - Write tests for existing EIS data access integration  
**Status:** ✅ ANALYSIS COMPLETE - All existing methods documented and ready for reuse