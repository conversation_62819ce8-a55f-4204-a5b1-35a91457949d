# Harri-EIS Employee Comparison System
## Updated Detailed System Design Document

**Document Information**
- **Title:** Harri-EIS Employee Comparison System - Updated Design
- **Version:** 1.1
- **Date:** 2025-07-14
- **Author:** <PERSON> 4 (Solutions Architect)
- **Status:** Updated with Stakeholder Requirements

---

## 1. Updated Requirements Analysis

### 1.1 Comparison Configuration Types

The system must support **three distinct comparison configurations**, each requiring different data access strategies:

1. **By Location (Store-based filtering)**
   - EIS: Filter employees by specific store locations
   - Harri: Filter employees by corresponding Harri tenant/location mappings

2. **By Tenant (Harri-specific filtering)**
   - EIS: All active employees (no store filtering)
   - Harri: Filter by specific Harri tenant IDs

3. **All (Complete dataset)**
   - EIS: All active employees across all stores
   - Harri: All active employees across all tenants

### 1.2 Two-Phase Comparison Strategy

**Comparison Type 1: Basic Status Comparison**
- **Harri**: 'ACTIVE' status employees (includes both Active and LOA)
- **EIS**: Active (CurrentStatus ≠ '11') + LOA (CurrentStatus = '03')
- **Purpose**: Quick overview comparison without LOA detail

**Comparison Type 2: Detailed LOA Status Comparison**
- **Harri**: 'ACTIVE' employees + individual LOA status API calls
- **EIS**: Active (CurrentStatus ≠ '11') + LOA (CurrentStatus = '03')
- **Purpose**: Detailed comparison distinguishing actual Active vs LOA employees

### 1.3 Rate Limiting Considerations

- **Single-threaded Harri access** due to rate limiting constraints
- **Individual API calls** required for each Harri employee to determine LOA status
- **Bulk/batch processing** for EIS database queries (no rate limiting)

---

## 2. Updated Domain Model

### 2.1 Enhanced Employee Data Model

```csharp
public class EmployeeData
{
    // Core identifiers
    public string EmployeeId { get; set; }        // EIS EmployeeId
    public string BadgeId { get; set; }           // EIS BadgeId
    public string GlobalId { get; set; }          // Harri GEID
    public string Name { get; set; }
    public string HomeLocation { get; set; }
    
    // Enhanced status information
    public EmployeeStatus Status { get; set; }
    public LeaveOfAbsenceStatus LoaStatus { get; set; }
    public DataSource Source { get; set; }
    
    // Location and tenant context
    public string StoreCode { get; set; }         // EIS store
    public Guid? HarriTenantId { get; set; }      // Harri tenant
    
    public DateTime LastModified { get; set; }
    public Dictionary<string, object> ExtendedProperties { get; set; }
}

public enum EmployeeStatus
{
    Active,
    Terminated,
    LeaveOfAbsence,
    Unknown
}

public enum LeaveOfAbsenceStatus
{
    None,           // Not on LOA
    OnLeave,        // Currently on LOA
    Unknown         // LOA status not determined
}

public class DetailedEmployeeData : EmployeeData
{
    public bool RequiresLoaCheck { get; set; }    // Flag for Harri LOA API call
    public DateTime? LoaCheckTimestamp { get; set; }
    public string LoaReason { get; set; }
    public DateTime? LoaStartDate { get; set; }
    public DateTime? LoaEndDate { get; set; }
}
```

### 2.2 Comparison Configuration Model

```csharp
public class ComparisonConfiguration
{
    public ComparisonMode Mode { get; set; }
    public ComparisonType Type { get; set; }
    public FilterConfiguration Filters { get; set; }
    public DataSourceConfiguration Harri { get; set; }
    public DataSourceConfiguration EIS { get; set; }
    public OutputConfiguration Output { get; set; }
    public PerformanceConfiguration Performance { get; set; }
}

public enum ComparisonMode
{
    ByLocation,     // Store-based filtering
    ByTenant,       // Harri tenant-based filtering
    All             // Complete dataset
}

public enum ComparisonType
{
    BasicStatus,    // ACTIVE vs Active+LOA (no individual LOA checks)
    DetailedLoa     // ACTIVE + individual LOA API calls vs Active+LOA
}

public class FilterConfiguration
{
    public List<string> SelectedStores { get; set; }       // For ByLocation mode
    public List<Guid> SelectedTenants { get; set; }        // For ByTenant mode
    public bool IncludeTerminated { get; set; }           // Include terminated employees
    public DateTime? AsOfDate { get; set; }               // Point-in-time comparison
}

public class PerformanceConfiguration
{
    public int MaxConcurrentEisQueries { get; set; } = 5;  // EIS can handle concurrent queries
    public int HarriRateLimitDelayMs { get; set; } = 300; // Rate limiting delay between Harri calls
    public int BatchSize { get; set; } = 1000;            // Batch size for processing
    public bool EnableCaching { get; set; } = true;       // Enable data caching
}
```

---

## 3. Updated Service Architecture

### 3.1 Configuration-Aware Data Access Strategy

```csharp
public interface IDataRetrievalStrategy
{
    bool Usage(ComparisonMode mode, DataSource source);
    Task<IEnumerable<EmployeeData>> RetrieveEmployeesAsync(
        ComparisonConfiguration config, 
        CancellationToken cancellationToken = default);
}

// Store-based EIS retrieval
public class LocationBasedEisStrategy : IDataRetrievalStrategy
{
    public bool Usage(ComparisonMode mode, DataSource source) =>
        mode == ComparisonMode.ByLocation && source == DataSource.EIS;

    public async Task<IEnumerable<EmployeeData>> RetrieveEmployeesAsync(
        ComparisonConfiguration config, 
        CancellationToken cancellationToken = default)
    {
        var employees = new List<EmployeeData>();
        
        foreach (var storeCode in config.Filters.SelectedStores)
        {
            var storeEmployees = await _eisRepository.GetActiveEmployeesByStoreAsync(
                storeCode, cancellationToken);
            employees.AddRange(storeEmployees);
        }
        
        return employees;
    }
}

// Tenant-based Harri retrieval
public class TenantBasedHarriStrategy : IDataRetrievalStrategy
{
    public bool Usage(ComparisonMode mode, DataSource source) =>
        mode == ComparisonMode.ByTenant && source == DataSource.Harri;

    public async Task<IEnumerable<EmployeeData>> RetrieveEmployeesAsync(
        ComparisonConfiguration config, 
        CancellationToken cancellationToken = default)
    {
        var employees = new List<EmployeeData>();
        
        // Single-threaded processing due to rate limiting
        foreach (var tenantId in config.Filters.SelectedTenants)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            var tenantEmployees = await _harriRepository.GetActiveEmployeesByTenantAsync(
                tenantId, cancellationToken);
            employees.AddRange(tenantEmployees);
            
            // Apply rate limiting delay
            await Task.Delay(config.Performance.HarriRateLimitDelayMs, cancellationToken);
        }
        
        return employees;
    }
}

// All data retrieval strategies
public class AllDataEisStrategy : IDataRetrievalStrategy
{
    public bool Usage(ComparisonMode mode, DataSource source) =>
        mode == ComparisonMode.All && source == DataSource.EIS;
        
    public async Task<IEnumerable<EmployeeData>> RetrieveEmployeesAsync(
        ComparisonConfiguration config, 
        CancellationToken cancellationToken = default)
    {
        // Can use parallel processing for EIS database
        return await _eisRepository.GetAllActiveEmployeesAsync(cancellationToken);
    }
}

public class AllDataHarriStrategy : IDataRetrievalStrategy
{
    public bool Usage(ComparisonMode mode, DataSource source) =>
        mode == ComparisonMode.All && source == DataSource.Harri;
        
    public async Task<IEnumerable<EmployeeData>> RetrieveEmployeesAsync(
        ComparisonConfiguration config, 
        CancellationToken cancellationToken = default)
    {
        // Single-threaded due to rate limiting
        return await _harriRepository.GetAllActiveEmployeesAsync(cancellationToken);
    }
}
```

### 3.2 Two-Phase Comparison Engine

```csharp
public interface IComparisonEngine
{
    Task<ComparisonResult> ExecuteComparisonAsync(
        ComparisonConfiguration config,
        CancellationToken cancellationToken = default);
}

public class TwoPhaseComparisonEngine : IComparisonEngine
{
    private readonly List<IDataRetrievalStrategy> _retrievalStrategies;
    private readonly IBasicComparisonService _basicComparison;
    private readonly IDetailedLoaComparisonService _detailedComparison;
    private readonly IHarriLoaService _harriLoaService;

    public async Task<ComparisonResult> ExecuteComparisonAsync(
        ComparisonConfiguration config,
        CancellationToken cancellationToken = default)
    {
        // Phase 1: Retrieve base employee data
        var harriEmployees = await RetrieveEmployeeData(DataSource.Harri, config, cancellationToken);
        var eisEmployees = await RetrieveEmployeeData(DataSource.EIS, config, cancellationToken);

        ComparisonResult result;

        if (config.Type == ComparisonType.BasicStatus)
        {
            // Basic comparison: ACTIVE vs Active+LOA
            result = await _basicComparison.CompareAsync(harriEmployees, eisEmployees, config);
        }
        else
        {
            // Phase 2: Enhanced comparison with individual LOA checks
            var detailedHarriEmployees = await EnrichWithLoaDetailsAsync(
                harriEmployees, config, cancellationToken);
            
            result = await _detailedComparison.CompareAsync(
                detailedHarriEmployees, eisEmployees, config);
        }

        return result;
    }

    private async Task<IEnumerable<DetailedEmployeeData>> EnrichWithLoaDetailsAsync(
        IEnumerable<EmployeeData> harriEmployees,
        ComparisonConfiguration config,
        CancellationToken cancellationToken)
    {
        var detailedEmployees = new List<DetailedEmployeeData>();

        // Single-threaded processing due to rate limiting
        foreach (var employee in harriEmployees)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var detailedEmployee = new DetailedEmployeeData();
            detailedEmployee.CopyFrom(employee);

            // Individual API call to determine LOA status
            var loaDetails = await _harriLoaService.GetLeaveOfAbsenceStatusAsync(
                employee.GlobalId, cancellationToken);

            detailedEmployee.LoaStatus = loaDetails.IsOnLeave 
                ? LeaveOfAbsenceStatus.OnLeave 
                : LeaveOfAbsenceStatus.None;
            detailedEmployee.LoaReason = loaDetails.Reason;
            detailedEmployee.LoaStartDate = loaDetails.StartDate;
            detailedEmployee.LoaEndDate = loaDetails.EndDate;
            detailedEmployee.LoaCheckTimestamp = DateTime.UtcNow;

            detailedEmployees.Add(detailedEmployee);

            // Apply rate limiting delay
            await Task.Delay(config.Performance.HarriRateLimitDelayMs, cancellationToken);
        }

        return detailedEmployees;
    }

    private async Task<IEnumerable<EmployeeData>> RetrieveEmployeeData(
        DataSource source,
        ComparisonConfiguration config,
        CancellationToken cancellationToken)
    {
        var strategy = _retrievalStrategies.FirstOrDefault(s => 
            s.Usage(config.Mode, source));

        if (strategy == null)
            throw new InvalidOperationException($"No strategy found for {config.Mode} mode and {source} source");

        return await strategy.RetrieveEmployeesAsync(config, cancellationToken);
    }
}
```

### 3.3 Enhanced Comparison Services

```csharp
public class BasicComparisonService : IBasicComparisonService
{
    public async Task<ComparisonResult> CompareAsync(
        IEnumerable<EmployeeData> harriEmployees,
        IEnumerable<EmployeeData> eisEmployees,
        ComparisonConfiguration config)
    {
        var discrepancies = new List<EmployeeDiscrepancy>();

        // Create lookup dictionaries
        var harriByGeid = harriEmployees.ToDictionary(e => e.GlobalId);
        var eisByBadgeId = eisEmployees.ToDictionary(e => e.BadgeId);

        // Find employees missing in Harri
        foreach (var eisEmployee in eisEmployees)
        {
            if (!harriByGeid.ContainsKey(eisEmployee.BadgeId))
            {
                discrepancies.Add(new EmployeeDiscrepancy
                {
                    Type = DiscrepancyType.MISSING_IN_HARRI,
                    EisEmployee = eisEmployee,
                    HarriEmployee = null,
                    ComparisonType = ComparisonType.BasicStatus
                });
            }
        }

        // Find employees missing in EIS and status mismatches
        foreach (var harriEmployee in harriEmployees)
        {
            if (!eisByBadgeId.ContainsKey(harriEmployee.GlobalId))
            {
                discrepancies.Add(new EmployeeDiscrepancy
                {
                    Type = DiscrepancyType.MISSING_IN_EIS,
                    EisEmployee = null,
                    HarriEmployee = harriEmployee,
                    ComparisonType = ComparisonType.BasicStatus
                });
            }
            else
            {
                var eisEmployee = eisByBadgeId[harriEmployee.GlobalId];
                
                // Basic status comparison (ACTIVE in Harri includes both Active and LOA)
                if (!IsStatusCompatible(harriEmployee.Status, eisEmployee.Status))
                {
                    discrepancies.Add(new EmployeeDiscrepancy
                    {
                        Type = DiscrepancyType.STATUS_MISMATCH,
                        EisEmployee = eisEmployee,
                        HarriEmployee = harriEmployee,
                        ComparisonType = ComparisonType.BasicStatus,
                        Details = $"Harri: {harriEmployee.Status}, EIS: {eisEmployee.Status}"
                    });
                }
            }
        }

        return new ComparisonResult
        {
            Discrepancies = discrepancies,
            TotalHarriEmployees = harriEmployees.Count(),
            TotalEisEmployees = eisEmployees.Count(),
            ComparisonType = ComparisonType.BasicStatus,
            ExecutionTime = DateTime.UtcNow
        };
    }

    private bool IsStatusCompatible(EmployeeStatus harriStatus, EmployeeStatus eisStatus)
    {
        // ACTIVE in Harri is compatible with both Active and LOA in EIS
        return harriStatus == EmployeeStatus.Active && 
               (eisStatus == EmployeeStatus.Active || eisStatus == EmployeeStatus.LeaveOfAbsence);
    }
}

public class DetailedLoaComparisonService : IDetailedLoaComparisonService
{
    public async Task<ComparisonResult> CompareAsync(
        IEnumerable<DetailedEmployeeData> harriEmployees,
        IEnumerable<EmployeeData> eisEmployees,
        ComparisonConfiguration config)
    {
        var discrepancies = new List<EmployeeDiscrepancy>();

        var harriByGeid = harriEmployees.ToDictionary(e => e.GlobalId);
        var eisByBadgeId = eisEmployees.ToDictionary(e => e.BadgeId);

        // Find employees missing in Harri
        foreach (var eisEmployee in eisEmployees)
        {
            if (!harriByGeid.ContainsKey(eisEmployee.BadgeId))
            {
                discrepancies.Add(new EmployeeDiscrepancy
                {
                    Type = DiscrepancyType.MISSING_IN_HARRI,
                    EisEmployee = eisEmployee,
                    HarriEmployee = null,
                    ComparisonType = ComparisonType.DetailedLoa
                });
            }
        }

        // Find employees missing in EIS and detailed status mismatches
        foreach (var harriEmployee in harriEmployees)
        {
            if (!eisByBadgeId.ContainsKey(harriEmployee.GlobalId))
            {
                discrepancies.Add(new EmployeeDiscrepancy
                {
                    Type = DiscrepancyType.MISSING_IN_EIS,
                    EisEmployee = null,
                    HarriEmployee = harriEmployee,
                    ComparisonType = ComparisonType.DetailedLoa
                });
            }
            else
            {
                var eisEmployee = eisByBadgeId[harriEmployee.GlobalId];
                
                // Detailed LOA status comparison
                var statusMismatch = AnalyzeDetailedStatusMismatch(harriEmployee, eisEmployee);
                if (statusMismatch != null)
                {
                    discrepancies.Add(statusMismatch);
                }
            }
        }

        return new ComparisonResult
        {
            Discrepancies = discrepancies,
            TotalHarriEmployees = harriEmployees.Count(),
            TotalEisEmployees = eisEmployees.Count(),
            ComparisonType = ComparisonType.DetailedLoa,
            ExecutionTime = DateTime.UtcNow,
            AdditionalMetrics = new Dictionary<string, object>
            {
                ["TotalLoaChecksPerformed"] = harriEmployees.Count(e => e.LoaCheckTimestamp.HasValue),
                ["EmployeesOnLoaInHarri"] = harriEmployees.Count(e => e.LoaStatus == LeaveOfAbsenceStatus.OnLeave),
                ["EmployeesOnLoaInEis"] = eisEmployees.Count(e => e.Status == EmployeeStatus.LeaveOfAbsence)
            }
        };
    }

    private EmployeeDiscrepancy AnalyzeDetailedStatusMismatch(
        DetailedEmployeeData harriEmployee, 
        EmployeeData eisEmployee)
    {
        var harriEffectiveStatus = harriEmployee.LoaStatus == LeaveOfAbsenceStatus.OnLeave
            ? EmployeeStatus.LeaveOfAbsence
            : EmployeeStatus.Active;

        if (harriEffectiveStatus != eisEmployee.Status)
        {
            return new EmployeeDiscrepancy
            {
                Type = DiscrepancyType.LOA_STATUS_MISMATCH,
                EisEmployee = eisEmployee,
                HarriEmployee = harriEmployee,
                ComparisonType = ComparisonType.DetailedLoa,
                Details = $"Harri Effective Status: {harriEffectiveStatus} (LOA: {harriEmployee.LoaStatus}), EIS: {eisEmployee.Status}",
                AdditionalContext = new Dictionary<string, object>
                {
                    ["HarriLoaReason"] = harriEmployee.LoaReason,
                    ["HarriLoaStartDate"] = harriEmployee.LoaStartDate,
                    ["HarriLoaEndDate"] = harriEmployee.LoaEndDate,
                    ["LoaCheckTimestamp"] = harriEmployee.LoaCheckTimestamp
                }
            };
        }

        return null;
    }
}
```

### 3.4 Enhanced Discrepancy Types

```csharp
public enum DiscrepancyType
{
    MISSING_IN_HARRI,           // Employee exists in EIS but not in Harri
    MISSING_IN_EIS,             // Employee exists in Harri but not in EIS
    STATUS_MISMATCH,            // Basic status mismatch (BasicStatus comparison)
    LOA_STATUS_MISMATCH,        // Detailed LOA status mismatch (DetailedLoa comparison)
    DUPLICATE_BADGE_ID,         // Multiple employees with same BadgeId
    DUPLICATE_GEID,             // Multiple employees with same GEID
    IDENTIFIER_MISMATCH         // Same person with different BadgeId/GEID
}

public class EmployeeDiscrepancy
{
    public DiscrepancyType Type { get; set; }
    public EmployeeData EisEmployee { get; set; }
    public EmployeeData HarriEmployee { get; set; }
    public ComparisonType ComparisonType { get; set; }
    public string Details { get; set; }
    public Dictionary<string, object> AdditionalContext { get; set; }
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
}
```

---

## 4. Harri LOA Service Implementation

### 4.1 Individual LOA Status Check Service

```csharp
public interface IHarriLoaService
{
    Task<HarriLoaDetails> GetLeaveOfAbsenceStatusAsync(
        string geid, 
        CancellationToken cancellationToken = default);
}

public class HarriLoaService : IHarriLoaService
{
    private readonly IHarriApiClient _apiClient;
    private readonly IRateLimit _rateLimit;
    private readonly ILogger _logger;
    private readonly IMemoryCache _cache;

    public async Task<HarriLoaDetails> GetLeaveOfAbsenceStatusAsync(
        string geid, 
        CancellationToken cancellationToken = default)
    {
        // Check cache first (short-term caching to avoid duplicate API calls)
        var cacheKey = $"harri_loa_{geid}";
        if (_cache.TryGetValue(cacheKey, out HarriLoaDetails cachedDetails))
        {
            return cachedDetails;
        }

        // Apply rate limiting
        await _rateLimit.TryMakeRequestAsync($"harri_loa_check_{geid}");

        try
        {
            // Individual API call to get employee LOA details
            var response = await _apiClient.GetEmployeeLeaveStatusAsync(geid, cancellationToken);
            
            var loaDetails = new HarriLoaDetails
            {
                Geid = geid,
                IsOnLeave = response.LeaveStatus?.IsActive == true,
                Reason = response.LeaveStatus?.Reason,
                StartDate = response.LeaveStatus?.StartDate,
                EndDate = response.LeaveStatus?.EndDate,
                RetrievedAt = DateTime.UtcNow
            };

            // Cache for 5 minutes to avoid duplicate API calls during same comparison run
            _cache.Set(cacheKey, loaDetails, TimeSpan.FromMinutes(5));

            _logger.LogDebug("Retrieved LOA status for GEID {Geid}: {IsOnLeave}", 
                geid, loaDetails.IsOnLeave);

            return loaDetails;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve LOA status for GEID {Geid}", geid);
            
            return new HarriLoaDetails
            {
                Geid = geid,
                IsOnLeave = false,
                Error = ex.Message,
                RetrievedAt = DateTime.UtcNow
            };
        }
    }
}

public class HarriLoaDetails
{
    public string Geid { get; set; }
    public bool IsOnLeave { get; set; }
    public string Reason { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime RetrievedAt { get; set; }
    public string Error { get; set; }
}
```

---

## 5. Enhanced Report Generation

### 5.1 Comparison-Type-Aware Report Generator

```csharp
public class EnhancedCsvReportGenerator : IReportGenerator
{
    public bool Usage(ReportFormat format) =>
        format == ReportFormat.PipeDelimitedCsv;

    public async Task<Report> GenerateReportAsync(
        ComparisonResult result,
        ReportConfiguration config)
    {
        var csv = new StringBuilder();

        // Generate header based on comparison type
        if (result.ComparisonType == ComparisonType.BasicStatus)
        {
            csv.AppendLine("EIS_EmployeeId|BadgeId|GEID|Name|Home_Location|Harri_Status|EIS_Status|Discrepancy_Type|Comparison_Type");
        }
        else
        {
            csv.AppendLine("EIS_EmployeeId|BadgeId|GEID|Name|Home_Location|Harri_Status|Harri_LOA_Status|EIS_Status|Discrepancy_Type|Comparison_Type|LOA_Reason|LOA_Start_Date|LOA_End_Date");
        }

        // Generate data rows
        foreach (var discrepancy in result.Discrepancies)
        {
            if (result.ComparisonType == ComparisonType.BasicStatus)
            {
                var row = string.Join("|",
                    discrepancy.EisEmployee?.EmployeeId ?? "NULL",
                    discrepancy.EisEmployee?.BadgeId ?? discrepancy.HarriEmployee?.GlobalId ?? "NULL",
                    discrepancy.HarriEmployee?.GlobalId ?? "NULL",
                    discrepancy.EisEmployee?.Name ?? discrepancy.HarriEmployee?.Name ?? "NULL",
                    discrepancy.EisEmployee?.HomeLocation ?? discrepancy.HarriEmployee?.HomeLocation ?? "NULL",
                    discrepancy.HarriEmployee?.Status.ToString() ?? "NULL",
                    discrepancy.EisEmployee?.Status.ToString() ?? "NULL",
                    discrepancy.Type.ToString(),
                    "BASIC_STATUS"
                );
                csv.AppendLine(row);
            }
            else
            {
                var detailedHarriEmployee = discrepancy.HarriEmployee as DetailedEmployeeData;
                var row = string.Join("|",
                    discrepancy.EisEmployee?.EmployeeId ?? "NULL",
                    discrepancy.EisEmployee?.BadgeId ?? discrepancy.HarriEmployee?.GlobalId ?? "NULL",
                    discrepancy.HarriEmployee?.GlobalId ?? "NULL",
                    discrepancy.EisEmployee?.Name ?? discrepancy.HarriEmployee?.Name ?? "NULL",
                    discrepancy.EisEmployee?.HomeLocation ?? discrepancy.HarriEmployee?.HomeLocation ?? "NULL",
                    discrepancy.HarriEmployee?.Status.ToString() ?? "NULL",
                    detailedHarriEmployee?.LoaStatus.ToString() ?? "NULL",
                    discrepancy.EisEmployee?.Status.ToString() ?? "NULL",
                    discrepancy.Type.ToString(),
                    "DETAILED_LOA",
                    detailedHarriEmployee?.LoaReason ?? "NULL",
                    detailedHarriEmployee?.LoaStartDate?.ToString("yyyy-MM-dd") ?? "NULL",
                    detailedHarriEmployee?.LoaEndDate?.ToString("yyyy-MM-dd") ?? "NULL"
                );
                csv.AppendLine(row);
            }
        }

        return new Report
        {
            Content = csv.ToString(),
            Format = ReportFormat.PipeDelimitedCsv,
            FileName = GenerateFileName(result.ComparisonType),
            CreatedAt = DateTime.UtcNow,
            Metadata = new Dictionary<string, object>
            {
                ["ComparisonType"] = result.ComparisonType.ToString(),
                ["TotalDiscrepancies"] = result.Discrepancies.Count,
                ["HarriEmployeeCount"] = result.TotalHarriEmployees,
                ["EisEmployeeCount"] = result.TotalEisEmployees,
                ["ExecutionTime"] = result.ExecutionTime
            }
        };
    }

    private string GenerateFileName(ComparisonType comparisonType)
    {
        var pacificTime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(
            DateTime.UtcNow, "Pacific Standard Time");
        var comparisonTypePrefix = comparisonType == ComparisonType.BasicStatus ? "Basic" : "DetailedLOA";
        return $"HarriEIS_{comparisonTypePrefix}_Compare_{pacificTime:yyyyMMdd_HHmmss}.csv";
    }
}
```

---

## 6. Performance and Scalability Updates

### 6.1 Rate-Limiting-Aware Processing

```csharp
public class RateLimitedHarriProcessor
{
    private readonly IHarriLoaService _loaService;
    private readonly ILogger _logger;
    private readonly SemaphoreSlim _semaphore;

    public RateLimitedHarriProcessor(IHarriLoaService loaService, ILogger logger)
    {
        _loaService = loaService;
        _logger = logger;
        // Single-threaded access to respect rate limiting
        _semaphore = new SemaphoreSlim(1, 1);
    }

    public async Task<IEnumerable<DetailedEmployeeData>> ProcessEmployeesWithLoaAsync(
        IEnumerable<EmployeeData> employees,
        ComparisonConfiguration config,
        IProgress<ProcessingProgress> progress = null,
        CancellationToken cancellationToken = default)
    {
        var results = new List<DetailedEmployeeData>();
        var employeeList = employees.ToList();
        var totalCount = employeeList.Count;
        var processedCount = 0;

        _logger.LogInformation("Starting LOA enrichment for {Count} employees", totalCount);

        foreach (var employee in employeeList)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                var detailedEmployee = new DetailedEmployeeData();
                detailedEmployee.CopyFrom(employee);

                // Individual API call with rate limiting
                var loaDetails = await _loaService.GetLeaveOfAbsenceStatusAsync(
                    employee.GlobalId, cancellationToken);

                detailedEmployee.LoaStatus = loaDetails.IsOnLeave 
                    ? LeaveOfAbsenceStatus.OnLeave 
                    : LeaveOfAbsenceStatus.None;
                detailedEmployee.LoaReason = loaDetails.Reason;
                detailedEmployee.LoaStartDate = loaDetails.StartDate;
                detailedEmployee.LoaEndDate = loaDetails.EndDate;
                detailedEmployee.LoaCheckTimestamp = DateTime.UtcNow;

                results.Add(detailedEmployee);

                processedCount++;
                progress?.Report(new ProcessingProgress
                {
                    TotalItems = totalCount,
                    ProcessedItems = processedCount,
                    CurrentItem = employee.GlobalId,
                    EstimatedTimeRemaining = CalculateEstimatedTime(processedCount, totalCount, config)
                });

                // Rate limiting delay
                if (processedCount < totalCount) // Don't delay after the last item
                {
                    await Task.Delay(config.Performance.HarriRateLimitDelayMs, cancellationToken);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        _logger.LogInformation("Completed LOA enrichment for {ProcessedCount}/{TotalCount} employees", 
            processedCount, totalCount);

        return results;
    }

    private TimeSpan CalculateEstimatedTime(int processed, int total, ComparisonConfiguration config)
    {
        if (processed == 0) return TimeSpan.Zero;

        var remaining = total - processed;
        var avgTimePerItem = TimeSpan.FromMilliseconds(config.Performance.HarriRateLimitDelayMs + 200); // API call time
        return TimeSpan.FromMilliseconds(remaining * avgTimePerItem.TotalMilliseconds);
    }
}

public class ProcessingProgress
{
    public int TotalItems { get; set; }
    public int ProcessedItems { get; set; }
    public string CurrentItem { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public double PercentComplete => TotalItems > 0 ? (double)ProcessedItems / TotalItems * 100 : 0;
}
```

### 6.2 Configuration-Specific Execution Planning

```csharp
public class ExecutionPlanner
{
    public ExecutionPlan CreateExecutionPlan(ComparisonConfiguration config)
    {
        var plan = new ExecutionPlan
        {
            Configuration = config,
            EstimatedDuration = CalculateEstimatedDuration(config),
            ResourceRequirements = CalculateResourceRequirements(config),
            RiskFactors = IdentifyRiskFactors(config),
            Recommendations = GenerateRecommendations(config)
        };

        return plan;
    }

    private TimeSpan CalculateEstimatedDuration(ComparisonConfiguration config)
    {
        var baseTime = TimeSpan.FromMinutes(5); // Base overhead

        // Estimate employee counts based on configuration
        var estimatedEmployees = EstimateEmployeeCount(config);
        
        if (config.Type == ComparisonType.DetailedLoa)
        {
            // Each Harri employee requires individual API call + rate limiting delay
            var harriTime = TimeSpan.FromMilliseconds(
                estimatedEmployees * (config.Performance.HarriRateLimitDelayMs + 300));
            baseTime = baseTime.Add(harriTime);
        }

        // EIS processing time (much faster)
        var eisTime = TimeSpan.FromSeconds(estimatedEmployees / 1000.0); // Assume 1000 records per second
        baseTime = baseTime.Add(eisTime);

        return baseTime;
    }

    private int EstimateEmployeeCount(ComparisonConfiguration config)
    {
        return config.Mode switch
        {
            ComparisonMode.ByLocation => config.Filters.SelectedStores?.Count * 50 ?? 0,
            ComparisonMode.ByTenant => config.Filters.SelectedTenants?.Count * 200 ?? 0,
            ComparisonMode.All => 50000,
            _ => 1000
        };
    }

    private List<string> IdentifyRiskFactors(ComparisonConfiguration config)
    {
        var risks = new List<string>();

        if (config.Type == ComparisonType.DetailedLoa)
        {
            var estimatedEmployees = EstimateEmployeeCount(config);
            if (estimatedEmployees > 10000)
            {
                risks.Add($"High employee count ({estimatedEmployees}) with individual LOA checks may take several hours");
            }
        }

        if (config.Mode == ComparisonMode.All)
        {
            risks.Add("Full dataset comparison may consume significant resources");
        }

        if (config.Performance.HarriRateLimitDelayMs < 200)
        {
            risks.Add("Rate limit delay may be too aggressive and cause API throttling");
        }

        return risks;
    }

    private List<string> GenerateRecommendations(ComparisonConfiguration config)
    {
        var recommendations = new List<string>();

        if (config.Type == ComparisonType.DetailedLoa && EstimateEmployeeCount(config) > 5000)
        {
            recommendations.Add("Consider running BasicStatus comparison first to identify major discrepancies");
            recommendations.Add("Consider filtering by specific stores/tenants to reduce processing time");
        }

        if (config.Performance.HarriRateLimitDelayMs > 1000)
        {
            recommendations.Add("Rate limit delay seems conservative; consider reducing for faster processing");
        }

        return recommendations;
    }
}

public class ExecutionPlan
{
    public ComparisonConfiguration Configuration { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public ResourceRequirements ResourceRequirements { get; set; }
    public List<string> RiskFactors { get; set; }
    public List<string> Recommendations { get; set; }
}

public class ResourceRequirements
{
    public long EstimatedMemoryUsageMB { get; set; }
    public int EstimatedApiCalls { get; set; }
    public int EstimatedDatabaseQueries { get; set; }
    public string RecommendedExecution { get; set; }
}
```

---

## 7. Updated Questions for Clarification

Based on this updated design, I have a few questions to ensure the implementation meets your exact requirements:

### 7.1 Harri LOA API Details
1. **What is the specific API endpoint** for checking individual employee LOA status in Harri?
2. **What authentication is required** for the LOA status endpoint? Same OAuth2 as other Harri APIs?
3. **What is the exact response format** from the Harri LOA status API?
4. **Are there any batch LOA endpoints** available, or must each employee be checked individually?

### 7.2 Comparison Logic Specifics
1. **For EIS LOA employees** (CurrentStatus = '03'), should these be considered "Active" or "LOA" in the comparison?
2. **Should terminated employees in EIS** (CurrentStatus = '11') be included in any comparison scenarios?
3. **What should happen when Harri LOA API call fails** for an individual employee? Skip, assume not on LOA, or flag as error?

### 7.3 Configuration and Filtering
1. **How do we map EIS store codes to Harri tenant IDs** for location-based filtering?
2. **For "ByTenant" mode**, should we still filter EIS employees by location, or include all EIS employees?
3. **Should the comparison configuration be stored in the config.sys file**, or passed as command-line arguments?

### 7.4 Performance and Rate Limiting
1. **What is the specific rate limit for Harri APIs** (requests per minute/second)?
2. **Is there a different rate limit for the LOA status endpoint** compared to other Harri APIs?
3. **Should we implement exponential backoff** if we hit rate limits, or just maintain constant delays?

### 7.5 Output Requirements
1. **Should we generate separate reports for BasicStatus vs DetailedLoa comparisons**, or combined?
2. **Do you want summary statistics** included in the CSV or separate file?
3. **Should we include execution metadata** (processing time, API call counts, etc.) in the report?

---

## 8. Conclusion

This updated design addresses the key requirements you've identified:

✅ **Multiple comparison configurations** (ByLocation, ByTenant, All)  
✅ **Different data access strategies** for each configuration mode  
✅ **Two-phase comparison strategy** (BasicStatus vs DetailedLoa)  
✅ **Single-threaded Harri access** with proper rate limiting  
✅ **Individual LOA status API calls** for detailed comparisons  
✅ **Enhanced discrepancy detection** with LOA-specific types  
✅ **Flexible service architecture** that maintains existing patterns  

The design maintains the modular, service-oriented architecture while accommodating the specific requirements for rate-limited API access and detailed LOA status checking.

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Review PRD documents in ComparisonReport workfolder", "status": "completed", "priority": "high"}, {"id": "2", "content": "Analyze current system architecture and identify service boundaries", "status": "completed", "priority": "high"}, {"id": "3", "content": "Design service decomposition strategy using DDD principles", "status": "completed", "priority": "high"}, {"id": "4", "content": "Define communication patterns and API contracts", "status": "completed", "priority": "high"}, {"id": "5", "content": "Document scalability and deployment considerations", "status": "completed", "priority": "medium"}, {"id": "6", "content": "Create detailed design document with updated requirements", "status": "completed", "priority": "high"}]