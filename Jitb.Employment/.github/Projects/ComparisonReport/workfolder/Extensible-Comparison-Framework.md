# Extensible Comparison Framework
## Design for Future Comparison Criteria

**Document Information**
- **Title:** Extensible Comparison Framework for Additional Criteria
- **Version:** 1.0
- **Date:** 2025-07-14
- **Author:** Claude Sonnet 4 (Solutions Architect)
- **Status:** Extension Design

---

## 1. Extensible Comparison Architecture

### 1.1 Comparison Criteria Framework

```csharp
public interface IComparisonCriteria<T>
{
    string CriteriaName { get; }
    ComparisonResult<T> Compare(T source1Value, T source2Value, ComparisonContext context);
    bool IsApplicable(EmployeeData employee1, EmployeeData employee2);
    CriteriaSeverity Severity { get; }
}

public abstract class BaseComparisonCriteria<T> : IComparisonCriteria<T>
{
    public abstract string CriteriaName { get; }
    public virtual CriteriaSeverity Severity => CriteriaSeverity.Medium;
    
    public abstract ComparisonResult<T> Compare(T source1Value, T source2Value, ComparisonContext context);
    
    public virtual bool IsApplicable(EmployeeData employee1, EmployeeData employee2)
    {
        return employee1 != null && employee2 != null;
    }
}

public class ComparisonResult<T>
{
    public bool IsMatch { get; set; }
    public T SourceValue { get; set; }
    public T TargetValue { get; set; }
    public string DiscrepancyDescription { get; set; }
    public double? VarianceAmount { get; set; }
    public double? VariancePercentage { get; set; }
    public Dictionary<string, object> AdditionalContext { get; set; }
}

public enum CriteriaSeverity
{
    Low,        // Informational differences (e.g., phone number formatting)
    Medium,     // Standard field mismatches (e.g., job codes)
    High,       // Critical mismatches (e.g., significant pay rate differences)
    Critical    // Identity-level mismatches (e.g., name, SSN)
}

public class ComparisonContext
{
    public EmployeeData SourceEmployee { get; set; }
    public EmployeeData TargetEmployee { get; set; }
    public ComparisonConfiguration Configuration { get; set; }
    public DateTime ComparisonDate { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}
```

### 1.2 Specific Comparison Criteria Implementations

#### **Job Code Comparison**
```csharp
public class JobCodeComparisonCriteria : BaseComparisonCriteria<string>
{
    public override string CriteriaName => "JobCode";
    public override CriteriaSeverity Severity => CriteriaSeverity.Medium;

    public override ComparisonResult<string> Compare(
        string harriJobCode, 
        string eisJobCode, 
        ComparisonContext context)
    {
        var isMatch = string.Equals(harriJobCode?.Trim(), eisJobCode?.Trim(), 
            StringComparison.OrdinalIgnoreCase);

        return new ComparisonResult<string>
        {
            IsMatch = isMatch,
            SourceValue = harriJobCode,
            TargetValue = eisJobCode,
            DiscrepancyDescription = isMatch ? null : 
                $"Job code mismatch: Harri='{harriJobCode}', EIS='{eisJobCode}'",
            AdditionalContext = new Dictionary<string, object>
            {
                ["HarriJobCodeNormalized"] = harriJobCode?.Trim().ToUpperInvariant(),
                ["EisJobCodeNormalized"] = eisJobCode?.Trim().ToUpperInvariant(),
                ["ComparisonMethod"] = "CaseInsensitive"
            }
        };
    }

    public override bool IsApplicable(EmployeeData employee1, EmployeeData employee2)
    {
        return base.IsApplicable(employee1, employee2) &&
               (!string.IsNullOrEmpty(employee1.JobCode) || !string.IsNullOrEmpty(employee2.JobCode));
    }
}

public class JobCodeMappingComparisonCriteria : BaseComparisonCriteria<string>
{
    private readonly IJobCodeMappingService _mappingService;
    
    public override string CriteriaName => "JobCodeWithMapping";
    public override CriteriaSeverity Severity => CriteriaSeverity.Medium;

    public override ComparisonResult<string> Compare(
        string harriJobCode, 
        string eisJobCode, 
        ComparisonContext context)
    {
        // Map Harri job code to equivalent EIS job code
        var mappedHarriJobCode = _mappingService.MapHarriToEisJobCode(harriJobCode);
        
        var isMatch = string.Equals(mappedHarriJobCode?.Trim(), eisJobCode?.Trim(), 
            StringComparison.OrdinalIgnoreCase);

        return new ComparisonResult<string>
        {
            IsMatch = isMatch,
            SourceValue = harriJobCode,
            TargetValue = eisJobCode,
            DiscrepancyDescription = isMatch ? null : 
                $"Mapped job code mismatch: Harri='{harriJobCode}' (mapped to '{mappedHarriJobCode}'), EIS='{eisJobCode}'",
            AdditionalContext = new Dictionary<string, object>
            {
                ["OriginalHarriJobCode"] = harriJobCode,
                ["MappedHarriJobCode"] = mappedHarriJobCode,
                ["EisJobCode"] = eisJobCode,
                ["MappingUsed"] = true
            }
        };
    }
}
```

#### **Phone Number Comparison**
```csharp
public class PhoneNumberComparisonCriteria : BaseComparisonCriteria<string>
{
    public override string CriteriaName => "PhoneNumber";
    public override CriteriaSeverity Severity => CriteriaSeverity.Low;

    public override ComparisonResult<string> Compare(
        string harriPhone, 
        string eisPhone, 
        ComparisonContext context)
    {
        var normalizedHarriPhone = NormalizePhoneNumber(harriPhone);
        var normalizedEisPhone = NormalizePhoneNumber(eisPhone);
        
        var isMatch = string.Equals(normalizedHarriPhone, normalizedEisPhone, 
            StringComparison.OrdinalIgnoreCase);

        return new ComparisonResult<string>
        {
            IsMatch = isMatch,
            SourceValue = harriPhone,
            TargetValue = eisPhone,
            DiscrepancyDescription = isMatch ? null : 
                $"Phone number mismatch: Harri='{harriPhone}' (normalized: '{normalizedHarriPhone}'), EIS='{eisPhone}' (normalized: '{normalizedEisPhone}')",
            AdditionalContext = new Dictionary<string, object>
            {
                ["NormalizedHarriPhone"] = normalizedHarriPhone,
                ["NormalizedEisPhone"] = normalizedEisPhone,
                ["OriginalHarriPhone"] = harriPhone,
                ["OriginalEisPhone"] = eisPhone,
                ["NormalizationApplied"] = true
            }
        };
    }

    private string NormalizePhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return null;

        // Remove all non-numeric characters
        var digitsOnly = new string(phoneNumber.Where(char.IsDigit).ToArray());
        
        // Handle US phone numbers
        if (digitsOnly.Length == 10)
        {
            return digitsOnly; // Return as 10-digit string
        }
        else if (digitsOnly.Length == 11 && digitsOnly.StartsWith("1"))
        {
            return digitsOnly.Substring(1); // Remove country code for US numbers
        }
        
        return digitsOnly; // Return as-is for other formats
    }

    public override bool IsApplicable(EmployeeData employee1, EmployeeData employee2)
    {
        return base.IsApplicable(employee1, employee2) &&
               (!string.IsNullOrEmpty(employee1.PhoneNumber) || !string.IsNullOrEmpty(employee2.PhoneNumber));
    }
}
```

#### **Pay Rate Comparison**
```csharp
public class PayRateComparisonCriteria : BaseComparisonCriteria<decimal?>
{
    public override string CriteriaName => "PayRate";
    public override CriteriaSeverity Severity => CriteriaSeverity.High;

    public override ComparisonResult<decimal?> Compare(
        decimal? harriPayRate, 
        decimal? eisPayRate, 
        ComparisonContext context)
    {
        if (!harriPayRate.HasValue && !eisPayRate.HasValue)
        {
            return new ComparisonResult<decimal?>
            {
                IsMatch = true,
                SourceValue = harriPayRate,
                TargetValue = eisPayRate
            };
        }

        if (!harriPayRate.HasValue || !eisPayRate.HasValue)
        {
            return new ComparisonResult<decimal?>
            {
                IsMatch = false,
                SourceValue = harriPayRate,
                TargetValue = eisPayRate,
                DiscrepancyDescription = $"Pay rate missing: Harri={harriPayRate?.ToString("C") ?? "NULL"}, EIS={eisPayRate?.ToString("C") ?? "NULL"}"
            };
        }

        var tolerance = GetPayRateTolerance(context.Configuration);
        var difference = Math.Abs(harriPayRate.Value - eisPayRate.Value);
        var percentageDifference = eisPayRate.Value > 0 
            ? (difference / eisPayRate.Value) * 100 
            : 0;

        var isMatch = difference <= tolerance;

        return new ComparisonResult<decimal?>
        {
            IsMatch = isMatch,
            SourceValue = harriPayRate,
            TargetValue = eisPayRate,
            VarianceAmount = difference,
            VariancePercentage = percentageDifference,
            DiscrepancyDescription = isMatch ? null : 
                $"Pay rate variance: Harri={harriPayRate:C}, EIS={eisPayRate:C}, Difference={difference:C} ({percentageDifference:F2}%)",
            AdditionalContext = new Dictionary<string, object>
            {
                ["ToleranceUsed"] = tolerance,
                ["AbsoluteDifference"] = difference,
                ["PercentageDifference"] = percentageDifference,
                ["ExceedsTolerance"] = !isMatch
            }
        };
    }

    private decimal GetPayRateTolerance(ComparisonConfiguration config)
    {
        // Allow configurable pay rate tolerance
        return config?.Criteria?.PayRateToleranceAmount ?? 0.01m; // Default 1 cent tolerance
    }

    public override bool IsApplicable(EmployeeData employee1, EmployeeData employee2)
    {
        return base.IsApplicable(employee1, employee2) &&
               (employee1.PayRate.HasValue || employee2.PayRate.HasValue);
    }
}
```

### 1.3 Enhanced Employee Data Model

```csharp
public class ExtendedEmployeeData : EmployeeData
{
    // Additional fields for future comparison criteria
    public string JobCode { get; set; }
    public string PhoneNumber { get; set; }
    public string AlternatePhoneNumber { get; set; }
    public decimal? PayRate { get; set; }
    public string PayType { get; set; } // Hourly, Salary, Commission
    public DateTime? HireDate { get; set; }
    public string Department { get; set; }
    public string Title { get; set; }
    public string ManagerBadgeId { get; set; }
    public string AddressLine1 { get; set; }
    public string AddressLine2 { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string ZipCode { get; set; }
    public string EmailAddress { get; set; }
    public DateTime? LastPayRateChangeDate { get; set; }
    
    // Extensibility for unknown future fields
    public Dictionary<string, object> CustomFields { get; set; } = new Dictionary<string, object>();
}
```

---

## 2. Configurable Comparison Engine

### 2.1 Criteria-Driven Comparison Service

```csharp
public interface IExtendedComparisonService
{
    Task<ExtendedComparisonResult> CompareEmployeesAsync(
        IEnumerable<ExtendedEmployeeData> sourceEmployees,
        IEnumerable<ExtendedEmployeeData> targetEmployees,
        ExtendedComparisonConfiguration config,
        CancellationToken cancellationToken = default);
}

public class ConfigurableComparisonService : IExtendedComparisonService
{
    private readonly List<IComparisonCriteria<object>> _availableCriteria;
    private readonly IComparisonCriteriaFactory _criteriaFactory;
    private readonly ILogger _logger;

    public async Task<ExtendedComparisonResult> CompareEmployeesAsync(
        IEnumerable<ExtendedEmployeeData> sourceEmployees,
        IEnumerable<ExtendedEmployeeData> targetEmployees,
        ExtendedComparisonConfiguration config,
        CancellationToken cancellationToken = default)
    {
        var result = new ExtendedComparisonResult
        {
            Configuration = config,
            StartTime = DateTime.UtcNow
        };

        // Get active comparison criteria from configuration
        var activeCriteria = GetActiveCriteria(config);
        
        var sourceDict = sourceEmployees.ToDictionary(e => GetEmployeeKey(e, config));
        var targetDict = targetEmployees.ToDictionary(e => GetEmployeeKey(e, config));

        // Find missing employees (existing logic)
        result.MissingEmployees.AddRange(FindMissingEmployees(sourceDict, targetDict, config));

        // Perform criteria-based comparisons for matching employees
        var matchingPairs = FindMatchingEmployeePairs(sourceDict, targetDict);
        
        foreach (var (sourceEmployee, targetEmployee) in matchingPairs)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            var employeeComparison = await CompareIndividualEmployeeAsync(
                sourceEmployee, targetEmployee, activeCriteria, config);
                
            if (employeeComparison.HasDiscrepancies)
            {
                result.DetailedDiscrepancies.Add(employeeComparison);
            }
        }

        result.EndTime = DateTime.UtcNow;
        result.ProcessingDuration = result.EndTime - result.StartTime;
        
        return result;
    }

    private async Task<EmployeeComparisonResult> CompareIndividualEmployeeAsync(
        ExtendedEmployeeData sourceEmployee,
        ExtendedEmployeeData targetEmployee,
        List<IComparisonCriteria<object>> criteria,
        ExtendedComparisonConfiguration config)
    {
        var employeeResult = new EmployeeComparisonResult
        {
            SourceEmployee = sourceEmployee,
            TargetEmployee = targetEmployee,
            ComparisonDate = DateTime.UtcNow
        };

        var context = new ComparisonContext
        {
            SourceEmployee = sourceEmployee,
            TargetEmployee = targetEmployee,
            Configuration = config,
            ComparisonDate = DateTime.UtcNow
        };

        foreach (var criteria in criteria)
        {
            if (!criteria.IsApplicable(sourceEmployee, targetEmployee))
                continue;

            try
            {
                var criteriaResult = await ExecuteCriteriaComparisonAsync(
                    criteria, sourceEmployee, targetEmployee, context);
                    
                employeeResult.CriteriaResults.Add(criteriaResult);
                
                if (!criteriaResult.IsMatch)
                {
                    employeeResult.HasDiscrepancies = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to execute criteria {CriteriaName} for employee {EmployeeId}", 
                    criteria.CriteriaName, sourceEmployee.EmployeeId);
                    
                employeeResult.CriteriaResults.Add(new CriteriaComparisonResult
                {
                    CriteriaName = criteria.CriteriaName,
                    IsMatch = false,
                    Error = ex.Message,
                    Severity = criteria.Severity
                });
            }
        }

        return employeeResult;
    }

    private async Task<CriteriaComparisonResult> ExecuteCriteriaComparisonAsync(
        IComparisonCriteria<object> criteria,
        ExtendedEmployeeData sourceEmployee,
        ExtendedEmployeeData targetEmployee,
        ComparisonContext context)
    {
        // Use reflection or strategy pattern to extract field values based on criteria
        var sourceValue = ExtractFieldValue(sourceEmployee, criteria.CriteriaName);
        var targetValue = ExtractFieldValue(targetEmployee, criteria.CriteriaName);

        var comparisonResult = criteria.Compare(sourceValue, targetValue, context);

        return new CriteriaComparisonResult
        {
            CriteriaName = criteria.CriteriaName,
            IsMatch = comparisonResult.IsMatch,
            SourceValue = comparisonResult.SourceValue,
            TargetValue = comparisonResult.TargetValue,
            DiscrepancyDescription = comparisonResult.DiscrepancyDescription,
            VarianceAmount = comparisonResult.VarianceAmount,
            VariancePercentage = comparisonResult.VariancePercentage,
            Severity = criteria.Severity,
            AdditionalContext = comparisonResult.AdditionalContext
        };
    }

    private object ExtractFieldValue(ExtendedEmployeeData employee, string criteriaName)
    {
        return criteriaName switch
        {
            "JobCode" => employee.JobCode,
            "PhoneNumber" => employee.PhoneNumber,
            "PayRate" => employee.PayRate,
            "HireDate" => employee.HireDate,
            "Department" => employee.Department,
            "Title" => employee.Title,
            "EmailAddress" => employee.EmailAddress,
            _ => employee.CustomFields.TryGetValue(criteriaName, out var value) ? value : null
        };
    }

    private List<IComparisonCriteria<object>> GetActiveCriteria(ExtendedComparisonConfiguration config)
    {
        var activeCriteria = new List<IComparisonCriteria<object>>();

        foreach (var criteriaConfig in config.ComparisonCriteria)
        {
            if (!criteriaConfig.Enabled)
                continue;

            var criteria = _criteriaFactory.CreateCriteria(criteriaConfig);
            if (criteria != null)
            {
                activeCriteria.Add(criteria);
            }
        }

        return activeCriteria;
    }
}
```

### 2.2 Extended Configuration Model

```csharp
public class ExtendedComparisonConfiguration : ComparisonConfiguration
{
    public List<ComparisonCriteriaConfig> ComparisonCriteria { get; set; } = new List<ComparisonCriteriaConfig>();
    public CriteriaFilterOptions CriteriaFilters { get; set; } = new CriteriaFilterOptions();
}

public class ComparisonCriteriaConfig
{
    public string CriteriaName { get; set; }
    public bool Enabled { get; set; } = true;
    public CriteriaSeverity Severity { get; set; } = CriteriaSeverity.Medium;
    public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
}

public class CriteriaFilterOptions
{
    public List<CriteriaSeverity> IncludedSeverities { get; set; } = new List<CriteriaSeverity>();
    public List<string> ExcludedCriteria { get; set; } = new List<string>();
    public bool IncludeOnlyDiscrepancies { get; set; } = true;
    public int MaxDiscrepanciesPerEmployee { get; set; } = 0; // 0 = no limit
}

// Sample configuration
public static class SampleConfigurations
{
    public static ExtendedComparisonConfiguration CreateBasicPlusJobCodesConfig()
    {
        return new ExtendedComparisonConfiguration
        {
            Mode = ComparisonMode.All,
            Type = ComparisonType.BasicStatus,
            ComparisonCriteria = new List<ComparisonCriteriaConfig>
            {
                new ComparisonCriteriaConfig
                {
                    CriteriaName = "JobCode",
                    Enabled = true,
                    Severity = CriteriaSeverity.Medium
                }
            },
            CriteriaFilters = new CriteriaFilterOptions
            {
                IncludedSeverities = new List<CriteriaSeverity> 
                { 
                    CriteriaSeverity.Medium, 
                    CriteriaSeverity.High, 
                    CriteriaSeverity.Critical 
                }
            }
        };
    }

    public static ExtendedComparisonConfiguration CreateComprehensiveConfig()
    {
        return new ExtendedComparisonConfiguration
        {
            Mode = ComparisonMode.All,
            Type = ComparisonType.DetailedLoa,
            ComparisonCriteria = new List<ComparisonCriteriaConfig>
            {
                new ComparisonCriteriaConfig
                {
                    CriteriaName = "JobCode",
                    Enabled = true,
                    Severity = CriteriaSeverity.Medium
                },
                new ComparisonCriteriaConfig
                {
                    CriteriaName = "PhoneNumber",
                    Enabled = true,
                    Severity = CriteriaSeverity.Low
                },
                new ComparisonCriteriaConfig
                {
                    CriteriaName = "PayRate",
                    Enabled = true,
                    Severity = CriteriaSeverity.High,
                    Parameters = new Dictionary<string, object>
                    {
                        ["ToleranceAmount"] = 0.05m, // 5 cent tolerance
                        ["TolerancePercentage"] = 1.0  // 1% tolerance
                    }
                },
                new ComparisonCriteriaConfig
                {
                    CriteriaName = "EmailAddress",
                    Enabled = true,
                    Severity = CriteriaSeverity.Medium
                }
            }
        };
    }
}
```

---

## 3. Enhanced Result Models

### 3.1 Detailed Comparison Results

```csharp
public class ExtendedComparisonResult
{
    public ExtendedComparisonConfiguration Configuration { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan ProcessingDuration { get; set; }
    
    // Traditional missing employee discrepancies
    public List<EmployeeDiscrepancy> MissingEmployees { get; set; } = new List<EmployeeDiscrepancy>();
    
    // Detailed criteria-based discrepancies
    public List<EmployeeComparisonResult> DetailedDiscrepancies { get; set; } = new List<EmployeeComparisonResult>();
    
    // Summary statistics
    public ComparisonSummary Summary { get; set; }
    
    // Performance metrics
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new Dictionary<string, object>();
}

public class EmployeeComparisonResult
{
    public ExtendedEmployeeData SourceEmployee { get; set; }
    public ExtendedEmployeeData TargetEmployee { get; set; }
    public DateTime ComparisonDate { get; set; }
    public bool HasDiscrepancies { get; set; }
    public List<CriteriaComparisonResult> CriteriaResults { get; set; } = new List<CriteriaComparisonResult>();
    
    public int CriticalDiscrepancies => CriteriaResults.Count(r => !r.IsMatch && r.Severity == CriteriaSeverity.Critical);
    public int HighDiscrepancies => CriteriaResults.Count(r => !r.IsMatch && r.Severity == CriteriaSeverity.High);
    public int MediumDiscrepancies => CriteriaResults.Count(r => !r.IsMatch && r.Severity == CriteriaSeverity.Medium);
    public int LowDiscrepancies => CriteriaResults.Count(r => !r.IsMatch && r.Severity == CriteriaSeverity.Low);
}

public class CriteriaComparisonResult
{
    public string CriteriaName { get; set; }
    public bool IsMatch { get; set; }
    public object SourceValue { get; set; }
    public object TargetValue { get; set; }
    public string DiscrepancyDescription { get; set; }
    public double? VarianceAmount { get; set; }
    public double? VariancePercentage { get; set; }
    public CriteriaSeverity Severity { get; set; }
    public string Error { get; set; }
    public Dictionary<string, object> AdditionalContext { get; set; }
}

public class ComparisonSummary
{
    public int TotalEmployeesCompared { get; set; }
    public int EmployeesWithDiscrepancies { get; set; }
    public int TotalMissingEmployees { get; set; }
    public Dictionary<string, int> DiscrepanciesByCriteria { get; set; } = new Dictionary<string, int>();
    public Dictionary<CriteriaSeverity, int> DiscrepanciesBySeverity { get; set; } = new Dictionary<CriteriaSeverity, int>();
    public List<string> MostCommonDiscrepancies { get; set; } = new List<string>();
}
```

---

## 4. Enhanced Report Generation

### 4.1 Criteria-Aware Report Generator

```csharp
public class ExtendedCsvReportGenerator : IReportGenerator
{
    public bool Usage(ReportFormat format) =>
        format == ReportFormat.DetailedPipeDelimitedCsv;

    public async Task<Report> GenerateReportAsync(
        ComparisonResult result,
        ReportConfiguration config)
    {
        if (result is not ExtendedComparisonResult extendedResult)
        {
            // Fallback to basic report generation
            return await GenerateBasicReportAsync(result, config);
        }

        var csv = new StringBuilder();

        // Dynamic header based on enabled criteria
        var headers = new List<string>
        {
            "EIS_EmployeeId", "BadgeId", "GEID", "Name", "Home_Location", 
            "Harri_Status", "EIS_Status", "Discrepancy_Type", "Comparison_Type"
        };

        // Add criteria-specific headers
        var enabledCriteria = extendedResult.Configuration.ComparisonCriteria
            .Where(c => c.Enabled)
            .Select(c => c.CriteriaName)
            .ToList();

        foreach (var criteria in enabledCriteria)
        {
            headers.Add($"{criteria}_Harri");
            headers.Add($"{criteria}_EIS");
            headers.Add($"{criteria}_Match");
            headers.Add($"{criteria}_Variance");
        }

        headers.AddRange(new[] { "Total_Discrepancies", "Severity_Summary", "Details" });

        csv.AppendLine(string.Join("|", headers));

        // Generate rows for missing employees (traditional discrepancies)
        foreach (var discrepancy in extendedResult.MissingEmployees)
        {
            var row = GenerateBasicDiscrepancyRow(discrepancy, enabledCriteria.Count);
            csv.AppendLine(row);
        }

        // Generate rows for detailed criteria discrepancies
        foreach (var employeeComparison in extendedResult.DetailedDiscrepancies)
        {
            var row = GenerateDetailedDiscrepancyRow(employeeComparison, enabledCriteria);
            csv.AppendLine(row);
        }

        return new Report
        {
            Content = csv.ToString(),
            Format = ReportFormat.DetailedPipeDelimitedCsv,
            FileName = GenerateExtendedFileName(extendedResult),
            CreatedAt = DateTime.UtcNow,
            Metadata = GenerateExtendedMetadata(extendedResult)
        };
    }

    private string GenerateDetailedDiscrepancyRow(
        EmployeeComparisonResult employeeComparison, 
        List<string> enabledCriteria)
    {
        var values = new List<string>
        {
            employeeComparison.TargetEmployee?.EmployeeId ?? "NULL",
            employeeComparison.TargetEmployee?.BadgeId ?? employeeComparison.SourceEmployee?.GlobalId ?? "NULL",
            employeeComparison.SourceEmployee?.GlobalId ?? "NULL",
            employeeComparison.TargetEmployee?.Name ?? employeeComparison.SourceEmployee?.Name ?? "NULL",
            employeeComparison.TargetEmployee?.HomeLocation ?? employeeComparison.SourceEmployee?.HomeLocation ?? "NULL",
            employeeComparison.SourceEmployee?.Status.ToString() ?? "NULL",
            employeeComparison.TargetEmployee?.Status.ToString() ?? "NULL",
            "CRITERIA_BASED_MISMATCH",
            "DETAILED_CRITERIA"
        };

        // Add criteria-specific values
        foreach (var criteriaName in enabledCriteria)
        {
            var criteriaResult = employeeComparison.CriteriaResults
                .FirstOrDefault(r => r.CriteriaName == criteriaName);

            if (criteriaResult != null)
            {
                values.Add(FormatValue(criteriaResult.SourceValue));
                values.Add(FormatValue(criteriaResult.TargetValue));
                values.Add(criteriaResult.IsMatch ? "MATCH" : "MISMATCH");
                values.Add(criteriaResult.VarianceAmount?.ToString("F2") ?? criteriaResult.VariancePercentage?.ToString("F2") + "%" ?? "NULL");
            }
            else
            {
                values.AddRange(new[] { "NULL", "NULL", "NOT_COMPARED", "NULL" });
            }
        }

        // Summary information
        values.Add(employeeComparison.CriteriaResults.Count(r => !r.IsMatch).ToString());
        values.Add($"Critical:{employeeComparison.CriticalDiscrepancies}|High:{employeeComparison.HighDiscrepancies}|Medium:{employeeComparison.MediumDiscrepancies}|Low:{employeeComparison.LowDiscrepancies}");
        
        var details = string.Join("; ", employeeComparison.CriteriaResults
            .Where(r => !r.IsMatch)
            .Select(r => $"{r.CriteriaName}: {r.DiscrepancyDescription}"));
        values.Add(details.Length > 500 ? details.Substring(0, 500) + "..." : details);

        return string.Join("|", values);
    }

    private string FormatValue(object value)
    {
        return value switch
        {
            null => "NULL",
            string s => s,
            decimal d => d.ToString("F2"),
            DateTime dt => dt.ToString("yyyy-MM-dd"),
            _ => value.ToString()
        };
    }

    private string GenerateExtendedFileName(ExtendedComparisonResult result)
    {
        var pacificTime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(
            DateTime.UtcNow, "Pacific Standard Time");
        
        var criteriaCount = result.Configuration.ComparisonCriteria.Count(c => c.Enabled);
        var comparisonTypePrefix = result.Configuration.Type == ComparisonType.BasicStatus ? "Basic" : "DetailedLOA";
        
        return $"HarriEIS_{comparisonTypePrefix}_Extended_{criteriaCount}Criteria_{pacificTime:yyyyMMdd_HHmmss}.csv";
    }

    private Dictionary<string, object> GenerateExtendedMetadata(ExtendedComparisonResult result)
    {
        return new Dictionary<string, object>
        {
            ["ComparisonType"] = result.Configuration.Type.ToString(),
            ["EnabledCriteria"] = result.Configuration.ComparisonCriteria.Where(c => c.Enabled).Select(c => c.CriteriaName).ToArray(),
            ["TotalDiscrepancies"] = result.MissingEmployees.Count + result.DetailedDiscrepancies.Count,
            ["TraditionalDiscrepancies"] = result.MissingEmployees.Count,
            ["CriteriaBasedDiscrepancies"] = result.DetailedDiscrepancies.Count,
            ["ProcessingDuration"] = result.ProcessingDuration.ToString(),
            ["Summary"] = result.Summary
        };
    }
}
```

### 4.2 Summary Report Generator

```csharp
public class ComparisonSummaryReportGenerator : IReportGenerator
{
    public bool Usage(ReportFormat format) =>
        format == ReportFormat.SummaryReport;

    public async Task<Report> GenerateReportAsync(
        ComparisonResult result,
        ReportConfiguration config)
    {
        if (result is not ExtendedComparisonResult extendedResult)
            throw new ArgumentException("Extended comparison result required for summary report");

        var summary = new StringBuilder();

        // Header
        summary.AppendLine("# Harri-EIS Employee Comparison Summary Report");
        summary.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss UTC}");
        summary.AppendLine($"Processing Duration: {extendedResult.ProcessingDuration}");
        summary.AppendLine();

        // Configuration Summary
        summary.AppendLine("## Configuration");
        summary.AppendLine($"- Comparison Mode: {extendedResult.Configuration.Mode}");
        summary.AppendLine($"- Comparison Type: {extendedResult.Configuration.Type}");
        summary.AppendLine($"- Enabled Criteria: {string.Join(", ", extendedResult.Configuration.ComparisonCriteria.Where(c => c.Enabled).Select(c => c.CriteriaName))}");
        summary.AppendLine();

        // Overall Statistics
        summary.AppendLine("## Overall Statistics");
        summary.AppendLine($"- Total Employees Compared: {extendedResult.Summary.TotalEmployeesCompared:N0}");
        summary.AppendLine($"- Employees with Discrepancies: {extendedResult.Summary.EmployeesWithDiscrepancies:N0}");
        summary.AppendLine($"- Missing Employees: {extendedResult.Summary.TotalMissingEmployees:N0}");
        summary.AppendLine($"- Match Rate: {((double)(extendedResult.Summary.TotalEmployeesCompared - extendedResult.Summary.EmployeesWithDiscrepancies) / extendedResult.Summary.TotalEmployeesCompared * 100):F1}%");
        summary.AppendLine();

        // Discrepancies by Criteria
        summary.AppendLine("## Discrepancies by Criteria");
        foreach (var criteria in extendedResult.Summary.DiscrepanciesByCriteria.OrderByDescending(c => c.Value))
        {
            summary.AppendLine($"- {criteria.Key}: {criteria.Value:N0}");
        }
        summary.AppendLine();

        // Discrepancies by Severity
        summary.AppendLine("## Discrepancies by Severity");
        foreach (var severity in extendedResult.Summary.DiscrepanciesBySeverity.OrderByDescending(s => s.Value))
        {
            summary.AppendLine($"- {severity.Key}: {severity.Value:N0}");
        }
        summary.AppendLine();

        // Most Common Discrepancies
        summary.AppendLine("## Most Common Discrepancy Types");
        foreach (var discrepancy in extendedResult.Summary.MostCommonDiscrepancies.Take(10))
        {
            summary.AppendLine($"- {discrepancy}");
        }

        return new Report
        {
            Content = summary.ToString(),
            Format = ReportFormat.SummaryReport,
            FileName = $"HarriEIS_Summary_{DateTime.UtcNow:yyyyMMdd_HHmmss}.md",
            CreatedAt = DateTime.UtcNow
        };
    }
}
```

---

## 5. Configuration Examples

### 5.1 Sample JSON Configuration

```json
{
  "ConnectionStrings": {
    "EIS": "Data Source=server;Initial Catalog=database;..."
  },
  "Harri": {
    "BaseUrl": "https://gateway.harri.com/open-api-hub/",
    "Authentication": {
      "ClientId": "client_id",
      "ClientSecret": "client_secret"
    },
    "RateLimit": {
      "Type": "Redis",
      "RequestsPerMinute": 200
    }
  },
  "OutputSettings": {
    "ReportPath": "C:\\Reports\\",
    "LogPath": "C:\\Logs\\"
  },
  "FilterOptions": {
    "Mode": "all"
  },
  "ComparisonCriteria": [
    {
      "CriteriaName": "JobCode",
      "Enabled": true,
      "Severity": "Medium",
      "Parameters": {
        "UseMappingTable": true,
        "CaseSensitive": false
      }
    },
    {
      "CriteriaName": "PhoneNumber",
      "Enabled": true,
      "Severity": "Low",
      "Parameters": {
        "NormalizeFormat": true,
        "IgnoreExtensions": true
      }
    },
    {
      "CriteriaName": "PayRate",
      "Enabled": true,
      "Severity": "High",
      "Parameters": {
        "ToleranceAmount": 0.05,
        "TolerancePercentage": 1.0,
        "CompareEffectiveDate": true
      }
    },
    {
      "CriteriaName": "EmailAddress",
      "Enabled": false,
      "Severity": "Medium"
    }
  ],
  "CriteriaFilters": {
    "IncludedSeverities": ["Medium", "High", "Critical"],
    "ExcludedCriteria": [],
    "IncludeOnlyDiscrepancies": true,
    "MaxDiscrepanciesPerEmployee": 0
  }
}
```

### 5.2 Command Line Configuration

```bash
# Basic comparison with job codes
HarriEisComparison.exe --mode all --type basic --criteria jobcode

# Detailed comparison with multiple criteria
HarriEisComparison.exe --mode all --type detailed --criteria jobcode,payrate,phone --severity high,critical

# Store-specific comparison with custom tolerance
HarriEisComparison.exe --mode location --stores "001,002,003" --criteria payrate --payrate-tolerance 0.10
```

---

## 6. Implementation Roadmap

### 6.1 Phase 1: Foundation (Current Sprint)
- ✅ Basic employee status comparison
- ✅ Core architecture with service boundaries
- ✅ Rate-limited Harri API integration
- ✅ Basic CSV report generation

### 6.2 Phase 2: Job Code Extension (Next Sprint)
- 🔄 Implement `JobCodeComparisonCriteria`
- 🔄 Add job code fields to employee data models
- 🔄 Create job code mapping service (if needed)
- 🔄 Update report generation for job code discrepancies
- 🔄 Add configuration support for job code comparison

### 6.3 Phase 3: Phone Number Extension (Sprint 3)
- 📋 Implement `PhoneNumberComparisonCriteria`
- 📋 Add phone normalization logic
- 📋 Handle multiple phone number fields
- 📋 Update data retrieval to include phone numbers

### 6.4 Phase 4: Pay Rate Extension (Sprint 4)
- 📋 Implement `PayRateComparisonCriteria`
- 📋 Add configurable tolerance levels
- 📋 Handle different pay types (hourly vs salary)
- 📋 Consider effective date comparisons

### 6.5 Phase 5: Advanced Features (Future)
- 📋 Custom criteria builder UI
- 📋 Real-time comparison monitoring
- 📋 Automated discrepancy resolution
- 📋 Historical trend analysis

---

## 7. Benefits of Extensible Design

### 7.1 Future-Proofing
- **Easy Addition**: New comparison criteria can be added without modifying core logic
- **Configuration-Driven**: Enable/disable criteria without code changes
- **Flexible Severity**: Adjust importance levels based on business needs
- **Custom Parameters**: Each criteria can have unique configuration options

### 7.2 Maintainability
- **Single Responsibility**: Each criteria class has one specific comparison logic
- **Independent Testing**: Each criteria can be unit tested in isolation
- **Minimal Coupling**: Core comparison engine doesn't need to know about specific criteria
- **Clear Boundaries**: Well-defined interfaces between components

### 7.3 Scalability
- **Parallel Processing**: Criteria comparisons can be executed in parallel
- **Selective Execution**: Only run enabled criteria to optimize performance
- **Batch Processing**: Handle large datasets efficiently with streaming
- **Resource Management**: Monitor and control resource usage per criteria

This extensible framework allows you to easily add job codes, phone numbers, pay rates, and any future comparison criteria while maintaining the clean architecture and leveraging existing patterns from the Jitb.Employment system.