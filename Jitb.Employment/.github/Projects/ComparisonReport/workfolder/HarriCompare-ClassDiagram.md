# HarriCompare System - Class Diagram

## Overview
This class diagram shows the complete architecture of the HarriCompare system, including both implemented and planned classes according to the Project Plan.

## Legend
- 🟢 **Implemented**: Class is fully implemented and tested
- 🟡 **Partially Implemented**: Interface exists but implementation incomplete
- 🔴 **Planned**: Class is defined in project plan but not yet implemented

## Class Diagrams

### Domain Layer - Core Business Objects

```mermaid
classDiagram
    class EmployeeComparisonData {
        🟢 IMPLEMENTED
        +string Id
        +string FirstName
        +string LastName
        +string Status
        +bool IsActive
        +string FullName
    }
    
    class ComparisonConfiguration {
        🟢 IMPLEMENTED
        +ComparisonFilterType FilterType
        +List~string~ SelectedStores
        +List~string~ SelectedTenants
        +string EisConnectionString
        +string HarriApiBaseUrl
        +string OutputPath
        +bool IsValid()
        +void Validate()
    }
    
    class ComparisonResult {
        🟢 IMPLEMENTED
        +List~EmployeeDiscrepancy~ Discrepancies
        +ComparisonSummary Summary
        +DateTime StartTime
        +DateTime EndTime
        +TimeSpan Duration
        +bool HasDiscrepancies()
        +void AddDiscrepancy(EmployeeDiscrepancy)
    }
    
    class EmployeeDiscrepancy {
        🟢 IMPLEMENTED
        +string EisEmployeeId
        +string BadgeId
        +string Geid
        +string Name
        +string HomeLocation
        +string HarriStatus
        +string EisStatus
        +DiscrepancyType Type
        +DiscrepancySeverity Severity
        +DataSourceType Source
        +DateTime DetectedAt
    }
    
    class ComparisonSummary {
        🟢 IMPLEMENTED
        +int TotalEisEmployees
        +int TotalHarriEmployees
        +int TotalDiscrepancies
        +int MissingInHarri
        +int MissingInEis
        +int StatusMismatches
        +int DuplicateBadgeIds
        +int DuplicateGeids
        +double ProcessingTimeSeconds
    }
    
    class DetailedEmployeeData {
        🟢 IMPLEMENTED
        +EmployeeComparisonData BaseData
        +HarriLoaDetails LoaDetails
        +bool IsOnLeaveOfAbsence
        +DateTime? LeaveStartDate
        +DateTime? LeaveEndDate
        +string LeaveReason
    }
    
    class HarriLoaDetails {
        🟢 IMPLEMENTED
        +string Geid
        +bool IsOnLeave
        +DateTime? LeaveStartDate
        +DateTime? LeaveEndDate
        +string LeaveType
        +string LeaveReason
        +DateTime LastUpdated
    }
    
    %% Domain Relationships
    ComparisonResult *-- EmployeeDiscrepancy : contains
    ComparisonResult *-- ComparisonSummary : contains
    ComparisonConfiguration --> ComparisonFilterType : uses
    EmployeeDiscrepancy --> DiscrepancyType : uses
    EmployeeDiscrepancy --> DiscrepancySeverity : uses
    EmployeeDiscrepancy --> DataSourceType : uses
    DetailedEmployeeData *-- EmployeeComparisonData : extends
    DetailedEmployeeData *-- HarriLoaDetails : contains
```

### Enumerations

```mermaid
classDiagram
    class DataSourceType {
        🟢 IMPLEMENTED
        <<enumeration>>
        EIS
        Harri
    }
    
    class DiscrepancyType {
        🟢 IMPLEMENTED
        <<enumeration>>
        MissingInHarri
        MissingInEis
        StatusMismatch
        DuplicateBadgeId
        DuplicateGeid
    }
    
    class DiscrepancySeverity {
        🟢 IMPLEMENTED
        <<enumeration>>
        Low
        Medium
        High
        Critical
    }
    
    class ComparisonFilterType {
        🟢 IMPLEMENTED
        <<enumeration>>
        Location
        Tenant
        AllData
    }
    
    class ExitCode {
        🟢 IMPLEMENTED
        <<enumeration>>
        Success
        GeneralFailure
        ConfigurationError
        HarriSystemUnavailable
        EisSystemUnavailable
        FileIoError
        DiskFullError
        EmptyDataset
    }
```

### Data Access Layer - Repository Interfaces

```mermaid
classDiagram
    class IEmployeeRepository {
        🟢 IMPLEMENTED
        <<interface>>
        +Task~List~EmployeeComparisonData~~ GetActiveEmployeesAsync()
        +Task~RepositoryHealthStatus~ GetHealthStatusAsync()
        +Task~bool~ TestConnectionAsync()
        +Task~void~ ValidateConfigurationAsync()
    }
    
    class IEisEmployeeRepository {
        🟢 IMPLEMENTED
        <<interface>>
        +Task~List~EmployeeComparisonData~~ GetActiveEmployeesByStoreAsync(List~string~)
        +Task~List~EisStore~~ GetAvailableStoresAsync()
        +Task~EisDatabaseConnectionResult~ TestDatabaseConnectionAsync()
        +Task~int~ GetActiveEmployeeCountAsync()
        +Task~List~EmployeeComparisonData~~ ExecuteCustomQueryAsync(string)
    }
    
    class IHarriEmployeeRepository {
        🟢 IMPLEMENTED
        <<interface>>
        +Task~List~EmployeeComparisonData~~ GetActiveEmployeesByLocationAsync(List~string~)
        +Task~List~EmployeeComparisonData~~ GetActiveEmployeesByTenantAsync(List~string~)
        +Task~HarriAuthenticationResult~ AuthenticateAsync()
        +Task~List~HarriTenant~~ GetAvailableTenantsAsync()
        +Task~List~HarriLocationMapping~~ GetLocationMappingsAsync()
        +Task~bool~ TestApiConnectionAsync()
    }
    
    class IHarriLoaService {
        🟢 IMPLEMENTED
        <<interface>>
        +Task~HarriLoaDetails~ GetLeaveOfAbsenceStatusAsync(string geid)
        +Task~List~HarriLoaDetails~~ GetBatchLeaveStatusAsync(List~string~)
        +Task~bool~ IsEmployeeOnLeaveAsync(string geid)
    }
    
    %% Repository Relationships
    IEisEmployeeRepository --|> IEmployeeRepository : extends
    IHarriEmployeeRepository --|> IEmployeeRepository : extends
```

### Data Access Layer - Strategy Interfaces

```mermaid
classDiagram
    class IDataRetrievalStrategy {
        🟢 IMPLEMENTED
        <<interface>>
        +Task~List~EmployeeComparisonData~~ RetrieveDataAsync(ComparisonConfiguration)
        +string GetStrategyName()
        +bool CanHandle(ComparisonConfiguration)
    }
    
    class ILocationBasedStrategy {
        🟢 IMPLEMENTED
        <<interface>>
        +Task~List~EmployeeComparisonData~~ GetEmployeesByLocationAsync(List~string~)
        +Task~List~string~~ GetAvailableLocationsAsync()
        +Task~void~ ValidateLocationsAsync(List~string~)
    }
    
    %% Strategy Relationships
    ILocationBasedStrategy --|> IDataRetrievalStrategy : extends
```

### Data Access Layer - Repository Implementations (Planned)

```mermaid
classDiagram
    class EisEmployeeRepository {
        🔴 PLANNED
        +IEmployeeRepository repo
        +string connectionString
        +Task~List~EmployeeComparisonData~~ GetActiveEmployeesAsync()
        +Task~List~EmployeeComparisonData~~ GetActiveEmployeesByStoreAsync(List~string~)
        +Task~RepositoryHealthStatus~ GetHealthStatusAsync()
        +Task~List~EisStore~~ GetAvailableStoresAsync()
    }
    
    class HarriEmployeeRepository {
        🔴 PLANNED
        +IHarriApiClient apiClient
        +IRateLimit rateLimit
        +Task~List~EmployeeComparisonData~~ GetActiveEmployeesAsync()
        +Task~List~EmployeeComparisonData~~ GetActiveEmployeesByLocationAsync(List~string~)
        +Task~HarriAuthenticationResult~ AuthenticateAsync()
        +Task~List~HarriTenant~~ GetAvailableTenantsAsync()
    }
    
    class HarriLoaService {
        🔴 PLANNED
        +IHarriApiClient apiClient
        +IRateLimit rateLimit
        +IMemoryCache cache
        +Task~HarriLoaDetails~ GetLeaveOfAbsenceStatusAsync(string)
        +Task~List~HarriLoaDetails~~ GetBatchLeaveStatusAsync(List~string~)
        +Task~bool~ IsEmployeeOnLeaveAsync(string)
    }
    
    %% Implementation Relationships
    EisEmployeeRepository ..|> IEisEmployeeRepository : implements
    HarriEmployeeRepository ..|> IHarriEmployeeRepository : implements
    HarriLoaService ..|> IHarriLoaService : implements
```

### Data Access Layer - Strategy Implementations (Planned)

```mermaid
classDiagram
    class LocationBasedEisStrategy {
        🔴 PLANNED
        +IEisEmployeeRepository repository
        +Task~List~EmployeeComparisonData~~ RetrieveDataAsync(ComparisonConfiguration)
        +Task~List~EmployeeComparisonData~~ GetEmployeesByLocationAsync(List~string~)
        +bool CanHandle(ComparisonConfiguration)
    }
    
    class LocationBasedHarriStrategy {
        🔴 PLANNED
        +IHarriEmployeeRepository repository
        +IRateLimit rateLimit
        +Task~List~EmployeeComparisonData~~ RetrieveDataAsync(ComparisonConfiguration)
        +Task~List~EmployeeComparisonData~~ GetEmployeesByLocationAsync(List~string~)
        +bool CanHandle(ComparisonConfiguration)
    }
    
    class TenantBasedHarriStrategy {
        🔴 PLANNED - FUTURE PHASE
        +IHarriEmployeeRepository repository
        +Task~List~EmployeeComparisonData~~ RetrieveDataAsync(ComparisonConfiguration)
        +Task~List~EmployeeComparisonData~~ GetEmployeesByTenantAsync(List~string~)
        +bool CanHandle(ComparisonConfiguration)
    }
    
    class AllDataEisStrategy {
        🔴 PLANNED - FUTURE PHASE
        +IEisEmployeeRepository repository
        +Task~List~EmployeeComparisonData~~ RetrieveDataAsync(ComparisonConfiguration)
        +bool CanHandle(ComparisonConfiguration)
    }
    
    class AllDataHarriStrategy {
        🔴 PLANNED - FUTURE PHASE
        +IHarriEmployeeRepository repository
        +Task~List~EmployeeComparisonData~~ RetrieveDataAsync(ComparisonConfiguration)
        +bool CanHandle(ComparisonConfiguration)
    }
    
    %% Strategy Implementation Relationships
    LocationBasedEisStrategy ..|> ILocationBasedStrategy : implements
    LocationBasedHarriStrategy ..|> ILocationBasedStrategy : implements
    TenantBasedHarriStrategy ..|> IDataRetrievalStrategy : implements
    AllDataEisStrategy ..|> IDataRetrievalStrategy : implements
    AllDataHarriStrategy ..|> IDataRetrievalStrategy : implements
```

### Data Access Layer - Data Models

```mermaid
classDiagram
    class RepositoryHealthStatus {
        🟢 IMPLEMENTED
        +bool IsHealthy
        +string SystemName
        +TimeSpan ResponseTime
        +string ErrorMessage
        +DateTime LastChecked
        +int RecordCount
    }
    
    class HarriTenant {
        🟢 IMPLEMENTED
        +string Id
        +string Name
        +bool IsActive
        +List~string~ Locations
        +DateTime LastUpdated
    }
    
    class EisStore {
        🟢 IMPLEMENTED
        +string StoreNumber
        +string StoreName
        +string Region
        +bool IsActive
        +int ActiveEmployeeCount
        +DateTime LastUpdated
    }
    
    class HarriAuthenticationResult {
        🟢 IMPLEMENTED
        +bool IsSuccessful
        +string AccessToken
        +DateTime ExpiresAt
        +string RefreshToken
        +string ErrorMessage
        +List~string~ Scopes
    }
    
    class HarriLocationMapping {
        🟢 IMPLEMENTED
        +string EisStoreNumber
        +string HarriLocationId
        +string LocationName
        +bool IsActive
        +DateTime LastSyncDate
    }
    
    class EisDatabaseConnectionResult {
        🟢 IMPLEMENTED
        +bool IsSuccessful
        +string DatabaseName
        +string ServerName
        +TimeSpan ConnectionTime
        +string ErrorMessage
        +int ActiveConnections
    }
```

### Configuration Layer

```mermaid
classDiagram
    class ConfigurationLoader {
        🟢 IMPLEMENTED
        +ComparisonConfiguration LoadConfiguration()
        +ComparisonConfiguration LoadFromFile(string)
        +void ValidateConfiguration(ComparisonConfiguration)
        +bool ConfigurationExists()
    }
    
    class ConfigurationValidator {
        🟢 IMPLEMENTED
        +bool IsValid(ComparisonConfiguration)
        +List~string~ ValidateConfiguration(ComparisonConfiguration)
        +void ValidateConnectionStrings(ComparisonConfiguration)
        +void ValidateFilePaths(ComparisonConfiguration)
        +void ValidateFilterConfiguration(ComparisonConfiguration)
    }
    
    class CommandLineParameterParser {
        🟢 IMPLEMENTED
        +CommandLineOptions ParseArguments(string[])
        +void ShowHelp()
        +bool IsValidArgument(string)
        +CommandLineOptions GetDefaultOptions()
    }
    
    class CommandLineOptions {
        🟢 IMPLEMENTED
        +string ConfigFile
        +string OutputPath
        +List~string~ SelectedStores
        +List~string~ SelectedTenants
        +ComparisonFilterType FilterType
        +bool ShowHelp
        +bool Verbose
    }
    
    class CommandLineConfigurationMerger {
        🟢 IMPLEMENTED
        +ComparisonConfiguration MergeConfiguration(ComparisonConfiguration, CommandLineOptions)
        +void ApplyCommandLineOverrides(ComparisonConfiguration, CommandLineOptions)
        +ComparisonConfiguration CreateFromCommandLine(CommandLineOptions)
    }
    
    class ConfigurationException {
        🟢 IMPLEMENTED
        +ExitCode ExitCode
        +string ConfigurationSection
        +string ValidationError
        +ConfigurationException(string, ExitCode)
        +ConfigurationException(string, string, ExitCode)
    }
    
    class ExitCodeMapper {
        🟢 IMPLEMENTED
        +ExitCode MapExceptionToExitCode(Exception)
        +ExitCode MapConfigurationErrorToExitCode(string)
        +int GetExitCodeValue(ExitCode)
        +string GetExitCodeDescription(ExitCode)
    }
    
    %% Configuration Relationships
    ConfigurationLoader --> ComparisonConfiguration : creates
    ConfigurationValidator --> ComparisonConfiguration : validates
    CommandLineParameterParser --> CommandLineOptions : creates
    CommandLineConfigurationMerger --> ComparisonConfiguration : merges
    ConfigurationException --> ExitCode : uses
    ExitCodeMapper --> ExitCode : uses
```

### Adapters Layer

```mermaid
classDiagram
    class EmployeeComparisonAdapter {
        🟡 PARTIALLY IMPLEMENTED
        +EmployeeComparisonData ConvertToComparisonData(Employee)
        +List~EmployeeComparisonData~ ConvertToComparisonData(List~Employee~)
        +Employee ConvertFromComparisonData(EmployeeComparisonData)
        +bool IsActiveEmployee(Employee)
    }
    
    class HarriEmployeeComparisonAdapter {
        🟡 PARTIALLY IMPLEMENTED
        +EmployeeComparisonData ConvertToComparisonData(HarriInboundEmployee)
        +List~EmployeeComparisonData~ ConvertToComparisonData(List~HarriInboundEmployee~)
        +HarriInboundEmployee ConvertFromComparisonData(EmployeeComparisonData)
        +bool IsActiveEmployee(HarriInboundEmployee)
    }
    
    class Employee {
        🟢 EXISTING DOMAIN
        +string EmployeeId
        +string BadgeId
        +string FirstName
        +string LastName
        +string CurrentStatus
        +string HomeLocation
        +bool IsActive()
    }
    
    class HarriInboundEmployee {
        🟢 EXISTING DOMAIN
        +string Geid
        +string FirstName
        +string LastName
        +string Status
        +string LocationId
        +bool IsActive()
    }
    
    %% Adapter Relationships
    EmployeeComparisonAdapter --> Employee : converts
    EmployeeComparisonAdapter --> EmployeeComparisonData : creates
    HarriEmployeeComparisonAdapter --> HarriInboundEmployee : converts
    HarriEmployeeComparisonAdapter --> EmployeeComparisonData : creates
```

### Services Layer - Comparison Engine

```mermaid
classDiagram
    class BasicComparisonService {
        🔴 PLANNED
        +Task~ComparisonResult~ CompareEmployeesAsync(List~EmployeeComparisonData~, List~EmployeeComparisonData~)
        +List~EmployeeDiscrepancy~ FindMissingInHarri(List~EmployeeComparisonData~, List~EmployeeComparisonData~)
        +List~EmployeeDiscrepancy~ FindMissingInEis(List~EmployeeComparisonData~, List~EmployeeComparisonData~)
        +List~EmployeeDiscrepancy~ FindStatusMismatches(List~EmployeeComparisonData~, List~EmployeeComparisonData~)
        +List~EmployeeDiscrepancy~ FindDuplicates(List~EmployeeComparisonData~, DataSourceType)
    }
    
    class DetailedLoaComparisonService {
        🔴 PLANNED - FUTURE PHASE
        +IHarriLoaService loaService
        +Task~ComparisonResult~ CompareEmployeesWithLoaAsync(List~EmployeeComparisonData~, List~EmployeeComparisonData~)
        +Task~List~DetailedEmployeeData~~ EnrichWithLoaDetailsAsync(List~EmployeeComparisonData~)
        +List~EmployeeDiscrepancy~ FindLoaStatusMismatches(List~DetailedEmployeeData~, List~EmployeeComparisonData~)
    }
    
    class TwoPhaseComparisonEngine {
        🔴 PLANNED
        +BasicComparisonService basicService
        +DetailedLoaComparisonService detailedService
        +Task~ComparisonResult~ ExecuteComparisonAsync(ComparisonConfiguration)
        +Task~ComparisonResult~ ExecuteBasicComparisonAsync(ComparisonConfiguration)
        +Task~ComparisonResult~ ExecuteDetailedComparisonAsync(ComparisonConfiguration)
        +Task~List~EmployeeComparisonData~~ RetrieveDataAsync(ComparisonConfiguration)
    }
    
    %% Service Relationships
    BasicComparisonService --> EmployeeComparisonData : processes
    BasicComparisonService --> ComparisonResult : creates
    DetailedLoaComparisonService --> IHarriLoaService : uses
    DetailedLoaComparisonService --> DetailedEmployeeData : creates
    TwoPhaseComparisonEngine --> BasicComparisonService : uses
    TwoPhaseComparisonEngine --> DetailedLoaComparisonService : uses
```

### Services Layer - Orchestration

```mermaid
classDiagram
    class ExecutionOrchestrator {
        🔴 PLANNED
        +ConfigurationLoader configLoader
        +TwoPhaseComparisonEngine comparisonEngine
        +CsvReportGenerator reportGenerator
        +Task~ExitCode~ ExecuteComparisonAsync(string[])
        +Task~ComparisonResult~ RunComparisonAsync(ComparisonConfiguration)
        +Task~void~ GenerateReportsAsync(ComparisonResult, ComparisonConfiguration)
        +ExitCode HandleExceptions(Exception)
    }
    
    %% Orchestration Relationships
    ExecutionOrchestrator --> ConfigurationLoader : uses
    ExecutionOrchestrator --> TwoPhaseComparisonEngine : uses
    ExecutionOrchestrator --> CsvReportGenerator : uses
    ExecutionOrchestrator --> ExitCode : returns
```

### Services Layer - Reporting

```mermaid
classDiagram
    class CsvReportGenerator {
        🔴 PLANNED
        +Task~void~ GenerateReportAsync(ComparisonResult, string)
        +string GenerateFilename(ComparisonConfiguration)
        +string GenerateHeaderRow(ComparisonConfiguration)
        +string FormatDiscrepancyRow(EmployeeDiscrepancy)
        +string HandleNullValues(string)
        +void ValidateOutputPath(string)
    }
    
    class ExtendedCsvReportGenerator {
        🔴 PLANNED - FUTURE PHASE
        +Task~void~ GenerateExtendedReportAsync(ComparisonResult, ComparisonConfiguration, string)
        +string GenerateDynamicHeaders(List~IComparisonCriteria~)
        +string FormatExtendedDiscrepancyRow(EmployeeDiscrepancy, List~IComparisonCriteria~)
        +void AddMetadataSection(ComparisonResult, StringBuilder)
    }
    
    %% Reporting Relationships
    CsvReportGenerator --> ComparisonResult : processes
    ExtendedCsvReportGenerator --> IComparisonCriteria : uses
```

### Main Entry Point

```mermaid
classDiagram
    class Program {
        🟡 PARTIALLY IMPLEMENTED
        +static Task~int~ Main(string[])
        +static void ConfigureServices(IServiceCollection)
        +static void ConfigureLogging(IServiceCollection)
        +static ExitCode HandleGlobalExceptions(Exception)
    }
    
    %% Main Entry Point
    Program --> ExecutionOrchestrator : uses
    Program --> ExitCode : returns
```

### Future Extensibility Framework

```mermaid
classDiagram
    class IComparisonCriteria {
        🔴 PLANNED - FUTURE PHASE
        <<interface>>
        +string CriteriaName
        +DiscrepancySeverity Severity
        +bool IsApplicable(EmployeeComparisonData, EmployeeComparisonData)
        +ComparisonResult Compare(EmployeeComparisonData, EmployeeComparisonData)
        +string FormatDiscrepancy(ComparisonResult)
    }
    
    class BaseComparisonCriteria {
        🔴 PLANNED - FUTURE PHASE
        <<abstract>>
        +string CriteriaName
        +DiscrepancySeverity Severity
        +abstract bool IsApplicable(EmployeeComparisonData, EmployeeComparisonData)
        +abstract ComparisonResult Compare(EmployeeComparisonData, EmployeeComparisonData)
        +virtual string FormatDiscrepancy(ComparisonResult)
    }
    
    class JobCodeComparisonCriteria {
        🔴 PLANNED - FUTURE PHASE
        +ComparisonResult Compare(EmployeeComparisonData, EmployeeComparisonData)
        +string NormalizeJobCode(string)
        +bool IsApplicable(EmployeeComparisonData, EmployeeComparisonData)
    }
    
    class PhoneNumberComparisonCriteria {
        🔴 PLANNED - FUTURE PHASE
        +ComparisonResult Compare(EmployeeComparisonData, EmployeeComparisonData)
        +string NormalizePhoneNumber(string)
        +bool IsValidPhoneNumber(string)
        +bool IsApplicable(EmployeeComparisonData, EmployeeComparisonData)
    }
    
    class PayRateComparisonCriteria {
        🔴 PLANNED - FUTURE PHASE
        +ComparisonResult Compare(EmployeeComparisonData, EmployeeComparisonData)
        +decimal CalculateVariance(decimal, decimal)
        +bool IsWithinTolerance(decimal, decimal, decimal)
        +bool IsApplicable(EmployeeComparisonData, EmployeeComparisonData)
    }
    
    class ExtendedComparisonService {
        🔴 PLANNED - FUTURE PHASE
        +List~IComparisonCriteria~ criteria
        +Task~ComparisonResult~ ExecuteExtendedComparisonAsync(List~EmployeeComparisonData~, List~EmployeeComparisonData~)
        +List~IComparisonCriteria~ GetApplicableCriteria(EmployeeComparisonData, EmployeeComparisonData)
        +ComparisonResult AggregateResults(List~ComparisonResult~)
    }
    
    %% Extensibility Relationships
    JobCodeComparisonCriteria --|> BaseComparisonCriteria : extends
    PhoneNumberComparisonCriteria --|> BaseComparisonCriteria : extends
    PayRateComparisonCriteria --|> BaseComparisonCriteria : extends
    BaseComparisonCriteria ..|> IComparisonCriteria : implements
    ExtendedComparisonService --> IComparisonCriteria : uses
```

## Architecture Summary

### **Phase 1 - Current Implementation (Tasks 1-4)**
- ✅ **Foundation Complete**: Domain models, interfaces, configuration, and test infrastructure
- ✅ **Core Abstractions**: Repository and strategy patterns defined
- 🟡 **Adapters**: Defined but not implemented
- 🔴 **Services**: Planned for implementation

### **Phase 2 - Core Implementation (Tasks 5-12)**
- 🔴 **Data Access**: Concrete repository implementations
- 🔴 **Comparison Engine**: Basic and detailed comparison services
- 🔴 **Orchestration**: Main execution workflow
- 🔴 **Reporting**: CSV generation and output

### **Phase 3 - Advanced Features (Tasks 13-16)**
- 🔴 **LOA Integration**: Detailed Leave of Absence handling
- 🔴 **Multi-Mode Support**: Tenant and all-data strategies
- 🔴 **Extensibility**: Pluggable comparison criteria framework
- 🔴 **Performance**: Optimization and monitoring

### **Key Architectural Patterns**
1. **Repository Pattern**: Abstracted data access for EIS and Harri systems
2. **Strategy Pattern**: Pluggable data retrieval strategies
3. **Builder Pattern**: Test data creation and configuration
4. **Command Pattern**: Configuration and command-line processing
5. **Factory Pattern**: Service creation and dependency injection
6. **Adapter Pattern**: Converting between external and internal models

This architecture provides a solid foundation for incremental development while maintaining clean separation of concerns and testability.