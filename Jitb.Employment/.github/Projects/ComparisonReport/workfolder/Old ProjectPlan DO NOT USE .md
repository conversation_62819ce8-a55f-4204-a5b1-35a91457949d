# Implementation Task Plan
## Harri-EIS Employee Comparison System

**Document Information**
- **Title:** Implementation Task Plan - Harri-EIS Employee Comparison System
- **Version:** 1.0
- **Date:** 2025-07-14
- **Author:** Claude Sonnet 4 (Solutions Architect)
- **Status:** Ready for Implementation

---

## Task Overview

This implementation plan is based on the PRD requirements and detailed system design documents. Tasks are ordered to ensure proper dependency management and incremental delivery of functionality.

**Complexity Scale:**
- 1-2: Simple configuration or utility tasks
- 3-4: Straightforward implementation following existing patterns
- 5-6: Moderate complexity requiring new logic or integration
- 7-8: Complex business logic or integration with multiple systems
- 9-10: High-risk, performance-critical, or architecturally significant tasks

**TDD Methodology:**
This plan follows Test-Driven Development (TDD) principles using the Red-Green-Refactor cycle:

1. **Red Phase** - Write failing unit tests that define the desired behavior
2. **Green Phase** - Implement minimal code to make the tests pass
3. **Refactor Phase** - Improve code quality while keeping tests green

Each implementation task is preceded by its corresponding test task, ensuring:
- **100% test coverage** by design
- **Clear requirements** defined through test specifications
- **Rapid feedback** on code correctness
- **Simplified debugging** through immediate test feedback
- **Design emergence** through test-first thinking

**Test Organization Note:**
In future phases of this project, add a Trait with the task number to each new unit test to enable easy identification and filtering by task. For example:
```csharp
[Fact]
[Trait("Task", "3.1")]
public void ConfigurationLoader_ShouldLoadValidConfiguration()
{
    // Test implementation
}
```

**Git Workflow & TDD Integration:**
- **Task Start**: Commit current work and create new branch (<Initial branch name>TaskN)
- **Task End**: Verify project builds and all TDD cycles complete (all tests pass)
- **TDD Note**: During Red phases, some tests will intentionally fail - this is expected
- **Build Verification**: Project must compile successfully at each task completion
- **Test Completion**: All tests must pass before task completion and branch creation

**Initial Implementation Scope:**
- **Phase 1 (Current)**: Compare by location only + Basic status comparison (Active + LOA combined)
- **Excluded from Phase 1**: By-tenant mode, all-data mode, individual LOA API calls
- **Future Phases**: By-tenant, all-data comparisons, and detailed LOA separation
- **Simplified Focus**: Single comparison mode reduces complexity and risk
- **Incremental Delivery**: Get working system first, then expand functionality
- **Reduced API Usage**: No individual LOA calls saves API quota and complexity

**Data Access Integration Strategy:**
- **Analysis First**: Tasks 5.1 and 6.1 are ANALYSIS ONLY - understand existing methods before coding
- **Reuse Existing**: Most data access methods already exist - create adapters, not new repositories
- **Ask Before Creating**: If you need new methods or repositories, ask first - usually not required
- **Integration Focus**: Create adapter classes that call existing methods rather than reimplementing
- **Pattern Recognition**: Identify existing patterns for OAuth2, rate limiting, error handling, and data mapping

---

## Git Workflow Integration

**Branch Management:**
- **Initial Setup (Task 1.0):** Initialize git repository with `<Initial branch name>` if not exists
- **Task Transitions:** Each high-level task (2-16) starts with Task N.0 to commit previous work and create new branch `<Initial branch name>TaskN`
- **Task Completion:** Each high-level task ends with verification that build succeeds and tests pass
- **Git Flow:** Previous task completion → commit → branch creation → new task work → verification

**Build and Test Verification:**
- **Project Build:** Must compile successfully at end of each high-level task
- **Test Status:** All tests must pass at task completion (after full TDD Red-Green-Refactor cycles)
- **TDD Compatibility:** During Red phases, tests intentionally fail - this is expected behavior
- **Completion Criteria:** Task only complete when all TDD cycles finished and tests green

**Project Structure:**
- **Main Project**: `src/Jitb.Employment.HarriCompare/` (Console Application)
- **Test Project**: `tests/Jitb.Employment.HarriCompare.Tests/` (xUnit Test Project)
- **Solution Folders**: Main project under "Processes", tests under "Tests"
- **Namespace**: `Jitb.Employment.HarriCompare.*`

**Git Workflow Example:**
```bash
# Task 2.0 - At start of Task 2
git add .
git commit -m "Complete Task 1 - Project Setup and Foundation"
git checkout -b <Initial branch name>Task2

# Task 2.1-2.8 - Do the actual Task 2 work
# ... implement domain models and tests ...

# Task 2.8 - At end of Task 2 (after all TDD cycles complete)
# Verify: Build Jitb.Employment.HarriCompare project and run tests
"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe" Jitb.Employment.sln

# Task 3.0 - At start of Task 3
git add .
git commit -m "Complete Task 2 - Core Domain Models & Test Setup"
git checkout -b <Initial branch name>Task3
```

---

## Implementation Task List

| Status | Task Number | Description | Complexity | Predecessor Task Numbers |
|--------|-------------|-------------|------------|--------------------------|
| Completed | 1 | **Project Setup and Foundation** | 2 | None |
| Completed | 1.0 | Initialize git repository with 20250714EISHarriCompare if not exists | 2 | None |
| Completed | 1.1 | Create Jitb.Employment.HarriCompare console application in src/ directory | 2 | 1.0 |
| Completed | 1.2 | Setup NuGet package references for main project (NLog, Newtonsoft.Json) | 2 | 1.1 |
| Completed | 1.2.1 | Setup test project NuGet packages (AutoFixture, xUnit, Moq, FluentAssertions) | 2 | 1.2 |
| Completed | 1.3 | Configure NLog logging framework following main project standards | 3 | 1.2.1 |
| Completed | 1.4 | Setup dependency injection container (StructureMap following existing patterns) | 4 | 1.2.1 |
| Completed | 1.5 | Create project directory structure (Domain, Services, Configuration) | 2 | 1.1 |
| Skipped | 1.5.1 | Add project to solution under Processes folder | 2 | 1.5 |
| Completed | 1.5.2 | Create test project Jitb.Employment.HarriCompare.Tests in tests/ directory | 2 | 1.5.1 |
| Skipped | 1.5.3 | Add test project to solution under Tests folder | 2 | 1.5.2 |
| Completed | 1.6 | Verify project builds successfully | 2 | 1.5.3 |
| Completed | 2 | **Core Domain Models & Test Setup** | 4 | 1 |
| Completed | 2.0 | Commit Task 1 completion and create <Initial branch name>Task2 branch | 2 | 1.6 |
| Completed | 2.0.1 | Evaluate current project state and assess if domain model changes are needed | 2 | 2.0 |
| Completed | 2.1 | Define EmployeeData domain model in Jitb.Employment.HarriCompare.Domain | 3 | 2.0.1 |
| Completed | 2.2 | Define DetailedEmployeeData model extending EmployeeData for LOA information | 4 | 2.1 |
| Completed | 2.3 | Define ComparisonConfiguration model for location-based comparison | 3 | 2.1 |
| Completed | 2.4 | Define EmployeeDiscrepancy model with all discrepancy types | 3 | 2.1 |
| Completed | 2.5 | Define ComparisonResult model for aggregating comparison outcomes | 3 | 2.4 |
| Completed | 2.6 | **FUTURE PHASE** - Define HarriLoaDetails model for LOA status information | 2 | 2.2 |
| Completed | 2.7 | Setup test data builders in Jitb.Employment.HarriCompare.Tests | 4 | 2.6 |
| Completed | 2.8 | Verify project builds and basic tests pass | 2 | 2.7 |
| Completed | 3 | **Configuration Management (TDD)** | 6 | 2 |
| Completed | 3.0 | Commit Task 2 completion and create <Initial branch name>Task3 branch | 2 | 2.8 |
| Completed | 3.0.1 | Evaluate existing configuration patterns and assess if plan adjustments needed | 2 | 3.0 |
| Completed | 3.1 | Write failing unit tests for ConfigurationLoader requirements | 4 | 2.3, 2.7, 3.0.1 |
| Completed | 3.2 | Implement ConfigurationLoader to make tests pass | 5 | 3.1 |
| Completed | 3.2.1 | Create basic app.config reading functionality | 3 | 3.1 |
| Completed | 3.2.2 | Implement app.config deserialization to configuration objects | 3 | 3.2.1 |
| Completed | 3.2.3 | Add file path resolution and error handling | 3 | 3.2.2 |
| Completed | 3.3 | Write failing tests for configuration validation logic | 4 | 3.2.3 |
| Completed | 3.4 | Implement configuration validation to make tests pass | 5 | 3.3 |
| Completed | 3.4.1 | Validate required configuration sections exist | 3 | 3.3 |
| Completed | 3.4.2 | Validate connection string formats | 3 | 3.4.1 |
| Completed | 3.4.3 | Validate file paths and permissions | 3 | 3.4.2 |
| Completed | 3.4.4 | Validate filter configuration parameters | 3 | 3.4.3 |
| Completed | 3.5 | Write failing tests for ConfigurationException and exit code mapping | 3 | 3.4.4 |
| Completed | 3.6 | Implement ConfigurationException to make tests pass | 3 | 3.5 |
| Completed | 3.7 | Write failing tests for command line parameter overrides | 4 | 3.6 |
| Completed | 3.8 | Implement command line parameter support to make tests pass | 4 | 3.7 |
| Completed | 3.9 | Create sample configuration files for different environments | 3 | 3.8 |
| Completed | 3.10 | Verify project builds and all TDD cycles complete (tests pass) | 2 | 3.9 |
| Completed | 4 | **Data Source Interfaces and Abstractions** | 4 | 2 |
| Completed | 4.0 | Commit Task 3 completion and create <Initial branch name>Task4 branch | 2 | 3.10 |
| Completed | 4.0.1 | Evaluate existing interfaces and assess if abstraction changes are needed | 2 | 4.0 |
| Completed | 4.1 | Define ILocationBasedStrategy interface for location filtering | 3 | 2.3, 4.0.1 |
| Completed | 4.2 | Define IEmployeeRepository interface for data access abstraction | 3 | 2.1 |
| Completed | 4.3 | Define IHarriEmployeeRepository extending IEmployeeRepository | 3 | 4.2 |
| Completed | 4.4 | Define IEisEmployeeRepository extending IEmployeeRepository | 3 | 4.2 |
| Not Started | 4.5 | **FUTURE PHASE** - Define IHarriLoaService interface for LOA status checking | 2 | 2.6 |
| Completed | 4.6 | Verify project builds successfully | 2 | 4.5 |
| Completed | 5 | **EIS Data Access Integration (Analysis & Reuse)** | 5 | 4 |
| Completed | 5.0 | Commit Task 4 completion and create <Initial branch name>Task5 branch | 2 | 4.6 |
| Completed | 5.0.1 | Evaluate current EIS integration and assess if data access plan needs adjustment | 3 | 5.0 |
| Completed | 5.0.2 | Reenable tests marked 'Temporarily Disabled'.  Characterize them as 'Task5' or 'Task6' depending on when they will be needed | 1 | None |
| Completed | 5.1 | **CRITICAL** - Review existing codebase before implementing new methods | 2 | 5.0.1 |
| Completed | 5.2 | **ANALYSIS ONLY** - Identify existing EIS data access methods | 4 | 5.1 |
| Completed | 5.2.1 | Locate existing Employee entity mappings and repositories | 3 | 5.2 |
| Completed | 5.2.2 | Identify existing methods for active employee retrieval | 3 | 5.2.1 |
| Completed | 5.2.3 | Document existing store-based filtering capabilities | 3 | 5.2.2 |
| Completed | 5.2.4 | Verify existing connection string and transaction patterns | 3 | 5.2.3 |
| Completed | 5.3 | Write tests for existing EIS data access integration | 4 | 5.2.4, 2.7 |
| Completed | 5.3.1 | Write tests for calling existing employee retrieval methods | 3 | 5.2.4, 2.7 |
| Completed | 5.3.2 | Write tests for existing active employee filtering | 3 | 5.3.1 |
| Completed | 5.3.3 | Write tests for existing error handling patterns | 3 | 5.3.2 |
| Completed | 5.4 | Create EIS data access adapter using existing methods | 4 | 5.3.3 |
| Not Started | 5.4.1 | Create adapter class that calls existing repository methods | 3 | 5.2.3 |
| Not Started | 5.4.2 | Implement GetActiveEmployeesByStore using existing queries | 3 | 5.4.1 |
| Not Started | 5.4.3 | Implement GetAllActiveEmployees using existing methods | 3 | 5.4.2 |
| Not Started | 5.4.4 | Integrate existing connection management patterns | 3 | 5.4.3 |
| Completed | 5.5 | Write tests for LocationBasedEisStrategy using existing methods | 4 | 5.4.4 |
| Completed | 5.6 | Implement LocationBasedEisStrategy calling existing repositories | 4 | 5.5 |
| Not Started | 5.6.1 | Implement location filtering logic | 3 | 5.5 |
| Not Started | 5.6.2 | Call existing store filtering methods | 3 | 5.6.1 |
| Not Started | 5.6.3 | Orchestrate existing methods for multiple stores | 3 | 5.6.2 |
| Not Started | 5.7 | **FUTURE PHASE** - Write tests for AllDataEisStrategy | 2 | 5.6.3 |
| Not Started | 5.8 | **FUTURE PHASE** - Implement AllDataEisStrategy | 2 | 5.7 |
| Completed | 5.9 | Write tests for data mapping from existing entities | 4 | 5.6.3 |
| Completed | 5.10 | Implement data mapping from existing EIS entities | 4 | 5.9 |
| Completed | 5.11 | Write tests for integrating existing error handling | 4 | 5.10 |
| Completed | 5.12 | Integrate existing EIS error handling patterns | 4 | 5.11 |
| Not Started | 5.12.1 | Reuse existing database connection error handling | 3 | 5.11 |
| Not Started | 5.12.2 | Integrate existing SQL exception handling and logging | 3 | 5.12.1 |
| Not Started | 5.12.3 | Reuse existing timeout and retry mechanisms | 3 | 5.12.2 |
| Completed | 5.13 | Verify project builds and all TDD cycles complete (tests pass) | 2 | 5.12.3 |
| Not Started | 6 | **Harri Data Access Integration (Analysis & Reuse)** | 6 | 4 |
| Not Started | 6.0 | Commit Task 5 completion and create <Initial branch name>Task6 branch | 2 | 5.13 |
| Not Started | 6.0.1 | Evaluate current Harri integration and assess if API access plan needs adjustment | 3 | 6.0 |
| Not Started | 6.1 | **CRITICAL** - Review existing Harri integration before creating new code | 2 | 6.0.1 |
| Not Started | 6.2 | **ANALYSIS ONLY** - Identify existing Harri API integration | 4 | 6.1 |
| Not Started | 6.1.1 | Locate existing OAuth2 authentication implementation | 3 | 4.3 |
| Not Started | 6.1.2 | Review existing Harri API client and REST patterns | 3 | 6.1.1 |
| Not Started | 6.1.3 | Identify existing rate limiting infrastructure (IRateLimit) | 3 | 6.1.2 |
| Not Started | 6.1.4 | Document existing Harri employee API endpoints and models | 3 | 6.1.3 |
| Not Started | 6.1.5 | Verify existing location mapping methods | 3 | 6.1.4 |
| Not Started | 6.2 | Write tests for existing Harri API integration | 5 | 6.1.5, 2.7 |
| Not Started | 6.2.1 | Write tests for existing OAuth2 authentication | 3 | 6.1.5, 2.7 |
| Not Started | 6.2.2 | Write tests for existing API client usage | 3 | 6.2.1 |
| Not Started | 6.2.3 | Write tests for existing employee data retrieval methods | 3 | 6.2.2 |
| Not Started | 6.2.4 | Write tests for existing API error response handling | 3 | 6.2.3 |
| Not Started | 6.3 | Create Harri data access adapter using existing APIs | 5 | 6.2.4 |
| Not Started | 6.3.1 | Create adapter class using existing OAuth2 and API clients | 3 | 6.2.4 |
| Not Started | 6.3.2 | Implement GetActiveEmployeesByTenant using existing methods | 3 | 6.3.1 |
| Not Started | 6.3.3 | Implement GetAllActiveEmployees using existing APIs | 3 | 6.3.2 |
| Not Started | 6.3.4 | Integrate existing API response processing patterns | 3 | 6.3.3 |
| Not Started | 6.4 | Write tests for existing rate limiting integration | 4 | 6.3.4 |
| Not Started | 6.4.1 | Write tests for existing IRateLimit strategy selection | 3 | 6.3.4 |
| Not Started | 6.4.2 | Write tests for existing rate limiting enforcement | 3 | 6.4.1 |
| Not Started | 6.4.3 | Write tests for existing rate limiting delay mechanisms | 3 | 6.4.2 |
| Not Started | 6.5 | Integrate existing rate limiting infrastructure | 4 | 6.4.3 |
| Not Started | 6.5.1 | Use existing IRateLimit strategy pattern | 3 | 6.4.3 |
| Not Started | 6.5.2 | Apply existing rate limiting to API calls | 3 | 6.5.1 |
| Not Started | 6.5.3 | Use existing configurable delay mechanisms | 3 | 6.5.2 |
| Not Started | 6.6 | Write tests for LocationBasedHarriStrategy using existing APIs | 4 | 6.5.3 |
| Not Started | 6.6.1 | Write tests for location filtering logic | 3 | 6.5.3 |
| Not Started | 6.6.2 | Write tests for location-based employee retrieval | 3 | 6.6.1 |
| Not Started | 6.6.3 | Write tests for single-threaded execution with existing rate limiting | 3 | 6.6.2 |
| Not Started | 6.7 | Implement LocationBasedHarriStrategy calling existing APIs | 4 | 6.6.3 |
| Not Started | 6.7.1 | Implement location filtering for Harri | 3 | 6.6.3 |
| Not Started | 6.7.2 | Orchestrate existing location APIs with rate limiting | 3 | 6.7.1 |
| Not Started | 6.7.3 | Add progress tracking using existing patterns | 3 | 6.7.2 |
| Not Started | 6.8 | **FUTURE PHASE** - Write tests for TenantBasedHarriStrategy | 2 | 6.7.3 |
| Not Started | 6.9 | **FUTURE PHASE** - Implement TenantBasedHarriStrategy | 2 | 6.8 |
| Not Started | 6.10 | **FUTURE PHASE** - Write tests for AllDataHarriStrategy | 2 | 6.9 |
| Not Started | 6.11 | **FUTURE PHASE** - Implement AllDataHarriStrategy | 2 | 6.10 |
| Not Started | 6.12 | Write tests for Harri data mapping using existing models | 4 | 6.7.3 |
| Not Started | 6.12.1 | Write tests for existing API response to domain mapping | 3 | 6.7.3 |
| Not Started | 6.12.2 | Write tests for existing data type conversions | 3 | 6.12.1 |
| Not Started | 6.12.3 | Write tests for existing null value handling | 3 | 6.12.2 |
| Not Started | 6.13 | Implement Harri data mapping using existing patterns | 4 | 6.12.3 |
| Not Started | 6.13.1 | Use existing DTO to domain model mapping | 3 | 6.12.3 |
| Not Started | 6.13.2 | Apply existing data validation and sanitization | 3 | 6.13.1 |
| Not Started | 6.13.3 | Use existing patterns for malformed data handling | 3 | 6.13.2 |
| Not Started | 6.14 | Write tests for existing Harri API error handling | 4 | 6.13.3 |
| Not Started | 6.14.1 | Write tests for existing HTTP error response handling | 3 | 6.13.3 |
| Not Started | 6.14.2 | Write tests for existing network timeout handling | 3 | 6.14.1 |
| Not Started | 6.14.3 | Write tests for existing authentication failure handling | 3 | 6.14.2 |
| Not Started | 6.15 | Integrate existing Harri API error handling | 4 | 6.14.3 |
| Not Started | 6.15.1 | Use existing HTTP status code error handling | 3 | 6.14.3 |
| Not Started | 6.15.2 | Use existing network and timeout error handling | 3 | 6.15.1 |
| Not Started | 6.15.3 | Use existing authentication error handling and token refresh | 3 | 6.15.2 |
| Not Started | 6.16 | Verify project builds and all TDD cycles complete (tests pass) | 2 | 6.15.3 |
| Not Started | 7 | **FUTURE PHASE** - Harri LOA Service Implementation (Individual API calls) | 2 | 6 |
| Not Started | 8 | **Core Comparison Engine (TDD)** | 6 | 2, 6 |
| Not Started | 8.0 | Commit Task 6 completion and create <Initial branch name>Task8 branch | 2 | 6.16 |
| Not Started | 8.0.1 | Evaluate current comparison logic and assess if engine design plan needs adjustment | 4 | 6.16 |
| Not Started | 8.1 | Write failing tests for employee identity matching (BadgeId vs GEID) | 6 | 2.5, 2.7 |
| Not Started | 8.1.1 | Write tests for exact BadgeId/GEID matching | 3 | 2.5, 2.7 |
| Not Started | 8.1.2 | Write tests for case sensitivity handling | 3 | 8.1.1 |
| Not Started | 8.1.3 | Write tests for whitespace normalization | 3 | 8.1.2 |
| Not Started | 8.1.4 | Write tests for null/empty ID handling | 3 | 8.1.3 |
| Not Started | 8.2 | Implement employee identity matching to make tests pass | 6 | 8.1.4 |
| Not Started | 8.2.1 | Create identity matching utility class | 3 | 8.1.4 |
| Not Started | 8.2.2 | Implement exact matching logic | 3 | 8.2.1 |
| Not Started | 8.2.3 | Add normalization and validation | 3 | 8.2.2 |
| Not Started | 8.3 | Write failing tests for missing employee detection algorithms | 6 | 8.2.3 |
| Not Started | 8.3.1 | Write tests for employees missing in Harri | 3 | 8.2.3 |
| Not Started | 8.3.2 | Write tests for employees missing in EIS | 3 | 8.3.1 |
| Not Started | 8.3.3 | Write tests for large dataset performance | 3 | 8.3.2 |
| Not Started | 8.4 | Implement missing employee detection to make tests pass | 6 | 8.3.3 |
| Not Started | 8.4.1 | Implement MISSING_IN_HARRI detection | 3 | 8.3.3 |
| Not Started | 8.4.2 | Implement MISSING_IN_EIS detection | 3 | 8.4.1 |
| Not Started | 8.4.3 | Optimize for large dataset processing | 3 | 8.4.2 |
| Not Started | 8.5 | Write failing tests for duplicate ID detection | 4 | 8.4.3 |
| Not Started | 8.6 | Implement duplicate ID detection to make tests pass | 5 | 8.5 |
| Not Started | 8.6.1 | Implement duplicate BadgeId detection | 3 | 8.5 |
| Not Started | 8.6.2 | Implement duplicate GEID detection | 3 | 8.6.1 |
| Not Started | 8.6.3 | Add duplicate reporting and logging | 3 | 8.6.2 |
| Not Started | 8.7 | Write failing tests for BasicComparisonService | 7 | 8.6.3, 5, 6 |
| Not Started | 8.7.1 | Write tests for service initialization and dependencies | 3 | 8.6.3, 5, 6 |
| Not Started | 8.7.2 | Write tests for basic comparison workflow | 4 | 8.7.1 |
| Not Started | 8.7.3 | Write tests for result aggregation | 3 | 8.7.2 |
| Not Started | 8.8 | Implement BasicComparisonService to make tests pass | 7 | 8.7.3 |
| Not Started | 8.8.1 | Create BasicComparisonService class | 3 | 8.7.3 |
| Not Started | 8.8.2 | Implement CompareEmployeesAsync method | 4 | 8.8.1 |
| Not Started | 8.8.3 | Add result compilation and statistics | 3 | 8.8.2 |
| Not Started | 8.9 | Write failing tests for status mismatch detection | 6 | 8.8.3 |
| Not Started | 8.9.1 | Write tests for ACTIVE vs Active matching | 3 | 8.8.3 |
| Not Started | 8.9.2 | Write tests for ACTIVE vs LOA compatibility | 3 | 8.9.1 |
| Not Started | 8.9.3 | Write tests for status mismatch scenarios | 3 | 8.9.2 |
| Not Started | 8.10 | Implement status mismatch detection to make tests pass | 7 | 8.9.3 |
| Not Started | 8.10.1 | Implement basic status compatibility checking | 3 | 8.9.3 |
| Not Started | 8.10.2 | Add Harri ACTIVE interpretation logic | 3 | 8.10.1 |
| Not Started | 8.10.3 | Add STATUS_MISMATCH discrepancy creation | 3 | 8.10.2 |
| Not Started | 8.11 | Write failing tests for DetailedLoaComparisonService | 7 | 8.10.3, 7 |
| Not Started | 8.11.1 | Write tests for LOA-enhanced comparison workflow | 4 | 8.10.3, 7 |
| Not Started | 8.11.2 | Write tests for LOA status integration | 3 | 8.11.1 |
| Not Started | 8.11.3 | Write tests for detailed discrepancy types | 3 | 8.11.2 |
| Not Started | 8.12 | Implement DetailedLoaComparisonService to make tests pass | 8 | 8.11.3 |
| Not Started | 8.12.1 | Create DetailedLoaComparisonService class | 3 | 8.11.3 |
| Not Started | 8.12.2 | Implement LOA-aware comparison logic | 4 | 8.12.1 |
| Not Started | 8.12.3 | Add detailed discrepancy classification | 3 | 8.12.2 |
| Not Started | 8.13 | Write failing tests for discrepancy classification logic | 5 | 8.12.3 |
| Not Started | 8.13.1 | Write tests for discrepancy type determination | 3 | 8.12.3 |
| Not Started | 8.13.2 | Write tests for discrepancy metadata population | 3 | 8.13.1 |
| Not Started | 8.13.3 | Write tests for complex discrepancy scenarios | 3 | 8.13.2 |
| Not Started | 8.14 | Implement discrepancy classification to make tests pass | 6 | 8.13.3 |
| Not Started | 8.14.1 | Implement discrepancy type classification rules | 3 | 8.13.3 |
| Not Started | 8.14.2 | Add discrepancy metadata and context | 3 | 8.14.1 |
| Not Started | 8.14.3 | Handle edge cases and complex scenarios | 3 | 8.14.2 |
| Not Started | 8.15 | Verify project builds and all TDD cycles complete (tests pass) | 2 | 8.14.3 |
| Not Started | 9 | **Two-Phase Comparison Orchestration (TDD)** | 9 | 8 |
| Not Started | 9.0 | Commit Task 8 completion and create <Initial branch name>Task9 branch | 2 | 8.15 |
| Not Started | 9.0.1 | Evaluate current orchestration needs and assess if coordination plan needs adjustment | 5 | 8.15 |
| Not Started | 9.1 | Write failing tests for TwoPhaseComparisonEngine coordination | 7 | 8.8, 8.12 |
| Not Started | 9.1.1 | Write tests for basic vs detailed comparison selection | 3 | 8.8, 8.12 |
| Not Started | 9.1.2 | Write tests for comparison workflow coordination | 4 | 9.1.1 |
| Not Started | 9.1.3 | Write tests for result aggregation and merging | 3 | 9.1.2 |
| Not Started | 9.2 | Implement TwoPhaseComparisonEngine to make tests pass | 8 | 9.1.3 |
| Not Started | 9.2.1 | Create TwoPhaseComparisonEngine class | 3 | 9.1.3 |
| Not Started | 9.2.2 | Implement ExecuteComparisonAsync method | 4 | 9.2.1 |
| Not Started | 9.2.3 | Add comparison type selection logic | 3 | 9.2.2 |
| Not Started | 9.3 | Write failing tests for LOA enrichment workflow | 7 | 9.2.3, 7 |
| Not Started | 9.3.1 | Write tests for LOA enrichment triggering | 3 | 9.2.3, 7 |
| Not Started | 9.3.2 | Write tests for batch LOA processing | 4 | 9.3.1 |
| Not Started | 9.3.3 | Write tests for LOA enrichment error handling | 3 | 9.3.2 |
| Not Started | 9.4 | Implement LOA enrichment workflow to make tests pass | 8 | 9.3.3 |
| Not Started | 9.4.1 | Implement EnrichWithLoaDetailsAsync method | 4 | 9.3.3 |
| Not Started | 9.4.2 | Add rate-limited LOA processing | 4 | 9.4.1 |
| Not Started | 9.4.3 | Add LOA enrichment error recovery | 3 | 9.4.2 |
| Not Started | 9.5 | Write failing tests for data retrieval strategy selection | 6 | 9.4.3, 5, 6 |
| Not Started | 9.5.1 | Write tests for strategy selection logic | 3 | 9.4.3, 5, 6 |
| Not Started | 9.5.2 | Write tests for concurrent data retrieval | 3 | 9.5.1 |
| Not Started | 9.5.3 | Write tests for data retrieval error handling | 3 | 9.5.2 |
| Not Started | 9.6 | Implement data retrieval strategy selection to make tests pass | 7 | 9.5.3 |
| Not Started | 9.6.1 | Implement strategy selection algorithm | 3 | 9.5.3 |
| Not Started | 9.6.2 | Add concurrent EIS/Harri data retrieval | 4 | 9.6.1 |
| Not Started | 9.6.3 | Add data retrieval coordination and error handling | 3 | 9.6.2 |
| Not Started | 9.7 | Write failing tests for progress tracking and cancellation | 5 | 9.6.3 |
| Not Started | 9.7.1 | Write tests for progress reporting | 3 | 9.6.3 |
| Not Started | 9.7.2 | Write tests for cancellation token handling | 3 | 9.7.1 |
| Not Started | 9.7.3 | Write tests for progress estimation | 3 | 9.7.2 |
| Not Started | 9.8 | Implement progress tracking and cancellation to make tests pass | 6 | 9.7.3 |
| Not Started | 9.8.1 | Implement IProgress<T> integration | 3 | 9.7.3 |
| Not Started | 9.8.2 | Add CancellationToken support throughout workflow | 3 | 9.8.1 |
| Not Started | 9.8.3 | Add progress estimation and reporting | 3 | 9.8.2 |
| Not Started | 9.9 | Write failing tests for memory management and performance monitoring | 6 | 9.8.3 |
| Not Started | 9.9.1 | Write tests for memory usage tracking | 3 | 9.8.3 |
| Not Started | 9.9.2 | Write tests for performance metrics collection | 3 | 9.9.1 |
| Not Started | 9.9.3 | Write tests for resource cleanup | 3 | 9.9.2 |
| Not Started | 9.10 | Implement memory management and performance monitoring to make tests pass | 7 | 9.9.3 |
| Not Started | 9.10.1 | Add memory usage monitoring | 3 | 9.9.3 |
| Not Started | 9.10.2 | Add performance metrics collection | 3 | 9.10.1 |
| Not Started | 9.10.3 | Implement resource cleanup and GC management | 3 | 9.10.2 |
| Not Started | 9.11 | Verify project builds and all TDD cycles complete (tests pass) | 2 | 9.10.3 |
| Not Started | 10 | **Report Generation (TDD)** | 6 | 8 |
| Not Started | 10.0 | Commit Task 9 completion and create <Initial branch name>Task10 branch | 2 | 9.11 |
| Not Started | 10.0.1 | Evaluate current reporting requirements and assess if generation plan needs adjustment | 4 | 9.11 |
| Not Started | 10.1 | Write failing tests for pipe-delimited CSV format requirements | 4 | 2.5, 2.7 |
| Not Started | 10.2 | Implement basic CsvReportGenerator to make tests pass | 5 | 10.1 |
| Not Started | 10.2.1 | Create CsvReportGenerator class structure | 3 | 10.1 |
| Not Started | 10.2.2 | Implement basic CSV generation logic | 3 | 10.2.1 |
| Not Started | 10.2.3 | Add pipe delimiter formatting | 3 | 10.2.2 |
| Not Started | 10.3 | Write failing tests for NULL value handling in CSV | 4 | 10.2.3 |
| Not Started | 10.4 | Implement NULL value handling to make tests pass | 4 | 10.3 |
| Not Started | 10.5 | Write failing tests for Pacific Time filename generation | 3 | 10.4 |
| Not Started | 10.6 | Implement Pacific Time filename generation to make tests pass | 3 | 10.5 |
| Not Started | 10.7 | Write failing tests for comparison type headers | 4 | 10.6, 8.12 |
| Not Started | 10.8 | Implement comparison type headers to make tests pass | 5 | 10.7 |
| Not Started | 10.8.1 | Add dynamic header generation based on comparison type | 3 | 10.7 |
| Not Started | 10.8.2 | Add LOA-specific columns for detailed comparisons | 3 | 10.8.1 |
| Not Started | 10.8.3 | Add header validation and formatting | 3 | 10.8.2 |
| Not Started | 10.9 | Write failing tests for UTF-8 encoding without BOM | 3 | 10.8.3 |
| Not Started | 10.10 | Implement UTF-8 encoding to make tests pass | 4 | 10.9 |
| Not Started | 10.11 | Write failing tests for report metadata and statistics | 4 | 10.10 |
| Not Started | 10.12 | Implement report metadata and statistics to make tests pass | 4 | 10.11 |
| Not Started | 10.13 | Verify project builds and all TDD cycles complete (tests pass) | 2 | 10.12 |
| Not Started | 11 | **Main Execution Orchestrator (TDD)** | 8 | 9, 10 |
| Not Started | 11.0 | Commit Task 10 completion and create <Initial branch name>Task11 branch | 2 | 10.13 |
| Not Started | 11.0.1 | Evaluate current execution patterns and assess if orchestrator plan needs adjustment | 5 | 10.13 |
| Not Started | 11.1 | Write failing tests for ExecutionOrchestrator workflow coordination | 6 | 3, 9, 10 |
| Not Started | 11.1.1 | Write tests for orchestrator initialization | 3 | 3, 9, 10 |
| Not Started | 11.1.2 | Write tests for workflow step coordination | 3 | 11.1.1 |
| Not Started | 11.1.3 | Write tests for error propagation across workflow | 3 | 11.1.2 |
| Not Started | 11.2 | Implement basic ExecutionOrchestrator to make tests pass | 7 | 11.1.3 |
| Not Started | 11.2.1 | Create ExecutionOrchestrator class | 3 | 11.1.3 |
| Not Started | 11.2.2 | Implement ExecuteComparisonAsync method | 4 | 11.2.1 |
| Not Started | 11.2.3 | Add basic workflow step coordination | 3 | 11.2.2 |
| Not Started | 11.3 | Write failing tests for configuration integration | 4 | 11.2.3, 3 |
| Not Started | 11.4 | Implement configuration integration to make tests pass | 5 | 11.3 |
| Not Started | 11.4.1 | Integrate ConfigurationLoader | 3 | 11.3 |
| Not Started | 11.4.2 | Add configuration validation in workflow | 3 | 11.4.1 |
| Not Started | 11.4.3 | Add configuration-driven behavior | 3 | 11.4.2 |
| Not Started | 11.5 | Write failing tests for data source strategy execution | 5 | 11.4.3, 5, 6 |
| Not Started | 11.5.1 | Write tests for strategy selection and execution | 3 | 11.4.3, 5, 6 |
| Not Started | 11.5.2 | Write tests for parallel data retrieval | 3 | 11.5.1 |
| Not Started | 11.5.3 | Write tests for data source coordination | 3 | 11.5.2 |
| Not Started | 11.6 | Implement data source strategy execution to make tests pass | 6 | 11.5.3 |
| Not Started | 11.6.1 | Implement strategy selection and invocation | 3 | 11.5.3 |
| Not Started | 11.6.2 | Add parallel data retrieval coordination | 3 | 11.6.1 |
| Not Started | 11.6.3 | Add data source result aggregation | 3 | 11.6.2 |
| Not Started | 11.7 | Write failing tests for comparison engine integration | 5 | 11.6.3, 9 |
| Not Started | 11.7.1 | Write tests for comparison engine invocation | 3 | 11.6.3, 9 |
| Not Started | 11.7.2 | Write tests for comparison result handling | 3 | 11.7.1 |
| Not Started | 11.7.3 | Write tests for comparison error scenarios | 3 | 11.7.2 |
| Not Started | 11.8 | Implement comparison engine integration to make tests pass | 6 | 11.7.3 |
| Not Started | 11.8.1 | Integrate TwoPhaseComparisonEngine | 3 | 11.7.3 |
| Not Started | 11.8.2 | Add comparison result processing | 3 | 11.8.1 |
| Not Started | 11.8.3 | Add comparison error handling | 3 | 11.8.2 |
| Not Started | 11.9 | Write failing tests for report generation integration | 4 | 11.8.3, 10 |
| Not Started | 11.10 | Implement report generation integration to make tests pass | 5 | 11.9 |
| Not Started | 11.10.1 | Integrate CsvReportGenerator | 3 | 11.9 |
| Not Started | 11.10.2 | Add report file output handling | 3 | 11.10.1 |
| Not Started | 11.10.3 | Add report generation error handling | 3 | 11.10.2 |
| Not Started | 11.11 | Write failing tests for comprehensive error handling and exit codes | 6 | 11.10.3 |
| Not Started | 11.11.1 | Write tests for all error scenario to exit code mappings | 3 | 11.10.3 |
| Not Started | 11.11.2 | Write tests for error logging and reporting | 3 | 11.11.1 |
| Not Started | 11.11.3 | Write tests for graceful error recovery | 3 | 11.11.2 |
| Not Started | 11.12 | Implement comprehensive error handling to make tests pass | 7 | 11.11.3 |
| Not Started | 11.12.1 | Implement error to exit code mapping | 3 | 11.11.3 |
| Not Started | 11.12.2 | Add comprehensive error logging | 3 | 11.12.1 |
| Not Started | 11.12.3 | Add error recovery and cleanup | 3 | 11.12.2 |
| Not Started | 11.13 | Write failing tests for performance monitoring and SLA compliance | 5 | 11.12.3 |
| Not Started | 11.13.1 | Write tests for execution time monitoring | 3 | 11.12.3 |
| Not Started | 11.13.2 | Write tests for memory usage monitoring | 3 | 11.13.1 |
| Not Started | 11.13.3 | Write tests for SLA compliance checking | 3 | 11.13.2 |
| Not Started | 11.14 | Implement performance monitoring to make tests pass | 6 | 11.13.3 |
| Not Started | 11.14.1 | Add execution time tracking | 3 | 11.13.3 |
| Not Started | 11.14.2 | Add memory usage monitoring | 3 | 11.14.1 |
| Not Started | 11.14.3 | Add SLA compliance validation | 3 | 11.14.2 |
| Not Started | 11.15 | Verify project builds and all TDD cycles complete (tests pass) | 2 | 11.14.3 |
| Not Started | 12 | **Console Application Entry Point (TDD)** | 5 | 11 |
| Not Started | 12.0 | Commit Task 11 completion and create <Initial branch name>Task12 branch | 2 | 11.15 |
| Not Started | 12.0.1 | Evaluate current console architecture and assess if entry point plan needs adjustment | 4 | 11.15 |
| Not Started | 12.1 | Write failing tests for Program.Main and command line processing | 4 | 1.4, 11.2 |
| Not Started | 12.2 | Implement Program.Main to make tests pass | 4 | 12.1 |
| Not Started | 12.3 | Write failing tests for dependency injection container setup | 4 | 12.2, 1.4 |
| Not Started | 12.4 | Implement dependency injection setup to make tests pass | 5 | 12.3 |
| Not Started | 12.4.1 | Configure StructureMap container | 3 | 12.3 |
| Not Started | 12.4.2 | Register all service dependencies | 3 | 12.4.1 |
| Not Started | 12.4.3 | Add container validation and diagnostics | 3 | 12.4.2 |
| Not Started | 12.5 | Write failing tests for exception handling and exit code management | 4 | 12.4.3, 11.12 |
| Not Started | 12.6 | Implement exception handling and exit codes to make tests pass | 5 | 12.5 |
| Not Started | 12.6.1 | Add top-level exception handling | 3 | 12.5 |
| Not Started | 12.6.2 | Implement exit code determination | 3 | 12.6.1 |
| Not Started | 12.6.3 | Add application cleanup on exit | 3 | 12.6.2 |
| Not Started | 12.7 | Write failing tests for console output and progress indication | 3 | 12.6 |
| Not Started | 12.8 | Implement console output to make tests pass | 3 | 12.7 |
| Not Started | 12.9 | Write failing tests for graceful shutdown and cleanup | 3 | 12.8 |
| Not Started | 12.10 | Implement graceful shutdown to make tests pass | 4 | 12.9 |
| Not Started | 12.11 | Verify project builds and all TDD cycles complete (tests pass) | 2 | 12.10 |
| Not Started | 13 | **Integration Testing** | 8 | 12 |
| Not Started | 13.0 | Commit Task 12 completion and create <Initial branch name>Task13 branch | 2 | 12.11 |
| Not Started | 13.0.1 | Evaluate current integration test scope and assess if testing plan needs adjustment | 5 | 12.11 |
| Not Started | 13.1 | Create integration tests for EIS database connectivity | 7 | 5, 12 |
| Not Started | 13.1.1 | Setup integration test database environment | 3 | 5, 12 |
| Not Started | 13.1.2 | Test database connection and query execution | 3 | 13.1.1 |
| Not Started | 13.1.3 | Test repository methods with real database | 4 | 13.1.2 |
| Not Started | 13.2 | Create integration tests for Harri API connectivity and authentication | 8 | 6, 12 |
| Not Started | 13.2.1 | Setup Harri API test environment | 3 | 6, 12 |
| Not Started | 13.2.2 | Test OAuth2 authentication flow | 4 | 13.2.1 |
| Not Started | 13.2.3 | Test API endpoint connectivity | 3 | 13.2.2 |
| Not Started | 13.2.4 | Test rate limiting behavior with real API | 4 | 13.2.3 |
| Not Started | 13.3 | Create end-to-end integration tests for basic comparison workflow | 7 | 11, 12 |
| Not Started | 13.3.1 | Setup end-to-end test data and environment | 3 | 11, 12 |
| Not Started | 13.3.2 | Test complete basic comparison workflow | 4 | 13.3.1 |
| Not Started | 13.3.3 | Validate basic comparison output and reports | 3 | 13.3.2 |
| Not Started | 13.4 | Create end-to-end integration tests for detailed LOA comparison workflow | 8 | 11, 12 |
| Not Started | 13.4.1 | Setup LOA test scenarios with test data | 3 | 11, 12 |
| Not Started | 13.4.2 | Test detailed LOA comparison with API calls | 4 | 13.4.1 |
| Not Started | 13.4.3 | Validate detailed LOA comparison output | 3 | 13.4.2 |
| Not Started | 13.4.4 | Test LOA rate limiting integration | 3 | 13.4.3 |
| Not Started | 13.5 | Create integration tests for configuration loading and error scenarios | 6 | 3, 12 |
| Not Started | 13.5.1 | Test configuration file loading integration | 3 | 3, 12 |
| Not Started | 13.5.2 | Test configuration validation with dependencies | 3 | 13.5.1 |
| Not Started | 13.5.3 | Test error scenario handling across components | 3 | 13.5.2 |
| Not Started | 13.6 | Verify project builds and all integration tests pass | 2 | 13.5.3 |
| Not Started | 14 | **Performance and Load Testing** | 9 | 13 |
| Not Started | 14.0 | Commit Task 13 completion and create <Initial branch name>Task14 branch | 2 | 13.6 |
| Not Started | 14.0.1 | Evaluate current performance requirements and assess if testing plan needs adjustment | 6 | 13.6 |
| Not Started | 14.1 | Create performance tests for 45-minute SLA compliance with 50,000 records | 9 | 13.3, 13.4 |
| Not Started | 14.1.1 | Setup large dataset test environment | 3 | 13.3, 13.4 |
| Not Started | 14.1.2 | Create 50,000 record test data generation | 4 | 14.1.1 |
| Not Started | 14.1.3 | Implement timing and SLA monitoring | 3 | 14.1.2 |
| Not Started | 14.1.4 | Execute and validate 45-minute SLA compliance | 4 | 14.1.3 |
| Not Started | 14.2 | Create memory usage tests for 1GB memory ceiling compliance | 8 | 14.1 |
| Not Started | 14.2.1 | Setup memory profiling and monitoring tools | 3 | 14.1 |
| Not Started | 14.2.2 | Create memory usage tracking throughout workflow | 4 | 14.2.1 |
| Not Started | 14.2.3 | Test memory usage with various dataset sizes | 3 | 14.2.2 |
| Not Started | 14.2.4 | Validate 1GB memory ceiling compliance | 3 | 14.2.3 |
| Not Started | 14.3 | Create rate limiting performance tests under load | 7 | 13.2 |
| Not Started | 14.3.1 | Setup rate limiting test scenarios | 3 | 13.2 |
| Not Started | 14.3.2 | Test rate limiting with high API call volumes | 4 | 14.3.1 |
| Not Started | 14.3.3 | Validate rate limiting effectiveness under load | 3 | 14.3.2 |
| Not Started | 14.4 | Create scalability tests for various dataset sizes | 7 | 14.1 |
| Not Started | 14.4.1 | Design scalability test matrix | 3 | 14.1 |
| Not Started | 14.4.2 | Test performance with 10K, 25K, 50K, 75K records | 4 | 14.4.1 |
| Not Started | 14.4.3 | Analyze performance trends and bottlenecks | 3 | 14.4.2 |
| Not Started | 14.5 | Verify project builds and all performance tests pass | 2 | 14.4.3 |
| Not Started | 15 | **Documentation and Deployment** | 4 | 14 |
| Not Started | 15.0 | Commit Task 14 completion and create <Initial branch name>Task15 branch | 2 | 14.5 |
| Not Started | 15.0.1 | Evaluate current deployment needs and assess if documentation plan needs adjustment | 3 | 14.5 |
| Not Started | 15.1 | Create user documentation for configuration and execution | 3 | 12 |
| Not Started | 15.2 | Create operational documentation for monitoring and troubleshooting | 4 | 14 |
| Not Started | 15.3 | Create deployment scripts and configuration templates | 4 | 15.1 |
| Not Started | 15.4 | Prepare production deployment package | 3 | 15.2, 15.3 |
| Not Started | 15.5 | Verify project builds and deployment package is complete | 2 | 15.4 |
| Not Started | 16 | **Future Extensibility Framework (Phase 2)** | 7 | 15 |
| Not Started | 16.0 | Commit Task 15 completion and create <Initial branch name>Task16 branch | 2 | 15.5 |
| Not Started | 16.0.1 | Evaluate current extensibility needs and assess if framework plan needs adjustment | 5 | 15.5 |
| Not Started | 16.1 | Write failing tests for IComparisonCriteria interface and framework | 5 | 8 |
| Not Started | 16.1.1 | Write tests for IComparisonCriteria interface definition | 3 | 8 |
| Not Started | 16.1.2 | Write tests for BaseComparisonCriteria abstract class | 3 | 16.1.1 |
| Not Started | 16.1.3 | Write tests for ComparisonResult and severity handling | 3 | 16.1.2 |
| Not Started | 16.2 | Implement IComparisonCriteria framework to make tests pass | 6 | 16.1 |
| Not Started | 16.2.1 | Implement IComparisonCriteria interface | 3 | 16.1.3 |
| Not Started | 16.2.2 | Implement BaseComparisonCriteria abstract class | 3 | 16.2.1 |
| Not Started | 16.2.3 | Implement ComparisonResult and supporting models | 3 | 16.2.2 |
| Not Started | 16.3 | Write failing tests for JobCodeComparisonCriteria | 4 | 16.2 |
| Not Started | 16.4 | Implement JobCodeComparisonCriteria to make tests pass | 5 | 16.3 |
| Not Started | 16.4.1 | Implement JobCodeComparisonCriteria class | 3 | 16.3 |
| Not Started | 16.4.2 | Add job code normalization and comparison logic | 3 | 16.4.1 |
| Not Started | 16.4.3 | Add job code discrepancy reporting | 3 | 16.4.2 |
| Not Started | 16.5 | Write failing tests for PhoneNumberComparisonCriteria | 5 | 16.4 |
| Not Started | 16.5.1 | Write tests for phone number format normalization | 3 | 16.4.3 |
| Not Started | 16.5.2 | Write tests for international phone number handling | 3 | 16.5.1 |
| Not Started | 16.5.3 | Write tests for phone number discrepancy classification | 3 | 16.5.2 |
| Not Started | 16.6 | Implement PhoneNumberComparisonCriteria to make tests pass | 6 | 16.5 |
| Not Started | 16.6.1 | Implement PhoneNumberComparisonCriteria class | 3 | 16.5.3 |
| Not Started | 16.6.2 | Add phone number normalization algorithms | 3 | 16.6.1 |
| Not Started | 16.6.3 | Add phone number validation and comparison logic | 3 | 16.6.2 |
| Not Started | 16.7 | Write failing tests for PayRateComparisonCriteria | 6 | 16.6 |
| Not Started | 16.7.1 | Write tests for decimal pay rate comparison | 3 | 16.6.3 |
| Not Started | 16.7.2 | Write tests for percentage variance calculations | 3 | 16.7.1 |
| Not Started | 16.7.3 | Write tests for pay rate tolerance thresholds | 3 | 16.7.2 |
| Not Started | 16.8 | Implement PayRateComparisonCriteria to make tests pass | 7 | 16.7 |
| Not Started | 16.8.1 | Implement PayRateComparisonCriteria class | 3 | 16.7.3 |
| Not Started | 16.8.2 | Add decimal comparison with tolerance logic | 4 | 16.8.1 |
| Not Started | 16.8.3 | Add variance calculation and reporting | 3 | 16.8.2 |
| Not Started | 16.9 | Write failing tests for ExtendedComparisonService | 7 | 16.8 |
| Not Started | 16.9.1 | Write tests for criteria collection injection | 3 | 16.8.3 |
| Not Started | 16.9.2 | Write tests for dynamic criteria execution | 4 | 16.9.1 |
| Not Started | 16.9.3 | Write tests for extended result aggregation | 3 | 16.9.2 |
| Not Started | 16.10 | Implement ExtendedComparisonService to make tests pass | 8 | 16.9 |
| Not Started | 16.10.1 | Create ExtendedComparisonService class | 3 | 16.9.3 |
| Not Started | 16.10.2 | Implement criteria collection handling | 4 | 16.10.1 |
| Not Started | 16.10.3 | Add dynamic criteria execution and result aggregation | 4 | 16.10.2 |
| Not Started | 16.11 | Write failing tests for ExtendedCsvReportGenerator | 5 | 16.10 |
| Not Started | 16.11.1 | Write tests for dynamic column generation | 3 | 16.10.3 |
| Not Started | 16.11.2 | Write tests for criteria-specific formatting | 3 | 16.11.1 |
| Not Started | 16.11.3 | Write tests for extended report metadata | 3 | 16.11.2 |
| Not Started | 16.12 | Implement ExtendedCsvReportGenerator to make tests pass | 6 | 16.11 |
| Not Started | 16.12.1 | Create ExtendedCsvReportGenerator class | 3 | 16.11.3 |
| Not Started | 16.12.2 | Implement dynamic header and column generation | 3 | 16.12.1 |
| Not Started | 16.12.3 | Add criteria-specific formatting and metadata | 3 | 16.12.2 |
| Not Started | 16.13 | Verify project builds and all extensibility tests pass | 2 | 16.12.3 |
| Not Started | 16.14 | Commit Task 16 completion to <Initial branch name> | 2 | 16.13 |

---

## Future Enhancement Roadmap

### Phase 2: Multi-Mode Comparison Support
**Removed from Phase 1 - Future Implementation:**

**Task 5.6-5.7**: All-Data EIS Strategy
- Write tests for AllDataEisStrategy using existing methods
- Implement AllDataEisStrategy calling existing repositories
- Purpose: Support comparison of all employees across all stores

**Task 6.8-6.11**: Tenant and All-Data Harri Strategies
- Write tests for TenantBasedHarriStrategy using existing APIs
- Implement TenantBasedHarriStrategy calling existing APIs
- Write tests for AllDataHarriStrategy using existing APIs  
- Implement AllDataHarriStrategy calling existing APIs
- Purpose: Support by-tenant filtering and complete dataset comparisons

**Configuration Enhancements:**
- Extend ComparisonConfiguration model for multiple comparison modes
- Add tenant-based filtering configuration
- Add all-data mode configuration
- Update IDataRetrievalStrategy interface with Usage() predicate pattern

### Phase 3: Detailed LOA Status Separation
**Removed from Phase 1 - Future Implementation:**

**Task 7**: Complete Harri LOA Service Implementation
- **7.1**: Write tests for HarriLoaService core functionality
  - Individual employee LOA status API calls
  - LOA status data structure validation
  - GEID parameter validation
- **7.2**: Implement basic HarriLoaService
  - Create LOA service class with API client integration
  - Implement GetLeaveOfAbsenceStatusAsync method
  - Add LOA API response processing
  - Add basic error handling for API failures
- **7.3-7.4**: LOA Rate Limiting Integration
  - Write tests for rate limiting strategy selection
  - Write tests for individual call rate limiting
  - Write tests for configurable delay between calls
  - Integrate IRateLimit strategy with LOA service
  - Add rate limiting before each LOA API call
  - Implement configurable delay timing
  - Add rate limiting monitoring and logging
- **7.5-7.6**: LOA Caching Mechanism
  - Write tests for cache key generation
  - Write tests for cache hit/miss scenarios
  - Write tests for cache expiration policies
  - Integrate IMemoryCache for LOA status caching
  - Implement cache-first lookup logic
  - Add cache population and expiration management
- **7.7-7.8**: LOA API Error Handling
  - Write tests for HTTP error responses
  - Write tests for network failures
  - Write tests for malformed API responses
  - Add HTTP status code error handling
  - Add network and timeout error handling
  - Add graceful degradation for API failures
- **7.9-7.10**: LOA Data Mapping and Validation
  - Write tests for LOA data mapping and validation
  - Implement API response to HarriLoaDetails mapping
  - Add date parsing and validation
  - Add null value and missing field handling

**Task 8.11-8.12**: Detailed LOA Comparison Service
- Write tests for DetailedLoaComparisonService
  - LOA-enhanced comparison workflow
  - LOA status integration
  - Detailed discrepancy types (LOA_STATUS_MISMATCH)
- Implement DetailedLoaComparisonService
  - Create DetailedLoaComparisonService class
  - Implement LOA-aware comparison logic
  - Add detailed discrepancy classification

**Domain Model Enhancements:**
- Complete HarriLoaDetails model for LOA status information
- Extend EmployeeDiscrepancy model with LOA-specific discrepancy types
- Add LeaveOfAbsenceStatus enum with detailed status codes

### Phase 4: Two-Phase Comparison Orchestration
**Enhanced from Phase 1 - Future Implementation:**

**Task 9**: Enhanced Two-Phase Comparison Orchestration
- **9.3-9.4**: LOA Enrichment Workflow
  - Write tests for LOA enrichment triggering
  - Write tests for batch LOA processing
  - Write tests for LOA enrichment error handling
  - Implement EnrichWithLoaDetailsAsync method
  - Add rate-limited LOA processing
  - Add LOA enrichment error recovery
- **9.5-9.6**: Enhanced Data Retrieval Strategy Selection
  - Write tests for multi-mode strategy selection logic
  - Write tests for concurrent data retrieval across modes
  - Implement strategy selection algorithm for all modes
  - Add concurrent EIS/Harri data retrieval
  - Add data retrieval coordination and error handling

### Phase 5: Advanced Comparison Criteria
**Task 16**: Future Extensibility Framework
- **16.1-16.2**: IComparisonCriteria Framework
  - Write tests for IComparisonCriteria interface and framework
  - Implement IComparisonCriteria framework
  - ComparisonResult with variance tracking
  - CriteriaSeverity levels (Low, Medium, High, Critical)
- **16.3-16.4**: Job Code Comparison
  - Write tests for JobCodeComparisonCriteria
  - Implement JobCodeComparisonCriteria
  - Case-insensitive job code matching
  - Job code normalization and validation
- **16.5-16.6**: Phone Number Comparison
  - Write tests for PhoneNumberComparisonCriteria
  - Implement PhoneNumberComparisonCriteria
  - Phone number format normalization
  - International phone number handling
- **16.7-16.8**: Pay Rate Comparison
  - Write tests for PayRateComparisonCriteria
  - Implement PayRateComparisonCriteria
  - Decimal comparison with tolerance logic
  - Percentage variance calculations
  - Pay rate tolerance thresholds
- **16.9-16.10**: Extended Comparison Service
  - Write tests for ExtendedComparisonService
  - Implement ExtendedComparisonService
  - Criteria collection handling
  - Dynamic criteria execution and result aggregation
- **16.11-16.12**: Extended CSV Report Generator
  - Write tests for ExtendedCsvReportGenerator
  - Implement ExtendedCsvReportGenerator
  - Dynamic column generation based on enabled criteria
  - Criteria-specific formatting and metadata

### Implementation Priority for Future Phases:

**Priority 1 (Phase 2)**: Multi-Mode Support
- **Business Value**: Support different organizational comparison needs
- **Technical Risk**: Low - extends existing location-based approach
- **Estimated Effort**: 1 week

**Priority 2 (Phase 3)**: Detailed LOA Separation
- **Business Value**: Precise employee status differentiation
- **Technical Risk**: Medium - requires individual API calls and rate limiting
- **Estimated Effort**: 2 weeks

**Priority 3 (Phase 4)**: Advanced Orchestration
- **Business Value**: Performance optimization and workflow enhancement
- **Technical Risk**: Medium - complex workflow coordination
- **Estimated Effort**: 1 week

**Priority 4 (Phase 5)**: Extensible Criteria Framework
- **Business Value**: Support for additional comparison fields
- **Technical Risk**: Low - well-architected extension framework
- **Estimated Effort**: 2 weeks

**Total Future Enhancement Effort**: ~6 weeks across 4 phases

---

## Implementation Phases

### Phase 1: Simplified Core Implementation (Tasks 1 - 12)
**Timeline:** Week 1-2  
**Scope:** Location-based comparison only, basic status matching  
**Deliverable:** Working console application with simplified comparison functionality  
**Key Features:**
- TDD-driven development with full unit test coverage
- Location-based employee comparison only
- Basic status comparison (Harri ACTIVE = EIS Active + LOA)
- Configuration management for location filtering
- CSV report generation with basic discrepancy types
- Error handling with proper exit codes
- Integration with existing EIS and Harri data access methods

**Excluded from Phase 1 (See Future Enhancement Roadmap):**
- By-tenant comparison mode
- All-data comparison mode  
- Individual LOA status API calls
- Detailed LOA vs Active separation
- Advanced comparison criteria (job codes, phone numbers, pay rates)

### Phase 1a: Integration and Performance Testing (Tasks 13 - 14)
**Timeline:** Week 2 (overlapping with Phase 1)  
**Deliverable:** Fully tested simplified system meeting core requirements  
**Key Features:**
- Integration testing with real EIS and Harri systems
- Performance validation for location-based comparison
- SLA compliance verification
- End-to-end workflow testing

### Phase 1b: Documentation and Deployment (Task 15)
**Timeline:** Week 2-3  
**Deliverable:** Production-ready deployment package for simplified system  
**Key Features:**
- User and operational documentation
- Deployment automation
- Production configuration templates
- Foundation for future enhancements

---

## Critical Path Analysis

**Critical Path Tasks (simplified scope):**
1 → 2 → 4 → 5 → 6 → 8 → 9 → 10 → 11 → 12 → 13 → 14 → 15

**Simplified Scope Notes:**
- Task 7 (Harri LOA Service) moved to Future Phase 3
- Tenant/All-data strategies moved to Future Phase 2
- Detailed LOA comparison moved to Future Phase 3
- Extensible criteria framework moved to Future Phase 5

**Total Tasks:** 314 tasks (95 high-level + 183 subtasks + 30 git workflow + 2 analysis + 4 project setup tasks)
**Phase 1 Active Tasks:** ~180 tasks (excluding future phase tasks)
**Estimated Timeline:** 2 weeks for simplified TDD implementation and testing

**TDD Workflow Streams:**
- **Stream A:** Configuration TDD (Task 3.x) - write tests, implement, refactor
- **Stream B:** EIS Data Access TDD (Task 5.x) - write tests, implement, refactor  
- **Stream C:** Harri Data Access TDD (Task 6.x) - write tests, implement, refactor
- **Stream D:** Comparison Engine TDD (Task 8.x) - write tests, implement, refactor

---

## Risk Mitigation

**High-Risk Tasks (Complexity 6+, Phase 1 only):**
- Task 6: Harri Data Access Integration - External API dependency (reduced scope)
- Task 8: Core Comparison Engine - Basic business logic (simplified)
- Task 9: Comparison Orchestration - Location-based workflow only (simplified)
- Task 14: Performance and Load Testing - SLA validation

**Deferred High-Risk Tasks (Future Phases):**
- Task 7: Harri LOA Service Implementation - Individual API calls with rate limiting
- Multi-mode strategies - Tenant and all-data comparison complexity
- Detailed LOA separation - Complex API integration and rate limiting

**Mitigation Strategies:**
- Early prototyping of Harri API integration
- Incremental testing of rate limiting mechanisms
- Memory profiling during development
- Performance testing with representative datasets
- Fallback plans for API failures

---

## Success Criteria

**Task Breakdown Summary:**
- **Total Tasks:** 314 (95 high-level + 183 subtasks + 30 git workflow + 2 analysis + 4 project setup)
- **Project Structure:** Jitb.Employment.HarriCompare in src/, tests in tests/ directory
- **Solution Organization:** Main project under Processes folder, tests under Tests folder
- **Subtask Breakdown:** All complexity 5+ tasks broken into manageable 3-4 complexity subtasks
- **Git Workflow:** Build verification and branch management for each high-level task
- **Data Access:** Analysis tasks emphasize reusing existing methods over creating new ones
- **TDD Structure:** Each implementation task preceded by corresponding test task

**Phase 1 Success Criteria (Week 1-2):**
- Tasks 1 through 12 completed using TDD methodology (~180 active tasks)
- Jitb.Employment.HarriCompare console application functional
- Location-based comparison working end-to-end
- Basic status comparison (Harri ACTIVE = EIS Active + LOA) implemented
- CSV report generation with pipe delimiters
- Comprehensive unit test coverage for implemented features
- All unit tests passing (integrated within TDD implementation tasks)

**Phase 1 Completion Criteria (Week 2-3):**
- Integration testing completed with existing EIS/Harri systems (Task 13)
- Performance requirements validated for location-based comparison (Task 14)
- Documentation and deployment ready for simplified system (Task 15)
- Foundation established for future enhancement phases

**Quality Gates:**
- All unit tests passing with >85% code coverage for implemented features
- Integration tests successful with real Harri and EIS systems (location-based only)
- Performance tests meeting 45-minute SLA and 1GB memory limits
- Core PRD requirements validated through testing (simplified scope)
- Future enhancement roadmap documented and prioritized

**Future Phase Readiness:**
- Architecture supports extension to multi-mode comparisons
- Data access patterns established for adding tenant/all-data modes
- Comparison engine designed for LOA service integration
- Configuration framework ready for additional comparison criteria