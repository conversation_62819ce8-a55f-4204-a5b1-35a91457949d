# GitHub Copilot Coding Agent Setup Configuration
# Customizes the development environment for the Jitb.Employment project

name: "Jitb.Employment Development Environment"
description: "Setup configuration for .NET employment management system in WSL/Windows environment"

setup:
  # Environment Detection
  environment:
    type: "wsl-windows"
    os: "linux"
    host_os: "windows"
    
  # Required Tools and SDKs
  tools:
    - name: "dotnet"
      version: "6.0+"
      path: "/usr/bin/dotnet"
      fallback_path: "/mnt/c/Program Files/dotnet/dotnet.exe"
      
    - name: "msbuild"
      version: "17.0+"
      path: "/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe"
      alternatives:
        - "/mnt/c/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/MSBuild.exe"
        - "/mnt/c/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/MSBuild.exe"
        
    - name: "nuget"
      version: "6.0+"
      
    - name: "git"
      version: "2.30+"

  # Project Configuration
  project:
    type: ".NET Framework / .NET Core Hybrid"
    solution_file: "Jitb.Employment.sln"
    test_framework: "NUnit/xUnit"
    architecture: "Domain-Driven Design"
    
  # Build Configuration
  build:
    primary_command: '"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe" Jitb.Employment.sln'
    test_command: '"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe" Jitb.Employment.sln /p:Configuration=Debug'
    fallback_build: "dotnet build Jitb.Employment.sln"
    
  # Dependencies and Packages
  dependencies:
    - "NServiceBus"
    - "NHibernate"
    - "Afterman.nRepo"
    - "AutoFixture"
    - "Moq"
    - "FluentAssertions"
    
  # Development Guidelines
  conventions:
    - "Follow Domain-Driven Design patterns"
    - "Use existing Employee.cs and HarriInboundEmployee.cs entities"
    - "Update TaskList.md and ProjectPlan.md after task completion"
    - "Mark unit tests with task number traits/categories"
    - "Use branch naming: HarriCompareTask<number>"
    
  # Integration Systems
  integrations:
    - "UltiPro HR System"
    - "Lawson ERP"
    - "eRestaurant"
    - "Harri Platform"
    - "Active Directory"
    
  # File Paths and Structure
  paths:
    domain: "src/Jitb.Employment.Domain/"
    tests: "tests/"
    docs: ".documentation/"
    build_scripts: "build/"
    
  # Memory and Context Files
  context_files:
    - "CLAUDE.md"
    - ".documentation/Notes.md"
    - "ProjectPlan.md"
    
# Agent Behavior Configuration
agent:
  preferences:
    - "Prefer Windows MSBuild for .NET Framework projects"
    - "Use Linux dotnet CLI for .NET Core projects"
    - "Always quote Windows paths with spaces"
    - "Follow repository patterns with specialized query methods"
    - "Implement provider pattern for business services"
    
  restrictions:
    - "Do not use obsolete EmployeeLocation (use EmployeeLocation2)"
    - "Always wait for human inspection before continuing development"
    - "Mark completed work in solution"