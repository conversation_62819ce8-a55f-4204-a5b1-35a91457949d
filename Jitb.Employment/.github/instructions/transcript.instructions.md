---
mode: agent
---
## Entry Format

Each entry must use the following format:

```
### [YYYY-MM-DDTHH:MM:SSZ] <User or Model> <Brief summary of prompt>
> verbatim text. Do not summarize.
```

- **Always use current UTC timestamp in ISO 8601 format: `YYYY-MM-DDTHH:MM:SSZ`**  
  This ensures consistent, sortable timestamps across different systems and time zones.
- For `<User>`, use the user's name or login if available. If unavailable, ask for it at the beginning of the conversation.
- For `<Model>`, use the model's name (e.g., GPT-4.1, Claude Sonnet 4.0). If the model cannot detect its name, ask for it at the beginning of the conversation.
- `<Brief summary of prompt>` is only required if the subject changes from the previous entry.title: transcript.md Guidelines
description: Rules and best practices for maintaining the transcript.md file to ensure traceability, compliance, and project continuity.
related_to: senior-business-analyst-prompt
version: 1.1
created: 2025-06-19
author: <PERSON><PERSON><PERSON>
---

## Purpose

The `transcript.md` file is the **verbatim, chronological log** of all meaningful prompts and responses exchanged during requirements gathering, analysis, and documentation.  
It is the authoritative record for reconstructing the decision-making process and ensuring traceability to `memory.md` and the final PRD.

---

## Entry Format

Each entry must use the following format:

```
### [YYYY-MM-DD HH:MM:SS] <User or Model> <Brief summary of prompt>
> verbatim text. Do not summarize.
```

- **Always fetch the current date and time from the web.**  
  Do not use the LLM’s internal clock, as it may be unreliable.
- For `<User>`, use the user’s name or login if available. If unavailable, ask for it at the beginning of the conversation.
- For `<Model>`, use the model’s name (e.g., GPT-4.1, Claude Sonnet 4.0). If the model cannot detect its name, ask for it at the beginning of the conversation.
- `<Brief summary of prompt>` is only required if the subject changes from the previous entry.

---

## What to Include

- All clarifying questions, answers, and decisions relevant to requirements, design, or scope.
- Explicit confirmations, rejections, or changes to requirements.
- Rationale or context provided by the user or analyst.
- References to related entries in `memory.md` (where applicable).

---

## What *Not* to Include

- Do not log system or tool-generated messages unless they affect requirements or decisions.
- Do not include speculative or off-topic conversation.
- Do not duplicate content from `memory.md`—only log the interaction, not the persistent summary.
- Exclude trivial, non-informative, or administrative prompts (e.g., "continue", "ok", "thanks") unless they clarify or confirm requirements.

---

## Format Standards

- Use the structure in `transcript.template.md`.
- Each entry must include:
  - **Timestamp** (from the web, not the LLM)
  - **Speaker** (User or Model, with name)
  - **Verbatim content** of the prompt or response
  - **Brief summary** only if the subject changes

- Optionally, maintain an **index table** for quick navigation.

---

## Compliance Notes

- `transcript.md` is governed by `.github/instructions/Context.md` and `.github/guidelines/memory.guidelines.md`.
- Ensure all logged content is **traceable** to decisions and requirements in `memory.md` and the PRD.
- Regularly update the revision log at the end of the file.

---

## Final Reminders

- Treat `transcript.md` as the **single source of truth** for the requirements conversation.
- If in doubt, ask:  
  > “Will this exchange be needed to justify or reconstruct a requirement or decision later?”  
  If yes, include it.

---
