---
title: File Locations Instructions
description: Standard locations for instructions, templates, and documentation files in this project.
version: 1.0
created: 2025-06-19
author: <PERSON><PERSON><PERSON> Brett
---

## File Locations

- **Instructions:**  
  `.github/instructions`

- **Templates:**  
  `.github/templates`
  
- **Work Files and Document (eg Memory.md, Transcript.md, Tasks.md):**  
  `.github/workfolder`

- **Project documents (eg PRD, Design Docs):**  
  `.github/documentation`

- **Permanent System Documentation (eg Support Docs, System Structure - files that are not associated with a specific phase of a project):**  
  `/documentation` (The root of the workspace - create directory if it is needed and doesn't exist)
---

## Usage

All project-related instruction files must be stored in the `.github/instructions` directory.  
All template files must be stored in the `.github/templates` directory.  
All finalized Product Requirements Documents (PRDs) must be stored in the `.github/documentation` directory.

If a referenced file is not found in its standard location, confirm with the project owner before proceeding.

---

## Revision Log

| Date        | Author      | Summary of Changes         |
|-------------|-------------|---------------------------|
| 2025-06-19  | <PERSON><PERSON><PERSON> <PERSON> | Initial version           |