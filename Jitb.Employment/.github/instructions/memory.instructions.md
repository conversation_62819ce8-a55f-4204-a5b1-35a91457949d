---
mode: agent
---

## Meta Data
title: memory.md Instructions
description: Guidelines for maintaining the memory.md file to ensure project continuity, traceability, and PRD generation.
related_to: senior-business-analyst-prompt
version: 1.1
created: 2025-06-19
author: <PERSON><PERSON><PERSON>
---

## Purpose

The `memory.md` file serves as the **persistent project memory**. It contains all confirmed requirements, decisions, design changes, naming conventions, and known constraints. It must be comprehensive enough to:

1. **Continue a requirements gathering session** in a separate conversation or tool without reintroducing prior context.
2. **Generate a full PRD** (Product Requirements Document) based solely on its contents, even without access to the transcript or user prompts.

---

## Role and Responsibilities

As an AI agent or contributor, you are responsible for:

- Refreshing from `memory.md` at the start of each session.
- Appending new finalized decisions, rationale, and requirements.
- Maintaining accurate summaries of design shifts, technical choices, and project structure.
- Ensuring the traceability of all PRD content to entries in `memory.md`.

---

## When to Update `memory.md`

Update the file **after each confirmed or clarified**:

- Functional, business, UX, or technical requirement
- Stakeholder decision or constraint
- Architectural or naming convention change
- Confirmed scope, tool, or design adjustment
- Rationale behind major trade-offs or selected approaches

---

## What to Include

Each entry must be:

- **Categorized** under the appropriate section (e.g., Functional Requirements, Decisions, Authentication, Logging, etc.)
- **Timestamped** and status-tagged (e.g., Proposed, Confirmed, Deprecated)
- **Concise but complete**, with enough context to be understood independently
- **Traceable**, with reference to related transcript entries or decisions

Use the provided template format in `memory.template.md` for consistency.

---

## Example Requirement Entry

```md
### FR-002: Support guest checkout
- **Status:** Confirmed
- **Confirmed On:** 2025-06-18
- **Rationale:** Reduces cart abandonment rate for anonymous users
- **Linked Transcript Entries:** 45, 46
```

## Example Decision Entry

```md
### DEC-005: Use MongoDB for product catalog
- **Status:** Confirmed
- **Date:** 2025-06-17
- **Confirmed By:** User
- **Rationale:** Optimized for hierarchical and document-based queries
- **Impacted Areas:** CatalogService, Search API
```

---

## Format Standards

- Follow the structure in `memory.template.md`.
- Use markdown headers (`###`) for subsections and entries.
- Always maintain **chronological order** of additions within each section.
- Fill out the **Traceability Matrix** section before generating the PRD.

---

## What *Not* to Include

- Do not log full Q&A or conversation history — that belongs in `transcript.md`.
- Do not record speculative or unconfirmed ideas unless clearly labeled.
- Do not delete or overwrite past confirmed entries; use status tags like `Deprecated`.

---

## Compliance Notes

- `memory.md` is governed by `.github/instructions/Context.md` and `.github/guidelines/memory.guidelines.md`.
- Ensure each entry aligns with your current AI prompting schema and is **persisted between conversations**.
- The revision log at the end of `memory.md` should be updated regularly with each meaningful edit.

---

## Final Reminders

- Treat `memory.md` as the **authoritative source of project context**.
- If in doubt about whether something belongs here, ask:
  > “Will someone need this later to justify or reconstruct what was decided?”

If yes, then include it.

---
