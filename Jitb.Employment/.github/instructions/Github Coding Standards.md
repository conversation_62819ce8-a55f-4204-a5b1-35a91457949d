# AI Agent Instructions

## File References
Please take note of the following instruction files located in `/instructions`:

1. **Coding Standards**: Follow guidelines in `GitHub Coding Standards.md`
2. **Unit Testing**: Follow guidelines in `GitHub Unit Test Guidelines.md`
3. **Other Files**: Files in `/instructions` directory do not apply unless explicitly instructed to use them

**Important**: These instructions and guidelines MUST be followed unless the specific context clearly indicates they should be ignored (e.g., legacy code maintenance, emergency fixes, or explicit override instructions).

## Project-Specific Guidelines

### HarriCompare Project
- **Trigger**: If a GitHub issue contains the tag `$HarriCompare`
- **Action**: Start investigation in the `Jitb.Employment.HarriCompare` project within the solution
- **Documentation**: Additional project documentation available in `projects/ComparisonReport`

## GitHub Copilot - Coding Agent Mode
When working on Pull Requests in Coding Agent Mode:

### **CRITICAL: Follow TDD Methodology**
- **MUST follow Test-Driven Development (TDD) methodology** as specified in Section 1 of the Coding Standards
- **Red-Green-Refactor cycle**: Write failing tests FIRST, then implement code to make tests pass, then refactor
- **Iterative approach**: Complete one cycle at a time until the task is fully implemented
- **Output discipline**: Provide only code and tests relevant to the current task

### Standard Workflow Requirements
1. **TDD Implementation**: Apply TDD methodology from Coding Standards throughout development
2. **Build Verification**: Ensure the entire application builds successfully
3. **Test Verification**: Confirm all tests pass (including new TDD tests)
4. **Pre-Review Check**: Complete all verifications before submitting the PR for review
5. **Documentation**: Update relevant documentation if code changes affect existing functionality

## General Workflow
1. Read and understand the GitHub issue or task
2. Check for project-specific tags (e.g., `$HarriCompare`)
3. Locate relevant project context and documentation
4. **Apply TDD methodology** (Red-Green-Refactor cycle)
5. Apply coding standards and testing guidelines from instruction files
6. Implement solution following established patterns
7. Verify build and test success
8. Submit for review with appropriate documentation