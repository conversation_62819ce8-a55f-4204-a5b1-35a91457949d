﻿using AutoFixture;
using CreateTestData;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.Internal.Contracts.Commands.HarriOutbound;
using Moq;
using NServiceBus;
using Xunit;

namespace Jitb.Employment.HarriOutbound.Endpoint.Tests
{
    public class SyncHarriEmployeeHireMessageAssistantTests
    {
        private readonly Fixture _fixture = new Fixture();
        private readonly InitialLoadToHarriMessageAssistant _assistant = new InitialLoadToHarriMessageAssistant();

        [Fact]
        [Trait("Category", "Harri")]
        public void Usage_ReturnsTrueForInitialLoadToHarri()
        {
            // Arrange
            var message = _fixture.Create<InitialLoadToHarri>();

            // Act
            var result = _assistant.Usage(message);

            // Assert
            Assert.True(result);
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public void Usage_ReturnsFalseForOtherMessageTypes(IHiredAnEmployee message)
        {
            // Arrange

            // Act
            var result = _assistant.Usage(message);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "Harri")]
        public void InitialMessageBody_ReturnsCorrectString()
        {
            // Arrange
            var message = _fixture.Create<InitialLoadToHarri>();
            var employee = _fixture.Create<Employee>();

            // Act
            var result = _assistant.InitialMessageBody(message, employee);

            // Assert
            Assert.Contains($"Sending existing employee to Harri: {message.EmployeeId}", result);
            Assert.Contains($"Badge: {employee.BadgeId}", result);
            Assert.Contains($"{employee.FirstName} {employee.LastName}", result);
        }

        [Fact]
        [Trait("Category", "Harri")]
        public void IsFromValidEndpoint_AlwaysReturnsTrue()
        {
            // Arrange
            var context = Mock.Of<IMessageHandlerContext>();

            // Act
            var result = _assistant.IsFromValidEndpoint(context);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "Harri")]
        public void IsValidForHarriOutbound_AlwaysReturnsTrue()
        {
            // Arrange
            var homeLocation = _fixture.Create<int>();

            // Act
            var result = _assistant.IsValidForHarriOutbound(homeLocation);

            // Assert
            Assert.True(result);
        }
    }
}
