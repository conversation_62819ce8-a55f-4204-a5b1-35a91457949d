﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Afterman.nRepo" version="2024.9.5.5" targetFramework="net48" />
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net48" />
  <package id="Ardalis.GuardClauses" version="4.5.0" targetFramework="net48" />
  <package id="AutoFixture" version="4.18.1" targetFramework="net48" />
  <package id="AutoFixture.AutoMoq" version="4.18.1" targetFramework="net48" />
  <package id="AutoFixture.Xunit2" version="4.18.1" targetFramework="net48" />
  <package id="AutoMapper" version="8.1.1" targetFramework="net48" />
  <package id="BouncyCastle.OpenPGP" version="1.8.1" targetFramework="net48" />
  <package id="Castle.Core" version="5.1.1" targetFramework="net48" />
  <package id="Fare" version="2.1.1" targetFramework="net48" />
  <package id="FluentAssertions" version="6.12.0" targetFramework="net48" />
  <package id="FluentNHibernate" version="2.0.3.0" targetFramework="net48" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net48" />
  <package id="Jitb.CommonLibrary" version="2023.8.9.3" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Moq" version="4.20.72" targetFramework="net48" />
  <package id="MSTest.TestAdapter" version="2.2.10" targetFramework="net48" />
  <package id="MSTest.TestFramework" version="2.2.10" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="NHibernate" version="5.3.3" targetFramework="net48" />
  <package id="NLog" version="5.3.4" targetFramework="net48" />
  <package id="NServiceBus" version="7.0.1" targetFramework="net48" />
  <package id="NServiceBus.Testing" version="7.0.0" targetFramework="net48" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net48" />
  <package id="Remotion.Linq.EagerFetching" version="2.2.0" targetFramework="net48" />
  <package id="RestSharp" version="110.2.0" targetFramework="net48" />
  <package id="SSH.NET" version="2020.0.1" targetFramework="net48" />
  <package id="StructureMap" version="4.7.1" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.4" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="xunit" version="2.8.1" targetFramework="net48" />
  <package id="xunit.abstractions" version="2.0.3" targetFramework="net48" />
  <package id="xunit.analyzers" version="1.14.0" targetFramework="net48" developmentDependency="true" />
  <package id="xunit.assert" version="2.8.1" targetFramework="net48" />
  <package id="xunit.core" version="2.8.1" targetFramework="net48" />
  <package id="xunit.extensibility.core" version="2.8.1" targetFramework="net48" />
  <package id="xunit.extensibility.execution" version="2.8.1" targetFramework="net48" />
</packages>