﻿using AutoFixture.Xunit2;
using CreateTestData;
using HarriConcepts;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Nsb;
using Jitb.Employment.Domain.Providers.HarriProviders;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Handlers;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.Internal.Contracts.Commands.HarriOutbound;
using Moq;
using NLog;
using NServiceBus;
using NServiceBus.Testing;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriInbound.Endpoint.XUnit.Tests.Handlers
{
    public class StartLoaHandlerTests
    {
        [Theory]
        [InlineAutoMoqData]
        [Trait("Category", "Harri")]
        public async Task Handle_WhenMessageIsStartHarriLoa_ShouldProcessLoa(
            StartHarriLoa message,
            [Frozen] IEmployeeRepository employeeRepo,
            [Frozen] ILoaOutboundProvider loaOutboundProvider,
            [Frozen] ITenantByLocationProvider tenantProvider,
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            StartLoaHandler sut)
        {
            // Arrange
            var context = new TestableMessageHandlerContext();

            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(It.IsAny<int>(), It.IsAny<Guid>()))
                .ReturnsAsync(new HarriInboundEmployee { Status = "ACTIVE" });

            // Act
            await sut.Handle(message, context);

            // Assert
            Mock.Get(loaOutboundProvider).Verify(x =>
                x.LoaStart(It.IsAny<Guid>(), It.IsAny<Employee>()), Times.Once);
        }

        [Theory]
        [InlineAutoMoqData]
        [Trait("Category", "Harri")]
        public async Task Handle_WhenMessageIsIStartedALeaveOfAbsence_And_IsFromUltipro_ShouldProcessLoa(
            IStartedALeaveOfAbsence message,
            [Frozen] ILoaOutboundProvider loaOutboundProvider,
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            [Frozen] IContextChecker contextChecker,
            StartLoaHandler sut)
        {
            // Arrange
            Mock.Get(contextChecker).Setup(x => x.IsFromUltiPro(It.IsAny<IMessageHandlerContext>())).Returns(true);
            var context = new TestableMessageHandlerContext();
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(It.IsAny<int>(), It.IsAny<Guid>()))
                .ReturnsAsync(new HarriInboundEmployee { Status = "ACTIVE" });
            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsAHarriLocation(It.IsAny<int>())).Returns(true);
            // Act
            await sut.Handle(message, context);

            // Assert
            Mock.Get(loaOutboundProvider).Verify(x =>
                x.LoaStart(It.IsAny<Guid>(), It.IsAny<Employee>()), Times.Once);
        }

        [Theory]
        [InlineAutoMoqData]
        [Trait("Category", "Harri")]
        public async Task Handle_WhenEmployeeTerminatedInHarri_ShouldNotProcessLoa(
            StartHarriLoa message,
            [Frozen] IEmployeeRepository employeeRepo,
            [Frozen] ILoaOutboundProvider loaOutboundProvider,
            [Frozen] ITenantByLocationProvider tenantProvider,
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] ILogger logger,
            StartLoaHandler sut)
        {
            // Arrange
            var context = new TestableMessageHandlerContext();

            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(It.IsAny<int>(), It.IsAny<Guid>()))
                .ReturnsAsync(new HarriInboundEmployee { Status = "TERMINATED" });
            // Act
            await sut.Handle(message, context);
            // Assert
            Mock.Get(loaOutboundProvider).Verify(x =>
                x.LoaStart(It.IsAny<Guid>(), It.IsAny<Employee>()), Times.Never);
            Mock.Get(logger).Verify(x =>
                    x.Info(It.Is<string>(s =>
                        s.Contains("Skipping Harri StartLoa") &&
                        s.Contains("already terminated"))),
                Times.Once);
        }

        [Theory]
        [InlineAutoMoqData]
        [Trait("Category", "Harri")]
        public async Task Handle_WhenNotHarriLocation_ShouldNotProcessLoa(
            IStartedALeaveOfAbsence message,
            [Frozen] IEmployeeRepository employeeRepo,
            [Frozen] ICheckIfHarriLocationProvider harriLocationProvider,
            [Frozen] ILoaOutboundProvider loaOutboundProvider,
            [Frozen] ILogger logger,
            [Frozen] IMessageHandlerContext context,
            [Frozen] IContextChecker _contextChecker,
            StartLoaHandler sut)
        {
            // Arrange
            //    context.SafeGetMessageHeader = new TestableMessageHandlerContext());
            Mock.Get(_contextChecker).Setup(x => x.IsFromUltiPro(It.IsAny<IMessageHandlerContext>())).Returns(false);
            Mock.Get(harriLocationProvider)
                .Setup(x => x.IsAHarriLocation(It.IsAny<int>()))
                .Returns(false);
            // Act
            await sut.Handle(message, context);
            // Assert
            Mock.Get(employeeRepo).Verify(x => x.Get(message.EmployeeId), Times.Once);
            Mock.Get(loaOutboundProvider).Verify(x =>
                x.LoaStart(It.IsAny<Guid>(), It.IsAny<Employee>()), Times.Never);
            Mock.Get(logger).Verify(x =>
                    x.Debug(It.Is<string>(s =>
                        s.Contains("Employee StartLoa - Skipping StartLoa for employee") &&
                        s.Contains($"because the message did not originate in Ultipro"))),
                Times.Once);
        }


        [Theory]
        [InlineAutoMoqData]
        [Trait("Category", "Harri")]
        public async Task Handle_WhenNotHarriLocation_ShouldNotProcessLo2a(
            IStartedALeaveOfAbsence message,
            [Frozen] IEmployeeRepository employeeRepo,
            [Frozen] ICheckIfHarriLocationProvider harriLocationProvider,
            [Frozen] ILoaOutboundProvider loaOutboundProvider,
            [Frozen] ILogger logger,
            [Frozen] IMessageHandlerContext context,
            [Frozen] IContextChecker _contextChecker,
            StartLoaHandler sut)
        {
            // Arrange
            //    context.SafeGetMessageHeader = new TestableMessageHandlerContext());
            Mock.Get(_contextChecker).Setup(x => x.IsFromUltiPro(It.IsAny<IMessageHandlerContext>())).Returns(true);
            Mock.Get(harriLocationProvider)
                .Setup(x => x.IsAHarriLocation(It.IsAny<int>()))
                .Returns(false);
            // Act
            await sut.Handle(message, context);
            // Assert
            Mock.Get(loaOutboundProvider).Verify(x =>
                x.LoaStart(It.IsAny<Guid>(), It.IsAny<Employee>()), Times.Never);
            Mock.Get(logger).Verify(x =>
                    x.Info(It.Is<string>(s =>
                        s.Contains("Skipping StartLoa") &&
                        s.Contains("not a Harri location"))),
                Times.Once);
        }
    }
}