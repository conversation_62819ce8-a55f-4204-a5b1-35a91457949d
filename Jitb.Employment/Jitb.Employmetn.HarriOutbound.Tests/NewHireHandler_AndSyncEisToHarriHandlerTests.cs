﻿using Ardalis.GuardClauses;
using AutoFixture;
using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Providers.HarriProviders;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Exceptions;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Handlers;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.Internal.Contracts.Commands.HarriOutbound;
using Moq;
using Newtonsoft.Json;
using NServiceBus;
using NServiceBus.Testing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriOutbound.Tests
{
    public class NewHireHandler_AndSyncEisToHarriHandlerTests
    {


        private static RestResponse GenerateEmployeeJson()
        {
            var harriEmployee = new HarriInboundEmployee()
            {
                Id = "12345",
                FirstName = "Simeon"
            };

            var response = new RestResponse
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ResponseStatus = ResponseStatus.Completed,
                Content = JsonConvert.SerializeObject(harriEmployee)

            };
            return response;
        }


        [Theory, AutoMoqData]
        [Trait("Category", "Harri")]
        public async Task Handle_WhenEmployeeNotFound_ThrowsException(
            [Frozen] IEmployeeRepository employeeRepository,
            NewHireHandler sut,
            IHiredAnEmployee message,
            IMessageHandlerContext context)
        {
            // Arrange

            Mock.Get(employeeRepository).Setup(r => r.Get(message.EmployeeId)).Returns((Employee)null);

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() => sut.Handle(message, context));
        }

        [Theory, AutoMoqData]
        [Trait("Category", "Harri")]
        public async Task Handle_WhenNotFromValidEndpoint_ReturnsEarly(
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] List<IEmployeeHireMessageAssistant> messageAssistantList,
            IEmployeeHireMessageAssistant assistant1,
            IEmployeeHireMessageAssistant assistant2,
            IEmployeeHireMessageAssistant assistant3,
            IHiredAnEmployee message,
            NewHireHandler sut,
            Fixture fixture
        )
        {
            // Arrange
            messageAssistantList[0] = assistant1;
            messageAssistantList[1] = assistant2;
            messageAssistantList[2] = assistant3;
            Mock.Get(assistant1).Setup(x => x.Usage(message)).Returns(true);
            Mock.Get(assistant2).Setup(x => x.Usage(message)).Returns(false);
            Mock.Get(assistant3).Setup(x => x.Usage(message)).Returns(false);
            var context = new TestableMessageHandlerContext();
            //       Mock.Get(messageAssistantList).Setup(x => x[0].Usage(message)).Returns(true);

            //   var context = Mock.Of<IMessageHandlerContext>();
            //   var employee = _fixture.Create<Employee>();
            fixture.Customize<Employee>(composer => composer
                .With(x => x.CurrentStatus, "01"));
            var employee = fixture.Create<Employee>();


            Mock.Get(employeeRepository).Setup(r => r.Get(message.EmployeeId)).Returns(employee);

            // Act
            await sut.Handle(message, context);

            // Assert
            Mock.Get(assistant1).Verify(x => x.IsFromValidEndpoint(context), Times.Once());
        }


        //Harri
        [Theory, AutoMoqData]

        public async Task Handle_WhenEmployeeIsNotInHarri_AddToHarri(
            [Frozen] List<IEmployeeHireMessageAssistant> messageAssistantList,
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            [Frozen] IHireEmployeeOutboundProvider hireEmployeeOutboundProvider,
            [Frozen] IGetHarriEmployeeProvider getHarriEmployee,
            [Frozen] Employee employee,

            NewHireHandler sut,

            InitialLoadToHarri message
 )
        {

            var context = new TestableMessageHandlerContext();

            Mock.Get(messageAssistantList[0]).Setup(x => x.Usage(message)).Returns(true);
            Mock.Get(messageAssistantList[1]).Setup(x => x.Usage(message)).Returns(false);
            Mock.Get(messageAssistantList[2]).Setup(x => x.Usage(message)).Returns(false);


            Mock.Get(messageAssistantList[0]).Setup(x => x.IsFromValidEndpoint(It.IsAny<IMessageHandlerContext>())).Returns(true);
            Mock.Get(messageAssistantList[0]).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(true);

            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsAHarriLocation(It.IsAny<int>())).Returns(true);
            Mock.Get(getHarriEmployee).Setup(x => x.GetHarriEmployee(employee.BadgeId, It.IsAny<Guid>()))
                .ReturnsAsync((HarriInboundEmployee)null);


            employee.CurrentStatus = "01";

            // Act
            await sut.Handle(message, context);

            // Assert
            Mock.Get(hireEmployeeOutboundProvider)
                .Verify(x => x.HireEmployee(It.IsAny<Guid>(), message, employee), Times.Once);
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public async Task NewHireHandler_WhenNotAHarriLocation_LeaveEarly(
            [Frozen] List<IEmployeeHireMessageAssistant> messageAssistantList,
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            IEmployeeHireMessageAssistant newHireAssistant,
            [Frozen] NLog.ILogger log,
            NewHireHandler sut,
            IHiredAnEmployee message
        )
        {
            //Arrange
            var context = new TestableMessageHandlerContext();

            messageAssistantList[0] = newHireAssistant;
            Mock.Get(newHireAssistant).Setup(x => x.Usage(message)).Returns(true);
            Mock.Get(newHireAssistant).Setup(x => x.IsFromValidEndpoint(context)).Returns(true);

            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsAHarriLocation(It.IsAny<int>())).Returns(false);

            //Act
            await sut.Handle(message, context);

            //Assert
            Mock.Get(log).Verify(x => x.Trace("Not a Harri Location"), Times.Once());
            Mock.Get(log).Verify(x => x.Trace("HireEmployee"), Times.Never());
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public async Task NewHireHandler_WhenTenantNotFound_ThrowException(
            [Frozen] List<IEmployeeHireMessageAssistant> messageAssistantList,
            IEmployeeHireMessageAssistant newHireAssistant,
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            [Frozen] ITenantByLocationProvider tenantByLocationProvider,
            [Frozen] NLog.ILogger log,
            NewHireHandler sut,
            IHiredAnEmployee message
        )
        {
            //Arrange
            var context = new TestableMessageHandlerContext();
            context.Headers.Add("SourceSystem", "Ultipro");

            messageAssistantList[0] = newHireAssistant;
            Mock.Get(newHireAssistant).Setup(x => x.Usage(message)).Returns(true);
            Mock.Get(newHireAssistant).Setup(x => x.IsFromValidEndpoint(context)).Returns(true);
            Mock.Get(newHireAssistant).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(true);

            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsAHarriLocation(It.IsAny<int>())).Returns(true);
            Mock.Get(tenantByLocationProvider).Setup(x => x.GetTenantIdByLocation(It.IsAny<int>()))
                .Returns(Task.FromResult((Guid?)null));


            //Act
            Func<Task> act = () => sut.Handle(message, context);

            //Assert
            await act.Should().ThrowAsync<TenantNotFoundForLocationException>()
                .WithMessage("Harri Tenant not found for location 0");

        }

        [Theory]
        [InlineAutoMoqData(1, "NickName")]
        [InlineAutoMoqData(20, "FirstName")]
        [Trait("Category", "Harri")]
        public async Task EmploymentHarriOutboundEndpoint_UseKnownAsInsteadOfFirstNameForCorporateEmployees
        (int companyId, string expectedFirstName,
            [Frozen] IHiredAnEmployee message,
            [Frozen] Employee employee,
            HireEmployeeOutboundProvider sut)
        {
            message.EmployeeId = employee.EmployeeId;
            employee.CompanyId = companyId;
            employee.FirstName = "FirstName";
            employee.NickName = "NickName";


            var methodInfo = typeof(HireEmployeeOutboundProvider).GetMethod("CreateNewHirePayload", BindingFlags.NonPublic | BindingFlags.Instance);

            // Act
            //    var result = _handler.CreateNewHirePayload(message, employee);
            var result = (HarriNewHirePayload)methodInfo.Invoke(sut, new object[] { message, employee });

            //   var result = sut.CreateNewHirePayload(message, employee);
            result.FirstName.Should().Be(expectedFirstName);
            result.KnownAs.Should().Be(employee.NickName);
        }


        [Theory]
        [InlineAutoMoqData(1, true)]
        [InlineAutoMoqData(20, false)]
        [Trait("Category", "Harri")]
        public async Task EmploymentHarriOutboundEndpoint_OnlySendPayrollIdForCorporateEmployees
        (int companyId, bool expectAPayrollId,
            [Frozen] IHiredAnEmployee message,
            [Frozen] Employee employee,
            HireEmployeeOutboundProvider sut)
        {
            message.EmployeeId = employee.EmployeeId;
            employee.CompanyId = companyId;
            employee.HREmployeeId = 1234567;


            var methodInfo = typeof(HireEmployeeOutboundProvider).GetMethod("CreateNewHirePayload", BindingFlags.NonPublic | BindingFlags.Instance);
            // Act
            //    var result = _handler.CreateNewHirePayload(message, employee);
            var result = (HarriNewHirePayload)methodInfo.Invoke(sut, new object[] { message, employee });

            //        var result = sut.CreateNewHirePayload(message, employee);
            if (expectAPayrollId)
                result.PayrollId.Should().Be("01234567");
            else
                result.PayrollId.Should().BeNull();
        }
    }
}



