﻿using AutoFixture;
using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using Xunit;

namespace Jitb.Employment.HarriOutbound.Tests
{

    [ExcludeFromCodeCoverage]
    public class SyncEmployeeTests
    {
        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public void SyncHarriProvider_WhenAllFieldsChanged_AllChangedFieldsAreInThePayload(
            [Frozen] SyncHarriProvider sut, [Frozen] Employee employee,
            [Frozen] HarriInboundEmployee harriEmployee, Fixture fixture)
        {
            employee.BirthDate = employee.BirthDate.Value.Date;
            employee.HomePhoneNumber = "************";
            employee.CellPhoneNumber = "************";
            harriEmployee.Phone = "************";
            harriEmployee.HomePhone = "************";
            var privateObject = new PrivateObject(sut);
            var parameters = new object[2];
            parameters[0] = employee;
            parameters[1] = harriEmployee;
            var result = privateObject.Invoke("SyncEmployeeInfo", parameters);

            var payloadSerialized = (string)result;
            var payload = JsonConvert.DeserializeObject<HarriUpdateEmployeePayload>(payloadSerialized);

            payload.Address.Should().NotBeNull();
            payload.Address.AddressLine1.Should().Be(employee.Address1);
            payload.Address.AddressLine2.Should().Be(employee.Address2);
            payload.Address.City.Should().Be(employee.City);
            payload.Address.StateCode.Should().Be(employee.StateCode);
            payload.Address.PostalCode.Should().Be(employee.ZipCode);

            payload.FirstName.Should().Be(employee.FirstName);
            payload.LastName.Should().Be(employee.LastName);
            payload.MiddleName.Should().Be(employee.MiddleName);

            payload.Email.Should().Be(employee.EmailAddress);
            payload.BirthDate.Should().Be(employee.BirthDate);
            payload.Ssn.Should().Be(employee.Ssn);
            payload.Phone.Should().Be(employee.CellPhoneNumber ?? employee.HomePhoneNumber);
            payload.HomePhone.Should().Be(employee.HomePhoneNumber);
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public void SyncHarriProvider_WhenNothingChanged_PayloadIsEmpty(
            [Frozen] SyncHarriProvider sut, [Frozen] Employee employee,
            [Frozen] HarriInboundEmployee harriEmployee, Fixture fixture)
        {
            employee.BirthDate = employee.BirthDate.Value.Date;
            harriEmployee.Address.AddressLine1 = employee.Address1;
            harriEmployee.Address.AddressLine2 = employee.Address2;
            harriEmployee.Address.City = employee.City;
            harriEmployee.Address.StateCode = employee.StateCode;
            harriEmployee.Address.PostalCode = employee.ZipCode;

            harriEmployee.FirstName = employee.FirstName;
            harriEmployee.LastName = employee.LastName;
            harriEmployee.MiddleName = employee.MiddleName;

            harriEmployee.Email = employee.EmailAddress;
            harriEmployee.BirthDate = employee.BirthDate ?? DateTime.Today;
            harriEmployee.Ssn = employee.Ssn;
            harriEmployee.Phone = employee.CellPhoneNumber ?? employee.HomePhoneNumber;
            harriEmployee.HomePhone = employee.HomePhoneNumber;

            var privateObject = new PrivateObject(sut);
            var parameters = new object[2];
            parameters[0] = employee;
            parameters[1] = harriEmployee;
            var result = privateObject.Invoke("SyncEmployeeInfo", parameters);

            var payloadSerialized = (string)result;
            var payload = JsonConvert.DeserializeObject<HarriUpdateEmployeePayload>(payloadSerialized);

            payload.Address.Should().BeNull();
            payload.FirstName.Should().BeNullOrEmpty();
            payload.LastName.Should().BeNullOrEmpty();
            payload.MiddleName.Should().BeNullOrEmpty();

            payload.Email.Should().BeNullOrEmpty();
            payload.BirthDate.Should().BeNull();
            payload.Ssn.Should().BeNullOrEmpty();
            payload.Phone.Should().BeNullOrEmpty();
            payload.HomePhone.Should().BeNullOrEmpty();
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public void SyncHarriProvider_WhenOneFieldChanges_ThePayloadContainsOnlyTheChangedField(
    [Frozen] SyncHarriProvider sut, [Frozen] Employee employee,
    [Frozen] HarriInboundEmployee harriEmployee, Fixture fixture)
        {
            employee.BirthDate = employee.BirthDate.Value.Date;
            harriEmployee.Address.AddressLine1 = employee.Address1;
            harriEmployee.Address.AddressLine2 = employee.Address2;
            harriEmployee.Address.City = employee.City;
            harriEmployee.Address.StateCode = employee.StateCode;
            harriEmployee.Address.PostalCode = employee.ZipCode;

            harriEmployee.FirstName = employee.FirstName;
            //     harriEmployee.LastName = employee.LastName;   // Last Name Changed
            harriEmployee.MiddleName = employee.MiddleName;

            harriEmployee.Email = employee.EmailAddress;
            harriEmployee.BirthDate = employee.BirthDate ?? DateTime.Today;
            harriEmployee.Ssn = employee.Ssn;
            harriEmployee.Phone = employee.CellPhoneNumber ?? employee.HomePhoneNumber;
            harriEmployee.HomePhone = employee.HomePhoneNumber;

            var privateObject = new PrivateObject(sut);
            var parameters = new object[2];
            parameters[0] = employee;
            parameters[1] = harriEmployee;
            var result = privateObject.Invoke("SyncEmployeeInfo", parameters);

            var payloadSerialized = (string)result;
            var payload = JsonConvert.DeserializeObject<HarriUpdateEmployeePayload>(payloadSerialized);

            payload.LastName.Should().Be(employee.LastName);


            payload.Address.Should().BeNull();
            payload.FirstName.Should().BeNullOrEmpty();

            payload.MiddleName.Should().BeNullOrEmpty();

            payload.Email.Should().BeNullOrEmpty();
            payload.BirthDate.Should().BeNull();
            payload.Ssn.Should().BeNullOrEmpty();
            payload.Phone.Should().BeNullOrEmpty();
            payload.HomePhone.Should().BeNullOrEmpty();
        }

        [Theory]
        [InlineAutoMoqData(100, 200)]
        [Trait("Category", "Harri")]
        public void SyncHarriProvider_SyncLocations_HomeLocationChanges(
            int eisHomeLocation, int harriHomeLocation,
[Frozen] SyncHarriProvider sut, [Frozen] Employee employee,
[Frozen] HarriInboundEmployee harriEmployee, Fixture fixture)
        {
            employee.InsertHomeLocation(new Location { LocationNumber = eisHomeLocation }, DateTime.Today.AddDays(-10));
            harriEmployee.Locations.Add(new HarriLocation { Id = harriHomeLocation, IsPrimary = true, IsActive = true });

            var privateObject = new PrivateObject(sut);
            var parameters = new object[2];
            parameters[0] = employee;
            parameters[1] = harriEmployee;
            var result = privateObject.Invoke("SyncLocations", parameters);

            var payloadSerialized = (((int homeLocation, string payload) transfer, (IEnumerable<int> adds, IEnumerable<int> drops)))result;
            var transferPayload = JsonConvert.DeserializeObject<HarriAttachLocationPayload>(payloadSerialized.transfer.payload);

            payloadSerialized.transfer.homeLocation.Should().Be(eisHomeLocation);
            transferPayload.IsPrimary.Should().BeTrue();
            transferPayload.KeepSourceLocation.Should().BeFalse();
        }

        [Theory]
        [Trait("Category", "Harri")]
        [InlineAutoMoqData(100, 100)]
        public void SyncHarriProvider_SyncLocations_HomeLocationChanges_WhenHomeLocationsAreEqual_NoPayloadIsProduced(
            int eisHomeLocation, int harriHomeLocation,
            [Frozen] SyncHarriProvider sut, [Frozen] Employee employee,
            [Frozen] HarriInboundEmployee harriEmployee, Fixture fixture)
        {
            employee.InsertHomeLocation(new Location { LocationNumber = eisHomeLocation }, DateTime.Today.AddDays(-10));
            harriEmployee.Locations.Add(new HarriLocation { Id = harriHomeLocation, IsPrimary = true, IsActive = true });

            var privateObject = new PrivateObject(sut);
            var parameters = new object[2];
            parameters[0] = employee;
            parameters[1] = harriEmployee;
            var result = privateObject.Invoke("SyncLocations", parameters);

            var payloadSerialized = (((int homeLocation, string payload) transfer, (IEnumerable<int> adds, IEnumerable<int> drops)))result;
            var transferPayload = JsonConvert.DeserializeObject<HarriAttachLocationPayload>(payloadSerialized.transfer.payload);

            payloadSerialized.transfer.homeLocation.Should().Be(eisHomeLocation);
            transferPayload.Should().BeNull();
        }

        [Theory]
        [Trait("Category", "Harri")]
        [InlineAutoMoqData(100, 200, "1,2,3,6", "2,4,5,6,7")]
        public void SyncHarriProvider_SyncLocations_HomeLocationChanges_ValidateChangedHomeLocationsAndMismatchedSharedLocations(
            int eisHomeLocation, int harriHomeLocation,
            string eisSharedLocations, string harriSharedLocations,
            [Frozen] SyncHarriProvider sut, [Frozen] Employee employee,
            [Frozen] HarriInboundEmployee harriEmployee, Fixture fixture)
        {
            employee.InsertHomeLocation(new Location { LocationNumber = eisHomeLocation }, DateTime.Today.AddDays(-10));
            var eisShared = eisSharedLocations.Split(',');
            foreach (var l in eisShared)
            {
                employee.BorrowTo(new Location { LocationNumber = int.Parse(l) }, DateTime.Today.AddDays(-10));
            }
            harriEmployee.Locations.Add(new HarriLocation { Id = harriHomeLocation, IsPrimary = true, IsActive = true });
            var harriShared = harriSharedLocations.Split(',');
            foreach (var l in harriShared)
            {
                harriEmployee.Locations.Add(new HarriLocation { Id = int.Parse(l), IsPrimary = false, IsActive = true });
            }
            var privateObject = new PrivateObject(sut);
            var parameters = new object[2];
            parameters[0] = employee;
            parameters[1] = harriEmployee;
            var result = privateObject.Invoke("SyncLocations", parameters);

            var payloadSerialized = (((int homeLocation, string payload) transfer, (IEnumerable<int> adds, IEnumerable<int> drops) sharedChanges))result;
            var transferPayload = JsonConvert.DeserializeObject<HarriAttachLocationPayload>(payloadSerialized.transfer.payload);

            payloadSerialized.transfer.homeLocation.Should().Be(eisHomeLocation);
            payloadSerialized.sharedChanges.adds.Any(x => x == 1).Should().BeTrue();
            payloadSerialized.sharedChanges.adds.Any(x => x == 2).Should().BeFalse();
            payloadSerialized.sharedChanges.adds.Any(x => x == 3).Should().BeTrue();
            payloadSerialized.sharedChanges.adds.Any(x => x == 4).Should().BeFalse();
            payloadSerialized.sharedChanges.adds.Any(x => x == 5).Should().BeFalse();
            payloadSerialized.sharedChanges.adds.Any(x => x == 6).Should().BeFalse();
            payloadSerialized.sharedChanges.adds.Any(x => x == 7).Should().BeFalse();

            payloadSerialized.sharedChanges.drops.Any(x => x == 1).Should().BeFalse();
            payloadSerialized.sharedChanges.drops.Any(x => x == 2).Should().BeFalse();
            payloadSerialized.sharedChanges.drops.Any(x => x == 3).Should().BeFalse();
            payloadSerialized.sharedChanges.drops.Any(x => x == 4).Should().BeTrue();
            payloadSerialized.sharedChanges.drops.Any(x => x == 5).Should().BeTrue();
            payloadSerialized.sharedChanges.drops.Any(x => x == 6).Should().BeFalse();
            payloadSerialized.sharedChanges.drops.Any(x => x == 7).Should().BeTrue();
        }
    }
}