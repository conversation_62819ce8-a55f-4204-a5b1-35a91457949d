using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Providers.HarriProviders;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Handlers;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Moq;
using NServiceBus.Testing;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using Xunit;

public class NewHireHandlerTests
{




    [Theory]
    [AutoMoqData]
    [Trait("Category", "Harri")]
    public void CreateNewHirePayload_ShouldReturnHourlyPayType_WhenSalaryClassIsS_AndJobcodeIsInstoreAndIsNotExempt(
        HireEmployeeOutboundProvider _hireEmployeeOutboundProvider
    )
    {
        // Arrange
        var employee = new Employee() { SalaryClass = "S", LastName = "Smith", FirstName = "John" };
        employee.InsertJobCode(new JobCode
        {
            IsInStore = true,
            IsSalary = false
        },
            DateTime.Today.AddDays(01));

        var message = new Mock<IHiredAnEmployee>().Object;
        var methodInfo = typeof(HireEmployeeOutboundProvider).GetMethod("CreateNewHirePayload", BindingFlags.NonPublic | BindingFlags.Instance);

        // Act
        var result = (HarriNewHirePayload)methodInfo.Invoke(_hireEmployeeOutboundProvider, new object[] { message, employee });

        // Assert
        Assert.Equal("HOURLY", result.PayType.Type);
    }

    [Theory]
    [AutoMoqData]
    [Trait("Category", "Harri")]
    public void CreateNewHirePayload_ShouldReturnSalaryPayType_WhenSalaryClassIsH_AndJobcodeIsNotInstoreAndIsNotExempt(
        HireEmployeeOutboundProvider _hireEmployeeOutboundProvider
    )
    {
        // Arrange
        var employee = new Employee() { SalaryClass = "H", LastName = "Smith", FirstName = "John" };
        employee.InsertJobCode(new JobCode
        {
            IsInStore = false,
            IsSalary = true
        },
            DateTime.Today.AddDays(01));

        var message = new Mock<IHiredAnEmployee>().Object;
        var methodInfo = typeof(HireEmployeeOutboundProvider).GetMethod("CreateNewHirePayload", BindingFlags.NonPublic | BindingFlags.Instance);


        // Act
        //    var result = _handler.CreateNewHirePayload(message, employee);
        var result = (HarriNewHirePayload)methodInfo.Invoke(_hireEmployeeOutboundProvider, new object[] { message, employee });


        // Assert
        Assert.Equal("SALARIED", result.PayType.Type);
    }




    [Theory]
    [AutoMoqData]
    [Trait("Category", "Harri")]
    public async Task Handle_ShouldProcessActiveEmployee(
         Employee employee
        , [Frozen] IHiredAnEmployee message
        , [Frozen] List<IEmployeeHireMessageAssistant> messageAssistantList
        , [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider
        , [Frozen] IHireEmployeeOutboundProvider hireEmployeeOutboundProvider
        , [Frozen] ILoaOutboundProvider loaOutboundProvider
        , [Frozen] IGetHarriEmployeeProvider getHarriEmployeeProvider

        , NewHireHandler sut

    )
    {
        // Arrange
        var context = new TestableMessageHandlerContext();

        employee.CurrentStatus = "01";
        var employeeId = employee.EmployeeId;



        Mock.Get(messageAssistantList[0]).Setup(x => x.Usage(message)).Returns(true);
        Mock.Get(messageAssistantList[1]).Setup(x => x.Usage(message)).Returns(false);
        Mock.Get(messageAssistantList[2]).Setup(x => x.Usage(message)).Returns(false);

        Mock.Get(messageAssistantList[0]).Setup(x => x.IsFromValidEndpoint(context)).Returns(true);
        Mock.Get(messageAssistantList[0]).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(true);

        Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsAHarriLocation(It.IsAny<int>())).Returns(true);
        Mock.Get(getHarriEmployeeProvider).Setup(x => x.GetHarriEmployee(It.IsAny<int>(), It.IsAny<Guid>())).ReturnsAsync((HarriInboundEmployee)null);


        // Act
        await sut.Handle(message, context);

        // Assert
        Mock.Get(hireEmployeeOutboundProvider).Verify(x => x.HireEmployee(It.IsAny<Guid>(), It.IsAny<IHiredAnEmployee>(), It.IsAny<Employee>()), Times.Once);
        Mock.Get(loaOutboundProvider).Verify(x => x.LoaStart(It.IsAny<Guid>(), employee), Times.Never);

    }

    [Theory]
    [AutoMoqData]
    [Trait("Category", "Harri")]
    public async Task Handle_ShouldProcessEmployeeOnLOA(
          [Frozen] Employee employee
        , [Frozen] IHiredAnEmployee message
        , [Frozen] List<IEmployeeHireMessageAssistant> messageAssistantList
        , [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider
        , [Frozen] IHireEmployeeOutboundProvider hireEmployeeOutboundProvider
        , [Frozen] ILoaOutboundProvider loaOutboundProvider
        , [Frozen] IGetHarriEmployeeProvider getHarriEmployeeProvider

        , NewHireHandler sut

        )
    {
        // Arrange
        var context = new TestableMessageHandlerContext();
        employee.CurrentStatus = "03";

        var employeeId = employee.EmployeeId;



        Mock.Get(messageAssistantList[0]).Setup(x => x.Usage(message)).Returns(true);
        Mock.Get(messageAssistantList[1]).Setup(x => x.Usage(message)).Returns(false);
        Mock.Get(messageAssistantList[2]).Setup(x => x.Usage(message)).Returns(false);

        Mock.Get(messageAssistantList[0]).Setup(x => x.IsFromValidEndpoint(context)).Returns(true);
        Mock.Get(messageAssistantList[0]).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(true);

        Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsAHarriLocation(It.IsAny<int>())).Returns(true);
        Mock.Get(getHarriEmployeeProvider).Setup(x => x.GetHarriEmployee(It.IsAny<int>(), It.IsAny<Guid>())).ReturnsAsync((HarriInboundEmployee)null);


        // Act
        await sut.Handle(message, context);

        // Assert
        Mock.Get(hireEmployeeOutboundProvider).Verify(x => x.HireEmployee(It.IsAny<Guid>(), It.IsAny<IHiredAnEmployee>(), It.IsAny<Employee>()), Times.Once);
        context.SentMessages.Length.Should().Be(1);

    }


}
