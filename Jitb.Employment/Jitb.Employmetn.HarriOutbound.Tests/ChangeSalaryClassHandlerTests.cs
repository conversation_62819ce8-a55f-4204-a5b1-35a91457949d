﻿using AutoFixture.Xunit2;
using CreateTestData;
using HarriConcepts;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Providers.HarriProviders;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Handlers;
using Moq;
using NLog;
using NServiceBus.Testing;
using RestSharp;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriOutbound.Tests
{
    public class ChangeSalaryClassHandlerTests
    {



        [Theory]
        [InlineAutoMoqData("S", "HOURLY", 0, 1, "Changing from Hourly to Salaried")]
        [InlineAutoMoqData("H", "SALARIED", 1, 0, "Changing from Salaried to Hourly")]
        [InlineAutoMoqData("H", "HOURLY", 0, 0, "Hourly - No Change")]
        [InlineAutoMoqData("S", "SALARIED", 0, 0, "Salaried - No Change")]
        [Trait("Category", "Harri")]
        public async Task ChangeSalaryClassHandler_ValidateNewSalaryClassIsCorrect(
            string messageSalaryClass, string harriSalaryClass, int changeToHourlyCount, int changeToSalaryCount, string testMessage,
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            [Frozen] ITenantByLocationProvider tenantByLocationProvider,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            [Frozen] ILogger log,
            [Frozen] HarriInboundEmployee harriEmployee,
            [Frozen] Employee employee,
            ChangedSalaryClassHandler sut,
            IChangedSalaryClass message)
        {
            //Arrange
            var context = new TestableMessageHandlerContext();
            context.MessageHeaders.Add("SourceSystem", "Ultipro");
            var tenantId = Guid.NewGuid();

            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(true);
            Mock.Get(tenantByLocationProvider).Setup(x => x.GetTenantIdByLocation(It.IsAny<int>())).Returns(Task.FromResult((Guid?)tenantId));
            Mock.Get(callHarriWebServiceProvider).Setup(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), "V1"))
                .ReturnsAsync(new RestResponse { StatusCode = System.Net.HttpStatusCode.OK });

            harriEmployee.PayType = new PayType { Type = harriSalaryClass };
            employee.SalaryClass = messageSalaryClass;

            message.NewSalaryClass = messageSalaryClass;

            //Act
            await sut.Handle(message, context);

            //Assert
            Mock.Get(log).Verify(x => x.Trace("Changing Salary Class to Hourly"), Times.Exactly(changeToHourlyCount));
            Mock.Get(log).Verify(x => x.Trace("Changing Salary Class to Salaried"), Times.Exactly(changeToSalaryCount));
        }
    }
}
