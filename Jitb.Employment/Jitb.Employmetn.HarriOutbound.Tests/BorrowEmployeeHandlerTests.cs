﻿using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Domain.Providers.HarriProviders;
using Jitb.Employment.HarriCaller.Domain.Exceptions;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Handlers;
using Moq;
using NLog;
using NServiceBus.Testing;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriOutbound.Tests
{
    public class BorrowEmployeeHandlerTests
    {


        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public void BorrowEmployee_WhenNotAHarriLocation_LeaveEarly(
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            [Frozen] ILogger log,
            BorrowEmployeeHandler sut,
            IBorrowedAnEmployee message
        )
        {
            //Arrange
            var context = new TestableMessageHandlerContext();
            context.MessageHeaders.Add("SourceSystem", "Ultipro");

            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(false);

            //Act
            sut.Handle(message, context).GetAwaiter().GetResult();

            //Assert
            Mock.Get(log).Verify(x => x.Trace("Not a Harri Location"), Times.Once());
            Mock.Get(log).Verify(x => x.Trace("Send Borrow to Harri"), Times.Never());
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public async Task BorrowEmployeeHandler_WhenTenantNotFound_ThrowException(
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            [Frozen] ITenantByLocationProvider tenantByLocationProvider,
            [Frozen] ILogger log,
            BorrowEmployeeHandler sut,
            IBorrowedAnEmployee message)
        {
            //Arrange
            var context = new TestableMessageHandlerContext();
            context.MessageHeaders.Add("SourceSystem", "Ultipro");

            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(true);
            Mock.Get(tenantByLocationProvider).Setup(x => x.GetTenantIdByLocation(It.IsAny<int>())).Returns(Task.FromResult((Guid?)null));

            //Act
            Func<Task> act = () => sut.Handle(message, context);

            //Assert
            await act.Should().ThrowAsync<TenantNotFoundForLocationException>()
               .WithMessage("Harri Tenant not found for location 0");

        }

    }
}

