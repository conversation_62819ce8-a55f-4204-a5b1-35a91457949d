﻿using AutoFixture;
using CreateTestData;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.Internal.Contracts.Commands.HarriOutbound;
using Moq;
using Xunit;
namespace Jitb.Employment.HarriOutbound.Tests
{
    public class HireEmployeeHireMessageAssistantTests
    {
        private readonly Fixture _fixture;
        private readonly Mock<ILocationRepository> _mockLocationRepository;
        private readonly HireEmployeeHireMessageAssistant _assistant;

        public HireEmployeeHireMessageAssistantTests()
        {
            _fixture = new Fixture();
            _mockLocationRepository = new Mock<ILocationRepository>();
            _assistant = new HireEmployeeHireMessageAssistant(_mockLocationRepository.Object);
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public void Usage_ReturnsTrueForNonInitialLoadToHarri(IHiredAnEmployee message)
        {
            // Arrange

            // Act
            var result = _assistant.Usage(message);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "Harri")]
        public void Usage_ReturnsFalseForSyncInitialLoadToHarri()
        {
            // Arrange
            var message = _fixture.Create<InitialLoadToHarri>();

            // Act
            var result = _assistant.Usage(message);

            // Assert
            Assert.False(result);
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public void InitialMessageBody_ReturnsCorrectString(IHiredAnEmployee message)
        {
            // Arrange
            var employee = _fixture.Create<Employee>();

            // Act
            var result = _assistant.InitialMessageBody(message, employee);

            // Assert
            Assert.Contains($"Hiring employee {message.EmployeeId}", result);
            Assert.Contains($"Badge: {employee.BadgeId}", result);
            Assert.Contains($"{employee.FirstName} {employee.LastName}", result);
        }


        [Fact]
        [Trait("Category", "Harri")]
        public void IsValidForHarriOutbound_ReturnsTrueForEntityNumber1()
        {
            // Arrange
            var homeLocation = _fixture.Create<int>();
            var location = new Location { EntityNumber = 1 };
            _mockLocationRepository.Setup(r => r.Get(homeLocation)).Returns(location);

            // Act
            var result = _assistant.IsValidForHarriOutbound(homeLocation);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "Harri")]
        public void IsValidForHarriOutbound_ReturnsFalseForOtherEntityNumbers()
        {
            // Arrange
            var homeLocation = _fixture.Create<int>();
            var location = new Location { EntityNumber = 2 };
            _mockLocationRepository.Setup(r => r.Get(homeLocation)).Returns(location);

            // Act
            var result = _assistant.IsValidForHarriOutbound(homeLocation);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "Harri")]
        public void IsValidForHarriOutbound_ReturnsFalseForNullLocation()
        {
            // Arrange
            var homeLocation = _fixture.Create<int>();
            _mockLocationRepository.Setup(r => r.Get(homeLocation)).Returns((Location)null);

            // Act
            var result = _assistant.IsValidForHarriOutbound(homeLocation);

            // Assert
            Assert.False(result);
        }

    }
}
