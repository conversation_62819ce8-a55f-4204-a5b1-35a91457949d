﻿using AutoFixture;
using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using Jitb.Employment.Domain;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.Domain.Repositories.Employment.Maps;
using Jitb.Employment.HarriCaller.Domain.Exceptions;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Handlers;
using Jitb.Employment.Internal.Contracts.Commands.HarriOutbound;
using Moq;
using NLog;
using NServiceBus.Testing;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

public class InitialLoadByLocationHandlerTests
{



    [Theory]
    [AutoMoqData]
    [Trait("Category", "Harri")]
    public async Task Handle_WhenTenantExists_ShouldProcessActiveEmployees(InitialLoadByLocation message
    , Guid tenant
    , [Frozen] IEmployeeLocation2Repository location2Repository
    , [Frozen] IEmployeeRepository employeeRepository
    , [Frozen] ILogger logger
    , [Frozen] InitialLoadByLocationHandler sut
    , Fixture fixture)
    {
        // Arrange
        message.Locations = "3";
        var employeeLocations = fixture.CreateMany<EmployeeLocation2>(3).ToList();

        Mock.Get(location2Repository).Setup(r => r.GetActivePrimaryByLocation(int.Parse(message.Locations))).Returns(employeeLocations);

        foreach (var el in employeeLocations)
        {
            var employee = fixture.Build<Employee>().With(e => e.CurrentStatus, "01").Create();
            Mock.Get(employeeRepository).Setup(r => r.Get(el.EmployeeId)).Returns(employee);
        }

        var context = new TestableMessageHandlerContext();
        // Act
        await sut.Handle(message, context);

        // Assert
        //   _mockContext.Verify(c => c.Send(It.IsAny<InitialLoadToHarri>(), It.IsAny<SendOptions>()), Times.Exactly(3));
        context.SentMessages.Length.Should().Be(3);
        Mock.Get(logger).Verify(x => x.Info(It.Is<string>(s => s.StartsWith("Employees sent to Harri:  3"))), Times.Once());
    }


    [Theory]
    [AutoMoqData]
    [Trait("Category", "Harri")]
    public async Task Handle_WhenTenantNotFoundAfterUpdate_ShouldThrowException(InitialLoadByLocation message
        , [Frozen] ITenantByLocationProvider tenantByLocationProvider
        , [Frozen] InitialLoadByLocationHandler sut
    )
    {
        // Arrange
        message.Locations = "3";
        Mock.Get(tenantByLocationProvider).Setup(r => r.GetTenantIdByLocation(int.Parse(message.Locations))).Returns(Task.FromResult<Guid?>(null));

        // Act & Assert
        await Assert.ThrowsAsync<TenantNotFoundForLocationException>(() => sut.Handle(message, null));
    }


    [Theory]
    [AutoMoqData]
    [Trait("Category", "Harri")]
    public async Task Handle_WhenEmployeeNotFound_ShouldLogWarningAndContinue([Frozen] InitialLoadByLocation message
        , [Frozen] IEmployeeRepository employeeRepository
        , [Frozen] IEmployeeLocation2Repository location2Repository
        , [Frozen] ILogger logger
        , [Frozen] InitialLoadByLocationHandler sut
        , [Frozen] Fixture fixture)
    {
        // Arrange
        message.Locations = "3";
        var employeeLocations = fixture.Build<EmployeeLocation2>()
            .With(el => el.Location, new Location { LocationNumber = int.Parse(message.Locations) })
            .CreateMany(2)
            .ToList();
        //     var employeeLocations = fixture.CreateMany<EmployeeLocation2>(2).ToList();

        Mock.Get(location2Repository).Setup(r => r.GetActivePrimaryByLocation(int.Parse(message.Locations))).Returns(employeeLocations);

        var activeEmployee = fixture.Build<Employee>().With(e => e.CurrentStatus, "01").Create();
        Mock.Get(employeeRepository).Setup(r => r.Get(employeeLocations[0].EmployeeId)).Returns((Employee)null);
        Mock.Get(employeeRepository).Setup(r => r.Get(employeeLocations[1].EmployeeId)).Returns(activeEmployee);

        var context = new TestableMessageHandlerContext();

        // Act
        await sut.Handle(message, context);

        // Assert

        Mock.Get(logger).Verify(x => x.Warn(It.Is<string>(s => s.Contains("Employee record not found"))), Times.Once());

        context.SentMessages.Length.Should().Be(1);
    }




    [Theory]
    [InlineAutoMoqData("3", 1)]
    [InlineAutoMoqData("3,23", 2)]
    [InlineAutoMoqData("3,24,4004", 3)]
    [Trait("Category", "Harri")]
    public async Task Handle_HandlerProcessesEachLocation(string locations, int locationsProcessed,
        InitialLoadByLocation message,
        [Frozen] ILogger log,
        InitialLoadByLocationHandler sut)
    {
        //Arrange
        message.Locations = locations;
        var context = new TestableMessageHandlerContext();

        //Act
        await sut.Handle(message, context);

        //Assert
        Mock.Get(log).Verify(x => x.Trace("Processing GenerateCommandsForSingleLocation"), Times.Exactly(locationsProcessed));
    }

    [Theory]
    [InlineAutoMoqData("3,2A4,4004", 2, 1)]
    [InlineAutoMoqData("a3,2A4,4004", 1, 2)]
    [InlineAutoMoqData("3,4,  4004 ,   25, 44a", 4, 1)]
    [InlineAutoMoqData("3,4d3,  4004 ,   25, 44a", 3, 2)]
    [Trait("Category", "Harri")]
    public async Task LocationSkippedIfNotNumeric(string locations, int locationsProcessed, int locationsInError,
        InitialLoadByLocation message,
        [Frozen] ILogger log,
        InitialLoadByLocationHandler sut)
    {
        //Arrange
        message.Locations = locations;
        var context = new TestableMessageHandlerContext();

        //Act
        await sut.Handle(message, context);

        //Assert
        Mock.Get(log).Verify(x => x.Trace("Processing GenerateCommandsForSingleLocation"), Times.Exactly(locationsProcessed));
        Mock.Get(log).Verify(x => x.Trace("Location not numeric"), Times.Exactly(locationsInError));
    }

    // removed Decrypt() test
}