﻿using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Providers.HarriProviders;
using Jitb.Employment.HarriCaller.Domain.Constants;
using Jitb.Employment.HarriCaller.Domain.Exceptions;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Handlers;
using Jitb.Employment.Internal.Contracts.Commands.HarriOutbound;
using Moq;
using NLog;
using NServiceBus.Testing;
using RestSharp;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriOutbound.Tests
{
    public class PayRateChangeTests
    {


        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public void PayRateChangeHandler_WhenNotAHarriLocation_LeaveEarly(
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            [Frozen] ILogger log,
            PayRateChangeHandler sut,
            IChangedAnEmployeePayRate message
        )
        {
            //Arrange
            var context = new TestableMessageHandlerContext();
            context.MessageHeaders.Add("SourceSystem", "Ultipro");

            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(false);

            //Act
            sut.Handle(message, context).GetAwaiter().GetResult();

            //Assert
            Mock.Get(log).Verify(x => x.Trace("Not a Harri Location"), Times.Once());
            Mock.Get(log).Verify(x => x.Trace("Send Borrow to Harri"), Times.Never());
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public async Task PayRateChangeHandler_WhenTenantNotFound_ThrowException(
            [Frozen] ICheckIfHarriLocationProvider checkIfHarriLocationProvider,
            [Frozen] ITenantByLocationProvider tenantByLocationProvider,
            [Frozen] ILogger log,
            PayRateChangeHandler sut,
            IChangedAnEmployeePayRate message)
        {
            //Arrange
            var context = new TestableMessageHandlerContext();
            context.MessageHeaders.Add("SourceSystem", "Ultipro");

            Mock.Get(checkIfHarriLocationProvider).Setup(x => x.IsValidForHarriOutbound(It.IsAny<int>())).Returns(true);
            Mock.Get(tenantByLocationProvider).Setup(x => x.GetTenantIdByLocation(It.IsAny<int>())).Returns(Task.FromResult((Guid?)null));

            //Act
            Func<Task> act = () => sut.Handle(message, context);

            //Assert
            await act.Should().ThrowAsync<TenantNotFoundForLocationException>()
               .WithMessage("Harri Tenant not found for location 0");

        }


        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public async Task PayRateChangeHandler_WhenChangingToASalariedPayRate_SendPayloadToChangePayRate(
            [Frozen] ILogger log,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,


            PayRateChangeHandler sut,
            IChangedAnEmployeePayRate message,
            [Frozen] Employee employee)
        {
            //Arrange
            var context = new TestableMessageHandlerContext();
            context.MessageHeaders.Add("SourceSystem", "Ultipro");

            employee.SalaryClass = "S";
            employee.JobCodes.FirstOrDefault().JobCode = new JobCode { Code = "RORM05" };
            employee.EmployeeLocations2.FirstOrDefault().Location = new Location { LocationNumber = 3 };

            var restResponse = new RestResponse
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ResponseStatus = ResponseStatus.Completed
                //         Content = GenerateEmployeeJson()
            };
            Mock.Get(callHarriWebServiceProvider).Setup(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(),
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), "V1")).Returns(Task.FromResult(restResponse));

            //Act
            await sut.Handle(message, context);

            //Assert
            Mock.Get(callHarriWebServiceProvider).Verify(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(),
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), "V1"), Times.Never);
            context.SentMessages.Length.Should().Be(1);
            context.SentMessages[0].Message.GetType().Should().Be(typeof(ChangeHarriSalaryClass));
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Harri")]
        public async Task PayRateChangeHandler_WhenChangingToAHourlyPayRate_SendPayloadToChangePayRateAndChangeRate(
            [Frozen] ILogger log,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,


            PayRateChangeHandler sut,
            IChangedAnEmployeePayRate message,
            [Frozen] Employee employee,
            [Frozen] HarriInboundEmployee harriEmployee)
        {
            //Arrange
            var context = new TestableMessageHandlerContext();
            context.MessageHeaders.Add("SourceSystem", "Ultipro");

            employee.SalaryClass = "H";
            employee.JobCodes.FirstOrDefault().JobCode = new JobCode { Code = "RORH05" };
            employee.EmployeeLocations2.FirstOrDefault().Location = new Location { LocationNumber = 3 };
            employee.PayRates.RemoveAt(0);
            employee.PayRates.Add(new EmployeePayRate(employee, 22.22M, DateTime.Today.AddDays(-10)));

            harriEmployee.PayType.Type = HarriPayTypes.Salaried;
            harriEmployee.PayType.PayRate.Rate = 0L;

            var restResponse = new RestResponse
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ResponseStatus = ResponseStatus.Completed
                //         Content = GenerateEmployeeJson()
            };
            Mock.Get(callHarriWebServiceProvider).Setup(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(),
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), "V1")).Returns(Task.FromResult(restResponse));

            //Act
            await sut.Handle(message, context);

            //Assert
            Mock.Get(callHarriWebServiceProvider).Verify(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(),
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), "V1"), Times.Never);
            context.SentMessages.Length.Should().Be(1);
            context.SentMessages.FirstOrDefault().Message.GetType().Should().Be(typeof(ChangeHarriSalaryClass));
        }

    }
}
