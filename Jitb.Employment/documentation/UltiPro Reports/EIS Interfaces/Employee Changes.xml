<report xmlns="http://developer.cognos.com/schemas/report/12.0/" useStyleVersion="10" expressionLocale="en-us">
				<modelPath>/content/folder[@name='UltiPro BI Content']/folder[@name='UltiPro BI for Core HR and Payroll']/folder[@name='_UltiPro Delivered Packages']/package[@name='Administrator Package']/model[@name='model']</modelPath>
				<drillBehavior/>
				<queries>
					<query name="Audit Table">
						<source>
							<model/>
						</source>
						<selection><dataItem aggregate="none" rollupAggregate="none" name="audUniqueKey"><expression>[Business Layer].[Employee Audit Information].[audUniqueKey]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="1"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem name="Employee Number" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[Employee Number]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Old Value" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[Old Value]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="New Value" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[New Value]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Date/Time Processed" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[Date/Time Processed]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Table Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[Table Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Field Updated" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[Field Updated]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Data Item1"><expression>[Field Updated] = [New Value]</expression></dataItem><dataItem name="Action Taken" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[Action Taken]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 2" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[Org Level 2]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 2 Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Audit Information].[Org Level 2 Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem></selection>
					<detailFilters><detailFilter><filterExpression>[Date/Time Processed] between ?From? and ?To?</filterExpression></detailFilter><detailFilter><filterExpression>[Action Taken] &lt;&gt; 'Insert' or 
([Action Taken]  = 'Insert' and 
[Field Updated] contains 'efoPhoneNumber' and 
[Table Name] contains 'EmpMPhon')</filterExpression></detailFilter><detailFilter use="prohibited"><filterExpression>[Field Updated] contains 'EepNameFirst' or
[Field Updated] contains 'EecDateOfLastHire' or
[Field Updated] contains 'EecHourlyPayRate' or
[Field Updated] contains 'EepGender' or
[Field Updated] contains 'EecDateofOriginalHire' or
[Field Updated] contains 'EepNamePreferred' or
[Field Updated] contains 'EepNameLast' or 
[Field Updated] contains 'EepNameMiddle' or
[Field Updated] contains 'EepDateOfBirth' or
[Field Updated] contains 'EepAddressLine1' or
[Field Updated] contains 'EepAddressLine2' or
[Field Updated] contains 'EepAddressCity' or
[Field Updated] contains 'EepAddressZipCode' or
[Field Updated] contains 'EepAddressState' or
[Field Updated] contains 'EecOrgLvl1' or
[Field Updated] contains 'EecOrgLvl2' or
[Field Updated] contains 'EecSalaryorHourly' or
[Field Updated] contains 'EecUDField12' and 
[Table Name] contains 'EmpComp' or 
[Field Updated] contains 'EecEmpNo' and 
[Table Name] contains 'EmpComp' or
[Field Updated] contains 'eecEmplStatus' and
[Table Name] contains 'rbsEEData' or
[Field Updated] contains 'EecJobCode' and
[Table Name] contains 'EmpComp' or
[Field Updated] contains 'eecEEType' and
[Table Name] contains 'rbsEEData' or
[Field Updated] contains 'EecSupervisorID' and
[Table Name] contains 'EmpComp' or
[Field Updated] = 'SSN' and
[Table Name] contains 'iEmpPers' or
[Field Updated] contains 'efoPhoneNumber' and
[Table Name] contains 'EmpMPhon' or
[Field Updated] = 'eepAddressEmail' and
[Table Name] contains 'EmpPers' or
[Field Updated] contains 'EecEEID' and
[Table Name] = 'EmpComp'
</filterExpression></detailFilter><detailFilter use="prohibited"><filterExpression>[Table Name] contains 'EmpComp' or 
[Field Updated] contains 'eecEmplStatus' and [Table Name] contains 'rbsEEData' or 
[Field Updated] contains 'eecEEType' and [Table Name] contains 'rbsEEData' or 
[Field Updated] contains 'EecSupervisorID' and [Table Name] contains 'iEmpPers' or 
[Field Updated] contains 'efoPhoneNumber' and [Table Name] contains 'EmpMPhon' or 
[Field Updated] = 'eepAddressEmail' and [Table Name] contains 'EmpPers' or 
[Field Updated] = 'JbcFLSAType' and [Table Name] contains 'JobCode'</filterExpression></detailFilter><detailFilter><filterExpression>[Table Name] contains 'EmpPers' or 
[Table Name] contains 'EmpComp' or 
[Field Updated] = 'SSN' and [Table Name] contains 'iEmpPers' or 
[Field Updated] contains 'eecEmplStatus' and [Table Name] contains 'rbsEEData' or 
[Field Updated] contains 'eecEEType' and [Table Name] contains 'rbsEEData' or 
[Field Updated] contains 'efoPhoneNumber' and [Table Name] contains 'EmpMPhon' or 
[Field Updated] contains 'JbcFLSAType' and [Table Name] contains 'JobCode' </filterExpression></detailFilter></detailFilters></query>
				<query name="Employee Data">
			<source>
				<model/>
			</source>
			<selection><dataItem aggregate="none" rollupAggregate="none" name="AuditKey_EmpPers"><expression>[Business Layer].[Employee].[AuditKey_EmpPers]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="1"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="AuditKey_EmpComp"><expression>[Business Layer].[Employee].[AuditKey_EmpComp]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="1"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem name="Last Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Last Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Middle Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Middle Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="First Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[First Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Number" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Employee Number]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Date Of Birth" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Date Of Birth]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Salary or Hourly" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Salary or Hourly]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EecEEID" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[EecEEID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EecUDField12" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[EecUDField12]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="EepDatetimeChanged"><expression>[Business Layer].[Employee].[EepDatetimeChanged]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="4"/><XMLAttribute output="no" name="RS_dataUsage" value="identifier"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="EecDateTimeChanged"><expression>[Business Layer].[Employee].[EecDateTimeChanged]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="4"/><XMLAttribute output="no" name="RS_dataUsage" value="identifier"/></XMLAttributes></dataItem></selection>
		</query><query name="Query3">
			<source>
				
			<joinOperation>
			<joinOperands>
				<joinOperand><queryRef refQuery="Audit Table"/></joinOperand>
				<joinOperand><queryRef refQuery="Employee Data"/></joinOperand>
			</joinOperands>
			<joinFilter>
				<filterExpression>[Audit Table].[Employee Number] = [Employee Data].[Employee Number]</filterExpression>
			</joinFilter>
		</joinOperation></source>
			<selection><dataItem name="audUniqueKey"><expression>[Audit Table].[audUniqueKey]</expression></dataItem><dataItem name="AuditKey_EmpPers"><expression>[Employee Data].[AuditKey_EmpPers]</expression></dataItem><dataItem name="New Value"><expression>if ([Employee Data].[Salary or Hourly] = 'salaried' and [Audit Table].[Field Updated] = 'EecHourlyPayRate') then 
('0')
else
([Audit Table].[New Value])
</expression></dataItem><dataItem name="Old Value"><expression>if ([Audit Table].[Field Updated] = 'EecHourlyPayRate' and [Employee Data].[Salary or Hourly] = 'salaried') then ('0')
else
([Audit Table].[Old Value])</expression></dataItem><dataItem name="Employee Number"><expression>[Employee Data].[Employee Number]</expression></dataItem><dataItem name="Date/Time Processed"><expression>[Audit Table].[Date/Time Processed]</expression></dataItem><dataItem name="Field Updated"><expression>[Audit Table].[Field Updated]</expression></dataItem><dataItem name="Table Name"><expression>[Audit Table].[Table Name]</expression></dataItem><dataItem name="Action Taken"><expression>[Audit Table].[Action Taken]</expression></dataItem><dataItem name="Salary or Hourly"><expression>[Employee Data].[Salary or Hourly]</expression></dataItem><dataItem name="EecEEID"><expression>[Employee Data].[EecEEID]</expression></dataItem><dataItem name="EepDatetimeChanged"><expression>[Employee Data].[EepDatetimeChanged]</expression></dataItem><dataItem name="EecDateTimeChanged"><expression>[Employee Data].[EecDateTimeChanged]</expression></dataItem><dataItem name="AuditKey_EmpComp"><expression>[Employee Data].[AuditKey_EmpComp]</expression></dataItem><dataItem name="Org Level 2"><expression>[Audit Table].[Org Level 2]</expression></dataItem><dataItem name="Org Level 2 Code"><expression>[Audit Table].[Org Level 2 Code]</expression></dataItem></selection>
		</query></queries>
				<layouts>
					<layout>
						<reportPages>
							<page name="Page1"><style><defaultStyles><defaultStyle refStyle="pg"/></defaultStyles></style>
								<pageBody><style><defaultStyles><defaultStyle refStyle="pb"/></defaultStyles></style>
									<contents>
										<list refQuery="Query3" horizontalPagination="true" name="List1">
											
											
											
											<noDataHandler>
												<contents>
													<block>
														<contents>
															<textItem>
																<dataSource>
																	<staticValue>No Data Available</staticValue>
																</dataSource>
																<style>
																	<CSS value="padding:10px 18px;"/>
																</style>
															</textItem>
														</contents>
													</block>
												</contents>
											</noDataHandler>
											<style>
												<defaultStyles>
													<defaultStyle refStyle="ls"/>
												</defaultStyles>
												<CSS value="border-collapse:collapse"/>
											</style>
										<listColumns><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Date/Time Processed"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Date/Time Processed"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employee Number"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Employee Number"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="EecEEID"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="EecEEID"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Field Updated"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Field Updated"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Salary or Hourly"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Salary or Hourly"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Old Value"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Old Value"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="New Value"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="New Value"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Table Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Table Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Action Taken"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Action Taken"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="audUniqueKey"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="audUniqueKey"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="AuditKey_EmpPers"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="AuditKey_EmpPers"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 2"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 2"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 2 Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 2 Code"/></dataSource></textItem></contents></listColumnBody></listColumn></listColumns><sortList><sortItem refDataItem="Date/Time Processed"/></sortList></list>
									</contents>
								</pageBody>
								<pageHeader>
									<contents>
										<block><style><defaultStyles><defaultStyle refStyle="ta"/></defaultStyles></style>
											<contents>
												<textItem><style><defaultStyles><defaultStyle refStyle="tt"/></defaultStyles></style>
													<dataSource>
														<staticValue>Employee Changes</staticValue>
													</dataSource>
												</textItem>
											</contents>
										</block>
									</contents>
									<style>
										<defaultStyles>
											<defaultStyle refStyle="ph"/>
										</defaultStyles>
										<CSS value="padding-bottom:10px"/>
									</style>
								</pageHeader>
								
							<pageFooter>
									<contents>
										<table>
											<tableRows>
												<tableRow>
													<tableCells>
														<tableCell>
															<contents>
																<date>
																	<style>
																		<dataFormat>
																			<dateFormat/>
																		</dataFormat>
																	</style>
																</date>
															</contents>
															<style>
																<CSS value="vertical-align:top;text-align:left;width:25%"/>
															</style>
														</tableCell>
														<tableCell>
															<contents>
																<pageNumber/>
															</contents>
															<style>
																<CSS value="vertical-align:top;text-align:center;width:50%"/>
															</style>
														</tableCell>
														<tableCell>
															<contents>
																<time>
																	<style>
																		<dataFormat>
																			<timeFormat/>
																		</dataFormat>
																	</style>
																</time>
															</contents>
															<style>
																<CSS value="vertical-align:top;text-align:right;width:25%"/>
															</style>
														</tableCell>
													</tableCells>
												</tableRow>
											</tableRows>
											<style>
												<defaultStyles>
													<defaultStyle refStyle="tb"/>
												</defaultStyles>
												<CSS value="border-collapse:collapse;width:100%"/>
											</style>
										</table>
									</contents>
									<style>
										<defaultStyles>
											<defaultStyle refStyle="pf"/>
										</defaultStyles>
										<CSS value="padding-top:10px"/>
									</style>
								</pageFooter></page>
						</reportPages>
					</layout>
				</layouts>
			<XMLAttributes><XMLAttribute name="RS_CreateExtendedDataItems" value="true" output="no"/><XMLAttribute name="listSeparator" value="," output="no"/><XMLAttribute name="RS_modelModificationTime" value="2017-12-06T15:08:24.180Z" output="no"/></XMLAttributes><reportName>Employee Changes</reportName></report>