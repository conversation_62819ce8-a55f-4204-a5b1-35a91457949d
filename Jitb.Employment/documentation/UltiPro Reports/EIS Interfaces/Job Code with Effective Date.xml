<report xmlns="http://developer.cognos.com/schemas/report/12.0/" expressionLocale="en"><!--RSU-SPC-0093 The report specification was upgraded from "http://developer.cognos.com/schemas/report/11.0/" to "http://developer.cognos.com/schemas/report/12.0/" at 2016-9-17. 15:41:4-->
  <!--RSU-SPC-0093 The report specification was upgraded from &amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;http://developer.cognos.com/schemas/report/10.0/&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot; to &amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;http://developer.cognos.com/schemas/report/11.0/&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot; at 2014-9-2. 14:3:11-->
  <!--RSU-SPC-0093 The report specification was upgraded from &amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;http://developer.cognos.com/schemas/report/6.0/&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot; to &amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;http://developer.cognos.com/schemas/report/10.0/&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot; at 2013-12-9. 11:54:42-->
  <modelPath>/content/folder[@name='UltiPro BI Content']/folder[@name='UltiPro BI for Core HR and Payroll']/folder[@name='_UltiPro Delivered Packages']/package[@name='Administrator Package']/model[@name='model']</modelPath>
  <drillBehavior/>
  <queries>
    <query name="Package Query">
      <source>
        <model/>
      </source>
      <selection>
        <dataItem aggregate="none" name="Job Code" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Job Code]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem>
        <dataItem aggregate="none" name="Job Title" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Job Title]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem>
        
        
        
        <dataItem aggregate="none" name="Job Group Code" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Job Group Code]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem>
        <dataItem aggregate="none" name="Job Group" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Job Group]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem>
      <dataItem aggregate="none" rollupAggregate="none" name="Job Family"><expression>[Business Layer].[Jobs].[Job Family]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Job Status"><expression>[Business Layer].[Jobs].[Job Status]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Job Status Code"><expression>[Business Layer].[Jobs].[Job Status Code]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Job Status As Of"><expression>[Business Layer].[Jobs].[Job Status As Of]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="4"/><XMLAttribute output="no" name="RS_dataUsage" value="identifier"/></XMLAttributes></dataItem></selection>
    </query>
    <query name="Configurable Fields Query">
      <source>
        <sqlQuery dataSource="CompanyDB" name="Custom Fields SQL">
          <sqlText>SELECT 
	* 
FROM 
	fn_MP_CustomFields_JobCode (#sq ($account.parameters.UserContextID)#, #sq ($account.parameters.USER_TYPE)#, NULL,NULL)
</sqlText>
          
        <mdProjectedItems><mdProjectedItem name="JbcJobCode"/><mdProjectedItem name="Job_JobClass"/></mdProjectedItems></sqlQuery>
      </source>
      <selection>
        <dataItem name="JbcJobCode">
          <expression>[Custom Fields SQL].[JbcJobCode]</expression>
        </dataItem>
      <dataItem name="Job_JobClass"><expression>[Custom Fields SQL].[Job_JobClass]</expression></dataItem></selection>
    </query>
    <query name="Display Query">
      <source>
        <joinOperation>
          <joinOperands>
            <joinOperand cardinality="1:1">
              <queryRef refQuery="Package Query"/>
            </joinOperand>
            <joinOperand cardinality="0:1">
              <queryRef refQuery="Configurable Fields Query"/>
            </joinOperand>
          </joinOperands>
          <joinFilter>
            <filterExpression>[Package Query].[Job Code] = [Configurable Fields Query].[JbcJobCode]</filterExpression>
          </joinFilter>
        </joinOperation>
      </source>
      <selection>
        
        
        
        
        
        
        
      <dataItem name="Job Family"><expression>[Package Query].[Job Family]</expression></dataItem><dataItem name="Job_JobClass"><expression>[Configurable Fields Query].[Job_JobClass]</expression></dataItem><dataItem name="Job Title"><expression>[Package Query].[Job Title]</expression></dataItem><dataItem name="Job Code"><expression>[Package Query].[Job Code]</expression></dataItem><dataItem name="Job Status Code"><expression>[Package Query].[Job Status Code]</expression></dataItem><dataItem name="Job Status As Of"><expression>[Package Query].[Job Status As Of]</expression></dataItem></selection>
    </query>
  </queries>
  <layouts>
    <layout>
      <reportPages>
        <page name="Page1">
          <style>
            <defaultStyles>
              <defaultStyle refStyle="pg"/>
            </defaultStyles>
          </style>
          <pageBody>
            <style>
              <defaultStyles>
                <defaultStyle refStyle="pb"/>
              </defaultStyles>
            </style>
            <contents>
              <list horizontalPagination="true" name="List1" refQuery="Display Query">
                <style>
                  <CSS value="border-collapse:collapse"/>
                  <defaultStyles>
                    <defaultStyle refStyle="ls"/>
                  </defaultStyles>
                </style>
                
                
                
              <listColumns><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job Family"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job Family"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job_JobClass"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job_JobClass"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job Title"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job Title"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job Status Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job Status Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job Status As Of"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job Status As Of"/></dataSource></textItem></contents></listColumnBody></listColumn></listColumns></list>
            </contents>
          </pageBody>
          <pageHeader>
            <contents>
              <block>
                <style>
                  <defaultStyles>
                    <defaultStyle refStyle="ta"/>
                  </defaultStyles>
                </style>
                <contents>
                  <textItem>
                    <style>
                      <defaultStyles>
                        <defaultStyle refStyle="tt"/>
                      </defaultStyles>
                    </style>
                    <dataSource>
                      <staticValue>Job Code and Class</staticValue>
                    </dataSource>
                  </textItem>
                </contents>
              </block>
            </contents>
            <style>
              <defaultStyles>
                <defaultStyle refStyle="ph"/>
              </defaultStyles>
              <CSS value="padding-bottom:10px"/>
            </style>
          </pageHeader>
          <pageFooter>
            <contents>
              <table>
                <tableRows>
                  <tableRow>
                    <tableCells>
                      <tableCell>
                        <contents>
                          <date>
                            <style>
                              <dataFormat>
                                <dateFormat/>
                              </dataFormat>
                            </style>
                          </date>
                        </contents>
                        <style>
                          <CSS value="vertical-align:top;text-align:left;width:25%"/>
                        </style>
                      </tableCell>
                      <tableCell>
                        <contents>
                          <pageNumber/>
                        </contents>
                        <style>
                          <CSS value="vertical-align:top;text-align:center;width:50%"/>
                        </style>
                      </tableCell>
                      <tableCell>
                        <contents>
                          <time>
                            <style>
                              <dataFormat>
                                <timeFormat/>
                              </dataFormat>
                            </style>
                          </time>
                        </contents>
                        <style>
                          <CSS value="vertical-align:top;text-align:right;width:25%"/>
                        </style>
                      </tableCell>
                    </tableCells>
                  </tableRow>
                </tableRows>
                <style>
                  <defaultStyles>
                    <defaultStyle refStyle="tb"/>
                  </defaultStyles>
                  <CSS value="border-collapse:collapse;width:100%"/>
                </style>
              </table>
            </contents>
            <style>
              <defaultStyles>
                <defaultStyle refStyle="pf"/>
              </defaultStyles>
              <CSS value="padding-top:10px"/>
            </style>
          </pageFooter>
        </page>
      </reportPages>
    </layout>
  </layouts>
  <XMLAttributes>
    <XMLAttribute name="RS_CreateExtendedDataItems" output="no" value="true"/>
    <XMLAttribute name="listSeparator" output="no" value=","/>
    <XMLAttribute name="RS_modelModificationTime" output="no" value="2017-12-06T15:08:24.180Z"/>
  </XMLAttributes>
  <reportName>Job Code with Effective Date</reportName>
</report>