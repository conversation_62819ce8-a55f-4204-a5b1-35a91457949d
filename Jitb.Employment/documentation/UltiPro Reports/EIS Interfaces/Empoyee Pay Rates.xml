<report xmlns="http://developer.cognos.com/schemas/report/12.0/" useStyleVersion="10" expressionLocale="en">
				<modelPath>/content/folder[@name='UltiPro BI Content']/folder[@name='UltiPro BI for Core HR and Payroll']/folder[@name='_UltiPro Delivered Packages']/package[@name='Administrator Package']/model[@name='model']</modelPath>
				<drillBehavior/>
				<layouts>
					<layout>
						<reportPages>
							<page name="Page1">
								<style>
									<defaultStyles>
										<defaultStyle refStyle="pg"/>
									</defaultStyles>
								</style>
								<pageBody>
									<style>
										<defaultStyles>
											<defaultStyle refStyle="pb"/>
										</defaultStyles>
									</style>
									<contents><list horizontalPagination="true" name="List1" refQuery="Query1">
			
			
			
			<noDataHandler>
				<contents>
					<block>
						<contents>
							<textItem>
								<dataSource>
									<staticValue>No Data Available</staticValue>
								</dataSource>
								<style>
									<CSS value="padding:10px 18px;"/>
								</style>
							</textItem>
						</contents>
					</block>
				</contents>
			</noDataHandler>
			<style>
				<CSS value="border-collapse:collapse"/>
				<defaultStyles>
					<defaultStyle refStyle="ls"/>
				</defaultStyles>
			</style>
		<listColumns><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employee Number"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Employee Number"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Effective Date"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Effective Date"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Salary Or Hourly Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Salary Or Hourly Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Hourly Pay Rate"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Hourly Pay Rate"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="EjhDateTimeCreated"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="EjhDateTimeCreated"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Is Rate Change"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Is Rate Change"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="EjhEEID"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="EjhEEID"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="AuditKey"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="AuditKey"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Reason"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Reason"/></dataSource></textItem></contents></listColumnBody></listColumn></listColumns></list></contents>
								</pageBody>
							</page>
						</reportPages>
					</layout>
				</layouts>
			<XMLAttributes><XMLAttribute name="RS_CreateExtendedDataItems" value="true" output="no"/><XMLAttribute name="listSeparator" value="," output="no"/><XMLAttribute name="RS_modelModificationTime" value="2017-12-06T15:08:24.180Z" output="no"/></XMLAttributes><queries><query name="Query1">
			<source>
				<model/>
			</source>
			<selection><dataItem name="Effective Date" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Effective Date]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Employment Status Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Employment Status Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Salary Or Hourly Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Salary Or Hourly Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Number" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Employee Number]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Hourly Pay Rate" aggregate="average"><expression>[Business Layer].[Employee Job History].[Hourly Pay Rate]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="2" output="no"/><XMLAttribute name="RS_dataUsage" value="fact" output="no"/></XMLAttributes></dataItem><dataItem name="Is Commission Employee" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Commission Employee]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is Job Change" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Job Change]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is Org Change" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Org Change]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is Outside Guidelines" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Outside Guidelines]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is Outside Range" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Outside Range]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is Pensioner" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Pensioner]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is Promotion" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Promotion]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is Rate Change" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Rate Change]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is System" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is System]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Is Transfer" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Is Transfer]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EjhDateTimeCreated" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[EjhDateTimeCreated]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="AuditAction" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[AuditAction]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="1" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="AuditKey" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[AuditKey]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="1" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EjhCoID" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[EjhCoID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="EjhDateTimeCreated1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[EjhDateTimeCreated]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="EjhEEID" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[EjhEEID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="EjhHomeCoID" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[EjhHomeCoID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EjhSystemID" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[EjhSystemID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Pay Frequency Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Pay Frequency Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Reason" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Reason]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem></selection>
		<detailFilters><detailFilter use="prohibited"><filterDefinition><filterCompare refDataItem="Effective Date" dataType="dateTime" operator="greaterThan"><filterValue>2017-01-01 00:00:00.000</filterValue></filterCompare></filterDefinition></detailFilter><detailFilter><filterExpression>[Is Rate Change]='Y' or ( [Is Rate Change]='N' and  [Reason] in ( 'Rehire', 'New Hire', 'Pay Change Only', 'Position and Pay Change'))</filterExpression></detailFilter><detailFilter use="prohibited"><filterExpression>[Employee Number]=82706748</filterExpression></detailFilter><detailFilter><filterExpression>[EjhDateTimeCreated] between ?from? and ?to?</filterExpression></detailFilter></detailFilters></query></queries><reportName>Employee Pay Rates</reportName></report>