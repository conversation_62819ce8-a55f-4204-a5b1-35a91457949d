<report xmlns="http://developer.cognos.com/schemas/report/12.0/" useStyleVersion="10" expressionLocale="en-us">
				<modelPath>/content/folder[@name='UltiPro BI Content']/folder[@name='UltiPro BI for Core HR and Payroll']/folder[@name='_UltiPro Delivered Packages']/package[@name='Administrator Package']/model[@name='model']</modelPath>
				<drillBehavior/>
				<queries>
					<query name="JobHist (EE)">
						<source>
							<model/>
						</source>
						<selection><dataItem aggregate="none" rollupAggregate="none" name="AuditKey"><expression>[Business Layer].[Employee Job History].[AuditKey]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="1"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="AuditKey_EmpComp"><expression>[Business Layer].[Employee].[AuditKey_EmpComp]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="1"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="AuditKey_EmpPers"><expression>[Business Layer].[Employee].[AuditKey_EmpPers]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="1"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem name="EjhEEID" aggregate="none" rollupAggregate="none" sort="ascending"><expression>[Business Layer].[Employee Job History].[EjhEEID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="EjhCoID" aggregate="none" rollupAggregate="none" sort="ascending"><expression>[Business Layer].[Employee Job History].[EjhCoID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Effective Date" aggregate="none" rollupAggregate="none" sort="descending"><expression>[Business Layer].[Employee Job History].[Effective Date]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="EjhDateTimeCreated" aggregate="none" rollupAggregate="none" sort="descending"><expression>[Business Layer].[Employee Job History].[EjhDateTimeCreated]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="rCount"><expression>running-count( [Effective Date] for [EjhEEID],[EjhCoID] )</expression></dataItem><dataItem name="Job Change Reason Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Job Change Reason Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Job Change Reason" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Job Change Reason]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Number" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Employee Number]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Last Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Last Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="First Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[First Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Preferred First Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Preferred First Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Gender Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Gender Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Gender" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Gender]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Original Hire Date" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Original Hire Date]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Last Hire Date" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Last Hire Date]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Termination Date" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Termination Date]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="SSN (Formatted)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[SSN (Formatted)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Email Address" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Email Address]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Home Phone (Formatted)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Home Phone (Formatted)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Work Phone (Formatted)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Work Phone (Formatted)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Phone Number (Formatted)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Additional Phones].[Phone Number (Formatted)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Supervisor Employee Number (History)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Supervisor History].[Supervisor Employee Number (History)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Supervisor Name (History)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Supervisor History].[Supervisor Name (History)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="OrgLevel2Desc" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[OrgLevel2Desc]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EjhOrgLvl2" aggregate="none" rollupAggregate="none"><expression>trim([Business Layer].[Employee Job History].[EjhOrgLvl2])</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Cost Center (Full)" rollupAggregate="none" aggregate="none"><expression>[EjhOrgLvl2] || ' - ' || [OrgLevel2Desc]</expression></dataItem><dataItem name="Company Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Company Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Job Code" aggregate="none" rollupAggregate="none"><expression>trim([Business Layer].[Employee Job History].[Job Code])</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Job" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Job]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Status Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Employee Status Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Status" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Employee Status]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Salary/Hourly Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Salary/Hourly Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Salary/Hourly" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Salary/Hourly]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Exempt_Emp" aggregate="none" rollupAggregate="none"><expression>IF ( [Salary/Hourly Code]='S' ) THEN ( 'Y' ) ELSE ( 'N' )</expression></dataItem><dataItem name="EjhOrgLvl1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[EjhOrgLvl1]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="OrgLevel1Desc" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[OrgLevel1Desc]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Hourly Pay Rate" aggregate="average"><expression>[Business Layer].[Employee Job History].[Hourly Pay Rate]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="2" output="no"/><XMLAttribute name="RS_dataUsage" value="fact" output="no"/></XMLAttributes></dataItem><dataItem name="Salary Grade Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Salary Grade Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Salary Grade" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Salary Grade]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Location Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Job History].[Location Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Location Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Location Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Middle Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Middle Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Date Of Birth" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Date Of Birth]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Address Line 1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Address Line 1]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Address Line 2" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Address Line 2]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="City" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[City]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="State/Province" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[State/Province]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Zip Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Zip Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Type" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Employee Type]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Address 1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Address Line 1]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Address 2" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Address Line 2]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="City1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[City]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="State/Province1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[State/Province]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Zip Code1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Zip Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Organization Levels].[Org Level 1]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Org Level 1 Code"><expression>[Business Layer].[Employee].[Org Level 1 Code]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem name="Org Level 2" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Organization Levels].[Org Level 2]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 2 Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Organization Levels].[Org Level 2 Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EecUDField12" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[EecUDField12]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EecEEID" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[EecEEID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Type Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Employee Type Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 2 Code1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Org Level 2 Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Timeclock Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Timeclock Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Work Phone" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Work Phone]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Work Extension" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Work Extension]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="OK To Rehire" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[OK To Rehire]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem></selection>
					<detailFilters><detailFilter><filterExpression>[EjhDateTimeCreated] between ?From? and ?To?</filterExpression></detailFilter><detailFilter postAutoAggregation="true"><filterExpression>[Job Change Reason Code] in ('100', '101')</filterExpression></detailFilter><detailFilter><filterExpression>[Employee Type Code] &lt;&gt; 'RET'</filterExpression></detailFilter></detailFilters></query>
				<query name="Locations">
			<source>
				<model/>
			</source>
			<selection><dataItem name="Location Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Locations].[Location Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Address Line 1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Locations].[Address Line 1]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="City" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Locations].[City]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="State/Province Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Locations].[State/Province Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Zip/Postal Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Locations].[Zip/Postal Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem></selection>
		</query><query name="EE + Location">
			<source>
				
			<joinOperation>
			<joinOperands>
				<joinOperand cardinality="1:1"><queryRef refQuery="JobHist (EE)"/></joinOperand>
				<joinOperand cardinality="0:1"><queryRef refQuery="Locations"/></joinOperand>
			</joinOperands>
			<joinFilter>
				<filterExpression>[JobHist (EE)].[Location Code] = [Locations].[Location Code]</filterExpression>
			</joinFilter>
		</joinOperation></source>
			<selection><dataItem name="AuditKey"><expression>[JobHist (EE)].[AuditKey]</expression></dataItem><dataItem name="AuditKey_EmpComp"><expression>[JobHist (EE)].[AuditKey_EmpComp]</expression></dataItem><dataItem name="AuditKey_EmpPers"><expression>[JobHist (EE)].[AuditKey_EmpPers]</expression></dataItem><dataItem name="EjhEEID" aggregate="none" rollupAggregate="none" sort="ascending"><expression>[JobHist (EE)].[EjhEEID]</expression></dataItem><dataItem name="EjhCoID" aggregate="none" rollupAggregate="none" sort="ascending"><expression>[JobHist (EE)].[EjhCoID]</expression></dataItem><dataItem name="Effective Date" aggregate="none" rollupAggregate="none" sort="descending"><expression>[JobHist (EE)].[Effective Date]</expression></dataItem><dataItem name="EjhDateTimeCreated" aggregate="none" rollupAggregate="none" sort="descending"><expression>[JobHist (EE)].[EjhDateTimeCreated]</expression></dataItem><dataItem name="Employee Number" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Employee Number]</expression></dataItem><dataItem name="Last Name" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Last Name]</expression></dataItem><dataItem name="First Name" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[First Name]</expression></dataItem><dataItem name="Preferred First Name" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Preferred First Name]</expression></dataItem><dataItem name="Gender Code" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Gender Code]</expression></dataItem><dataItem name="Original Hire Date" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Original Hire Date]</expression></dataItem><dataItem name="Last Hire Date" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Last Hire Date]</expression></dataItem><dataItem name="Termination Date" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Termination Date]</expression></dataItem><dataItem name="SSN (Formatted)" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[SSN (Formatted)]</expression></dataItem><dataItem name="Email Address" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Email Address]</expression></dataItem><dataItem name="Home Phone (Formatted)" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Home Phone (Formatted)]</expression></dataItem><dataItem name="Work Phone (Formatted)" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Work Phone (Formatted)]</expression></dataItem><dataItem name="Phone Number (Formatted)" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Phone Number (Formatted)]</expression></dataItem><dataItem name="Supervisor Employee Number (History)" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Supervisor Employee Number (History)]</expression></dataItem><dataItem name="Supervisor Name (History)" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Supervisor Name (History)]</expression></dataItem><dataItem name="OrgLevel2Desc" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[OrgLevel2Desc]</expression></dataItem><dataItem name="EjhOrgLvl2" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[EjhOrgLvl2]</expression></dataItem><dataItem name="Cost Center (Full)" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Cost Center (Full)]</expression></dataItem><dataItem name="Company Code" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Company Code]</expression></dataItem><dataItem name="Job Code" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Job Code]</expression></dataItem><dataItem name="Job" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Job]</expression></dataItem><dataItem name="Employee Status Code" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Employee Status Code]</expression></dataItem><dataItem name="Employee Status" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Employee Status]</expression></dataItem><dataItem name="Salary/Hourly Code" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Salary/Hourly Code]</expression></dataItem><dataItem name="Salary/Hourly" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Salary/Hourly]</expression></dataItem><dataItem name="Exempt_Emp" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Exempt_Emp]</expression></dataItem><dataItem name="EjhOrgLvl1" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[EjhOrgLvl1]</expression></dataItem><dataItem name="OrgLevel1Desc" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[OrgLevel1Desc]</expression></dataItem><dataItem name="Hourly Pay Rate" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Hourly Pay Rate]</expression></dataItem><dataItem name="Salary Grade Code" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Salary Grade Code]</expression></dataItem><dataItem name="Salary Grade" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Salary Grade]</expression></dataItem><dataItem name="Location Name" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Location Name]</expression></dataItem><dataItem name="Location Code" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Location Code]</expression></dataItem><dataItem name="Address Line 1" rollupAggregate="none" aggregate="none"><expression>[Locations].[Address Line 1]</expression></dataItem><dataItem name="City" rollupAggregate="none" aggregate="none"><expression>[Locations].[City]</expression></dataItem><dataItem name="State/Province Code" rollupAggregate="none" aggregate="none"><expression>[Locations].[State/Province Code]</expression></dataItem><dataItem name="Zip/Postal Code" rollupAggregate="none" aggregate="none"><expression>[Locations].[Zip/Postal Code]</expression></dataItem><dataItem name="Middle Name"><expression>[JobHist (EE)].[Middle Name]</expression></dataItem><dataItem name="Date Of Birth"><expression>[JobHist (EE)].[Date Of Birth]</expression></dataItem><dataItem name="Employee Type"><expression>[JobHist (EE)].[Employee Type]</expression></dataItem><dataItem name="Address 1"><expression>[JobHist (EE)].[Address 1]</expression></dataItem><dataItem name="Address 2"><expression>[JobHist (EE)].[Address 2]</expression></dataItem><dataItem name="City1"><expression>[JobHist (EE)].[City1]</expression></dataItem><dataItem name="State/Province1"><expression>[JobHist (EE)].[State/Province1]</expression></dataItem><dataItem name="Zip Code1"><expression>[JobHist (EE)].[Zip Code1]</expression></dataItem><dataItem name="Gender" aggregate="none" rollupAggregate="none"><expression>[JobHist (EE)].[Gender]</expression></dataItem><dataItem name="Org Level 1"><expression>[JobHist (EE)].[Org Level 1]</expression></dataItem><dataItem name="Org Level 1 Code"><expression>[JobHist (EE)].[Org Level 1 Code]</expression></dataItem><dataItem name="Org Level 2"><expression>[JobHist (EE)].[Org Level 2]</expression></dataItem><dataItem name="Org Level 2 Code"><expression>[JobHist (EE)].[Org Level 2 Code]</expression></dataItem><dataItem name="EecUDField12"><expression>[JobHist (EE)].[EecUDField12]</expression></dataItem><dataItem name="EecEEID"><expression>[JobHist (EE)].[EecEEID]</expression></dataItem><dataItem name="Org Level 2 Code1"><expression>[JobHist (EE)].[Org Level 2 Code1]</expression></dataItem><dataItem name="Timeclock Code"><expression>[JobHist (EE)].[Timeclock Code]</expression></dataItem><dataItem name="Work Phone"><expression>[JobHist (EE)].[Work Phone]</expression></dataItem><dataItem name="Work Extension"><expression>[JobHist (EE)].[Work Extension]</expression></dataItem><dataItem name="OK To Rehire"><expression>[JobHist (EE)].[OK To Rehire]</expression></dataItem></selection>
		</query><query name="PCF (Job)">
			<source>
				
			<sqlQuery name="SQL1" dataSource="CompanyDB">
			<sqlText>SELECT 
	* 
FROM 
	fn_MP_CustomFields_JobCode (#sq ($account.parameters.UserContextID)#, #sq ($account.parameters.USER_TYPE)#, NULL,NULL)</sqlText>
		<mdProjectedItems><mdProjectedItem name="JbcJobCode"/><mdProjectedItem name="Job_JobClass"/></mdProjectedItems></sqlQuery></source>
			<selection><dataItem name="JbcJobCode"><expression>trim([SQL1].[JbcJobCode])</expression></dataItem><dataItem name="Job_JobClass"><expression>[SQL1].[Job_JobClass]</expression></dataItem></selection>
		</query><query name="Main Query">
			<source>
				
			<joinOperation>
			<joinOperands>
				<joinOperand cardinality="1:1"><queryRef refQuery="EE + Location"/></joinOperand>
				<joinOperand cardinality="0:1"><queryRef refQuery="PCF (Job)"/></joinOperand>
			</joinOperands>
			<joinFilter>
				<filterExpression>[EE + Location].[Job Code] = [PCF (Job)].[JbcJobCode]</filterExpression>
			</joinFilter>
		</joinOperation></source>
			<selection><dataItem name="AuditKey"><expression>[EE + Location].[AuditKey]</expression></dataItem><dataItem name="AuditKey_EmpComp"><expression>[EE + Location].[AuditKey_EmpComp]</expression></dataItem><dataItem name="AuditKey_EmpPers"><expression>[EE + Location].[AuditKey_EmpPers]</expression></dataItem><dataItem name="EjhEEID" rollupAggregate="none" aggregate="none" sort="ascending"><expression>[EE + Location].[EjhEEID]</expression></dataItem><dataItem name="EjhCoID" rollupAggregate="none" aggregate="none" sort="ascending"><expression>[EE + Location].[EjhCoID]</expression></dataItem><dataItem name="Effective Date" rollupAggregate="none" aggregate="none" sort="descending"><expression>[EE + Location].[Effective Date]</expression></dataItem><dataItem name="EjhDateTimeCreated" rollupAggregate="none" aggregate="none" sort="descending"><expression>[EE + Location].[EjhDateTimeCreated]</expression></dataItem><dataItem name="Employee Number" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Employee Number]</expression></dataItem><dataItem name="Last Name" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Last Name]</expression></dataItem><dataItem name="First Name" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[First Name]</expression></dataItem><dataItem name="Preferred First Name" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Preferred First Name]</expression></dataItem><dataItem name="Gender Code" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Gender Code]</expression></dataItem><dataItem name="Gender" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Gender]</expression></dataItem><dataItem name="Original Hire Date" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Original Hire Date]</expression></dataItem><dataItem name="Last Hire Date" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Last Hire Date]</expression></dataItem><dataItem name="Termination Date" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Termination Date]</expression></dataItem><dataItem name="SSN (Formatted)" rollupAggregate="none" aggregate="none" label="SSN"><expression>[EE + Location].[SSN (Formatted)]</expression></dataItem><dataItem name="Email Address" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Email Address]</expression></dataItem><dataItem name="Home Phone (Formatted)" rollupAggregate="none" aggregate="none" label="Home Phone"><expression>[EE + Location].[Home Phone (Formatted)]</expression></dataItem><dataItem name="Work Phone (Formatted)" rollupAggregate="none" aggregate="none" label="Work Phone"><expression>[EE + Location].[Work Phone (Formatted)]</expression></dataItem><dataItem name="Phone Number (Formatted)" rollupAggregate="none" aggregate="none" label="Alternate Phone"><expression>[EE + Location].[Phone Number (Formatted)]</expression></dataItem><dataItem name="Supervisor Employee Number (History)" rollupAggregate="none" aggregate="none" label="Supervisor EE #"><expression>[EE + Location].[Supervisor Employee Number (History)]</expression></dataItem><dataItem name="Supervisor Name (History)" rollupAggregate="none" aggregate="none" label="Supervisor Name"><expression>[EE + Location].[Supervisor Name (History)]</expression></dataItem><dataItem name="OrgLevel2Desc" rollupAggregate="none" aggregate="none" label="Cost Center (Desc)"><expression>[EE + Location].[OrgLevel2Desc]</expression></dataItem><dataItem name="EjhOrgLvl2" rollupAggregate="none" aggregate="none" label="Cost Center (Code)"><expression>[EE + Location].[EjhOrgLvl2]</expression></dataItem><dataItem name="Cost Center (Full)" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Cost Center (Full)]</expression></dataItem><dataItem name="Company Code" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Company Code]</expression></dataItem><dataItem name="Job Code" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Job Code]</expression></dataItem><dataItem name="Job" rollupAggregate="none" aggregate="none" label="Job Title"><expression>[EE + Location].[Job]</expression></dataItem><dataItem name="Employee Status Code" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Employee Status Code]</expression></dataItem><dataItem name="Employee Status" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Employee Status]</expression></dataItem><dataItem name="Salary/Hourly Code" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Salary/Hourly Code]</expression></dataItem><dataItem name="Salary/Hourly" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Salary/Hourly]</expression></dataItem><dataItem name="Exempt_Emp" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Exempt_Emp]</expression></dataItem><dataItem name="OrgLevel1Desc" rollupAggregate="none" aggregate="none" label="Company Brand"><expression>[EE + Location].[OrgLevel1Desc]</expression></dataItem><dataItem name="Hourly Pay Rate" rollupAggregate="none" aggregate="none"><expression>if ([EE + Location].[Salary/Hourly Code] = 'S') then ([EE + Location].[Hourly Pay Rate] * 0)
else
([EE + Location].[Hourly Pay Rate] *1)

</expression></dataItem><dataItem name="Salary Grade Code" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Salary Grade Code]</expression></dataItem><dataItem name="Salary Grade" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Salary Grade]</expression></dataItem><dataItem name="Location Name" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Location Name]</expression></dataItem><dataItem name="Location Code" rollupAggregate="none" aggregate="none"><expression>[EE + Location].[Location Code]</expression></dataItem><dataItem name="City" rollupAggregate="none" aggregate="none" label="Location City"><expression>[EE + Location].[City]</expression></dataItem><dataItem name="State/Province Code" rollupAggregate="none" aggregate="none" label="Location State"><expression>[EE + Location].[State/Province Code]</expression></dataItem><dataItem name="Zip/Postal Code" rollupAggregate="none" aggregate="none" label="Location Zip"><expression>[EE + Location].[Zip/Postal Code]</expression></dataItem><dataItem name="Job_JobClass" rollupAggregate="none" aggregate="none" label="Job Class"><expression>[PCF (Job)].[Job_JobClass]</expression></dataItem><dataItem name="Middle Name"><expression>[EE + Location].[Middle Name]</expression></dataItem><dataItem name="Date Of Birth"><expression>[EE + Location].[Date Of Birth]</expression></dataItem><dataItem name="OrgLevel1Desc1"><expression>[EE + Location].[OrgLevel1Desc]</expression></dataItem><dataItem name="Employee Type"><expression>[EE + Location].[Employee Type]</expression></dataItem><dataItem name="Address 1"><expression>[EE + Location].[Address 1]</expression></dataItem><dataItem name="Address 2"><expression>[EE + Location].[Address 2]</expression></dataItem><dataItem name="City1"><expression>[EE + Location].[City1]</expression></dataItem><dataItem name="State/Province1"><expression>[EE + Location].[State/Province1]</expression></dataItem><dataItem name="Zip Code1"><expression>[EE + Location].[Zip Code1]</expression></dataItem><dataItem name="Org Level 1"><expression>[EE + Location].[Org Level 1 Code]</expression></dataItem><dataItem name="Org Level 2"><expression>[EE + Location].[Org Level 2]</expression></dataItem><dataItem name="Org Level 2 Code"><expression>[EE + Location].[Org Level 2 Code]</expression></dataItem><dataItem name="EecUDField12"><expression>[EE + Location].[EecUDField12]</expression></dataItem><dataItem name="EecEEID"><expression>[EE + Location].[EecEEID]</expression></dataItem><dataItem name="Org Level 2 Code1" label="Department Code"><expression>substring([EE + Location].[Org Level 2 Code1],1,7)</expression></dataItem><dataItem name="Timeclock Code" label="Badge Id"><expression>[EE + Location].[Timeclock Code]</expression></dataItem><dataItem name="Work Phone"><expression>[EE + Location].[Work Phone]</expression></dataItem><dataItem name="Work Extension"><expression>[EE + Location].[Work Extension]</expression></dataItem><dataItem name="OK To Rehire"><expression>[EE + Location].[OK To Rehire]</expression></dataItem></selection>
		</query></queries>
				<layouts>
					<layout>
						<reportPages>
							<page name="Page1"><style><defaultStyles><defaultStyle refStyle="pg"/></defaultStyles></style>
								<pageBody><style><defaultStyles><defaultStyle refStyle="pb"/></defaultStyles></style>
									<contents>
										<list horizontalPagination="true" name="List1" refQuery="Main Query">
											
											
											
											<noDataHandler>
												<contents>
													<block>
														<contents>
															<textItem>
																<dataSource>
																	<staticValue>No Data Available</staticValue>
																</dataSource>
																<style>
																	<CSS value="padding:10px 18px;"/>
																</style>
															</textItem>
														</contents>
													</block>
												</contents>
											</noDataHandler>
											<style>
												<defaultStyles>
													<defaultStyle refStyle="ls"/>
												</defaultStyles>
												<CSS value="border-collapse:collapse"/>
											</style>
										<listColumns><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employee Number"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Employee Number"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 2"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 2"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 2 Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 2 Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="EecUDField12"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="EecUDField12"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="OrgLevel1Desc1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="OrgLevel1Desc1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Last Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Last Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="First Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="First Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Middle Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Middle Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Preferred First Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Preferred First Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Gender Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Gender Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Original Hire Date"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Original Hire Date"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Last Hire Date"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Last Hire Date"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Termination Date"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Termination Date"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="SSN (Formatted)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="SSN (Formatted)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Email Address"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Email Address"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Home Phone (Formatted)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Home Phone (Formatted)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Work Phone (Formatted)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Work Phone (Formatted)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Phone Number (Formatted)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Phone Number (Formatted)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Address 1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Address 1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Address 2"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Address 2"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="City1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="City1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="State/Province1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="State/Province1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Zip Code1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Zip Code1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Supervisor Employee Number (History)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Supervisor Employee Number (History)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Supervisor Name (History)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Supervisor Name (History)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="OrgLevel2Desc"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="OrgLevel2Desc"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="EjhOrgLvl2"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="EjhOrgLvl2"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Cost Center (Full)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Cost Center (Full)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Company Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Company Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employee Status Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Employee Status Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Salary/Hourly Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Salary/Hourly Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Exempt_Emp"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Exempt_Emp"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="OrgLevel1Desc"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="OrgLevel1Desc"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Hourly Pay Rate"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Hourly Pay Rate"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Date Of Birth"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Date Of Birth"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Salary Grade"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Salary Grade"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Location Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Location Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Location Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Location Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="City"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="City"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="State/Province Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="State/Province Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Zip/Postal Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Zip/Postal Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employee Type"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Employee Type"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job_JobClass"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lc"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Job_JobClass"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="EecEEID"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="EecEEID"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="AuditKey"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="AuditKey"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="AuditKey_EmpComp"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="AuditKey_EmpComp"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="AuditKey_EmpPers"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="AuditKey_EmpPers"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 2 Code1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 2 Code1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Timeclock Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Timeclock Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Work Phone"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Work Phone"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="Work Extension"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="Work Extension"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles></style><contents><textItem><dataSource><dataItemLabel refDataItem="OK To Rehire"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles></style><contents><textItem><dataSource><dataItemValue refDataItem="OK To Rehire"/></dataSource></textItem></contents></listColumnBody></listColumn></listColumns></list>
									</contents>
								</pageBody>
								
								
							<pageHeader>
									<contents>
										<block><style><defaultStyles><defaultStyle refStyle="ta"/></defaultStyles></style>
											<contents>
												<textItem><style><defaultStyles><defaultStyle refStyle="tt"/></defaultStyles></style>
													<dataSource>
														<staticValue>Employee Hires</staticValue>
													</dataSource>
												</textItem>
											</contents>
										</block>
									</contents>
									<style>
										<defaultStyles>
											<defaultStyle refStyle="ph"/>
										</defaultStyles>
										<CSS value="padding-bottom:10px"/>
									</style>
								</pageHeader><pageFooter>
									<contents>
										<table>
											<tableRows>
												<tableRow>
													<tableCells>
														<tableCell>
															<contents>
																<date>
																	<style>
																		<dataFormat>
																			<dateFormat/>
																		</dataFormat>
																	</style>
																</date>
															</contents>
															<style>
																<CSS value="vertical-align:top;text-align:left;width:25%"/>
															</style>
														</tableCell>
														<tableCell>
															<contents>
																<pageNumber/>
															</contents>
															<style>
																<CSS value="vertical-align:top;text-align:center;width:50%"/>
															</style>
														</tableCell>
														<tableCell>
															<contents>
																<time>
																	<style>
																		<dataFormat>
																			<timeFormat/>
																		</dataFormat>
																	</style>
																</time>
															</contents>
															<style>
																<CSS value="vertical-align:top;text-align:right;width:25%"/>
															</style>
														</tableCell>
													</tableCells>
												</tableRow>
											</tableRows>
											<style>
												<defaultStyles>
													<defaultStyle refStyle="tb"/>
												</defaultStyles>
												<CSS value="border-collapse:collapse;width:100%"/>
											</style>
										</table>
									</contents>
									<style>
										<defaultStyles>
											<defaultStyle refStyle="pf"/>
										</defaultStyles>
										<CSS value="padding-top:10px"/>
									</style>
								</pageFooter></page>
						</reportPages>
					</layout>
				</layouts>
			<XMLAttributes><XMLAttribute name="RS_CreateExtendedDataItems" value="true" output="no"/><XMLAttribute name="listSeparator" value="," output="no"/><XMLAttribute name="RS_modelModificationTime" value="2017-12-06T15:08:24.180Z" output="no"/></XMLAttributes><reportName>Employee Hires</reportName></report>