<report xmlns="http://developer.cognos.com/schemas/report/12.0/" expressionLocale="en"><!--RSU-SPC-0093 The report specification was upgraded from "http://developer.cognos.com/schemas/report/11.0/" to "http://developer.cognos.com/schemas/report/12.0/" at 2016-9-22. 9:15:21-->
  <!--RSU-SPC-0093 The report specification was upgraded from &amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;http://developer.cognos.com/schemas/report/10.0/&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot; to &amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;http://developer.cognos.com/schemas/report/11.0/&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot; at 2014-9-2. 14:3:11-->
  <!--RSU-SPC-0093 The report specification was upgraded from &amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;http://developer.cognos.com/schemas/report/6.0/&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot; to &amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;http://developer.cognos.com/schemas/report/10.0/&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot; at 2013-12-9. 11:54:42-->
  <modelPath>/content/folder[@name='UltiPro BI Content']/folder[@name='UltiPro BI for Core HR and Payroll']/folder[@name='_UltiPro Delivered Packages']/package[@name='Administrator Package']/model[@name='model']</modelPath>
  <drillBehavior/>
  <queries>
    <query name="Package Query">
      <source>
        <model/>
      </source>
      <selection>
        
        
        <dataItem aggregate="none" name="Employee Name (Last Suffix, First MI)" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Employee Name (Last Suffix, First MI)]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem>
        
        <dataItem aggregate="none" name="Company" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Company]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem>
        <dataItem aggregate="none" name="Job Group Code" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Job Group Code]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem>
        <dataItem aggregate="none" name="Job Group" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Job Group]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem>
      <dataItem aggregate="none" rollupAggregate="none" name="OK To Rehire"><expression>[Business Layer].[Employee].[OK To Rehire]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem name="Data Item1"><expression>'ADD'</expression></dataItem><dataItem name="Data Item2"><expression>' '</expression></dataItem><dataItem name="Data Item3"><expression>'Job Class'</expression></dataItem><dataItem name="Data Item4"><expression>' '</expression></dataItem><dataItem name="Data Item5"><expression>' '</expression></dataItem><dataItem name="Data Item6"><expression>' '</expression></dataItem><dataItem name="Data Item7"><expression>' '</expression></dataItem><dataItem name="Data Item8"><expression>' '</expression></dataItem><dataItem name="Data Item9"><expression>' '</expression></dataItem><dataItem name="Data Item10"><expression>' '</expression></dataItem><dataItem name="Data Item11"><expression>' '</expression></dataItem><dataItem name="Data Item12"><expression>' '</expression></dataItem><dataItem name="Data Item13"><expression>' '</expression></dataItem><dataItem name="Data Item14"><expression>' '</expression></dataItem><dataItem name="Data Item15"><expression>' '</expression></dataItem><dataItem name="Data Item16"><expression>' '</expression></dataItem><dataItem name="Data Item17"><expression>' '</expression></dataItem><dataItem name="Company Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Company Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Organization Levels].[Org Level 1]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem aggregate="none" name="Employee Number" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Employee Number]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem><dataItem name="Employment Status" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Employment Status]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="First Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[First Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Last Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Last Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Preferred First Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Preferred First Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Number (Supervisor)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Supervisor Information].[Employee Number (Supervisor)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 2" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Organization Levels].[Org Level 2]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem aggregate="none" name="Job Code" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Job Code]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem><dataItem name="Email Address" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Email Address]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Salary or Hourly" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Salary or Hourly]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="FLSA Type Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Jobs].[FLSA Type Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 3" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Organization Levels].[Org Level 3]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Location Code" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Locations].[Location Code]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Location Name" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Locations].[Location Name]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Org Level 4" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Organization Levels].[Org Level 4]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Address 1 (Location)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Locations].[Address 1 (Location)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="City (Location)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Locations].[City (Location)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="State (Location)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Locations].[State (Location)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Zip Code (Location)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Locations].[Zip Code (Location)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Employee Number (Supervisor)1" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Supervisor Information].[Employee Number (Supervisor)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Work Phone (Formatted)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Work Phone (Formatted)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Phone Number (Formatted)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee Additional Phones].[Phone Number (Formatted)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Home Phone (Formatted)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Home Phone (Formatted)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Termination Date" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Termination Date]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Last Hire Date" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Last Hire Date]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem name="Salary Grade" aggregate="none" rollupAggregate="none"><expression>'TST'</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Original Hire Date" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Original Hire Date]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem aggregate="none" name="Job Title" rollupAggregate="none">
          <expression>[Business Layer].[Employee].[Job Title]</expression>
          <XMLAttributes>
            <XMLAttribute name="RS_dataType" output="no" value="3"/>
            <XMLAttribute name="RS_dataUsage" output="no" value="attribute"/>
          </XMLAttributes>
        </dataItem><dataItem name="EecEEID" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[EecEEID]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="EecUDField12" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[EecUDField12]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="SSN (Formatted)" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[SSN (Formatted)]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="3" output="no"/><XMLAttribute name="RS_dataUsage" value="attribute" output="no"/></XMLAttributes></dataItem><dataItem name="Date Of Birth" aggregate="none" rollupAggregate="none"><expression>[Business Layer].[Employee].[Date Of Birth]</expression><XMLAttributes><XMLAttribute name="RS_dataType" value="4" output="no"/><XMLAttribute name="RS_dataUsage" value="identifier" output="no"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Timeclock Code"><expression>[Business Layer].[Employee].[Timeclock Code]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Org Level 2 Code"><expression>[Business Layer].[Employee].[Org Level 2 Code]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Org Level 2 Code1"><expression>[Business Layer].[Employee].[Org Level 2 Code]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Work Extension"><expression>[Business Layer].[Employee].[Work Extension]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="none" rollupAggregate="none" name="Work Phone"><expression>[Business Layer].[Employee].[Work Phone]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="3"/><XMLAttribute output="no" name="RS_dataUsage" value="attribute"/></XMLAttributes></dataItem><dataItem aggregate="average" name="Hourly Pay Rate"><expression>[Business Layer].[Current Employee Information].[Hourly Pay Rate]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="2"/><XMLAttribute output="no" name="RS_dataUsage" value="fact"/></XMLAttributes></dataItem><dataItem aggregate="total" name="Annual Salary"><expression>[Business Layer].[Employee].[Annual Salary]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="2"/><XMLAttribute output="no" name="RS_dataUsage" value="fact"/></XMLAttributes></dataItem><dataItem aggregate="total" name="Annual Salary (Converted)"><expression>[Business Layer].[Employee].[Annual Salary (Converted)]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="9"/><XMLAttribute output="no" name="RS_dataUsage" value="fact"/></XMLAttributes></dataItem><dataItem aggregate="average" name="Hourly Pay Rate1"><expression>[Business Layer].[Employee].[Hourly Pay Rate]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="2"/><XMLAttribute output="no" name="RS_dataUsage" value="fact"/></XMLAttributes></dataItem><dataItem aggregate="average" name="Hourly Pay Rate (Converted)"><expression>[Business Layer].[Employee].[Hourly Pay Rate (Converted)]</expression><XMLAttributes><XMLAttribute output="no" name="RS_dataType" value="9"/><XMLAttribute output="no" name="RS_dataUsage" value="fact"/></XMLAttributes></dataItem></selection>
    </query>
    <query name="Configurable Fields Query">
      <source>
        <sqlQuery dataSource="CompanyDB" name="Custom Fields SQL">
          <sqlText>SELECT 
	* 
FROM 
	fn_MP_CustomFields_JobCode (#sq ($account.parameters.UserContextID)#, #sq ($account.parameters.USER_TYPE)#, NULL,NULL)
</sqlText>
          
        <mdProjectedItems><mdProjectedItem name="JbcJobCode"/><mdProjectedItem name="Job_JobClass"/></mdProjectedItems></sqlQuery>
      </source>
      <selection>
        <dataItem name="JbcJobCode">
          <expression>[Custom Fields SQL].[JbcJobCode]</expression>
        </dataItem>
      <dataItem name="Job_JobClass"><expression>[Custom Fields SQL].[Job_JobClass]</expression></dataItem></selection>
    </query>
    <query name="Display Query">
      <source>
        <joinOperation>
          <joinOperands>
            <joinOperand cardinality="1:1">
              <queryRef refQuery="Package Query"/>
            </joinOperand>
            <joinOperand cardinality="0:1">
              <queryRef refQuery="Configurable Fields Query"/>
            </joinOperand>
          </joinOperands>
          <joinFilter>
            <filterExpression>[Package Query].[Job Code] = [Configurable Fields Query].[JbcJobCode]</filterExpression>
          </joinFilter>
        </joinOperation>
      </source>
      <selection>
        <dataItem name="Job Code" label="JOB_CODE">
          <expression>[Package Query].[Job Code]</expression>
        </dataItem>
        
        
        <dataItem name="Employee Number" label="EMPLOYEEID">
          <expression>[Package Query].[Employee Number]</expression>
        </dataItem>
        
        
        
      <dataItem name="Job_JobClass" label="JOB_CLASS"><expression>[Configurable Fields Query].[Job_JobClass]</expression></dataItem><dataItem name="OK To Rehire"><expression>[Package Query].[OK To Rehire]</expression></dataItem><dataItem name="Data Item18"><expression>[Package Query].[Data Item17]</expression></dataItem><dataItem name="Data Item17" label="EMAIL_TYPE"><expression>[Package Query].[Data Item16]</expression></dataItem><dataItem name="Data Item16" label="MailNickName"><expression>[Package Query].[Data Item15]</expression></dataItem><dataItem name="Data Item15" label="MailBoxGroup"><expression>[Package Query].[Data Item14]</expression></dataItem><dataItem name="Data Item14" label="FIMStatus"><expression>[Package Query].[Data Item13]</expression></dataItem><dataItem name="Data Item13" label="EMPLOYEE_REFID"><expression>[Package Query].[Data Item12]</expression></dataItem><dataItem name="Data Item12" label="SUPERVISOR_REFCD"><expression>[Package Query].[Data Item11]</expression></dataItem><dataItem name="Data Item11" label="DefaultPWD"><expression>[Package Query].[Data Item10]</expression></dataItem><dataItem name="Data Item10" label="EMPLOYEE_TYPE"><expression>[Package Query].[Data Item9]</expression></dataItem><dataItem name="Data Item8" label="EDCP"><expression>[Package Query].[Data Item7]</expression></dataItem><dataItem name="Data Item7" label="MANAGEMENT_TYPE"><expression>[Package Query].[Data Item6]</expression></dataItem><dataItem name="Data Item6" label="PROCESS_LEVEL"><expression>[Package Query].[Data Item5]</expression></dataItem><dataItem name="Data Item5" label="User_OU"><expression>[Package Query].[Data Item4]</expression></dataItem><dataItem name="Data Item4" label="dnRequested"><expression>' '</expression></dataItem><dataItem name="Data Item2" label="NETWORK_ID"><expression>[Package Query].[Data Item2]</expression></dataItem><dataItem name="Data Item1" label="ACTION_CODE"><expression>[Package Query].[Data Item1]</expression></dataItem><dataItem name="Job Title1" label="JOB_DESCRIPTION"><expression>[Package Query].[Job Title]</expression></dataItem><dataItem name="Original Hire Date" label="HIRE_DATE"><expression>[Package Query].[Original Hire Date]</expression></dataItem><dataItem name="Salary Grade" label="GRADE"><expression>[Package Query].[Salary Grade]</expression></dataItem><dataItem name="Last Hire Date" label="ADJ_HIRE_DATE"><expression>[Package Query].[Last Hire Date]</expression></dataItem><dataItem name="Termination Date" label="TERM_DATE"><expression>[Package Query].[Termination Date]</expression></dataItem><dataItem name="Home Phone (Formatted)" label="HOME_PHONE"><expression>[Package Query].[Home Phone (Formatted)]</expression></dataItem><dataItem name="Phone Number (Formatted)" label="ALTERNATE_PHONE"><expression>[Package Query].[Phone Number (Formatted)]</expression></dataItem><dataItem name="Work Phone (Formatted)" label="WORK_PHONE"><expression>[Package Query].[Work Phone (Formatted)]</expression></dataItem><dataItem name="Employee Number (Supervisor)1" label="SUPERVISOR"><expression>[Package Query].[Employee Number (Supervisor)1]</expression></dataItem><dataItem name="Zip Code (Location)" label="ZIP_CODE"><expression>[Package Query].[Zip Code (Location)]</expression></dataItem><dataItem name="State (Location)" label="STATE_CODE"><expression>[Package Query].[State (Location)]</expression></dataItem><dataItem name="City (Location)" label="CITY"><expression>[Package Query].[City (Location)]</expression></dataItem><dataItem name="Address 1 (Location)" label="ADDR1"><expression>[Package Query].[Address 1 (Location)]</expression></dataItem><dataItem name="Org Level 4" label="DEPARTMENT"><expression>[Package Query].[Org Level 2]</expression></dataItem><dataItem name="Location Name" label="LOCAT_NAME"><expression>[Package Query].[Location Name]</expression></dataItem><dataItem name="Location Code" label="LOCATION"><expression>[Package Query].[Location Code]</expression></dataItem><dataItem name="Org Level 3" label="COST_CENTER"><expression>left ([Package Query].[Org Level 2], 7)</expression></dataItem><dataItem name="FLSA Type Code" label="EXEMPT_EMP"><expression>IF ([Package Query].[FLSA Type Code] = 'E') THEN
('Y')
ELSE
('N')</expression></dataItem><dataItem name="Salary or Hourly" label="SALARY_CLASS"><expression>[Package Query].[Salary or Hourly]</expression></dataItem><dataItem name="Email Address" label="EMAIL_ADDRESS"><expression>[Package Query].[Email Address]</expression></dataItem><dataItem name="Job Code1"><expression>[Package Query].[Job Code]</expression></dataItem><dataItem name="Org Level 2" label="DEPARTMENT_CD"><expression>substring ([Package Query].[Org Level 2], 10,50)</expression></dataItem><dataItem name="Employee Number (Supervisor)" label="SUPERVISOR_CD"><expression>[Package Query].[Employee Number (Supervisor)]</expression></dataItem><dataItem name="Preferred First Name" label="NICK_NAME"><expression>[Package Query].[Preferred First Name]</expression></dataItem><dataItem name="Last Name1"><expression>[Package Query].[Last Name]</expression></dataItem><dataItem name="Last Name" label="LAST_NAME"><expression>[Package Query].[Last Name]</expression></dataItem><dataItem name="First Name" label="FIRST_NAME"><expression>[Package Query].[First Name]</expression></dataItem><dataItem name="Employment Status" label="EMP_STATUS"><expression>[Package Query].[Employment Status]</expression></dataItem><dataItem name="Employee Number1"><expression>[Package Query].[Employee Number]</expression></dataItem><dataItem name="Org Level 1" label="COMPANY"><expression>[Package Query].[Org Level 1]</expression></dataItem><dataItem name="Company Code" label="COMPANYBRAND"><expression>[Package Query].[Company Code]</expression></dataItem><dataItem name="EecEEID"><expression>[Package Query].[EecEEID]</expression></dataItem><dataItem name="SSN (Formatted)"><expression>[Package Query].[SSN (Formatted)]</expression></dataItem><dataItem name="Date Of Birth"><expression>[Package Query].[Date Of Birth]</expression></dataItem><dataItem name="EecUDField12" label="DOMAIN"><expression>[Package Query].[EecUDField12]</expression></dataItem><dataItem name="Timeclock Code" label="Badge Id"><expression>[Package Query].[Timeclock Code]</expression></dataItem><dataItem name="Org Level 2 Code" label="Cost Center"><expression>[Package Query].[Org Level 2 Code]</expression></dataItem><dataItem name="Org Level 2 Code1" label="Department Code"><expression>substring([Package Query].[Org Level 2 Code1],1,7)</expression></dataItem><dataItem name="Work Extension"><expression>[Package Query].[Work Extension]</expression></dataItem><dataItem name="Work Phone"><expression>[Package Query].[Work Phone]</expression></dataItem></selection>
    </query>
  </queries>
  <layouts>
    <layout>
      <reportPages>
        <page name="Page1">
          <style>
            <defaultStyles>
              <defaultStyle refStyle="pg"/>
            </defaultStyles>
          </style>
          <pageBody>
            <style>
              <defaultStyles>
                <defaultStyle refStyle="pb"/>
              </defaultStyles>
            </style>
            <contents>
              <list horizontalPagination="true" name="List1" refQuery="Display Query">
                <style>
                  <CSS value="border-collapse:collapse"/>
                  <defaultStyles>
                    <defaultStyle refStyle="ls"/>
                  </defaultStyles>
                </style>
                <listColumns>
                  <listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="OK To Rehire"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="OK To Rehire"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Company Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Company Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="EecEEID"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="EecEEID"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employee Number"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Employee Number"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employment Status"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Employment Status"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item2"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item2"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="First Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="First Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Last Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Last Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Preferred First Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Preferred First Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Date Of Birth"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Date Of Birth"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="SSN (Formatted)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="SSN (Formatted)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employee Number (Supervisor)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Employee Number (Supervisor)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 2"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 2"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn>
                    <listColumnTitle>
                      <style>
                        <defaultStyles>
                          <defaultStyle refStyle="lt"/>
                        </defaultStyles>
                        <CSS value="font-weight:normal;text-decoration:none"/>
                      </style>
                      <contents>
                        <textItem>
                          <dataSource>
                            <dataItemLabel refDataItem="Job Code"/>
                          </dataSource>
                        </textItem>
                      </contents>
                    </listColumnTitle>
                    <listColumnBody>
                      <style>
                        <defaultStyles>
                          <defaultStyle refStyle="lm"/>
                        </defaultStyles>
                        <CSS value="text-align:left"/>
                      </style>
                      <contents>
                        <textItem>
                          <dataSource>
                            <dataItemValue refDataItem="Job Code"/>
                          </dataSource>
                        </textItem>
                      </contents>
                      
                    </listColumnBody>
                  </listColumn>
                  <listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job_JobClass"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Job_JobClass"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item4"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item4"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item5"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item5"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Email Address"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Email Address"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Salary or Hourly"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Salary or Hourly"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="FLSA Type Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="FLSA Type Code"/></dataSource></textItem></contents></listColumnBody></listColumn>
                  
                  
                  
                  <listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 3"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 3"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Location Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Location Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Location Name"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Location Name"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 4"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 4"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item6"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item6"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item7"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item7"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Address 1 (Location)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Address 1 (Location)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="City (Location)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="City (Location)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="State (Location)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="State (Location)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Zip Code (Location)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Zip Code (Location)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Employee Number (Supervisor)1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Employee Number (Supervisor)1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item8"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item8"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Work Phone (Formatted)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Work Phone (Formatted)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Phone Number (Formatted)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Phone Number (Formatted)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Home Phone (Formatted)"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Home Phone (Formatted)"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="EecUDField12"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="EecUDField12"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item10"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item10"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item11"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item11"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item12"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item12"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item13"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item13"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item14"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item14"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item15"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item15"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item16"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item16"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Termination Date"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Termination Date"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Last Hire Date"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Last Hire Date"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Salary Grade"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Salary Grade"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Data Item17"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Data Item17"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Original Hire Date"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Original Hire Date"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Job Title1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Job Title1"/></dataSource></textItem></contents></listColumnBody></listColumn>
                  
                <listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 2 Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 2 Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Org Level 2 Code1"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Org Level 2 Code1"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Timeclock Code"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Timeclock Code"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Work Phone"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Work Phone"/></dataSource></textItem></contents></listColumnBody></listColumn><listColumn><listColumnTitle><style><defaultStyles><defaultStyle refStyle="lt"/></defaultStyles><CSS value="font-weight:normal;text-decoration:none"/></style><contents><textItem><dataSource><dataItemLabel refDataItem="Work Extension"/></dataSource></textItem></contents></listColumnTitle><listColumnBody><style><defaultStyles><defaultStyle refStyle="lm"/></defaultStyles><CSS value="text-align:left"/></style><contents><textItem><dataSource><dataItemValue refDataItem="Work Extension"/></dataSource></textItem></contents></listColumnBody></listColumn></listColumns>
                
                
              </list>
            </contents>
          </pageBody>
          <pageHeader>
            <contents>
              <block>
                <style>
                  <defaultStyles>
                    <defaultStyle refStyle="ta"/>
                  </defaultStyles>
                </style>
                <contents>
                  <textItem>
                    <style>
                      <defaultStyles>
                        <defaultStyle refStyle="tt"/>
                      </defaultStyles>
                    </style>
                    <dataSource>
                      <staticValue>FIM Report</staticValue>
                    </dataSource>
                  </textItem>
                </contents>
              </block>
            </contents>
            <style>
              <defaultStyles>
                <defaultStyle refStyle="ph"/>
              </defaultStyles>
              <CSS value="padding-bottom:10px"/>
            </style>
          </pageHeader>
          
        </page>
      </reportPages>
    </layout>
  </layouts>
  <XMLAttributes>
    <XMLAttribute name="RS_CreateExtendedDataItems" output="no" value="true"/>
    <XMLAttribute name="listSeparator" output="no" value=","/>
    <XMLAttribute name="RS_modelModificationTime" output="no" value="2017-12-06T15:08:24.180Z"/>
  </XMLAttributes>
  <reportName>FIM Report</reportName>
</report>