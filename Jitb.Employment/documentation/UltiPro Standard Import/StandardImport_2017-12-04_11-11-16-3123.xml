﻿<?xml version="1.0" encoding="utf-8"?>
<Transactions>
  <Transaction>
    <Header>
      <TransactionType>HIRE</TransactionType>
      <IdentifierRule>SSN</IdentifierRule>
      <ApprovalRule>FORCEAUTO</ApprovalRule>
    </Header>
    <KeyFields>
      <CompanyCode>JACK</CompanyCode>
      <Identifier>*********</Identifier>
    </KeyFields>
    <Personal>
      <NameFirst>Jane</NameFirst>
      <NameLast>Doe</NameLast>
      <NameMiddle></NameMiddle>
      <NamePreferred>Jane</NamePreferred>
      <GenderCode>F</GenderCode>
      <DateOfBirth>12/13/1956</DateOfBirth>
      <PhoneHomeNumber>7019554506</PhoneHomeNumber>
      <MaritalStatusCode>Z</MaritalStatusCode>
      <EthnicId>1</EthnicId>
    </Personal>
    <PrimaryAddress>
      <AddressLine1>213 Schlosser Ave</AddressLine1>
      <AddressLine2></AddressLine2>
      <AddressCity>Mandan</AddressCity>
      <AddressStateCode>ND</AddressStateCode>
      <AddressZipCode>58554</AddressZipCode>
      <AddressCountryCode>USA</AddressCountryCode>
    </PrimaryAddress>
    <Status>
      <DateOfLastHire>12/02/2017</DateOfLastHire>
      <DateOfSeniority>12/02/2017</DateOfSeniority>
      <DateOfOriginalHire>08/21/2017</DateOfOriginalHire>
      <DateOfBenefitSeniority>12/02/2017</DateOfBenefitSeniority>
    </Status>
    <JobInformation>
      <JobCode>QDRH60</JobCode>
      <OrgLevel1Code>QDOBA</OrgLevel1Code>
      <OrgLevel2Code>Q02414</OrgLevel2Code>
      <LocationCode>002414</LocationCode>
      <PayGroup>HRLY</PayGroup>
      <ScheduledWorkHours>60</ScheduledWorkHours>
      <EarningsGroupCode>TEAM</EarningsGroupCode>
      <BenefitGroupCode>NOBEN</BenefitGroupCode>
      <EmployeeTypeCode>REG</EmployeeTypeCode>
      <SalaryOrHourly>H</SalaryOrHourly>
      <FullOrPartTimeCode>P</FullOrPartTimeCode>
      <HourlyPayRate>13.00</HourlyPayRate>
      <ShiftCode>Z</ShiftCode>
      <IsAutopaid>N</IsAutopaid>
    </JobInformation>
    <USTaxes>
      <FederalFilingStatusCode>S</FederalFilingStatusCode>
      <FederalTotalAllowances>0</FederalTotalAllowances>
      <FederalAdditionalAmountWithheld>0</FederalAdditionalAmountWithheld>
      <FederalEmployeeClaimsExemption>N</FederalEmployeeClaimsExemption>
      <WorkInFilingStatusCode>S</WorkInFilingStatusCode>
      <WorkInTotalAllowances>0</WorkInTotalAllowances>
      <WorkInAdditionalAllowances>0</WorkInAdditionalAllowances>
      <WorkInAdditionalAmountWithheld>0</WorkInAdditionalAmountWithheld>
      <WorkInEmployeeClaimsExemption>N</WorkInEmployeeClaimsExemption>
      <ResidentFilingStatusCode>S</ResidentFilingStatusCode>
      <ResidentTotalAllowances>0</ResidentTotalAllowances>
      <ResidentAdditionalAllowances>0</ResidentAdditionalAllowances>
      <ResidentAdditionalAmountWithheld>0</ResidentAdditionalAmountWithheld>
      <ResidentEmployeeClaimsExemption>N</ResidentEmployeeClaimsExemption>
    </USTaxes>
    <DirectDeposit>
      <DDRoutingNumber>*********</DDRoutingNumber>
      <DDAccountNumber>329177</DDAccountNumber>
      <DDAccountTypeCode>C</DDAccountTypeCode>
      <DDDepositRuleCode>A</DDDepositRuleCode>
    </DirectDeposit>
  </Transaction>
</Transactions>