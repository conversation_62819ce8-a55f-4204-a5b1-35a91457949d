﻿<?xml version="1.0" encoding="utf-8"?>
<Transactions>
  <Transaction>
    <Header>
      <TransactionType>HIRE</TransactionType>
      <IdentifierRule>SSN</IdentifierRule>
      <ApprovalRule>FORCEAUTO</ApprovalRule>
    </Header>
    <KeyFields>
      <CompanyCode>JACK</CompanyCode>
    </KeyFields>
    <Personal>
      <NameFirst>Joe</NameFirst>
      <NameLast>Dirt</NameLast>
      <NameMiddle></NameMiddle>
      <NamePreferred>Joe</NamePreferred>
      <GenderCode>M</GenderCode>
      <DateOfBirth>10/13/1989</DateOfBirth>
      <PhoneHomeNumber>6177687192</PhoneHomeNumber>
      <MaritalStatusCode>Z</MaritalStatusCode>
      <EthnicId>3</EthnicId>
    </Personal>
    <PrimaryAddress>
      <AddressLine1>19 Vinal Street</AddressLine1>
      <AddressLine2></AddressLine2>
      <AddressCity>Revere</AddressCity>
      <AddressStateCode>MA</AddressStateCode>
      <AddressZipCode>02151</AddressZipCode>
      <AddressCountryCode>USA</AddressCountryCode>
    </PrimaryAddress>
    <Status>
      <DateOfLastHire>12/04/2017</DateOfLastHire>
      <DateOfSeniority>12/04/2017</DateOfSeniority>
      <DateOfOriginalHire>12/04/2017</DateOfOriginalHire>
      <DateOfBenefitSeniority>12/04/2017</DateOfBenefitSeniority>
    </Status>
    <JobInformation>
      <JobCode>QDRH65</JobCode>
      <OrgLevel1Code>QDOBA</OrgLevel1Code>
      <OrgLevel2Code>Q01739</OrgLevel2Code>
      <LocationCode>001739</LocationCode>
      <PayGroup>HRLY</PayGroup>
      <ScheduledWorkHours>60</ScheduledWorkHours>
      <EarningsGroupCode>TEAM</EarningsGroupCode>
      <BenefitGroupCode>NOBEN</BenefitGroupCode>
      <EmployeeTypeCode>REG</EmployeeTypeCode>
      <SalaryOrHourly>H</SalaryOrHourly>
      <FullOrPartTimeCode>P</FullOrPartTimeCode>
      <HourlyPayRate>12.00</HourlyPayRate>
      <ShiftCode>Z</ShiftCode>
      <IsAutopaid>N</IsAutopaid>
    </JobInformation>
    <ID>
      <SSN>*********</SSN>
    </ID>
    <USTaxes>
      <FederalFilingStatusCode>S</FederalFilingStatusCode>
      <FederalTotalAllowances>1</FederalTotalAllowances>
      <FederalAdditionalAmountWithheld>0</FederalAdditionalAmountWithheld>
      <FederalEmployeeClaimsExemption>N</FederalEmployeeClaimsExemption>
      <WorkInFilingStatusCode>S</WorkInFilingStatusCode>
      <WorkInTotalAllowances>0</WorkInTotalAllowances>
      <WorkInAdditionalAllowances>0</WorkInAdditionalAllowances>
      <WorkInAdditionalAmountWithheld>0</WorkInAdditionalAmountWithheld>
      <WorkInEmployeeClaimsExemption>N</WorkInEmployeeClaimsExemption>
      <ResidentFilingStatusCode>S</ResidentFilingStatusCode>
      <ResidentTotalAllowances>0</ResidentTotalAllowances>
      <ResidentAdditionalAllowances>0</ResidentAdditionalAllowances>
      <ResidentAdditionalAmountWithheld>0</ResidentAdditionalAmountWithheld>
      <ResidentEmployeeClaimsExemption>N</ResidentEmployeeClaimsExemption>
    </USTaxes>
    <DirectDeposit>
      <DDRoutingNumber>0257984</DDRoutingNumber>
      <DDAccountNumber>************</DDAccountNumber>
      <DDAccountTypeCode>C</DDAccountTypeCode>
      <DDDepositRuleCode>A</DDDepositRuleCode>
    </DirectDeposit>
  </Transaction>
</Transactions>