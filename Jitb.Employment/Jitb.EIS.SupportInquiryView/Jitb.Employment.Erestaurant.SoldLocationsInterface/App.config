﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<configuration>
  <configSections>
    <section name="nlog" type="NLog.Config.ConfigSection<PERSON>andler, NLog" />
    <section name="Logging" type="NServiceBus.Config.Logging,NServiceBus.Core" />

    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <system.transactions>
    <defaultSettings timeout="00:10:00" />
  </system.transactions>
  <connectionStrings configSource="ConnectionStrings.config" />
  <!--
  -->
  <!--
  <connectionStrings>
    <add name="NServiceBus/Persistence" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNSB;integrated security=false;user id=NServiceBus;password=**********;enlist=false;"/>
    <add name="Default" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNServiceBus;integrated security=false;user id=NServiceBus;password=**********;enlist=false;"/>
    <add name="ErestaurantIntegration" connectionString="Data Source=.;Initial Catalog=Jitb_Employment;Integrated Security=SSPI;enlist=false;" />
    <add name="LocationDb" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbLocation;integrated security=false;user id=NServiceBus;password=**********;enlist=false;"/>
    <add name="Sitecenter" connectionString="Data Source=.;Initial Catalog=Jitb_Employment;Integrated Security=SSPI;enlist=false;" />
    <add name="Config" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNServiceBus_Employment_Config;integrated security=false;user id=NServiceBus;password=**********;enlist=false;"/>
    <add name="CSNsbSqlAG-Lsnr" connectionString="data source=.;initial catalog=Jitb_Employment;integrated security=SSPI;enlist=false;" />
    <add name="JibLocationEntityModel" connectionString="Data source=.;initial catalog=jitb_employment;integrated security=true;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="HireEligibility" connectionString="Data Source=.;Initial Catalog=Jitb_Employment;Integrated Security=SSPI;enlist=false;" />
    <add name="Model_FIMFMADEMP_WPS" connectionString="data source=CSSQLT01V\vtest1;initial catalog=dbNServiceBus;integrated security=false;user id=NServiceBus;password=**********;enlist=false;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="ModelEmployeeAction" connectionString="data source=CSSQLT01V\vtest1;initial catalog=dbNServiceBus;integrated security=false;user id=NServiceBus;password=**********;enlist=false;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="Frantracker" connectionString="data source=cssqlt01v\vtest1;initial catalog=dbTracker;integrated security=false;user id=NServiceBus;password=**********;enlist=false;" />
  </connectionStrings>
  -->
  <Logging Threshold="Debug" />
  <appSettings>
    <add key="SendonlyEndpointAddress" value="SendInterfacesToErest" />
    <add key="nhibernate-logger" value="Jitb.CommonLibrary.NLogFactory, Jitb.CommonLibrary" />
    <add key="env" value="" />
    <add key="ServiceControl/Queue" value="particular.servicecontrol" />
    <add key="EncryptionEnabled" value="false" />
    <add key="ERestaurant/JackUrl" value="https://c3.xformity.com/?id=cbPost" />
    <add key="ERestaurant/QdobaUrl" value="https://c3.xformity.com/?id=cbPost" />
    <add key="ERestaurant/XAuth" value="1vlFbcb7Zy8mxzyiOuCQtzTmk8bi1pRh" />
    <add key="ERestaurant/QdobaConcept" value="qdoba.test" />
    <add key="ERestaurant/EncryptionEnabled" value="true" />
    <add key="ERestaurant/DecryptionEnabled" value="true" />
    <add key="ERestaurant/JackConcept" value="jib.test" />
    <add key="ERestaurant/PgpPassphrase" value="1nt3rP0|nyc" />
    <add key="ERestaurant/PgpPrivateKeyFile" value="PrivateKey.txt" />
    <add key="ERestaurant/PgpPublicKeyFile" value="PublicKey.txt" />
    <add key="ERestaurant/ProxyServer" value="http://csproxy.corp.jitb.net:9090" />
    <add key="ERestaurant/ProxyUser" value="nsbtest_svc" />
    <add key="ERestaurant/ProxyPassword" value="**********" />
    <add key="ERestaurant/ProxyDomain" value="corp" />
    <add key="NServiceBus/License" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&lt;license UpgradeProtectionExpiration=&quot;2017-03-02&quot; Applications=&quot;All&quot; Edition=&quot;Enterprise&quot; Quantity=&quot;3&quot; MaxMessageThroughputPerSecond=&quot;Max&quot; AllowedNumberOfWorkerNodes=&quot;Max&quot; WorkerThreads=&quot;Max&quot; LicenseVersion=&quot;5.0&quot; type=&quot;Standard&quot; expiration=&quot;2116-04-19T18:03:27.1278394&quot; id=&quot;3116f539-3039-4d9b-b402-9dffcc8692dc&quot;&gt;&lt;name&gt;Jack in the Box Inc.&lt;/name&gt;&lt;Signature xmlns=&quot;http://www.w3.org/2000/09/xmldsig#&quot;&gt;&lt;SignedInfo&gt;&lt;CanonicalizationMethod Algorithm=&quot;http://www.w3.org/TR/2001/REC-xml-c14n-20010315&quot;/&gt;&lt;SignatureMethod Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#rsa-sha1&quot;/&gt;&lt;Reference URI=&quot;&quot;&gt;&lt;Transforms&gt;&lt;Transform Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#enveloped-signature&quot;/&gt;&lt;/Transforms&gt;&lt;DigestMethod Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#sha1&quot;/&gt;&lt;DigestValue&gt;Q4DLcl52+SRajlhxJqUk4HbVzEo=&lt;/DigestValue&gt;&lt;/Reference&gt;&lt;/SignedInfo&gt;&lt;SignatureValue&gt;Y6EcGxepSX2omKPtn1psTRPhvhOVmY2Ja9XgVSVtrMwSlBERip96YD/Y2Dvs0nByuRb4qodvLJiIou3js9U4WQxrgOujfWPbYBiXJsrBIXXJTSQsaL/SHPe1cYsNNKGzBVZwur+PTR7+aUddgHJmk9IQ41WZnpzlu2n4YpUjDEA=&lt;/SignatureValue&gt;&lt;/Signature&gt;&lt;/license&gt;" />
    <add key="MessageFilters" value="ERS_EMPLOYEESRT,ERS_EMPLOYEES" />
    <add key="ProcessAnyMessage" value="false" />
    <add key="ERestaurant/LocationDaysBeforeOpen" value="21" />
    <add key="ERestaurant/RolloutComplete" value="No" />
    <add key="ERestaurant/DefaultLocation" value="999999" />
    <add key="ERestaurant/IsTestRun" value="Yes" />
    <add key="ERestaurant/TestLocations" value="13,20,23,29,31,32,42,74,76,90,97,168,178,450,3012,3019,3033,3040,3046,3050,3051,3069,3077,3081,3200,3239,3276,3369,5392,5407,9815,9826,9874,53,54,73,74,75,76,77,78,79,80" />
    <add key="ERestaurant/DefaultPassword" value="password" />
    <add key="ERestaurant/UserStoreAlignmentFilePath" value="C:\test\UserStoreAlignments" />
    <add key="RouteMapsFromDatabase" value="true" />
    <add key="RoutingBridge/Address" value="Jitb.Employment.RoutingBridge.Endpoint" />
    <add key="errorQueue" value="error" />
    <add key="auditQueue" value="audit" />
  </appSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="NServiceBus.Core" publicKeyToken="9fc386479f8a226c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="AutoMapper" publicKeyToken="be96cd2c38ef1005" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Renci.SshNet" publicKeyToken="1cee9f8bde3db106" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2016.1.0.0" newVersion="2016.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NServiceBus.Encryption.MessageProperty" publicKeyToken="9fc386479f8a226c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.0.0.0" newVersion="1.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NServiceBus.NHibernate" publicKeyToken="9fc386479f8a226c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NServiceBus.NLog" publicKeyToken="9fc386479f8a226c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Iesi.Collections" publicKeyToken="aa95f207798dfdb4" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.4000" newVersion="4.0.0.4000" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.4.1" newVersion="4.0.4.1" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
  </startup>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>
