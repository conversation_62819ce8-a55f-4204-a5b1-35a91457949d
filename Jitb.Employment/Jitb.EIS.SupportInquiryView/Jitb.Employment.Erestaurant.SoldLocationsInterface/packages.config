﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Afterman.nRepo" version="2019.8.15.1" targetFramework="net461" />
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net461" />
  <package id="AutoMapper" version="8.0.0" targetFramework="net461" />
  <package id="AWSSDK.Core" version="3.3.24.2" targetFramework="net461" />
  <package id="AWSSDK.S3" version="3.3.18.5" targetFramework="net461" />
  <package id="AWSSDK.SQS" version="3.3.3.10" targetFramework="net461" />
  <package id="BouncyCastle.OpenPgp" version="1.8.1.1" targetFramework="net461" />
  <package id="Castle.Core" version="4.3.1" targetFramework="net461" />
  <package id="FluentNHibernate" version="2.0.3.0" targetFramework="net461" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net461" />
  <package id="Jitb.Common.Contracts" version="2019.8.15.1" targetFramework="net461" />
  <package id="Jitb.CommonLibrary" version="2019.8.15.1" targetFramework="net461" />
  <package id="Jitb.NSB.Commons" version="2021.3.4.5" targetFramework="net461" />
  <package id="Jitb.SharedServices.Domain.Common" version="2019.8.15.2" targetFramework="net461" />
  <package id="log4net" version="2.0.8" targetFramework="net461" />
  <package id="Moq" version="4.10.1" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="12.0.1" targetFramework="net461" />
  <package id="NHibernate" version="4.0.4.4000" targetFramework="net461" />
  <package id="NLog" version="4.5.11" targetFramework="net461" />
  <package id="NServiceBus" version="7.3.0" targetFramework="net461" />
  <package id="NServiceBus.AmazonSQS" version="4.4.1" targetFramework="net461" />
  <package id="NServiceBus.Callbacks" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.CustomChecks" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.Encryption.MessageProperty" version="2.0.0" targetFramework="net461" />
  <package id="NServiceBus.Heartbeat" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.Log4Net" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.Newtonsoft.Json" version="2.2.0" targetFramework="net461" />
  <package id="NServiceBus.NHibernate" version="8.0.1" targetFramework="net461" />
  <package id="NServiceBus.NLog" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.Raw" version="3.2.1" targetFramework="net461" />
  <package id="NServiceBus.Router" version="3.8.1" targetFramework="net461" />
  <package id="NServiceBus.Router.Connector" version="3.8.1" targetFramework="net461" />
  <package id="NServiceBus.SagaAudit" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.StructureMap" version="7.0.0" targetFramework="net461" />
  <package id="NServiceBus.Transport.Msmq" version="1.0.1" targetFramework="net461" />
  <package id="Remotion.Linq" version="2.1.2" targetFramework="net461" />
  <package id="Remotion.Linq.EagerFetching" version="2.1.0" targetFramework="net461" />
  <package id="SSH.NET" version="2016.1.0" targetFramework="net461" />
  <package id="StructureMap" version="4.7.1" targetFramework="net461" />
  <package id="System.Data.SqlClient" version="4.4.3" targetFramework="net461" />
  <package id="System.Reflection.Emit" version="4.7.0" targetFramework="net461" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.2" targetFramework="net461" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.2" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net461" />
</packages>