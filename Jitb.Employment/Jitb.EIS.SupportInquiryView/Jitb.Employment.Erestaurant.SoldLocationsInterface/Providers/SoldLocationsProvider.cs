﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jitb.Employment.Erestaurant.SoldLocationsInterface.Providers
{
    using System.Configuration;
    using System.Xml;
    using CommonLibrary.Xml;
    using Domain.Integrations.Components.Erestaurant.Outgoing.Builders;
    using Domain.Repositories.Config;
    using Domain.Repositories.Employment;
    using SharedServices.Domain.Common.Concepts;
    using SharedServices.Domain.Common.Repositories.ErestaurantIntegration;
    using Jitb.Employment.Contracts.Commands.ErestaurantSync;
    using UserManagement.Erestaurant.OutgoingInterfaces.GlobalsAndSingletons;
    using NServiceBus;
    using UserManagement.Erestaurant.OutgoingInterfaces.Builders;

    public interface ISoldLocationsProvider
    {
        void ProcessSoldLocations();
    }
    public class SoldLocationsProvider : ISoldLocationsProvider
    {
        private readonly IERestaurantInterfaceJobRepository _eRestaurantInterfaceJobRepository;
        private readonly IInterfaceCodeRepository _interfaceCodeRepository;
        private readonly IJobInterfaceRepository _jobInterfaceRepository;
        private readonly IErestaurantPayloadRepository _erestaurantPayloadRepository;
        private readonly ILocationSiteRepository _locationSiteRepository;
        private readonly ICreateUserInterfaceBuilder _createUserInterfaceBuilder;

        public SoldLocationsProvider(IERestaurantInterfaceJobRepository eRestaurantInterfaceJobRepository
        , IInterfaceCodeRepository interfaceCodeRepository
        , IJobInterfaceRepository jobInterfaceRepository
        , IErestaurantPayloadRepository erestaurantPayloadRepository
        , ILocationSiteRepository locationSiteRepository
        , ICreateUserInterfaceBuilder createUserInterfaceBuilder)
        {
            this._eRestaurantInterfaceJobRepository = eRestaurantInterfaceJobRepository;
            this._interfaceCodeRepository = interfaceCodeRepository;
            this._jobInterfaceRepository = jobInterfaceRepository;
            this._erestaurantPayloadRepository = erestaurantPayloadRepository;
            this._locationSiteRepository = locationSiteRepository;
            this._createUserInterfaceBuilder = createUserInterfaceBuilder;

        }
        public async void ProcessSoldLocations()
        {
  
            var soldSitesCount = 0;

            var lastJob = _eRestaurantInterfaceJobRepository.GetLastCompletedJob();
            var locationCode = this._interfaceCodeRepository.FirstOrDefault(x => x.InterfaceType == "Location")
                .InterfaceCodeId;
            var interfaceRecord = this._jobInterfaceRepository.GetByJobAndInterface(lastJob, locationCode);
            var locationKey = interfaceRecord.ReportLogId;




            var locationInterfaceOld = this._erestaurantPayloadRepository.Get(locationKey);
            var document = new XmlDocument();
            var payload = locationInterfaceOld.Payload;
            document.LoadXml(payload);
            var pollNode = document.GetElementsByTagName("Poll")[0];
            pollNode.Attributes["date"].Value = DateTime.Now.ToString("yyyyMMdd");
            pollNode.Attributes["created"].Value = DateTime.Now.ToString("yyyy-MM-ddThh:mm:ss");
            var locationsXml = document.GetElementsByTagName("AHS");
            foreach (XmlNode item in locationsXml)
            {
                var location = int.Parse(item.Attributes["ds"].Value);
                var storeStatus = item.Attributes["ss"].Value;

                if (storeStatus != "CLOSED")
                {
                    var locationRecord = this._locationSiteRepository.Get(location);
                    var ownerId = int.Parse(item.Attributes["oid"].Value);
                    if (location != int.Parse(ConfigurationManager.AppSettings["ERestaurant/DummyLocation"]) &&
                        ownerId != locationRecord.Entity)
                    {
                        soldSitesCount++;
                        item.Attributes["ss"].Value = "CLOSED";
                    }
                }
            }

            if (soldSitesCount > 0)
            {
                var locationInterface = document.InnerXml;
                var userInterface = this._createUserInterfaceBuilder.CreateEmptyUserInterface("JA");

                // get the alignment record and resend it.
                var alignmentCode = this._interfaceCodeRepository.FirstOrDefault(x => x.InterfaceType == "Alignment")
                    .InterfaceCodeId;
                var alignmentRecord = this._jobInterfaceRepository.GetByJobAndInterface(lastJob, alignmentCode);
                var alignmentKey = alignmentRecord.ReportLogId;

                var alignmentInterface = this._erestaurantPayloadRepository.Get(alignmentKey);
                var alignmentInterfaceNew = this.ConvertAlignment(alignmentInterface);

                var locationInterfacePayload = new ErestaurantPayload
                {
                    Payload = locationInterface,
                    UnencryptedPayload = locationInterface,
                    IsJack = true,
                    DateReceived = DateTime.Now,
                    ReportType = $"Outbound.Location.Sold.Interface",
                    IsProcessed = false,
                };
                this._erestaurantPayloadRepository.Add(locationInterfacePayload);

                var userInterfacePayload = new ErestaurantPayload
                {
                    Payload = $@"<data>{userInterface}</data>",
                    UnencryptedPayload = $@"<data>{userInterface}</data>",
                    IsJack = true,
                    DateReceived = DateTime.Now,
                    ReportType = $"Outbound.User.Sold.Interface",
                    IsProcessed = false,
                };
                this._erestaurantPayloadRepository.Add(userInterfacePayload);

                var hierarchyInterfacePayload = new ErestaurantPayload
                {
                    Payload = $@"<data>{alignmentInterfaceNew}</data>",
                    UnencryptedPayload = $@"<data>{alignmentInterfaceNew}</data>",
                    IsJack = true,
                    DateReceived = DateTime.Now,
                    ReportType = $"Outbound.Hierarchy.Sold.Interface",
                    IsProcessed = false,
                };
                this._erestaurantPayloadRepository.Add(hierarchyInterfacePayload);

                await Globals.Bus.Send(new RequestSendToErestaurant { PayloadId = userInterfacePayload.Id });
                await Globals.Bus.Send(new RequestSendToErestaurant { PayloadId = locationInterfacePayload.Id });
                await Globals.Bus.Send(new RequestSendToErestaurant { PayloadId = hierarchyInterfacePayload.Id });

            }
        }


        private string ConvertAlignment(ErestaurantPayload alignmentPayload)
        {
            var payload = alignmentPayload.UnencryptedPayload
                .Substring(0, alignmentPayload.UnencryptedPayload.Length - 7).Substring(6);
            var document = new XmlDocument();
            document.LoadXml(payload);
            var node = document.SelectSingleNode(@"/Poll");
            foreach (XmlNode att in node.Attributes)
            {
                if (att.Name == "date")
                {
                    att.Value = DateTime.Now.ToString("yyyyMMdd");
                }

                if (att.Name == "created")
                {
                    att.Value = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss");
                }
            }

            return document.InnerXml;
        }

    }
}
