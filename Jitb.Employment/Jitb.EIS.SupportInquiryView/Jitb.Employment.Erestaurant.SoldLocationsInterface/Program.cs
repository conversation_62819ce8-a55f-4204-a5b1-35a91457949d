﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jitb.Employment.Erestaurant.SoldLocationsInterface
{
    using Domain.Integrations.Components.Erestaurant.Outgoing.Providers;
    using Jitb.Employment.Erestaurant.SoldLocationsInterface.GlobalsAndSingletons;
    using Jitb.UserManagement.Erestaurant.OutgoingInterfaces.Builders;
    using NServiceBus;
    using Providers;
    using StructureMap;

    class Program
    {

        static Program()
        {
            //            var bootstrapper = new Bootstrapper();
            //            bootstrapper.BootstrapStructureMap();
            //            _container = bootstrapper.Container;

            Globals.Container = new BusStartup().Start(); 

            Globals.Container.Configure(x=>
            {
                x.For<ISoldLocationsProvider>()
                .Use<SoldLocationsProvider>();
                x.For<ICreateUserInterfaceBuilder>()
                .Use<CreateUserInterfaceBuilder>();
            });
            Globals.Bus = Globals.Container.GetInstance<IEndpointInstance>();
        }

        static void Main(string[] args)
        {


            var userStoreAlignmentInterfaceProvider = Globals.Container.GetInstance<ISoldLocationsProvider>();
            userStoreAlignmentInterfaceProvider.ProcessSoldLocations();
        }


    }
}
