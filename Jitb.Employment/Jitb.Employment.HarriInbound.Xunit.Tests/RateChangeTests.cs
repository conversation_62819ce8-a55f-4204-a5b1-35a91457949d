﻿using AutoFixture;
using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Contracts.Commands.Employment;
using Jitb.Employment.Contracts.Commands.HarriInbound;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Constants;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriInbound.Endpoint.Handlers;
using Moq;
using NServiceBus.Testing;
using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace Jitb.Employment.HarriInbound.Endpoint.Xunit.Tests
{
    public class RateChangeTests
    {
        [Theory]
        [AutoMoqData]
        public void WithAnHourlyEmployee_WhenRateHasChanged_RateChangeCommandCreated(
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] GeneratePayRateUpdatedCommand message, ChangePayRateHandler sut, Fixture fixture)
        {
            fixture.Customize<HarriInboundEmployee>(composer => composer
                .With(x => x.Positions, fixture.CreateMany<HarriPosition>(1).ToArray())
                .With(x => x.PayType, new PayType { Type = HarriPayTypes.Hourly })
                    );

            fixture.Customize<Employee>(composer => composer
                .With(x => x.SalaryClass, "H")
            );


            var employee = fixture.Freeze<Employee>();
            employee.InsertPayRate(10m, DateTime.Today.AddDays(-10));

            var harriEmployee = fixture.Create<HarriInboundEmployee>();
            harriEmployee.Positions[0].PayRate.Rate = employee.CurrPayRateAmt + 1;

            Mock.Get(employeeRepository).Setup(x => x.GetByBadgeId(It.IsAny<int>()))
                .Returns(new List<Employee> { employee });




            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            var sentMessages = context.SentMessages;
            sentMessages.Length.Should().Be(1);
            var payRateChangedMessage = sentMessages.FirstOrDefault(x => x.Message.GetType() == typeof(ChangePayRate));
            (payRateChangedMessage.Message as ChangePayRate).PayRate.Should()
                .Be(harriEmployee.Positions[0].PayRate.Rate);
        }

        [Theory]
        [AutoMoqData]
        public void WithAnSalariedEmployee_RateIsAlwaysZero(
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] GeneratePayRateUpdatedCommand message, ChangePayRateHandler sut, Fixture fixture)
        {
            fixture.Customize<HarriInboundEmployee>(composer => composer
                .With(x => x.Positions, fixture.CreateMany<HarriPosition>(1).ToArray())
                .With(x => x.PayType, new PayType { Type = HarriPayTypes.Salaried, AnnualPayRate = new AnnualPayRate() })
            );

            fixture.Customize<Employee>(composer => composer
                .With(x => x.SalaryClass, "S")
            );


            var employee = fixture.Freeze<Employee>();
            employee.InsertPayRate(10m, DateTime.Today.AddDays(-10));

            var harriEmployee = fixture.Create<HarriInboundEmployee>();
            //          harriEmployee.Positions[0].PayRate.Rate = employee.CurrPayRateAmt + 1;

            Mock.Get(employeeRepository).Setup(x => x.GetByBadgeId(It.IsAny<int>()))
                .Returns(new List<Employee> { employee });




            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            var sentMessages = context.SentMessages;
            sentMessages.Length.Should().Be(1);
            var payRateChangedMessage = sentMessages.FirstOrDefault(x => x.Message.GetType() == typeof(ChangePayRate));
            (payRateChangedMessage.Message as ChangePayRate).PayRate.Should()
                .Be(0);
        }
        [Theory]
        [AutoMoqData]
        public void WhenAnHourlyEmployeeChangesToSalaried_AChangeToZeroAndAnUpdateEmployeeCommandIsSent(
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] GeneratePayRateUpdatedCommand message, ChangePayRateHandler sut, Fixture fixture)
        {
            fixture.Customize<HarriInboundEmployee>(composer => composer
                .With(x => x.Positions, fixture.CreateMany<HarriPosition>(1).ToArray())
                .With(x => x.PayType, new PayType { Type = HarriPayTypes.Salaried, AnnualPayRate = new AnnualPayRate() })
            );

            fixture.Customize<Employee>(composer => composer
                .With(x => x.SalaryClass, "H")
            );


            var employee = fixture.Freeze<Employee>();
            employee.InsertPayRate(10m, DateTime.Today.AddDays(-10));

            var harriEmployee = fixture.Create<HarriInboundEmployee>();
            //          harriEmployee.Positions[0].PayRate.Rate = employee.CurrPayRateAmt + 1;

            Mock.Get(employeeRepository).Setup(x => x.GetByBadgeId(It.IsAny<int>()))
                .Returns(new List<Employee> { employee });




            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            var sentMessages = context.SentMessages;
            sentMessages.Length.Should().Be(2);
            var payRateChangedMessage = sentMessages.FirstOrDefault(x => x.Message.GetType() == typeof(ChangePayRate));
            (payRateChangedMessage.Message as ChangePayRate).PayRate.Should()
                .Be(0);
            var updateEmployeeMessage = sentMessages.FirstOrDefault(x => x.Message.GetType() == typeof(UpdateEmployee));
            var change = ((UpdateEmployee)updateEmployeeMessage.Message).Changes[0];
            change.OldValue.Should().Be("H");
            change.Value.Should().Be("S");
        }

        [Theory]
        [AutoMoqData]
        public void WhenASalariedEmployeeChangesToHourly_AChangeToTheHourlyRateAndAnUpdateEmployeeCommandIsSent(
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] GeneratePayRateUpdatedCommand message, ChangePayRateHandler sut, Fixture fixture)
        {
            fixture.Customize<HarriInboundEmployee>(composer => composer
                .With(x => x.Positions, fixture.CreateMany<HarriPosition>(1).ToArray())
                .With(x => x.PayType, new PayType { Type = HarriPayTypes.Hourly })
            );

            fixture.Customize<Employee>(composer => composer
                .With(x => x.SalaryClass, "S")
            );


            var employee = fixture.Freeze<Employee>();
            employee.InsertPayRate(0m, DateTime.Today.AddDays(-10));

            var harriEmployee = fixture.Create<HarriInboundEmployee>();
            harriEmployee.Positions[0].PayRate.Rate = 20m;

            Mock.Get(employeeRepository).Setup(x => x.GetByBadgeId(It.IsAny<int>()))
                .Returns(new List<Employee> { employee });




            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            var sentMessages = context.SentMessages;
            sentMessages.Length.Should().Be(2);
            var payRateChangedMessage = sentMessages.FirstOrDefault(x => x.Message.GetType() == typeof(ChangePayRate));
            (payRateChangedMessage.Message as ChangePayRate).PayRate.Should()
                .Be(harriEmployee.Positions[0].PayRate.Rate);
            var updateEmployeeMessage = sentMessages.FirstOrDefault(x => x.Message.GetType() == typeof(UpdateEmployee));
            var change = ((UpdateEmployee)updateEmployeeMessage.Message).Changes[0];
            change.OldValue.Should().Be("S");
            change.Value.Should().Be("H");
        }

        [Theory]
        [AutoMoqData]
        public void IfTheHourlyRateStaysTheSame_NothingIsSent(
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] GeneratePayRateUpdatedCommand message, ChangePayRateHandler sut, Fixture fixture)
        {
            fixture.Customize<HarriInboundEmployee>(composer => composer
                .With(x => x.Positions, fixture.CreateMany<HarriPosition>(1).ToArray())
                .With(x => x.PayType, new PayType { Type = HarriPayTypes.Hourly })
            );

            fixture.Customize<Employee>(composer => composer
                .With(x => x.SalaryClass, "H")
            );


            var employee = fixture.Freeze<Employee>();
            employee.InsertPayRate(20m, DateTime.Today.AddDays(-10));

            var harriEmployee = fixture.Create<HarriInboundEmployee>();
            harriEmployee.Positions[0].PayRate.Rate = employee.CurrPayRate.PayRate ?? 20m;

            Mock.Get(employeeRepository).Setup(x => x.GetByBadgeId(It.IsAny<int>()))
                .Returns(new List<Employee> { employee });




            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            var sentMessages = context.SentMessages;
            sentMessages.Length.Should().Be(0);

        }

        [Theory]
        [AutoMoqData]
        public void IfTheSalariedStatusStaysTheSame_NothingIsSent(
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] GeneratePayRateUpdatedCommand message, ChangePayRateHandler sut, Fixture fixture)
        {
            fixture.Customize<HarriInboundEmployee>(composer => composer
                .With(x => x.Positions, fixture.CreateMany<HarriPosition>(1).ToArray())
                .With(x => x.PayType, new PayType { Type = HarriPayTypes.Salaried, AnnualPayRate = new AnnualPayRate() })
            );

            fixture.Customize<Employee>(composer => composer
                .With(x => x.SalaryClass, "S")
            );


            var employee = fixture.Freeze<Employee>();
            employee.InsertPayRate(0m, DateTime.Today.AddDays(-10));

            var harriEmployee = fixture.Create<HarriInboundEmployee>();
            harriEmployee.Positions[0].PayRate.Rate = employee.CurrPayRate.PayRate ?? 20m;

            Mock.Get(employeeRepository).Setup(x => x.GetByBadgeId(It.IsAny<int>()))
                .Returns(new List<Employee> { employee });




            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            var sentMessages = context.SentMessages;
            sentMessages.Length.Should().Be(0);

        }
    }
}
