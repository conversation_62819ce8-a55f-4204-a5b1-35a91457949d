﻿using AutoFixture;
using AutoFixture.Xunit2;
using CreateTestData;
using CreateTestData.FormatGenerators;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriInbound.Endpoint.Providers;
using Moq;
using NServiceBus.Testing;
using System.Threading.Tasks;
using ValueObjects;
using Xunit;

namespace Jitb.Employment.HarriInbound.Xunit.Tests
{
    public class SyncEisFromHarriDemographicDataTests
    {
        private readonly IFixture _fixture;
        private readonly Mock<IEmployeeRepository> _employeeRepositoryMock;
        private readonly Mock<IGenderDictionary> _genderDictMock;
        private readonly SyncEisFromHarriDemographicData _sut;

        public SyncEisFromHarriDemographicDataTests()
        {
            _fixture = new Fixture();
            _employeeRepositoryMock = new Mock<IEmployeeRepository>();
            _genderDictMock = new Mock<IGenderDictionary>();
            _sut = new SyncEisFromHarriDemographicData(_employeeRepositoryMock.Object, _genderDictMock.Object);
        }


        [Theory, InlineAutoMoqData]
        [Trait("Category", "HarriInboundSync")]
        [Trait("Category", "2025-03")]
        public async Task SyncEmployeeInfo_ShouldUpdateEmployeeInfo_WhenChangesFound(
            [Frozen] Employee employee)
        {
            // Arrange

            _fixture.Customize<HarriInboundEmployee>(composer =>
                composer
                    .With(p => p.Phone, PhoneNumberGenerator.Generate)
                    .With(p => p.HomePhone, PhoneNumberGenerator.Generate));
            var harriEmployee = _fixture.Create<HarriInboundEmployee>();


            _genderDictMock.Setup(g => g.Translate(It.IsAny<string>())).Returns("M");
            var context = new TestableMessageHandlerContext();
            // Act
            await _sut.SyncEmployeeInfo(employee, harriEmployee, context);

            // Assert
            context.SentMessages.Length.Should().Be(1);
        }

        [Fact, InlineAutoMoqData]
        [Trait("Category", "HarriInboundSync")]
        [Trait("Category", "2025-03")]
        public async Task SyncEmployeeInfo_ShouldNotUpdateEmployeeInfo_WhenNoChangesFound()
        {
            // Arrange
            _fixture.Customize<Employee>(composer =>
                composer
                    .With(p => p.CellPhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.HomePhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.WorkPhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.AlternateEmailAddress, "<EMAIL>"));

            var employee = _fixture.Create<Employee>();
            var harriAddress = new Address()
            {
                AddressLine1 = employee.Address1,
                AddressLine2 = employee.Address2,
                City = employee.City,
                StateCode = employee.StateCode,
                PostalCode = employee.ZipCode
            };

            var harriEmployee = _fixture.Build<HarriInboundEmployee>()
                .With(h => h.FirstName, employee.FirstName)
                .With(h => h.LastName, employee.LastName)
                .With(h => h.MiddleName, employee.MiddleName)
                .With(h => h.Email, employee.AlternateEmailAddress)
                .With(h => h.BirthDate, employee.BirthDate)
                .With(h => h.Ssn, employee.Ssn)
                .With(h => h.Gender, employee.Sex)
                .With(h => h.Phone, $"{PhoneNumber.From(employee.HomePhoneNumber).Formatted}")
                .With(h => h.HomePhone, $"{PhoneNumber.From(employee.HomePhoneNumber).Formatted}")
                .With(h => h.Address, harriAddress)
                .Create();
            _genderDictMock.Setup(g => g.Translate(It.IsAny<string>())).Returns(employee.Sex);
            var context = new TestableMessageHandlerContext();

            employee.CellPhoneNumber = PhoneNumber.From(employee.HomePhoneNumber).Formatted;
            employee.HomePhoneNumber = PhoneNumber.From(employee.HomePhoneNumber).Formatted;

            // Act
            await _sut.SyncEmployeeInfo(employee, harriEmployee, context);

            // Assert
            context.SentMessages.Length.Should().Be(0);
        }

        [Fact, InlineAutoMoqData]
        [Trait("Category", "HarriInboundSync")]
        [Trait("Category", "2025-03")]
        public async Task SyncEmployeeInfo_ShouldNotUpdateEmployeeInfo_WhenSsnChangesFound()
        {
            // Arrange
            _fixture.Customize<Employee>(composer =>
                composer
                    .With(p => p.CellPhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.HomePhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.WorkPhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.AlternateEmailAddress, "<EMAIL>"));

            var employee = _fixture.Create<Employee>();
            var harriAddress = new Address()
            {
                AddressLine1 = employee.Address1,
                AddressLine2 = employee.Address2,
                City = employee.City,
                StateCode = employee.StateCode,
                PostalCode = employee.ZipCode
            };

            var harriEmployee = _fixture.Build<HarriInboundEmployee>()
                .With(h => h.FirstName, employee.FirstName)
                .With(h => h.LastName, employee.LastName)
                .With(h => h.MiddleName, employee.MiddleName)
                .With(h => h.Email, employee.AlternateEmailAddress)
                .With(h => h.BirthDate, employee.BirthDate)
                .With(h => h.Ssn, "242-55-XXXX")
                .With(h => h.Gender, employee.Sex)
                .With(h => h.Phone, $"{employee.HomePhoneNumber}")
                .With(h => h.HomePhone, $"{employee.HomePhoneNumber}")
                .With(h => h.Address, harriAddress)
                .Create();
            _genderDictMock.Setup(g => g.Translate(It.IsAny<string>())).Returns(employee.Sex);
            var context = new TestableMessageHandlerContext();

            employee.CellPhoneNumber = PhoneNumber.From(employee.HomePhoneNumber).Formatted;
            employee.HomePhoneNumber = PhoneNumber.From(employee.HomePhoneNumber).Formatted;

            // Act
            await _sut.SyncEmployeeInfo(employee, harriEmployee, context);

            // Assert
            context.SentMessages.Length.Should().Be(0);

        }

        [Fact, InlineAutoMoqData]
        [Trait("Category", "HarriInboundSync")]
        [Trait("Category", "2025-03")]
        public async Task SyncEmployeeInfo_WhenEmployeeCellPhoneIsNull_AndHarriHomePhoneIsNotNull_ChangeIsSent()
        {
            // Arrange
            _fixture.Customize<Employee>(composer =>
                composer
                    .With(p => p.CellPhoneNumber, (string)null)
                    .With(p => p.HomePhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.WorkPhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.AlternateEmailAddress, "<EMAIL>"));

            var employee = _fixture.Create<Employee>();
            var harriAddress = new Address()
            {
                AddressLine1 = employee.Address1,
                AddressLine2 = employee.Address2,
                City = employee.City,
                StateCode = employee.StateCode,
                PostalCode = employee.ZipCode
            };

            var harriEmployee = _fixture.Build<HarriInboundEmployee>()
                .With(h => h.FirstName, employee.FirstName)
                .With(h => h.LastName, employee.LastName)
                .With(h => h.MiddleName, employee.MiddleName)
                .With(h => h.Email, employee.AlternateEmailAddress)
                .With(h => h.BirthDate, employee.BirthDate)
                .With(h => h.Ssn, "242-55-XXXX")
                .With(h => h.Gender, employee.Sex)
                .With(h => h.Phone, PhoneNumberGenerator.Generate)
                .With(h => h.HomePhone, $"{employee.HomePhoneNumber}")
                .With(h => h.Address, harriAddress)
                .Create();
            _genderDictMock.Setup(g => g.Translate(It.IsAny<string>())).Returns(employee.Sex);
            var context = new TestableMessageHandlerContext();

            // Act
            await _sut.SyncEmployeeInfo(employee, harriEmployee, context);

            // Assert
            context.SentMessages.Length.Should().Be(1);

        }

        [Fact, InlineAutoMoqData]
        [Trait("Category", "HarriInboundSync")]
        [Trait("Category", "2025-03")]
        public async Task SyncEmployeeInfo_WhenEmployeePhonesAreEqual_NothingIsSent()
        {
            // Arrange
            _fixture.Customize<Employee>(composer =>
                composer
                    .With(p => p.CellPhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.HomePhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.WorkPhoneNumber, PhoneNumberGenerator.Generate)
                    .With(p => p.AlternateEmailAddress, "<EMAIL>"));

            var employee = _fixture.Create<Employee>();
            var harriAddress = new Address()
            {
                AddressLine1 = employee.Address1,
                AddressLine2 = employee.Address2,
                City = employee.City,
                StateCode = employee.StateCode,
                PostalCode = employee.ZipCode
            };

            var harriEmployee = _fixture.Build<HarriInboundEmployee>()
                .With(h => h.FirstName, employee.FirstName)
                .With(h => h.LastName, employee.LastName)
                .With(h => h.MiddleName, employee.MiddleName)
                .With(h => h.Email, employee.AlternateEmailAddress)
                .With(h => h.BirthDate, employee.BirthDate)
                .With(h => h.Ssn, "242-55-XXXX")
                .With(h => h.Gender, employee.Sex)
                .With(h => h.Phone, employee.CellPhoneNumber)
                .With(h => h.HomePhone, $"{employee.HomePhoneNumber}")
                .With(h => h.Address, harriAddress)
                .Create();
            _genderDictMock.Setup(g => g.Translate(It.IsAny<string>())).Returns(employee.Sex);
            var context = new TestableMessageHandlerContext();

            employee.CellPhoneNumber = PhoneNumber.From(employee.HomePhoneNumber).Formatted;
            employee.HomePhoneNumber = PhoneNumber.From(employee.HomePhoneNumber).Formatted;

            // Act
            await _sut.SyncEmployeeInfo(employee, harriEmployee, context);

            // Assert
            context.SentMessages.Length.Should().Be(0);

        }
    }
}
