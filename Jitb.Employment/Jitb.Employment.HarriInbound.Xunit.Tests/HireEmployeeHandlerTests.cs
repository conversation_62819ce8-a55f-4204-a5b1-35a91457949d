﻿using AutoFixture;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Contracts.Commands.Employment;
using Jitb.Employment.Contracts.Commands.HarriInbound;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Concepts;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriInbound.Endpoint.Handlers;
using Jitb.Employment.Internal.Contracts.Commands.HarriInbound;
using Moq;
using Newtonsoft.Json;
using NServiceBus.Testing;
using RestSharp;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Payloads = Jitb.Employment.HarriCaller.Domain.Constants.PayloadMappingEndpoints;

namespace Jitb.Employment.HarriInbound.Endpoint.Xunit.Tests
{

    public class HireEmployeeHandlerTests
    {
        private readonly Mock<IGetHarriEmployeeProvider> _harriEmployeeProviderMock;
        private readonly Mock<IEmployeeRepository> _employeeRepositoryMock;
        private readonly Mock<IHarriTenantTable> _harriTenantTableMock;
        private readonly Mock<ICallHarriWebServiceProvider> _callHarriWebServiceProviderMock;
        private readonly Fixture _fixture;
        private readonly HireEmployeeHandler _handler;

        public HireEmployeeHandlerTests()
        {
            _harriEmployeeProviderMock = new Mock<IGetHarriEmployeeProvider>();
            _employeeRepositoryMock = new Mock<IEmployeeRepository>();
            _harriTenantTableMock = new Mock<IHarriTenantTable>();
            _callHarriWebServiceProviderMock = new Mock<ICallHarriWebServiceProvider>();
            _fixture = new Fixture();
            _handler = new HireEmployeeHandler(
                _harriEmployeeProviderMock.Object,
                _employeeRepositoryMock.Object,
                _harriTenantTableMock.Object,
                _callHarriWebServiceProviderMock.Object);

            var primaryActiveLocation = new HarriLocation
            {
                Id = _fixture.Create<int>(),
                LocationName = _fixture.Create<string>(),
                IsPrimary = true,
                IsActive = true,
                PayrollId = _fixture.Create<string>()
            };
            _fixture.Customize<HarriInboundEmployee>(c => c
                .With(e => e.Locations, new List<HarriLocation> { primaryActiveLocation }));
        }

        [Fact]
        public async Task Handle_ValidBadgeId_ProcessesRehire()
        {
            // Arrange
            var message = _fixture.Create<GenerateEmployeeHiredCommand>();
            var context = new TestableMessageHandlerContext();
            var harriEmployee = _fixture.Create<HarriInboundEmployee>();
            var employee = _fixture.Create<Employee>();

            _harriEmployeeProviderMock.Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(await Task.FromResult(harriEmployee));

            _employeeRepositoryMock.Setup(x => x.GetByBadgeId((int)message.BadgeId))
                .Returns(new List<Employee> { employee }.AsQueryable());

            // Act
            await _handler.Handle(message, context);

            // Assert
            _harriEmployeeProviderMock.Verify(x => x.GetHarriEmployee(message.BadgeId, message.TenantId), Times.Once);
            _employeeRepositoryMock.Verify(x => x.GetByBadgeId((int)message.BadgeId), Times.Once);

            // Additional assertions
            context.SentMessages.Should().ContainSingle(); 
            var sentMessage = context.SentMessages.Single().Message;
            sentMessage.Should().BeOfType<RehireEmployee>(); 
        }

        [Fact]
        public async Task Handle_ZeroBadgeId_ProcessesUnmappedEmployees()
        {
           //Arrange
           var message = _fixture.Build<GenerateEmployeeHiredCommand>()
                                  .With(m => m.BadgeId, 0)
                                  .Create();
            var context = new TestableMessageHandlerContext();
            var harriTenant = new HarriTenant { TenantId = message.TenantId };
            var unmappedEmployees1 = new UnmappedEmployees
            {
                UnmappedEmployeesArray = new List<UnmappedEmployee>
                {
                    new UnmappedEmployee { },
                    new UnmappedEmployee { }
                }.ToArray()
            };
            var unmappedEmployees2 = new UnmappedEmployees
            {
                UnmappedEmployeesArray = new List<UnmappedEmployee>{}.ToArray()
            };

            var employee = _fixture.Build<HarriInboundEmployee>().With(e => e.Id, "1").Create();
            var harriinbondEmployeeData = new HarriInboundEmployeeData()
            {
                Pagination = new HarriCaller.Domain.HarriPayloads.Pagination()
                {
                    Page = 1,
                    PerPage = 1,
                    Total = 1,
                    TotalPages = 1
                },
                Employees = new List<HarriInboundEmployee>()
                {
                    employee
                }
            };

            _harriTenantTableMock.Setup(x => x.Get(message.TenantId)).Returns(harriTenant);
            _employeeRepositoryMock.Setup(x => x.GetBadgeIdsNotInEmployeeTable(new List<int>() { 1 }))
                .Returns(new List<int>() { 1 });

            _callHarriWebServiceProviderMock.SetupSequence(x => x.Call(harriTenant, Method.Get, 
                Payloads.GetEndpoint(Payloads.GetUnmappedEmployeeMappings), null, true, "V1"))
                    .ReturnsAsync(new RestResponse { Content = JsonConvert.SerializeObject(unmappedEmployees1) })
                    .ReturnsAsync(new RestResponse { Content = JsonConvert.SerializeObject(unmappedEmployees2) });

            _callHarriWebServiceProviderMock.Setup(x => x.Call(harriTenant, Method.Get,
                Payloads.GetEndpoint(Payloads.GetEmployees), null, true, "V2"))
                    .ReturnsAsync(new RestResponse { Content = JsonConvert.SerializeObject(harriinbondEmployeeData) });

            // Act
            await _handler.Handle(message, context);

            // Assert
            _harriTenantTableMock.Verify(x => x.Get(message.TenantId), Times.AtLeastOnce());
            _callHarriWebServiceProviderMock.Verify(x => x.Call(harriTenant, 
                Method.Get, 
                Payloads.GetEndpoint(Payloads.GetUnmappedEmployeeMappings), null, true, "V1"), 
                Times.Exactly(2));
            _callHarriWebServiceProviderMock.Verify(x => x.Call(harriTenant,
                Method.Get,
                Payloads.GetEndpoint(Payloads.GetEmployees), null, true, "V2"),
                Times.Exactly(1));
            context.SentMessages.Should().ContainSingle(); 
            var sentMessage = context.SentMessages.Single().Message;
            sentMessage.Should().BeOfType<HireHarriEmployee>(); 
        }
    }
}