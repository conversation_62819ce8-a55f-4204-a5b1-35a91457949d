﻿using AutoFixture;
using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Contracts.Commands.Employment;
using Jitb.Employment.Contracts.Commands.HarriInbound;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Constants;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriInbound.Endpoint.Handlers;
using Moq;
using NServiceBus.Testing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriInbound.Endpoint.Xunit.Tests
{
    public class JobCodeChangedTests
    {
        [Theory]
        [InlineAutoMoqData("RORH10", "RORH05", "H", "H")]
        [InlineAutoMoqData("RORH10", "RORM20", "H", "S")]
        public void WhenJobCodeChanges_JobCodeChangeMessageIsSent(
            string oldJobCode, string newJobCode, string oldSalaryClass, string newSalaryClass,
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] GenerateEmployeePositionCreatedCommand message, ChangeJobCodeHandler sut, Fixture fixture)
        {
            var harriPayType = (newSalaryClass == "H") ? HarriPayTypes.Hourly : HarriPayTypes.Salaried;
            fixture.Customize<HarriInboundEmployee>(composer => composer
                .With(x => x.Positions, fixture.CreateMany<HarriPosition>(1).ToArray())
                .With(x => x.PayType, new PayType { Type = harriPayType })
            );

            fixture.Customize<Employee>(composer => composer
                .With(x => x.SalaryClass, oldSalaryClass)
            );


            var employee = fixture.Freeze<Employee>();
            employee.InsertJobCode(new JobCode { Code = oldJobCode }, DateTime.Today.AddDays(-10));

            var harriEmployee = fixture.Create<HarriInboundEmployee>();
            harriEmployee.Positions[0].JobCode = newJobCode;

            Mock.Get(employeeRepository).Setup(x => x.GetByBadgeId(It.IsAny<int>()))
                .Returns(new List<Employee> { employee
                });

            Mock.Get(harriEmployeeProvider).Setup(x => x.GetHarriEmployee(It.IsAny<int>(), It.IsAny<Guid?>()))
                .Returns(Task.FromResult(harriEmployee));

            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            var sentMessages = context.SentMessages;
            sentMessages.Length.Should().Be(1);
            var jobCodeChangedMessage = sentMessages.FirstOrDefault(x => x.Message.GetType() == typeof(ChangeEmployeeJobCode));
            (jobCodeChangedMessage.Message as ChangeEmployeeJobCode).NewJobCode.Should()
                .Be(newJobCode);
            (jobCodeChangedMessage.Message as ChangeEmployeeJobCode).ChangeSalaryClass.Should()
                .Be(oldSalaryClass != newSalaryClass);

            if (oldSalaryClass != newSalaryClass)
                (jobCodeChangedMessage.Message as ChangeEmployeeJobCode).NewSalaryClass.Should()
                    .Be(newSalaryClass);
        }
        [Theory]
        [InlineAutoMoqData("RORH10", "RORH10", "H", "H")]

        public void WhenJobCodeDoesNotChange_NoMessageIsSent(
      string oldJobCode, string newJobCode, string oldSalaryClass, string newSalaryClass,
      [Frozen] IEmployeeRepository employeeRepository,
      [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
      [Frozen] GenerateEmployeePositionCreatedCommand message, ChangeJobCodeHandler sut, Fixture fixture)
        {
            var harriPayType = (newSalaryClass == "H") ? HarriPayTypes.Hourly : HarriPayTypes.Salaried;
            fixture.Customize<HarriInboundEmployee>(composer => composer
                .With(x => x.Positions, fixture.CreateMany<HarriPosition>(1).ToArray())
                .With(x => x.PayType, new PayType { Type = harriPayType })
            );

            fixture.Customize<Employee>(composer => composer
                .With(x => x.SalaryClass, oldSalaryClass)
            );


            var employee = fixture.Freeze<Employee>();
            employee.InsertJobCode(new JobCode { Code = oldJobCode }, DateTime.Today.AddDays(-10));

            var harriEmployee = fixture.Create<HarriInboundEmployee>();
            harriEmployee.Positions[0].JobCode = newJobCode;

            Mock.Get(employeeRepository).Setup(x => x.GetByBadgeId(It.IsAny<int>()))
                .Returns(new List<Employee> { employee
                });

            Mock.Get(harriEmployeeProvider).Setup(x => x.GetHarriEmployee(It.IsAny<int>(), It.IsAny<Guid?>()))
                .Returns(Task.FromResult(harriEmployee));

            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            var sentMessages = context.SentMessages;
            sentMessages.Length.Should().Be(0);

        }

    }
}
