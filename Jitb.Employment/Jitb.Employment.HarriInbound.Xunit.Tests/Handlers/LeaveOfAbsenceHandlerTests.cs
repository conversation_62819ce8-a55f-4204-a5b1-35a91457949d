﻿using Ardalis.GuardClauses;
using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Contracts.Commands.Employment;
using Jitb.Employment.Contracts.Commands.HarriInbound;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Concepts;
using Jitb.Employment.HarriCaller.Domain.Constants;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriInbound.Endpoint.Handlers;
using Moq;
using NServiceBus.Testing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriInbound.Xunit.Tests.Handlers
{
    public class LeaveOfAbsenceHandlerTests
    {
        private TestableMessageHandlerContext context = new TestableMessageHandlerContext();

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_StartLoaCommand_WhenEmployeeNotOnLoa_DoesNotSendCommand(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeStartLoaCommand message,
            HarriInboundEmployee harriEmployee)
        {
            // Arrange
            harriEmployee.Status = HarriStatusCodes.Active;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            // Act
            await sut.Handle(message, context);

            // Assert
            context.SentMessages.Should().BeEmpty();
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_StartLoaCommand_WithCorporateEmployee_DoesNotSendCommand(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeStartLoaCommand message,
            HarriInboundEmployee harriEmployee,
            Employee employee)
        {
            // Arrange
            employee.CompanyId = 1;
            harriEmployee.Status = HarriStatusCodes.Loa;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            Mock.Get(employeeRepository)
                .Setup(x => x.GetByBadgeId(message.BadgeId))
                .Returns(new List<Employee> { employee }.AsQueryable());


            // Act
            await sut.Handle(message, context);

            // Assert
            context.SentMessages.Should().BeEmpty();
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_StartLoaCommand_ValidEmployee_SendsStartLeaveOfAbsenceCommand(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeStartLoaCommand message,
            HarriInboundEmployee harriEmployee,
            Employee employee,
            HarriEmployeeAbsence absenceRecord)
        {
            // Arrange
            employee.CompanyId = 1234;
            harriEmployee.Status = HarriStatusCodes.Loa;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            Mock.Get(employeeRepository)
                .Setup(x => x.GetByBadgeId(message.BadgeId))
                .Returns(new List<Employee> { employee }.AsQueryable());

            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetEmployeeAbsence(message.BadgeId, message.AbsenceId, message.TenantId))
                .Returns(Task.FromResult(absenceRecord));

            absenceRecord.StartDate = DateTime.Today;
            absenceRecord.EndDate = DateTime.Today.AddDays(30);

            // Act
            await sut.Handle(message, context);

            // Assert
            context.SentMessages.Should().HaveCount(1);
            var sentCommand = context.SentMessages[0].Message;
            sentCommand.Should().BeOfType<StartLeaveOfAbsence>();

            var loaCommand = (StartLeaveOfAbsence)sentCommand;
            loaCommand.EmployeeId.Should().Be(employee.EmployeeId);
            loaCommand.LeaveOfAbsenceBeginDate.Should().Be(absenceRecord.StartDate);
            loaCommand.LeaveOfAbsenceEndDate.Should().Be(absenceRecord.EndDate);
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_EndLoaCommand_WhenEmployeeNotActive_DoesNotSendCommand(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeEndLoaCommand message,
            HarriInboundEmployee harriEmployee)
        {
            // Arrange
            harriEmployee.Status = HarriStatusCodes.Loa;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            // Act
            await sut.Handle(message, context);

            // Assert
            context.SentMessages.Should().BeEmpty();
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_EndLoaCommand_WithCorporateEmployee_DoesNotSendCommand(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeEndLoaCommand message,
            HarriInboundEmployee harriEmployee,
            Employee employee)
        {
            // Arrange
            employee.CompanyId = 1;
            harriEmployee.Status = HarriStatusCodes.Active;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            Mock.Get(employeeRepository)
                .Setup(x => x.GetByBadgeId(message.BadgeId))
                .Returns(new List<Employee> { employee }.AsQueryable());


            // Act
            await sut.Handle(message, context);

            // Assert
            context.SentMessages.Should().BeEmpty();
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_EndLoaCommand_WithNullEndDate_DoesNotSendCommand(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeEndLoaCommand message,
            HarriInboundEmployee harriEmployee,
            Employee employee,
            HarriEmployeeAbsence absenceRecord)
        {
            // Arrange
            employee.CompanyId = 1234;
            harriEmployee.Status = HarriStatusCodes.Active;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            Mock.Get(employeeRepository)
                .Setup(x => x.GetByBadgeId(message.BadgeId))
                .Returns(new List<Employee> { employee }.AsQueryable());

            absenceRecord.EndDate = null;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetEmployeeAbsence(message.BadgeId, message.AbsenceId, message.TenantId))
                .Returns(Task.FromResult(absenceRecord));

            // Act
            await sut.Handle(message, context);

            // Assert
            context.SentMessages.Should().BeEmpty();
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_EndLoaCommand_ValidEmployee_SendsEndLeaveOfAbsenceCommand(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeEndLoaCommand message,
            HarriInboundEmployee harriEmployee,
            Employee employee,
            HarriEmployeeAbsence absenceRecord)
        {
            // Arrange
            employee.CompanyId = 1234;
            harriEmployee.Status = HarriStatusCodes.Active;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            Mock.Get(employeeRepository)
                .Setup(x => x.GetByBadgeId(message.BadgeId))
                .Returns(new List<Employee> { employee }.AsQueryable());

            absenceRecord.EndDate = DateTime.Today;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetEmployeeAbsence(message.BadgeId, message.AbsenceId, message.TenantId))
                .Returns(Task.FromResult(absenceRecord));

            // Act
            await sut.Handle(message, context);

            // Assert
            context.SentMessages.Should().HaveCount(1);
            var sentCommand = context.SentMessages[0].Message;
            sentCommand.Should().BeOfType<EndLeaveOfAbsence>();

            var loaCommand = (EndLeaveOfAbsence)sentCommand;
            loaCommand.EmployeeId.Should().Be(employee.EmployeeId);
            loaCommand.LeaveOfAbsenceEndDate.Should().Be(absenceRecord.EndDate);
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_StartLoaCommand_WhenEmployeeNotFound_ThrowsException(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeStartLoaCommand message,
            HarriInboundEmployee harriEmployee)
        {
            // Arrange
            harriEmployee.Status = HarriStatusCodes.Loa;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            Mock.Get(employeeRepository)
                .Setup(x => x.GetByBadgeId(message.BadgeId))
                .Returns(Enumerable.Empty<Employee>().AsQueryable());

            // Act & Assert\
            Assert.ThrowsAsync<NotFoundException>(() => sut.Handle(message, context));
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_EndLoaCommand_WhenEmployeeNotFound_ThrowsException(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeEndLoaCommand message,
            HarriInboundEmployee harriEmployee)
        {
            // Arrange
            harriEmployee.Status = HarriStatusCodes.Active;
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult(harriEmployee));

            Mock.Get(employeeRepository)
                .Setup(x => x.GetByBadgeId(message.BadgeId))
                .Returns(Enumerable.Empty<Employee>().AsQueryable());

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() => sut.Handle(message, context));
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_StartLoaCommand_WhenHarriEmployeeNotFound_ThrowsException(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeStartLoaCommand message)
        {
            // Arrange
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult((HarriInboundEmployee)null));

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() => sut.Handle(message, context));
        }

        [Theory(Skip = "Ignoring all tests in this class"), AutoMoqData]
        [Trait("Category", "Loa")]
        public async Task Handle_EndLoaCommand_WhenHarriEmployeeNotFound_ThrowsException(
            [Frozen] IGetHarriEmployeeProvider harriEmployeeProvider,
            LeaveOfAbsenceHandler sut,
            GenerateEmployeeEndLoaCommand message)
        {
            // Arrange
            Mock.Get(harriEmployeeProvider)
                .Setup(x => x.GetHarriEmployee(message.BadgeId, message.TenantId))
                .Returns(Task.FromResult((HarriInboundEmployee)null));

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() => sut.Handle(message, context));
        }
    }
}