﻿using FluentAssertions;
using HarriConcepts;
using Jitb.Employment.Contracts.Commands.HarriInbound;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriInbound.Endpoint.Handlers;
using Moq;
using NServiceBus.Testing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

public class ProcessThisEventHandlerTests
{
    private readonly Mock<IHarriEventProcessedRepository> _mockHarriEventProcessedRepo;
    private readonly Mock<IGetHarriEmployeeProvider> _mockHarriEmployeeProvider;
    private readonly Mock<IEmployeeRepository> _mockEmployeeRepository;
    private readonly ProcessThisEventHandler _handler;
    private readonly TestableMessageHandlerContext _context;

    public ProcessThisEventHandlerTests()
    {
        _mockHarriEventProcessedRepo = new Mock<IHarriEventProcessedRepository>();
        _mockHarriEmployeeProvider = new Mock<IGetHarriEmployeeProvider>();
        _mockEmployeeRepository = new Mock<IEmployeeRepository>();
        _handler = new ProcessThisEventHandler(
            _mockHarriEventProcessedRepo.Object,
            _mockHarriEmployeeProvider.Object,
            _mockEmployeeRepository.Object);
        _context = new TestableMessageHandlerContext();
    }

    [Fact]
    public async Task Handle_EmployeeTerminateEvent_SendsCorrectCommand()
    {
        // Arrange
        var badgeId = 12345;
        var tenantId = Guid.NewGuid();
        var eventId = Guid.NewGuid().ToString();

        var message = new ProcessThisEvent
        {
            BadgeId = badgeId,
            TenantId = tenantId,
            EventId = eventId,
            EventType = "EMPLOYEE.TERMINATE",
            Status = "SUCCESS",
            PublishTime = DateTime.Now,
            Location = 11
        };

        var employee = new Employee { EmployeeId = 1, CompanyId = 2 };
        var harriEmployee = new HarriInboundEmployee();

        _mockEmployeeRepository.Setup(r => r.GetByBadgeId(badgeId))
            .Returns(new List<Employee> { employee }.AsQueryable());

        _mockHarriEmployeeProvider.Setup(p => p.GetHarriEmployee(badgeId, tenantId))
            .Returns(Task.FromResult(harriEmployee));

        // Act
        await _handler.Handle(message, _context);

        // Assert
        _context.SentMessages.Should().HaveCount(1);
        var sentCommand = _context.SentMessages[0].Message;
        sentCommand.Should().BeOfType<GenerateEmployeeTerminateCommand>();

        var terminateCommand = (GenerateEmployeeTerminateCommand)sentCommand;
        terminateCommand.EventId.Should().Be(eventId);
        terminateCommand.EventType.Should().Be("EMPLOYEE.TERMINATE");
        terminateCommand.Status.Should().Be("SUCCESS");
        terminateCommand.BadgeId.Should().Be(badgeId);
        terminateCommand.Location.Should().Be(message.Location);

        _mockHarriEventProcessedRepo.Verify(r => r.Add(It.Is<HarriEventProcessed>(e =>
            e.EventId == Guid.Parse(eventId) &&
            e.TenantId == tenantId &&
            e.EventType == "EMPLOYEE.TERMINATE")), Times.Once());
    }

    [Fact]
    public async Task Handle_EmployeePositionCreatedEvent_SendsCorrectCommand()
    {
        // Arrange
        var badgeId = 12345;
        var tenantId = Guid.NewGuid();
        var eventId = Guid.NewGuid().ToString();
        var jobCode = "MGMT";

        var message = new ProcessThisEvent
        {
            BadgeId = badgeId,
            TenantId = tenantId,
            EventId = eventId,
            EventType = "EMPLOYEE_POSITION.CREATED",
            Status = "SUCCESS",
            PublishTime = DateTime.Now,
            Location = 22,
            JobCode = jobCode
        };

        var employee = new Employee { EmployeeId = 1, CompanyId = 2 };
        var harriEmployee = new HarriInboundEmployee();

        _mockEmployeeRepository.Setup(r => r.GetByBadgeId(badgeId))
            .Returns(new List<Employee> { employee }.AsQueryable());

        _mockHarriEmployeeProvider.Setup(p => p.GetHarriEmployee(badgeId, tenantId))
            .Returns(Task.FromResult(harriEmployee));

        // Act
        await _handler.Handle(message, _context);

        // Assert
        _context.SentMessages.Should().HaveCount(1);
        var sentCommand = _context.SentMessages[0].Message;
        sentCommand.Should().BeOfType<GenerateEmployeePositionCreatedCommand>();

        var positionCommand = (GenerateEmployeePositionCreatedCommand)sentCommand;
        positionCommand.EventId.Should().Be(eventId);
        positionCommand.EventType.Should().Be("EMPLOYEE_POSITION.CREATED");
        positionCommand.Status.Should().Be("SUCCESS");
        positionCommand.BadgeId.Should().Be(badgeId);
        positionCommand.Location.Should().Be(message.Location);
        positionCommand.JobCode.Should().Be(jobCode);

        _mockHarriEventProcessedRepo.Verify(r => r.Add(It.Is<HarriEventProcessed>(e =>
            e.EventId == new Guid(eventId) &&
            e.TenantId == tenantId &&
            e.EventType == "EMPLOYEE_POSITION.CREATED")), Times.Once());


    }

    [Fact]
    public async Task Handle_EmployeeAbsenceCreatedEvent_SendsCorrectCommand()
    {
        // Arrange
        var badgeId = 12345;
        var tenantId = Guid.NewGuid();
        var eventId = Guid.NewGuid().ToString();
        var absenceId = 252525;

        var message = new ProcessThisEvent
        {
            BadgeId = badgeId,
            TenantId = tenantId,
            EventId = eventId,
            EventType = "EMPLOYEE_ABSENCE.CREATED",
            Status = "SUCCESS",
            PublishTime = DateTime.Now,
            Location = 33,
            AbsenceId = absenceId
        };

        var employee = new Employee { EmployeeId = 1, CompanyId = 2 };
        var harriEmployee = new HarriInboundEmployee();

        _mockEmployeeRepository.Setup(r => r.GetByBadgeId(badgeId))
            .Returns(new List<Employee> { employee }.AsQueryable());

        _mockHarriEmployeeProvider.Setup(p => p.GetHarriEmployee(badgeId, tenantId))
            .Returns(Task.FromResult(harriEmployee));

        // Act
        await _handler.Handle(message, _context);

        // Assert
        _context.SentMessages.Should().HaveCount(1);
        var sentCommand = _context.SentMessages[0].Message;
        sentCommand.Should().BeOfType<GenerateEmployeeStartLoaCommand>();

        var loaCommand = (GenerateEmployeeStartLoaCommand)sentCommand;
        loaCommand.EventId.Should().Be(eventId);
        loaCommand.EventType.Should().Be("EMPLOYEE_ABSENCE.CREATED");
        loaCommand.Status.Should().Be("SUCCESS");
        loaCommand.BadgeId.Should().Be(badgeId);
        loaCommand.Location.Should().Be(message.Location);

        _mockHarriEventProcessedRepo.Verify(r => r.Add(It.Is<HarriEventProcessed>(e =>
            e.EventId == Guid.Parse(eventId) &&
            e.TenantId == tenantId &&
            e.EventType == "EMPLOYEE_ABSENCE.CREATED")), Times.Once());
    }
}
