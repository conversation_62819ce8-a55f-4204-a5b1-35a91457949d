﻿CREATE TABLE [dbo].[Employee] (
    [Employee<PERSON>]                       BIGINT         IDENTITY (1, 1) NOT NULL,
    [BadgeID]                          INT            NULL,
    [Address1]                         VARCHAR (50)   NULL,
    [Address2]                         VARCHAR (50)   NULL,
    [City]                             VARCHAR (50)   NULL,
    [StateCode]                        CHAR (2)       NULL,
    [ZipCode]                          CHAR (10)      NULL,
    [WorkPhoneNumber]                  VARCHAR (15)   NULL,
    [HomePhoneNumber]                  VARCHAR (15)   NULL,
    [CellPhoneNumber]                  VARCHAR (15)   NULL,
    [NetworkID]                        VARCHAR (50)   NULL,
    [FimEmailPreferenceCode]           VARCHAR (10)   NULL,
    [LawsonCompanyID]                  INT            NULL,
    [LawsonEmployeeID]                 INT            NULL,
    [LawsonSupervisorCode]             CHAR (10)      NULL,
    [LawsonSupervisorID]               INT            NULL,
    [LawsonSuperKeyID]                 BIGINT         NULL,
    [LawsonEmpStatus]                  VARCHAR (5)    NULL,
    [LawsonDepartment<PERSON>ode]             VARCHAR (10)   NULL,
    [LawsonProcess<PERSON>evelCode]           VARCHAR (5)    NULL,
    [LawsonCost<PERSON>enter]                 VARCHAR (10)   NULL,
    [eRestaurantEmployeeID]            VARCHAR (30)   NULL,
    [eRestaurantSupervisorID]          VARCHAR (30)   NULL,
    [HREmployeeIdentityID]             VARCHAR (15)   NULL,
    [HREmployeeID]                     NUMERIC (9)    NULL,
    [HRSupervisorID]                   NUMERIC (9)    NULL,
    [HRDepartmentCode]                 VARCHAR (50)   NULL,
    [HRDepartment]                     VARCHAR (50)   NULL,
    [HRCompany]                        VARCHAR (30)   NULL,
    [HRCompanyBrand]                   VARCHAR (30)   NULL,
    [EligibleForRehire]                CHAR (1)       NULL,
    [SupervisorEmployeeID]             BIGINT         NULL,
    [HireDate]                         DATE           NULL,
    [PreviousHireDate]                 DATE           NULL,
    [TerminationDate]                  DATE           NULL,
    [AdjustedHireDate]                 DATE           NULL,
    [EmailAddress]                     VARCHAR (50)   NULL,
    [ExemptStatus]                     CHAR (1)       NULL,
    [PayGrade]                         CHAR (3)       NULL,
    [SalaryClass]                      CHAR (1)       NULL,
    [BirthDate]                        DATE           NULL,
    [SSN]                              VARCHAR (11)   NULL,
    [Sex]                              CHAR (1)       NULL,
    [CurrentStatus]                    CHAR (2)       NULL,
    [EmployeeType]                     CHAR (1)       NULL,
    [MiddleInitial]                    CHAR (1)       NULL,
    [FirstName]                        VARCHAR (50)   NULL,
    [MiddleName]                       VARCHAR (50)   NULL,
    [LastName]                         VARCHAR (50)   NULL,
    [NickName]                         VARCHAR (50)   NULL,
    [TrainingFlag]                     NCHAR (10)     NULL,
    [DomainName]                       CHAR (10)      NULL,
    [JobCode]                          VARCHAR (10)   NULL,
    [JobClass]                         VARCHAR (10)   NULL,
    [CostCenterCode]                   VARCHAR (10)   NULL,
    [DateTimeStamp]                    DATETIME       NULL,
    [VersionNumber]                    BIGINT         NULL,
    [JobCodeEffectiveDate]             DATE           NULL,
    [LeaveOfAbsenceDate]               DATE           NULL,
    [ExpectedLeaveOfAbsenceReturnDate] DATE           NULL,
    [AlternateEmailAddress]            VARCHAR (50)   NULL,
    [CompanyId]                        INT            NULL,
    [HomeLocationNumber]               INT            NULL,
    [CreatedDate]                      DATETIME       NULL,
    [SSNSha256Digest]                  VARBINARY (32) NULL,
    [LastNameSha256Digest]             VARBINARY (32) NULL,
    [BirthDateSha256Digest]            VARBINARY (32) NULL,
    [eRestaurantPayrollID]             VARCHAR (30)   NULL,
    [LastReceivedFromUltiPro]          DATETIME       NULL,
    CONSTRAINT [PK_Employee] PRIMARY KEY CLUSTERED ([EmployeeID] ASC)
);


GO

CREATE TRIGGER [dbo].[tr_Employee_ItopsOutstandingUpdates]
ON [dbo].[Employee]
AFTER insert
AS
BEGIN
SET NOCOUNT ON;
DECLARE @Employeeid BIGINT
SELECT @Employeeid = INSERTED.EmployeeId
FROM INSERTED

insert into [ItopsOutstandingUpdate] (TableId,ActionId,UpdatedTableKey)
values (2,1,@Employeeid)

END

GO
