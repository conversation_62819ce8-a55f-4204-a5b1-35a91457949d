﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F6193A1B-4B36-41DF-834D-B543FEAE687A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Jitb.Location.BingUpdate.Endpoint</RootNamespace>
    <AssemblyName>Jitb.Location.BingUpdate.Endpoint</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework">
      <HintPath>..\..\..\..\nServiceBus20161225\Jitb.Employment\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <HintPath>..\..\..\..\nServiceBus20161225\Jitb.Employment\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Core">
      <HintPath>..\..\..\..\nServiceBus20161225\Jitb.Employment\packages\NServiceBus.6.1.0\lib\net452\NServiceBus.Core.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Host">
      <HintPath>..\..\..\..\nServiceBus20161225\Jitb.Employment\packages\NServiceBus.Host.7.0.0\lib\net452\NServiceBus.Host.exe</HintPath>
    </Reference>
    <Reference Include="NServiceBus.NHibernate">
      <HintPath>..\..\..\..\nServiceBus20161225\Jitb.Employment\packages\NServiceBus.NHibernate.7.0.1\lib\net452\NServiceBus.NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.ObjectBuilder.StructureMap">
      <HintPath>..\..\..\..\nServiceBus20161225\Jitb.Employment\packages\NServiceBus.StructureMap.6.0.0\lib\net452\NServiceBus.ObjectBuilder.StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap">
      <HintPath>..\..\..\..\nServiceBus20161225\Jitb.Employment\packages\structuremap.4.0.0.315\lib\dotnet\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap.Web">
      <HintPath>..\..\..\..\nServiceBus20161225\Jitb.Employment\packages\structuremap.web.4.0.0.315\lib\net40\StructureMap.Web.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Class1.cs" />
    <Compile Include="EndpointConfig.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="App.prod.config" />
    <None Include="App.qa.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\src\Jitb.Employment.Contracts\Jitb.Employment.Contracts.csproj">
      <Project>{cee04b09-0c80-488a-82d4-33b625ccd064}</Project>
      <Name>Jitb.Employment.Contracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\src\Jitb.Employment.Domain\Jitb.Employment.Domain.csproj">
      <Project>{3955df0a-7228-4f87-9810-09659e9561b0}</Project>
      <Name>Jitb.Employment.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\src\Jitb.Employment.Internal.Contracts\Jitb.Employment.Internal.Contracts.csproj">
      <Project>{7f1194bc-b0ec-44c5-b85d-79cd3b664780}</Project>
      <Name>Jitb.Employment.Internal.Contracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\src\Jitb.Extensions\Jitb.CommonLibrary.csproj">
      <Project>{e96f81f7-a76b-4384-b189-21b8088b2d57}</Project>
      <Name>Jitb.CommonLibrary</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>