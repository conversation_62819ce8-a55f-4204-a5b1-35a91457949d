﻿<?xml version="1.0" encoding="utf-8" ?>
<!-- For more information on using transformations 
     see the web.config examples at http://go.microsoft.com/fwlink/?LinkId=214134. -->
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <connectionStrings>
    <add name="NServiceBus/Persistence" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNServiceBus;integrated security=false;user id=***********;password=***********;"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    <add name="Default" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNServiceBus;integrated security=false;user id=***********;password=***********;"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
  </connectionStrings>
  <appSettings>
    <!--<add key="FileShareDataBus" value="\\example"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>-->
    <add key="ServiceControl/Queue" value="particular.servicecontrol@CSNSBT01V"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="env" value="qa" 
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EncryptionEnabled" value ="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="FIMJBX_UserID" value="NServiceBus"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="FIMJBX_Password" value="4l/5XBp0/i30KFxAF2SStw=="
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="FIMJBX_DataSource" value="CSSQLT01V\vtest1"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="FIMJBX_Catalog" value="dbNServiceBus"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="nServiceBus_UserID" value="NServiceBus"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="nServiceBus_Password" value="4l/5XBp0/i30KFxAF2SStw=="
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="nServiceBus_DataSource" value="CSSQLT01V\vtest1"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="nServiceBus_Catalog" value="dbNServiceBus"/>
    <add key="J_LDAPConnectString" value="LDAP://OU=DEVRestaurant Employees,DC=ds,DC=jitb,DC=net"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_ADUserID" value="DevUserProvisioning"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_ADPassword" value="INIfGzbWeBRWKN1y+Kfjqw=="
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_ADDomain" value="ds.jitb.net"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_ReplacementGroupDNString" value="LDAP://CN=DEVRestrictedRestaurantEmployees,CN=Users,DC=ds,DC=jitb,DC=net"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_OriginalGroupDNString" value=""
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_LDAPConnectString" value="LDAP://OU=DEVRestaurant Employees,OU=FIM,OU=Users,OU=_QDOBA,DC=rest,DC=qdoba"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_ADUserID" value="DevQDUserProvisioning"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_ADPassword" value=""
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_ADDomain" value="rest.qdoba"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_ReplacementGroupDNString" value=""
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_OriginalGroupDNString" value=""
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>

  <MessageForwardingInCaseOfFaultConfig ErrorQueue="error@CSNSBT01V" xdt:Transform="SetAttributes" />
  <AuditConfig QueueName="audit@CSNSBT01V" xdt:Transform="SetAttributes" />
  
</configuration>
