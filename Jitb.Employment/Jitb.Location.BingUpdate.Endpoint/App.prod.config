﻿<?xml version="1.0" encoding="utf-8" ?>
<!-- For more information on using transformations 
     see the web.config examples at http://go.microsoft.com/fwlink/?LinkId=214134. -->
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <connectionStrings>
    <add name="NServiceBus/Persistence" connectionString="data source=SQL12P1\VSQL1;initial catalog=dbNSB;integrated security=false;user id=nsbprod;password=**********;"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    <add name="Default" connectionString="data source=SQL12P1\VSQL1;initial catalog=dbNserviceBus;integrated security=false;user id=nsbprod;password=**********;"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
  </connectionStrings>
  <appSettings>
    <add key="ServiceControl/Queue" value="particular.servicecontrol@CSNSBP01V"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="env" value="prod" 
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EncryptionEnabled" value ="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="FIMJBX_UserID" value="fmpwps"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="FIMJBX_Password" value="3l6Pnot4QX9/SaXhfen4eA=="
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="FIMJBX_DataSource" value="SQL12P1\VSQL1"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="FIMJBX_Catalog" value="FIMJBX"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="nServiceBus_UserID" value="nsbprod"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="nServiceBus_Password" value="LT0FGn8YkVc8imqEA7Klew=="
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="nServiceBus_DataSource" value="SQL12P1\VSQL1"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="nServiceBus_Catalog" value="dbNServiceBus"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_LDAPConnectString" value="LDAP://OU=Restaurant Employees,DC=ds,DC=jitb,DC=net"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_ADUserID" value="UserProvisioning"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_ADPassword" value="Pz8AW44kJSVNG94hEBInzg=="
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_ADDomain" value="ds.jitb.net"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_ReplacementGroupDNString" value="LDAP://CN=RestrictedRestaurantEmployees,CN=Users,DC=ds,DC=jitb,DC=net"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="J_OriginalGroupDNString" value=""
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_LDAPConnectString" value="LDAP://OU=Restaurant Employees,OU=FIM,OU=Users,OU=_QDOBA,DC=rest,DC=qdoba"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_ADUserID" value="QDUserProvisioning"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_ADPassword" value="i8LKQ/Sc1y8kgarNztvlug=="
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_ADDomain" value="rest.qdoba"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_ReplacementGroupDNString" value=""
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Q_OriginalGroupDNString" value=""
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EndpointEnabled" value="true"
      xdt:Transform="Insert"/>
  </appSettings>
  <MessageForwardingInCaseOfFaultConfig ErrorQueue="error@CSNSBP01V" xdt:Transform="SetAttributes" />
  <AuditConfig QueueName="audit@CSNSBP01V" xdt:Transform="SetAttributes" />
</configuration>
