﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<configuration>
  <configSections>
    <section name="MessageForwardingInCaseOfFaultConfig" type="NServiceBus.Config.MessageForwardingInCaseOfFaultConfig, NServiceBus.Core"/>
    <section name="UnicastBusConfig" type="NServiceBus.Config.UnicastBusConfig, NServiceBus.Core"/>
    <section name="RijndaelEncryptionServiceConfig" type="NServiceBus.Config.RijndaelEncryptionServiceConfig, NServiceBus.Core"/>
    <section name="AuditConfig" type="NServiceBus.Config.AuditConfig, NServiceBus.Core"/>
  </configSections>
  <connectionStrings>
    <add name="NServiceBus/Persistence" connectionString="Data Source=.;Initial Catalog=Jitb_Employment;integrated security=SSPI;"/>
    <add name="Default" connectionString="Data Source=.;Initial Catalog=JitbUserManagement;Integrated Security=SSPI;"/>
    <add name="Model_FIMFMADEMP_WPS" connectionString="data source=CSSQLT01V\vtest1;initial catalog=dbNServiceBus;integrated security=False;user id=NServiceBus;password=***********;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="ModelEmployeeAction" connectionString="data source=CSSQLT01V\vtest1;initial catalog=dbNServiceBus;integrated security=False;User Id=NServiceBus;Password=***********;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <MessageForwardingInCaseOfFaultConfig ErrorQueue="error"/>
  <RijndaelEncryptionServiceConfig Key="tWIrVnPy3Smd2r/xcSb9r/1wcVY9/3KWbrzIaemx5yw="
                                   KeyIdentifier="2015-10"
                                   KeyFormat="Base64">
  </RijndaelEncryptionServiceConfig>
  <appSettings>
    <add key="env" value=""/>
    <add key="ServiceControl/Queue" value="particular.servicecontrol"/>
    <add key="EncryptionEnabled" value ="true"/>
    <add key="FIMJBX_UserID" value="NServiceBus"/>
    <add key="FIMJBX_Password" value="4l/5XBp0/i30KFxAF2SStw=="/>
    <add key="FIMJBX_DataSource" value="CSSQLT01V\vtest1"/>
    <add key="FIMJBX_Catalog" value="dbNServiceBus"/>
    <add key="nServiceBus_UserID" value="NServiceBus"/>
    <add key="nServiceBus_Password" value="4l/5XBp0/i30KFxAF2SStw=="/>
    <add key="nServiceBus_DataSource" value="CSSQLT01V\vtest1"/>
    <add key="nServiceBus_Catalog" value="dbNServiceBus"/>
    <add key="J_LDAPConnectString" value="LDAP://OU=DEVRestaurant Employees,DC=ds,DC=jitb,DC=net"/>
    <add key="J_ADUserID" value="DevUserProvisioning"/>
    <add key="J_ADPassword" value="INIfGzbWeBRWKN1y+Kfjqw=="/>
    <add key="J_ADDomain" value="ds.jitb.net"/>
    <add key="J_ReplacementGroupDNString" value="LDAP://CN=DEVRestrictedRestaurantEmployees,CN=Users,DC=ds,DC=jitb,DC=net"/>
    <add key="J_OriginalGroupDNString" value=""/>
    <add key="Q_LDAPConnectString" value="LDAP://OU=DEVRestaurant Employees,OU=FIM,OU=Users,OU=_QDOBA,DC=rest,DC=qdoba"/>
    <add key="Q_ADUserID" value="DevQDUserProvisioning"/>
    <add key="Q_ADPassword" value=""/>
    <add key="Q_ADDomain" value="rest.qdoba"/>
    <add key="Q_ReplacementGroupDNString" value=""/>
    <add key="Q_OriginalGroupDNString" value=""/>
    <add key="NServiceBus/License" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&lt;license UpgradeProtectionExpiration=&quot;2017-03-02&quot; Applications=&quot;All&quot; Edition=&quot;Enterprise&quot; Quantity=&quot;3&quot; MaxMessageThroughputPerSecond=&quot;Max&quot; AllowedNumberOfWorkerNodes=&quot;Max&quot; WorkerThreads=&quot;Max&quot; LicenseVersion=&quot;5.0&quot; type=&quot;Standard&quot; expiration=&quot;2116-04-19T18:03:27.1278394&quot; id=&quot;3116f539-3039-4d9b-b402-9dffcc8692dc&quot;&gt;&lt;name&gt;Jack in the Box Inc.&lt;/name&gt;&lt;Signature xmlns=&quot;http://www.w3.org/2000/09/xmldsig#&quot;&gt;&lt;SignedInfo&gt;&lt;CanonicalizationMethod Algorithm=&quot;http://www.w3.org/TR/2001/REC-xml-c14n-20010315&quot;/&gt;&lt;SignatureMethod Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#rsa-sha1&quot;/&gt;&lt;Reference URI=&quot;&quot;&gt;&lt;Transforms&gt;&lt;Transform Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#enveloped-signature&quot;/&gt;&lt;/Transforms&gt;&lt;DigestMethod Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#sha1&quot;/&gt;&lt;DigestValue&gt;Q4DLcl52+SRajlhxJqUk4HbVzEo=&lt;/DigestValue&gt;&lt;/Reference&gt;&lt;/SignedInfo&gt;&lt;SignatureValue&gt;Y6EcGxepSX2omKPtn1psTRPhvhOVmY2Ja9XgVSVtrMwSlBERip96YD/Y2Dvs0nByuRb4qodvLJiIou3js9U4WQxrgOujfWPbYBiXJsrBIXXJTSQsaL/SHPe1cYsNNKGzBVZwur+PTR7+aUddgHJmk9IQ41WZnpzlu2n4YpUjDEA=&lt;/SignatureValue&gt;&lt;/Signature&gt;&lt;/license&gt;"/>
  </appSettings>
  <AuditConfig QueueName="audit"/>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="NServiceBus.Core" publicKeyToken="9fc386479f8a226c" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2"/></startup></configuration>
