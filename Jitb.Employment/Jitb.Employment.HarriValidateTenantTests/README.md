# Jitb.Employment.HarriValidateTenantTests

This is the comprehensive unit test project for the HarriValidateTenant application, following Test-Driven Development (TDD) methodology and the unit testing guidelines established in the workspace.

## Overview

This test project provides extensive coverage for the HarriValidateTenant functionality, including:
- Configuration service validation
- Location validation logic
- Skip behavior for preexisting employees
- API interaction patterns
- Error handling scenarios
- Integration testing

## Testing Framework

The project follows the established testing patterns used throughout the workspace:

### Core Technologies
- **xUnit** - Primary testing framework
- **Moq** - Mocking framework for dependencies
- **AutoFixture** - Automatic test data generation
- **AutoMoq** - Automatic mock creation and injection
- **FluentAssertions** - Readable assertion syntax

### Test Attributes
- `[AutoMoqData]` - Automatically creates test data and mocks
- `[InlineAutoMoqData]` - Parameterized tests with inline data
- `[Frozen]` - Ensures consistent instances across test execution
- `[Trait("Category", "...")]` - Test categorization for filtering

## Test Structure

### ConfigurationServiceTests
Tests for configuration reading and validation:
- Setting validation (skip behavior, minimum counts)
- Default value handling
- Error scenarios for invalid configuration
- Backward compatibility verification

### LocationValidationServiceTests
Core business logic tests:
- Skip logic based on configuration and employee counts
- Employee loading scenarios (RORM20 job code validation)
- API interaction patterns
- Dependency injection validation

### LocationValidationServiceIntegrationTests
End-to-end and error scenario tests:
- Exception handling (API failures, JSON parsing errors)
- Edge cases (invalid location IDs, empty responses)
- Concurrent execution scenarios
- Various HTTP status code handling

### ProgramTests
Application-level tests:
- Dependency injection configuration
- Interface contract validation
- Type and namespace verification
- Service lifecycle testing

## Test Categories

Tests are organized by category for easy filtering:

- **Configuration** - Configuration service and settings tests
- **LocationValidation** - Core validation logic tests
- **Integration** - Integration and error scenario tests
- **Application** - Application startup and DI tests

## Following TDD Methodology

This test suite follows TDD principles:

1. **Red** - Tests are written first to define expected behavior
2. **Green** - Implementation is created to make tests pass
3. **Refactor** - Code is improved while maintaining test coverage

### Test Naming Convention
Tests follow the pattern: `MethodName_When[Condition]_Should[ExpectedResult]`

Examples:
- `ValidateLocationAsync_WhenTenantNotFound_ShouldReturnEarlyWithoutFurtherProcessing`
- `SkipEmployeeLoadWhenPreexistingFound_WhenSettingIsTrue_ShouldReturnTrue`
- `MinimumExistingEmployeeCountToSkip_WhenSettingMissing_ShouldReturnDefaultValue`

## Running Tests

### Visual Studio
- Use Test Explorer to run all tests or filter by category
- Right-click on project and select "Run Tests"

### Command Line
```bash
# Run all tests
dotnet test

# Run tests by category
dotnet test --filter "Category=Configuration"
dotnet test --filter "Category=LocationValidation"
dotnet test --filter "Category=Integration"
dotnet test --filter "Category=Application"
```

## Test Coverage Areas

### Configuration Service
- ? Settings validation and parsing
- ? Default value handling
- ? Error scenarios for invalid configuration
- ? Consistency across multiple calls
- ? Backward compatibility

### Location Validation Service
- ? Skip logic based on configuration
- ? Employee count thresholds
- ? RORM20 employee detection
- ? API interactions (locations, employees, events)
- ? Employee loading process
- ? Schedule inclusion
- ? Event verification after employee creation

### Error Handling
- ? API failures and timeouts
- ? Invalid JSON responses
- ? Database connection issues
- ? Null reference handling
- ? Edge case location IDs

### Integration Scenarios
- ? Dependency injection validation
- ? Interface contract verification
- ? Concurrent execution
- ? Various HTTP status codes
- ? Empty and malformed responses

## Key Test Patterns

### Parameter-Based Variable Definition
```csharp
[Theory]
[AutoMoqData]
public async Task ValidateLocationAsync_WhenTenantNotFound_ShouldReturnEarly(
    int locationId,
    [Frozen] IHarriTenantByLocationRepository repository,
    LocationValidationService sut)
```

### Minimal Arrangement with AutoFixture
```csharp
// Arrange
Mock.Get(repository)
    .Setup(x => x.GetTenantByLocation(locationId))
    .Returns((HarriTenantByLocation)null);

// Act
await sut.ValidateLocationAsync(locationId);

// Assert
Mock.Get(callHarriWebServiceProvider)
    .Verify(x => x.Call(...), Times.Never, "should not make API calls when tenant is not found");
```

### Behavior Verification
Tests focus on verifying behavior and interactions rather than implementation details:
- Verify method calls that matter for the scenario
- Use meaningful assertion messages
- Test the outcome, not the internal workings

## Maintenance Guidelines

1. **Add tests first** - When adding new functionality, write tests first
2. **One assertion per test** - Keep tests focused on single scenarios
3. **Descriptive test names** - Names should explain the scenario and expected outcome
4. **Use categories** - Categorize tests for easy filtering and organization
5. **Mock only what you need** - Don't over-mock; focus on the interaction being tested
6. **Maintain test independence** - Tests should not depend on each other

## Dependencies

The test project references:
- Main application project (`Jitb.Employment.HarriValidateTenant`)
- Domain projects for entity definitions
- HarriCaller.Domain for API interactions

All external dependencies are mocked using the AutoMoq pattern to ensure fast, reliable, and isolated tests.

## Future Enhancements

Areas for potential test expansion:
- Performance testing for large employee datasets
- Load testing for concurrent validation scenarios
- Additional error scenarios for specific API failure modes
- Configuration validation edge cases
- Enhanced integration testing with real API responses (test doubles)

This test suite provides a solid foundation for maintaining and extending the HarriValidateTenant application with confidence.