<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoFixture" version="4.18.0" targetFramework="net48" />
  <package id="AutoFixture.AutoMoq" version="4.18.0" targetFramework="net48" />
  <package id="AutoFixture.Xunit2" version="4.18.0" targetFramework="net48" />
  <package id="Castle.Core" version="4.4.0" targetFramework="net48" />
  <package id="FluentAssertions" version="6.12.0" targetFramework="net48" />
  <package id="Moq" version="4.16.1" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="RestSharp" version="106.15.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="xunit" version="2.9.3" targetFramework="net48" />
  <package id="xunit.abstractions" version="2.0.3" targetFramework="net48" />
  <package id="xunit.analyzers" version="1.18.0" targetFramework="net48" developmentDependency="true" />
  <package id="xunit.assert" version="2.9.3" targetFramework="net48" />
  <package id="xunit.core" version="2.9.3" targetFramework="net48" />
  <package id="xunit.extensibility.core" version="2.9.3" targetFramework="net48" />
  <package id="xunit.extensibility.execution" version="2.9.3" targetFramework="net48" />
  <package id="xunit.runner.visualstudio" version="3.0.2" targetFramework="net48" developmentDependency="true" />
</packages>