using AutoFixture.Xunit2;
using FluentAssertions;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.HarriValidateTenant.Services;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace Jitb.Employment.HarriValidateTenantTests
{
    /// <summary>
    /// Unit tests for the main Program class and application startup logic
    /// Following TDD methodology to ensure proper configuration and dependency injection
    /// </summary>
    public class ProgramTests
    {
        [Theory]
        [AutoMoqData]
        [Trait("Category", "Application")]
        public void ConfigurationService_WhenUsedInDI_ShouldImplementInterface(
            ConfigurationService configurationService)
        {
            // Arrange & Act
            var asInterface = configurationService as IConfigurationService;

            // Assert
            asInterface.Should().NotBeNull("ConfigurationService should implement IConfigurationService");
            asInterface.Should().Be(configurationService, "should be the same instance");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Application")]
        public void LocationValidationService_WhenUsedInDI_ShouldImplementInterface(
            LocationValidationService locationValidationService)
        {
            // Arrange & Act
            var asInterface = locationValidationService as ILocationValidationService;

            // Assert
            asInterface.Should().NotBeNull("LocationValidationService should implement ILocationValidationService");
            asInterface.Should().Be(locationValidationService, "should be the same instance");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Application")]
        public void ConfigurationService_GetProcessLocations_ShouldReturnValidLocationIds(
            ConfigurationService configurationService)
        {
            // Arrange & Act
            var locations = configurationService.GetProcessLocations();

            // Assert
            locations.Should().NotBeNull("should never return null");
            locations.Should().NotBeEmpty("should contain at least one location");
            locations.All(x => x > 0).Should().BeTrue("all location IDs should be positive");
            locations.Should().OnlyHaveUniqueItems("should not contain duplicates");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Application")]
        public void ConfigurationService_SkipConfiguration_ShouldReturnConsistentValues(
            ConfigurationService configurationService)
        {
            // Arrange
            var skipValue1 = configurationService.SkipEmployeeLoadWhenPreexistingFound();
            var skipValue2 = configurationService.SkipEmployeeLoadWhenPreexistingFound();
            var countValue1 = configurationService.MinimumExistingEmployeeCountToSkip();
            var countValue2 = configurationService.MinimumExistingEmployeeCountToSkip();

            // Act & Assert
            skipValue2.Should().Be(skipValue1, "skip setting should be consistent across calls");
            countValue2.Should().Be(countValue1, "count setting should be consistent across calls");
            countValue1.Should().BeGreaterOrEqualTo(0, "count should never be negative");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Application")]
        public void LocationValidationService_Constructor_ShouldValidateAllDependencies(
            [Frozen] IConfigurationService configurationService)
        {
            // Arrange & Act & Assert
            var nullHarriTenantAction = () => new LocationValidationService(
                null, Mock.Of<IEmployeeRepository>(), Mock.Of<ICallHarriWebServiceProvider>(),
                Mock.Of<IGenderDictionary>(), Mock.Of<IHarriTransformsProvider>(), configurationService);
            nullHarriTenantAction.Should().Throw<ArgumentNullException>()
                .And.ParamName.Should().Be("harriTenantByLocationRepository");

            var nullEmployeeAction = () => new LocationValidationService(
                Mock.Of<IHarriTenantByLocationRepository>(), null, Mock.Of<ICallHarriWebServiceProvider>(),
                Mock.Of<IGenderDictionary>(), Mock.Of<IHarriTransformsProvider>(), configurationService);
            nullEmployeeAction.Should().Throw<ArgumentNullException>()
                .And.ParamName.Should().Be("employeeRepository");

            var nullCallHarriAction = () => new LocationValidationService(
                Mock.Of<IHarriTenantByLocationRepository>(), Mock.Of<IEmployeeRepository>(), null,
                Mock.Of<IGenderDictionary>(), Mock.Of<IHarriTransformsProvider>(), configurationService);
            nullCallHarriAction.Should().Throw<ArgumentNullException>()
                .And.ParamName.Should().Be("callHarriWebServiceProvider");

            var nullGenderAction = () => new LocationValidationService(
                Mock.Of<IHarriTenantByLocationRepository>(), Mock.Of<IEmployeeRepository>(), Mock.Of<ICallHarriWebServiceProvider>(),
                null, Mock.Of<IHarriTransformsProvider>(), configurationService);
            nullGenderAction.Should().Throw<ArgumentNullException>()
                .And.ParamName.Should().Be("genderDictionary");

            var nullTransformsAction = () => new LocationValidationService(
                Mock.Of<IHarriTenantByLocationRepository>(), Mock.Of<IEmployeeRepository>(), Mock.Of<ICallHarriWebServiceProvider>(),
                Mock.Of<IGenderDictionary>(), null, configurationService);
            nullTransformsAction.Should().Throw<ArgumentNullException>()
                .And.ParamName.Should().Be("harriTransformsProvider");

            var nullConfigAction = () => new LocationValidationService(
                Mock.Of<IHarriTenantByLocationRepository>(), Mock.Of<IEmployeeRepository>(), Mock.Of<ICallHarriWebServiceProvider>(),
                Mock.Of<IGenderDictionary>(), Mock.Of<IHarriTransformsProvider>(), null);
            nullConfigAction.Should().Throw<ArgumentNullException>()
                .And.ParamName.Should().Be("configurationService");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Application")]
        public void ServiceInterfaces_ShouldBeProperlyDefined_ForDependencyInjection()
        {
            // Arrange & Act
            var configType = typeof(IConfigurationService);
            var validationServiceType = typeof(ILocationValidationService);

            // Assert
            configType.Should().BeInterface("IConfigurationService should be an interface");
            configType.IsPublic.Should().BeTrue("IConfigurationService should be public");

            validationServiceType.Should().BeInterface("ILocationValidationService should be an interface");
            validationServiceType.IsPublic.Should().BeTrue("ILocationValidationService should be public");

            // Verify interface methods
            var configMethods = configType.GetMethods();
            configMethods.Should().NotBeEmpty("IConfigurationService should have methods");
            configMethods.Should().Contain(m => m.Name == "GetProcessLocations", "should have GetProcessLocations method");
            configMethods.Should().Contain(m => m.Name == "SkipEmployeeLoadWhenPreexistingFound", "should have skip method");
            configMethods.Should().Contain(m => m.Name == "MinimumExistingEmployeeCountToSkip", "should have count method");

            var validationMethods = validationServiceType.GetMethods();
            validationMethods.Should().NotBeEmpty("ILocationValidationService should have methods");
            validationMethods.Should().Contain(m => m.Name == "ValidateLocationAsync", "should have ValidateLocationAsync method");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Application")]
        public void ApplicationConfiguration_ShouldSupportVariousLocationFormats(
            ConfigurationService configurationService)
        {
            // Arrange & Act
            var locations = configurationService.GetProcessLocations();

            // Assert
            locations.Should().NotBeNull("locations should never be null");
            
            // Test that locations are valid integers
            foreach (var location in locations)
            {
                location.Should().BeGreaterThan(0, $"location {location} should be positive");
                location.Should().BeLessOrEqualTo(999999, $"location {location} should be reasonable size");
            }
        }

        [Fact]
        [Trait("Category", "Application")]
        public void Program_ApplicationTypes_ShouldBeInCorrectNamespace()
        {
            // Arrange & Act
            var programType = Type.GetType("Jitb.Employment.HarriValidateTenant.Program, Jitb.Employment.HarriValidateTenant");
            var configServiceType = typeof(ConfigurationService);
            var validationServiceType = typeof(LocationValidationService);

            // Assert
            configServiceType.Namespace.Should().Be("Jitb.Employment.HarriValidateTenant.Services", 
                "ConfigurationService should be in Services namespace");
            validationServiceType.Namespace.Should().Be("Jitb.Employment.HarriValidateTenant.Services", 
                "LocationValidationService should be in Services namespace");
            
            if (programType != null)
            {
                programType.Namespace.Should().Be("Jitb.Employment.HarriValidateTenant", 
                    "Program should be in root namespace");
            }
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Application")]
        public void Services_ShouldHaveAsyncMethods_WhereAppropriate(
            LocationValidationService locationValidationService)
        {
            // Arrange & Act
            var validationServiceType = typeof(LocationValidationService);
            var validateMethod = validationServiceType.GetMethod("ValidateLocationAsync");

            // Assert
            validateMethod.Should().NotBeNull("ValidateLocationAsync method should exist");
            validateMethod.ReturnType.Name.Should().Contain("Task", "async methods should return Task");
            
            var parameters = validateMethod.GetParameters();
            parameters.Should().HaveCount(1, "ValidateLocationAsync should take one parameter");
            parameters[0].ParameterType.Should().Be(typeof(int), "parameter should be int location");
        }
    }
}