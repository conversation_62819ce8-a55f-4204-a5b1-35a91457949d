using AutoFixture.Xunit2;
using FluentAssertions;
using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.HarriValidateTenant.Services;
using Moq;
using RestSharp;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriValidateTenantTests.Services
{
    /// <summary>
    /// Integration tests for LocationValidationService focusing on error scenarios and edge cases
    /// Following TDD methodology to ensure robustness
    /// </summary>
    public class LocationValidationServiceIntegrationTests
    {
        [Theory]
        [AutoMoqData]
        [Trait("Category", "Integration")]
        public async Task ValidateLocationAsync_WhenHarriApiThrowsException_ShouldHandleGracefully(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(), It.IsAny<string>(), 
                    It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("API connection failed"));

            // Act & Assert
            var action = () => sut.ValidateLocationAsync(locationId);
            await action.Should().ThrowAsync<Exception>("should propagate API exceptions to caller");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Integration")]
        public async Task ValidateLocationAsync_WhenApiReturnsInvalidJson_ShouldHandleGracefully(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            var invalidJsonResponse = new RestResponse
            {
                IsSuccessful = true,
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = "{ invalid json content"
            };

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(tenantId, Method.Get, 
                    It.Is<string>(url => url.Contains("locations")), 
                    null, false, "V1"))
                .ReturnsAsync(invalidJsonResponse);

            // Act & Assert
            var action = () => sut.ValidateLocationAsync(locationId);
            await action.Should().ThrowAsync<Exception>("should handle JSON parsing errors appropriately");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Integration")]
        public async Task ValidateLocationAsync_WhenApiReturnsEmptyResponse_ShouldHandleGracefully(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            var emptyResponse = new RestResponse
            {
                IsSuccessful = true,
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = ""
            };

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(), It.IsAny<string>(), 
                    It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>()))
                .ReturnsAsync(emptyResponse);

            // Act
            await sut.ValidateLocationAsync(locationId);

            // Assert
            // Should complete without throwing - validation should handle empty responses gracefully
            Mock.Get(callHarriWebServiceProvider).Verify(
                x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(), It.IsAny<string>(), 
                    It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>()), 
                Times.AtLeastOnce, "should attempt API calls even with empty responses");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Integration")]
        public async Task ValidateLocationAsync_WhenRepositoryThrowsException_ShouldPropagate(
            int locationId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            LocationValidationService sut)
        {
            // Arrange
            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Throws(new Exception("Database connection failed"));

            // Act & Assert
            var action = () => sut.ValidateLocationAsync(locationId);
            await action.Should().ThrowAsync<Exception>("should propagate repository exceptions to caller");
        }

        [Theory]
        [InlineAutoMoqData(0)]
        [InlineAutoMoqData(-1)]
        [InlineAutoMoqData(int.MaxValue)]
        [Trait("Category", "Integration")]
        public async Task ValidateLocationAsync_WithEdgeCaseLocationIds_ShouldHandleGracefully(
            int edgeCaseLocationId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            LocationValidationService sut)
        {
            // Arrange
            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(edgeCaseLocationId))
                .Returns((HarriTenantByLocation)null);

            // Act
            await sut.ValidateLocationAsync(edgeCaseLocationId);

            // Assert
            Mock.Get(harriTenantByLocationRepository)
                .Verify(x => x.GetTenantByLocation(edgeCaseLocationId), Times.Once, 
                    "should attempt to process any location ID value");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Integration")]
        public async Task ValidateLocationAsync_WhenAllDependenciesAreNull_ShouldThrowAppropriateException(
            int locationId)
        {
            // Arrange & Act & Assert
            var action = () => new LocationValidationService(null, null, null, null, null, null);
            action.Should().Throw<ArgumentNullException>("should validate constructor dependencies");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Integration")]
        public void LocationValidationService_WhenCreatedWithValidDependencies_ShouldInitializeSuccessfully(
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            [Frozen] IGenderDictionary genderDictionary,
            [Frozen] IHarriTransformsProvider harriTransformsProvider,
            [Frozen] IConfigurationService configurationService)
        {
            // Arrange & Act
            var action = () => new LocationValidationService(
                harriTenantByLocationRepository,
                employeeRepository,
                callHarriWebServiceProvider,
                genderDictionary,
                harriTransformsProvider,
                configurationService);

            // Assert
            action.Should().NotThrow("should create successfully with all valid dependencies");
            var result = action();
            result.Should().NotBeNull("created service should not be null");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Integration")]
        public async Task ValidateLocationAsync_WhenCalledConcurrently_ShouldHandleMultipleRequests(
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            LocationValidationService sut)
        {
            // Arrange
            var locationIds = new[] { 1, 2, 3, 4, 5 };
            
            foreach (var locationId in locationIds)
            {
                Mock.Get(harriTenantByLocationRepository)
                    .Setup(x => x.GetTenantByLocation(locationId))
                    .Returns((HarriTenantByLocation)null);
            }

            // Act
            var tasks = new Task[locationIds.Length];
            for (int i = 0; i < locationIds.Length; i++)
            {
                var locationId = locationIds[i];
                tasks[i] = sut.ValidateLocationAsync(locationId);
            }

            // Assert
            var action = () => Task.WaitAll(tasks);
            action.Should().NotThrow("should handle concurrent validation requests");

            foreach (var locationId in locationIds)
            {
                Mock.Get(harriTenantByLocationRepository)
                    .Verify(x => x.GetTenantByLocation(locationId), Times.Once, 
                        $"should process location {locationId} exactly once");
            }
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "Integration")]
        public async Task ValidateLocationAsync_WhenApiReturnsDifferentStatusCodes_ShouldHandleAppropriately(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            var statusCodes = new[]
            {
                System.Net.HttpStatusCode.Unauthorized,
                System.Net.HttpStatusCode.Forbidden,
                System.Net.HttpStatusCode.InternalServerError,
                System.Net.HttpStatusCode.BadGateway
            };

            foreach (var statusCode in statusCodes)
            {
                var response = new RestResponse
                {
                    IsSuccessful = false,
                    StatusCode = statusCode,
                    Content = $"Error: {statusCode}"
                };

                Mock.Get(callHarriWebServiceProvider)
                    .Setup(x => x.Call(tenantId, Method.Get, 
                        It.Is<string>(url => url.Contains("locations")), 
                        null, false, "V1"))
                    .ReturnsAsync(response);

                // Act
                await sut.ValidateLocationAsync(locationId);

                // Assert is implicit - should not throw and should handle error gracefully
            }

            Mock.Get(callHarriWebServiceProvider)
                .Verify(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(), It.IsAny<string>(), 
                    It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>()), 
                    Times.AtLeast(statusCodes.Length), "should attempt API calls for all status codes");
        }
    }
}