using AutoFixture.Xunit2;
using FluentAssertions;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.HarriPayloads;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.HarriValidateTenant.Services;
using Moq;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriValidateTenantTests.Services
{
    /// <summary>
    /// Unit tests for LocationValidationService following TDD methodology
    /// Tests cover validation logic, skip behavior, and API interactions
    /// </summary>
    public class LocationValidationServiceTests
    {
        [Theory]
        [AutoMoqData]
        [Trait("Category", "LocationValidation")]
        public async Task ValidateLocationAsync_WhenTenantNotFound_ShouldReturnEarlyWithoutFurtherProcessing(
            int locationId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            LocationValidationService sut)
        {
            // Arrange
            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns((HarriTenantByLocation)null);

            // Act
            await sut.ValidateLocationAsync(locationId);

            // Assert
            Mock.Get(callHarriWebServiceProvider)
                .Verify(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(), It.IsAny<string>(), 
                    It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>()), 
                    Times.Never, "should not make any API calls when tenant is not found");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "LocationValidation")]
        public async Task ValidateLocationAsync_WhenSkipEnabledAndEnoughEmployees_ShouldSkipEmployeeLoading(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            [Frozen] IConfigurationService configurationService,
            [Frozen] IEmployeeRepository employeeRepository,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };
            const int existingEmployeeCount = 15;
            const int minimumCountToSkip = 10;

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            Mock.Get(configurationService)
                .Setup(x => x.SkipEmployeeLoadWhenPreexistingFound())
                .Returns(true);

            Mock.Get(configurationService)
                .Setup(x => x.MinimumExistingEmployeeCountToSkip())
                .Returns(minimumCountToSkip);

            // Setup successful location validation
            SetupSuccessfulLocationValidation(callHarriWebServiceProvider, tenantId, locationId);

            // Setup employee count response that meets skip criteria
            SetupEmployeeCountResponse(callHarriWebServiceProvider, tenantId, locationId, existingEmployeeCount);

            // Act
            await sut.ValidateLocationAsync(locationId);

            // Assert
            Mock.Get(employeeRepository)
                .Verify(x => x.GetListByLocationId(It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<bool>()), 
                    Times.Never, "should not attempt to get employees when skipping is enabled and threshold is met");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "LocationValidation")]
        public async Task ValidateLocationAsync_WhenSkipDisabledButEnoughEmployees_ShouldContinueWithEmployeeLoading(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            [Frozen] IConfigurationService configurationService,
            [Frozen] IEmployeeRepository employeeRepository,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };
            const int existingEmployeeCount = 15;
            const int minimumCountToSkip = 10;

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            Mock.Get(configurationService)
                .Setup(x => x.SkipEmployeeLoadWhenPreexistingFound())
                .Returns(false);

            Mock.Get(configurationService)
                .Setup(x => x.MinimumExistingEmployeeCountToSkip())
                .Returns(minimumCountToSkip);

            // Setup successful location validation
            SetupSuccessfulLocationValidation(callHarriWebServiceProvider, tenantId, locationId);

            // Setup employee count response
            SetupEmployeeCountResponse(callHarriWebServiceProvider, tenantId, locationId, existingEmployeeCount);

            // Setup empty employee list to avoid null reference
            Mock.Get(employeeRepository)
                .Setup(x => x.GetListByLocationId(locationId, false, false))
                .Returns(new List<Employee>().AsQueryable());

            // Act
            await sut.ValidateLocationAsync(locationId);

            // Assert
            Mock.Get(employeeRepository)
                .Verify(x => x.GetListByLocationId(locationId, false, false), 
                    Times.Once, "should attempt to get employees when skipping is disabled");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "LocationValidation")]
        public async Task ValidateLocationAsync_WhenSkipEnabledButBelowThreshold_ShouldContinueWithEmployeeLoading(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            [Frozen] IConfigurationService configurationService,
            [Frozen] IEmployeeRepository employeeRepository,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };
            const int existingEmployeeCount = 5;
            const int minimumCountToSkip = 10;

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            Mock.Get(configurationService)
                .Setup(x => x.SkipEmployeeLoadWhenPreexistingFound())
                .Returns(true);

            Mock.Get(configurationService)
                .Setup(x => x.MinimumExistingEmployeeCountToSkip())
                .Returns(minimumCountToSkip);

            // Setup successful location validation
            SetupSuccessfulLocationValidation(callHarriWebServiceProvider, tenantId, locationId);

            // Setup employee count below threshold
            SetupEmployeeCountResponse(callHarriWebServiceProvider, tenantId, locationId, existingEmployeeCount);

            // Setup empty employee list to avoid null reference
            Mock.Get(employeeRepository)
                .Setup(x => x.GetListByLocationId(locationId, false, false))
                .Returns(new List<Employee>().AsQueryable());

            // Act
            await sut.ValidateLocationAsync(locationId);

            // Assert
            Mock.Get(employeeRepository)
                .Verify(x => x.GetListByLocationId(locationId, false, false), 
                    Times.Once, "should attempt to get employees when threshold is not met");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "LocationValidation")]
        public async Task ValidateLocationAsync_WhenLocationValidationFails_ShouldReturnEarly(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            [Frozen] IEmployeeRepository employeeRepository,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // Setup failed location validation
            var failedLocationResponse = new RestResponse
            {
                IsSuccessful = false,
                StatusCode = System.Net.HttpStatusCode.NotFound,
                Content = "Location not found"
            };

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(tenantId, Method.Get, 
                    It.Is<string>(url => url.Contains("locations")), 
                    null, false, "V1"))
                .ReturnsAsync(failedLocationResponse);

            // Act
            await sut.ValidateLocationAsync(locationId);

            // Assert
            Mock.Get(employeeRepository)
                .Verify(x => x.GetListByLocationId(It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<bool>()), 
                    Times.Never, "should not attempt to get employees when location validation fails");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "LocationValidation")]
        public async Task ValidateLocationAsync_WhenRorm20EmployeeFound_ShouldAttemptToLoadEmployee(
            int locationId,
            Guid tenantId,
            int badgeId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            [Frozen] IConfigurationService configurationService,
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] IHarriTransformsProvider harriTransformsProvider,
            [Frozen] IGenderDictionary genderDictionary,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };
            var employee = CreateRorm20Employee(badgeId);

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            Mock.Get(configurationService)
                .Setup(x => x.SkipEmployeeLoadWhenPreexistingFound())
                .Returns(false); // Force employee loading

            // Setup successful location validation
            SetupSuccessfulLocationValidation(callHarriWebServiceProvider, tenantId, locationId);

            // Setup low employee count to force loading
            SetupEmployeeCountResponse(callHarriWebServiceProvider, tenantId, locationId, 0);

            // Setup employee repository to return RORM20 employee
            Mock.Get(employeeRepository)
                .Setup(x => x.GetListByLocationId(locationId, false, false))
                .Returns(new List<Employee> { employee }.AsQueryable());

            // Setup transform providers
            Mock.Get(harriTransformsProvider)
                .Setup(x => x.GetEmployeeEmail(employee))
                .Returns("<EMAIL>");

            Mock.Get(harriTransformsProvider)
                .Setup(x => x.GetFirstName(employee))
                .Returns(employee.FirstName);

            Mock.Get(genderDictionary)
                .Setup(x => x.Translate(employee.Sex))
                .Returns("Male");

            // Setup successful employee creation
            var successfulEmployeeResponse = new RestResponse
            {
                IsSuccessful = true,
                StatusCode = System.Net.HttpStatusCode.Created,
                Content = "{\"id\":\"" + badgeId + "\"}"
            };

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(tenantId, Method.Post, "employees", 
                    It.IsAny<string>(), false, "V3"))
                .ReturnsAsync(successfulEmployeeResponse);

            // Setup successful schedule update
            var successfulScheduleResponse = new RestResponse
            {
                IsSuccessful = true,
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = "{\"success\":true}"
            };

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(tenantId, Method.Put, 
                    $"employees/{badgeId}/include_in_schedule", 
                    It.IsAny<string>(), false, "V1"))
                .ReturnsAsync(successfulScheduleResponse);

            // Setup events endpoint for event checking
            var eventsResponse = new RestResponse
            {
                IsSuccessful = true,
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = "{\"events\":[]}"
            };

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(tenantId, Method.Get, "events", null, false, "V1"))
                .ReturnsAsync(eventsResponse);

            // Act
            await sut.ValidateLocationAsync(locationId);

            // Assert
            Mock.Get(callHarriWebServiceProvider)
                .Verify(x => x.Call(tenantId, Method.Post, "employees", 
                    It.IsAny<string>(), false, "V3"), 
                    Times.Once, "should attempt to create employee in Harri");

            Mock.Get(callHarriWebServiceProvider)
                .Verify(x => x.Call(tenantId, Method.Put, 
                    $"employees/{badgeId}/include_in_schedule", 
                    It.IsAny<string>(), false, "V1"), 
                    Times.Once, "should attempt to add employee to schedule");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "LocationValidation")]
        public async Task ValidateLocationAsync_WhenNoRorm20EmployeeFound_ShouldNotAttemptEmployeeLoading(
            int locationId,
            Guid tenantId,
            [Frozen] IHarriTenantByLocationRepository harriTenantByLocationRepository,
            [Frozen] ICallHarriWebServiceProvider callHarriWebServiceProvider,
            [Frozen] IConfigurationService configurationService,
            [Frozen] IEmployeeRepository employeeRepository,
            LocationValidationService sut)
        {
            // Arrange
            var harriTenant = new HarriTenantByLocation { LocationId = locationId, TenantId = tenantId };
            var nonRorm20Employee = CreateNonRorm20Employee();

            Mock.Get(harriTenantByLocationRepository)
                .Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            Mock.Get(configurationService)
                .Setup(x => x.SkipEmployeeLoadWhenPreexistingFound())
                .Returns(false);

            // Setup successful location validation
            SetupSuccessfulLocationValidation(callHarriWebServiceProvider, tenantId, locationId);

            // Setup low employee count to force loading attempt
            SetupEmployeeCountResponse(callHarriWebServiceProvider, tenantId, locationId, 0);

            // Setup employee repository to return non-RORM20 employee
            Mock.Get(employeeRepository)
                .Setup(x => x.GetListByLocationId(locationId, false, false))
                .Returns(new List<Employee> { nonRorm20Employee }.AsQueryable());

            // Act
            await sut.ValidateLocationAsync(locationId);

            // Assert
            Mock.Get(callHarriWebServiceProvider)
                .Verify(x => x.Call(It.IsAny<Guid>(), Method.Post, "employees", 
                    It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>()), 
                    Times.Never, "should not attempt to create employee when no RORM20 employee is found");
        }

        [Theory]
        [AutoMoqData]
        [Trait("Category", "LocationValidation")]
        public void LocationValidationService_WhenInstantiated_ShouldNotThrow(
            LocationValidationService sut)
        {
            // Arrange & Act - constructor called by AutoFixture
            
            // Assert
            sut.Should().NotBeNull("service should be created successfully");
        }

        // Helper methods for test setup
        private static void SetupSuccessfulLocationValidation(
            ICallHarriWebServiceProvider callHarriWebServiceProvider, 
            Guid tenantId, 
            int locationId)
        {
            var locationResponse = new RestResponse
            {
                IsSuccessful = true,
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = $"[{{\"Id\": {locationId}, \"Name\": \"J{locationId:D6}\", \"Type\": \"Store\"}}]"
            };

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(tenantId, Method.Get, 
                    It.Is<string>(url => url.Contains("locations")), 
                    null, false, "V1"))
                .ReturnsAsync(locationResponse);
        }

        private static void SetupEmployeeCountResponse(
            ICallHarriWebServiceProvider callHarriWebServiceProvider, 
            Guid tenantId, 
            int locationId, 
            int employeeCount)
        {
            var employees = new List<object>();
            for (int i = 0; i < employeeCount; i++)
            {
                employees.Add(new { Id = i + 1, Name = $"Employee{i + 1}" });
            }

            var employeeResponse = new RestResponse
            {
                IsSuccessful = true,
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = $"{{\"Employees\": {JsonConvert.SerializeObject(employees)}}}"
            };

            Mock.Get(callHarriWebServiceProvider)
                .Setup(x => x.Call(tenantId, Method.Get, 
                    It.Is<string>(url => url.Contains("employees") && url.Contains($"location_id={locationId}")), 
                    null, false, "V2"))
                .ReturnsAsync(employeeResponse);
        }

        private static Employee CreateRorm20Employee(int badgeId)
        {
            return new Employee
            {
                BadgeId = badgeId,
                EmployeeId = badgeId,
                FirstName = "John",
                LastName = "Doe",
                CurrJobCodeCode = "RORM20",
                CurrentStatus = "01",
                LastHomeLocationNumber = 123,
                HireDate = DateTime.Today.AddDays(-30),
                BirthDate = DateTime.Today.AddYears(-25),
                Sex = "M"
            };
        }

        private static Employee CreateNonRorm20Employee()
        {
            return new Employee
            {
                BadgeId = 99999,
                EmployeeId = 99999,
                FirstName = "Jane",
                LastName = "Smith",
                CurrJobCodeCode = "MGMT",
                CurrentStatus = "01",
                LastHomeLocationNumber = 123
            };
        }
    }
}