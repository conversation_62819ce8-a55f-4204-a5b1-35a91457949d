using AutoFixture.Xunit2;
using FluentAssertions;
using Jitb.Employment.HarriValidateTenant.Services;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriValidateTenantTests.Services
{
    /// <summary>
    /// Unit tests for TenantProcessingResultCapture following TDD methodology.
    /// These tests define the expected behavior for capturing validation results during tenant processing.
    /// CRITICAL: These tests are designed to FAIL initially (TDD Red phase) as implementation does not exist yet.
    /// </summary>
    [Trait("Category", "ResultCapture")]
    public class TenantProcessingResultCaptureTests
    {
        #region Result Capture Tests

        [Theory]
        [AutoMoqData]
        public void CaptureValidationResults_WhenValidationCompletes_CapturesAllComponents(
            string tenantId,
            int locationId,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            var expectedComponents = new[] 
            { 
                "Tenant Lookup", "GetLocations", "Load Employee", 
                "Position Mapping", "Event Received", "Final Result" 
            };

            // Act
            var result = sut.CaptureValidationForTenant(tenantId, locationId);

            // Assert
            result.Should().NotBeNull("validation result should be captured");
            result.TenantId.Should().Be(tenantId, "should capture correct tenant ID");
            result.ProcessedLocation.Should().Be(locationId, "should capture processed location");
            
            // Verify all expected components are tracked
            result.ComponentResults.Should().HaveCount(expectedComponents.Length, 
                "should capture results for all validation components");
            
            foreach (var expectedComponent in expectedComponents)
            {
                result.ComponentResults.Should().Contain(c => c.ComponentName == expectedComponent,
                    $"should capture result for {expectedComponent} component");
            }
            
            // Test will fail initially because TenantValidationResultCapture doesn't exist yet
        }

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenTenantLookupFails_CapturesTenantNotFoundResult(
            int locationId,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(It.IsAny<string>(), locationId))
                .ThrowsAsync(new InvalidOperationException("Tenant not found for location"));

            // Act
            var result = await sut.CaptureValidationForTenantAsync(null, locationId);

            // Assert
            result.TenantId.Should().BeNull("should capture null tenant ID for unfound tenant");
            result.ComponentResults.Should().Contain(c => 
                c.ComponentName == "Tenant Lookup" && 
                c.Status == "Tenant Not Found",
                "should capture tenant not found status");
            
            // Test will fail initially because async capture method doesn't exist yet
        }

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenGetLocationsSucceeds_CapturesPassResult(
            string tenantId,
            int locationId,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(tenantId, locationId))
                .Returns(Task.CompletedTask);

            // Mock successful GetLocations API call
            sut.SetGetLocationsResult(true, "");

            // Act
            var result = await sut.CaptureValidationForTenantAsync(tenantId, locationId);

            // Assert
            result.ComponentResults.Should().Contain(c => 
                c.ComponentName == "GetLocations" && 
                c.Status == "Pass",
                "should capture successful GetLocations result");
            
            // Test will fail initially because result setting methods don't exist yet
        }

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenEmployeeLoadSkipped_CapturesSkippedWithReason(
            string tenantId,
            int locationId,
            string skipReason,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(tenantId, locationId))
                .Returns(Task.CompletedTask);

            // Mock employee load being skipped
            sut.SetEmployeeLoadResult(false, skipReason, true); // success=false, reason=skipReason, isSkipped=true

            // Act
            var result = await sut.CaptureValidationForTenantAsync(tenantId, locationId);

            // Assert
            var employeeLoadResult = result.ComponentResults.First(c => c.ComponentName == "Load Employee");
            employeeLoadResult.Status.Should().Be("Skipped", "should capture skipped status");
            employeeLoadResult.Details.Should().Be(skipReason, "should capture skip reason");
            
            // Test will fail initially because skip logic doesn't exist yet
        }

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenPositionMappingFails_CapturesDetailedFailureInfo(
            string tenantId,
            int locationId,
            List<string> missingPositions,
            List<string> additionalPositions,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(tenantId, locationId))
                .Returns(Task.CompletedTask);

            // Mock position mapping failure with specific missing and additional positions
            sut.SetPositionMappingResult(false, missingPositions, additionalPositions);

            // Act
            var result = await sut.CaptureValidationForTenantAsync(tenantId, locationId);

            // Assert
            var positionResult = result.ComponentResults.First(c => c.ComponentName == "Position Mapping");
            positionResult.Status.Should().Be("Fail", "should capture failed status");
            positionResult.Details.Should().Contain($"Missing: {string.Join(", ", missingPositions)}", 
                "should capture missing positions");
            positionResult.Details.Should().Contain($"Additional: {string.Join(", ", additionalPositions)}", 
                "should capture additional positions");
            
            // Test will fail initially because detailed failure capture doesn't exist yet
        }

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenEventReceived_CapturesTrueResult(
            string tenantId,
            int locationId,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(tenantId, locationId))
                .Returns(Task.CompletedTask);

            sut.SetEventReceivedResult(true);

            // Act
            var result = await sut.CaptureValidationForTenantAsync(tenantId, locationId);

            // Assert
            result.ComponentResults.Should().Contain(c => 
                c.ComponentName == "Event Received" && 
                c.Status == "True",
                "should capture event received as True");
            
            // Test will fail initially because event tracking doesn't exist yet
        }

        #endregion

        #region Multiple Tenant Processing Tests

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenMultipleTenantsProcessed_AggregatesToSummary(
            List<string> tenantIds,
            List<int> locationIds,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            var expectedResultCount = Math.Min(tenantIds.Count, locationIds.Count);
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(It.IsAny<string>(), It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var results = new List<TenantValidationResult>();
            for (int i = 0; i < expectedResultCount; i++)
            {
                var result = await sut.CaptureValidationForTenantAsync(tenantIds[i], locationIds[i]);
                results.Add(result);
            }

            // Assert
            results.Should().HaveCount(expectedResultCount, "should capture results for all processed tenants");
            
            // Verify tenant isolation - each result should have unique tenant/location combination
            var tenantLocationPairs = results.Select(r => new { r.TenantId, r.ProcessedLocation }).ToList();
            tenantLocationPairs.Should().OnlyHaveUniqueItems("each tenant/location combination should be unique");
            
            // Verify no cross-contamination between tenant results
            foreach (var result in results)
            {
                result.ComponentResults.Should().NotBeEmpty("each tenant should have component results");
                result.ComponentResults.Should().AllSatisfy(c => 
                    !string.IsNullOrEmpty(c.ComponentName), "all components should have names");
            }
            
            // Test will fail initially because multi-tenant aggregation doesn't exist yet
        }

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenProcessingWithUntestedLocations_TracksUntestedCorrectly(
            string tenantId,
            int processedLocationId,
            List<int> untestedLocationIds,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(tenantId, processedLocationId))
                .Returns(Task.CompletedTask);

            sut.SetUntestedLocationsForTenant(tenantId, untestedLocationIds);

            // Act
            var result = await sut.CaptureValidationForTenantAsync(tenantId, processedLocationId);

            // Assert
            result.UntestedLocations.Should().BeEquivalentTo(untestedLocationIds, 
                "should track all untested locations for the tenant");
            
            result.ComponentResults.Should().Contain(c => 
                c.ComponentName == "Additional Locations" && 
                c.Status == "Not Tested" &&
                c.Details == string.Join(", ", untestedLocationIds),
                "should include untested locations as a component result");
            
            // Test will fail initially because untested location tracking doesn't exist yet
        }

        #endregion

        #region Final Result Determination Tests

        [Theory]
        [InlineAutoMoqData(true, true, true, true, "Pass")]
        [InlineAutoMoqData(true, true, false, true, "Fail")]
        [InlineAutoMoqData(true, false, true, true, "Fail")]
        [InlineAutoMoqData(false, true, true, true, "Fail")]
        public async Task CaptureValidationResults_WhenComponentsComplete_DeterminesFinalResultCorrectly(
            bool getLocationsSuccess,
            bool loadEmployeeSuccess,
            bool positionMappingSuccess,
            bool eventReceived,
            string expectedFinalResult,
            string tenantId,
            int locationId,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(tenantId, locationId))
                .Returns(Task.CompletedTask);

            sut.SetGetLocationsResult(getLocationsSuccess, "");
            sut.SetEmployeeLoadResult(loadEmployeeSuccess, "", false);
            sut.SetPositionMappingResult(positionMappingSuccess, new List<string>(), new List<string>());
            sut.SetEventReceivedResult(eventReceived);

            // Act
            var result = await sut.CaptureValidationForTenantAsync(tenantId, locationId);

            // Assert
            result.FinalResult.Should().Be(expectedFinalResult, 
                "final result should be determined based on component success/failure");
            
            result.ComponentResults.Should().Contain(c => 
                c.ComponentName == "Final Result" && 
                c.Status == expectedFinalResult,
                "should include final result as a component");
            
            // Test will fail initially because final result determination logic doesn't exist yet
        }

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenEmployeeLoadSkippedButOthersPass_ShouldStillPass(
            string tenantId,
            int locationId,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange - employee load is skipped but other components pass
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(tenantId, locationId))
                .Returns(Task.CompletedTask);

            sut.SetGetLocationsResult(true, "");
            sut.SetEmployeeLoadResult(false, "Sufficient existing employees", true); // skipped
            sut.SetPositionMappingResult(true, new List<string>(), new List<string>());
            sut.SetEventReceivedResult(false); // Expected to be false when employee load is skipped

            // Act
            var result = await sut.CaptureValidationForTenantAsync(tenantId, locationId);

            // Assert
            result.FinalResult.Should().Be("Pass", 
                "final result should be Pass when employee load is skipped for valid reasons");
            
            var employeeLoadResult = result.ComponentResults.First(c => c.ComponentName == "Load Employee");
            employeeLoadResult.Status.Should().Be("Skipped");
            
            // Test will fail initially because skipped component handling in final result doesn't exist yet
        }

        #endregion

        #region Performance and Timing Tests

        [Theory]
        [AutoMoqData]
        public async Task CaptureValidationResults_WhenValidationCompletes_CapturesTimingInformation(
            string tenantId,
            int locationId,
            [Frozen] Mock<ITenantValidationService> mockValidationService,
            TenantValidationResultCapture sut)
        {
            // Arrange
            mockValidationService
                .Setup(x => x.ValidateLocationForTenantAsync(tenantId, locationId))
                .Returns(Task.Delay(100)); // Simulate processing time

            var startTime = DateTime.Now;

            // Act
            var result = await sut.CaptureValidationForTenantAsync(tenantId, locationId);
            var endTime = DateTime.Now;

            // Assert
            result.Should().NotBeNull();
            result.ProcessingStartTime.Should().BeCloseTo(startTime, TimeSpan.FromSeconds(1), 
                "should capture processing start time");
            result.ProcessingEndTime.Should().BeCloseTo(endTime, TimeSpan.FromSeconds(1), 
                "should capture processing end time");
            result.ProcessingDuration.Should().BeGreaterThan(TimeSpan.FromMilliseconds(50), 
                "should capture processing duration");
            
            // Test will fail initially because timing capture doesn't exist yet
        }

        #endregion

        #region Error Handling Tests

        [Theory]
        [AutoMoqData]
        public void CaptureValidationForTenant_WhenGivenNullTenantId_AcceptsNullForUnfoundTenants(
            int locationId,
            TenantValidationResultCapture sut)
        {
            // Act & Assert - Should not throw for null tenant (valid scenario)
            Action act = () => sut.CaptureValidationForTenant(null, locationId);
            act.Should().NotThrow("null tenant ID should be accepted for locations without tenants");
            
            // Test will fail initially because null tenant handling doesn't exist yet
        }

        [Theory]
        [AutoMoqData]
        public void CaptureValidationForTenant_WhenGivenInvalidLocationId_ThrowsArgumentException(
            string tenantId,
            TenantValidationResultCapture sut)
        {
            // Act & Assert
            Action act = () => sut.CaptureValidationForTenant(tenantId, 0);
            act.Should().Throw<ArgumentException>()
                .WithMessage("*locationId*", "should validate location ID parameter");
            
            Action act2 = () => sut.CaptureValidationForTenant(tenantId, -1);
            act2.Should().Throw<ArgumentException>()
                .WithMessage("*locationId*", "should validate location ID parameter");
            
            // Test will fail initially because input validation doesn't exist yet
        }

        #endregion
    }

    #region Service Interfaces and Models (These will be defined by the failing tests)

    /// <summary>
    /// Service for capturing tenant validation results during processing.
    /// This interface will be created to make the failing tests pass.
    /// </summary>
    public interface ITenantValidationResultCapture
    {
        /// <summary>
        /// Captures validation results for a tenant synchronously.
        /// </summary>
        TenantValidationResult CaptureValidationForTenant(string tenantId, int locationId);

        /// <summary>
        /// Captures validation results for a tenant asynchronously.
        /// </summary>
        Task<TenantValidationResult> CaptureValidationForTenantAsync(string tenantId, int locationId);

        /// <summary>
        /// Sets the result for GetLocations component.
        /// </summary>
        void SetGetLocationsResult(bool success, string details);

        /// <summary>
        /// Sets the result for Employee Load component.
        /// </summary>
        void SetEmployeeLoadResult(bool success, string details, bool isSkipped);

        /// <summary>
        /// Sets the result for Position Mapping component.
        /// </summary>
        void SetPositionMappingResult(bool success, List<string> missingPositions, List<string> additionalPositions);

        /// <summary>
        /// Sets the result for Event Received component.
        /// </summary>
        void SetEventReceivedResult(bool received);

        /// <summary>
        /// Sets the untested locations for a tenant.
        /// </summary>
        void SetUntestedLocationsForTenant(string tenantId, List<int> untestedLocationIds);
    }

    /// <summary>
    /// Implementation of tenant validation result capture.
    /// This class will be created to make the failing tests pass.
    /// </summary>
    public class TenantValidationResultCapture : ITenantValidationResultCapture
    {
        public TenantValidationResult CaptureValidationForTenant(string tenantId, int locationId)
        {
            // This implementation will be created during the TDD Green phase
            // Currently throwing NotImplementedException to ensure tests fail (TDD Red phase)
            throw new NotImplementedException("TenantValidationResultCapture.CaptureValidationForTenant not implemented yet - this is expected in TDD Red phase");
        }

        public Task<TenantValidationResult> CaptureValidationForTenantAsync(string tenantId, int locationId)
        {
            // This implementation will be created during the TDD Green phase
            // Currently throwing NotImplementedException to ensure tests fail (TDD Red phase)
            throw new NotImplementedException("TenantValidationResultCapture.CaptureValidationForTenantAsync not implemented yet - this is expected in TDD Red phase");
        }

        public void SetGetLocationsResult(bool success, string details)
        {
            throw new NotImplementedException("SetGetLocationsResult not implemented yet - this is expected in TDD Red phase");
        }

        public void SetEmployeeLoadResult(bool success, string details, bool isSkipped)
        {
            throw new NotImplementedException("SetEmployeeLoadResult not implemented yet - this is expected in TDD Red phase");
        }

        public void SetPositionMappingResult(bool success, List<string> missingPositions, List<string> additionalPositions)
        {
            throw new NotImplementedException("SetPositionMappingResult not implemented yet - this is expected in TDD Red phase");
        }

        public void SetEventReceivedResult(bool received)
        {
            throw new NotImplementedException("SetEventReceivedResult not implemented yet - this is expected in TDD Red phase");
        }

        public void SetUntestedLocationsForTenant(string tenantId, List<int> untestedLocationIds)
        {
            throw new NotImplementedException("SetUntestedLocationsForTenant not implemented yet - this is expected in TDD Red phase");
        }
    }

    #endregion

    #region Extended Data Models

    /// <summary>
    /// Extended TenantValidationResult with timing information.
    /// This extension will be added to make timing tests pass.
    /// </summary>
    public partial class TenantValidationResult
    {
        public DateTime ProcessingStartTime { get; set; }
        public DateTime ProcessingEndTime { get; set; }
        public TimeSpan ProcessingDuration => ProcessingEndTime - ProcessingStartTime;
    }

    #endregion
}