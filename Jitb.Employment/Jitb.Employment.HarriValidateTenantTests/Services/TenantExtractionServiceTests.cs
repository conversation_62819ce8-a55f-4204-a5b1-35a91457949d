using System;
using System.Collections.Generic;
using System.Linq;
using AutoFixture.Xunit2;
using FluentAssertions;
using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.HarriValidateTenant.Services;
using Moq;
using Xunit;

namespace Jitb.Employment.HarriValidateTenantTests.Services
{
    /// <summary>
    /// TDD Tests for TenantExtractionService - these tests define the expected behavior for tenant extraction logic.
    /// These tests WILL FAIL initially as the implementation does not exist yet (Red phase of TDD).
    /// </summary>
    public class TenantExtractionServiceTests
    {
        /// <summary>
        /// Test that given a list of locations with mixed tenant associations, 
        /// the service correctly groups them by unique tenants including null tenant handling.
        /// 
        /// This test WILL FAIL initially because TenantExtractionService does not exist yet.
        /// </summary>
        [Theory]
        [AutoMoqData]
        public void ExtractTenantsFromLocations_WhenGivenLocationList_ReturnsUniqueTenantMappings(
            [Frozen] IHarriTenantByLocationRepository tenantRepository,
            TenantExtractionService sut)
        {
            // Arrange - Define test scenario with mixed tenant associations
            var locations = new List<int> { 1234, 1235, 2345, 2346, 2347, 3456, 3457, 3458, 3459 };
            
            var tenantA = Guid.NewGuid();
            var tenantB = Guid.NewGuid();
            
            // Setup repository responses - some locations have tenants, some don't
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(1234))
                .Returns((HarriTenantByLocation)null); // No tenant
                
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(1235))
                .Returns((HarriTenantByLocation)null); // No tenant
                
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(2345))
                .Returns(new HarriTenantByLocation { TenantId = tenantA, Location = 2345 });
                
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(2346))
                .Returns(new HarriTenantByLocation { TenantId = tenantA, Location = 2346 });
                
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(2347))
                .Returns(new HarriTenantByLocation { TenantId = tenantA, Location = 2347 });
                
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(3456))
                .Returns(new HarriTenantByLocation { TenantId = tenantB, Location = 3456 });
                
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(3457))
                .Returns(new HarriTenantByLocation { TenantId = tenantB, Location = 3457 });
                
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(3458))
                .Returns(new HarriTenantByLocation { TenantId = tenantB, Location = 3458 });
                
            Mock.Get(tenantRepository)
                .Setup(repo => repo.GetTenantByLocation(3459))
                .Returns(new HarriTenantByLocation { TenantId = tenantB, Location = 3459 });

            // Act - This will fail because ExtractTenantsFromLocations method doesn't exist
            var result = sut.ExtractTenantsFromLocations(locations);

            // Assert - Define expected tenant-location mappings
            result.Should().HaveCount(3, "there should be 3 unique tenants: (null), TenantA, and TenantB");
            
            result.Should().ContainKey("(null)")
                .WhoseValue.Should().BeEquivalentTo(new List<int> { 1234, 1235 }, 
                "locations without tenants should be grouped under '(null)' key");
                
            result.Should().ContainKey(tenantA.ToString())
                .WhoseValue.Should().BeEquivalentTo(new List<int> { 2345, 2346, 2347 },
                "locations belonging to TenantA should be grouped together");
                
            result.Should().ContainKey(tenantB.ToString())
                .WhoseValue.Should().BeEquivalentTo(new List<int> { 3456, 3457, 3458, 3459 },
                "locations belonging to TenantB should be grouped together");
        }

        /// <summary>
        /// Test edge case where no locations have associated tenants.
        /// Should return single "(null)" tenant with all locations.
        /// 
        /// This test WILL FAIL initially because TenantExtractionService does not exist yet.
        /// </summary>
        [Theory]
        [AutoMoqData]
        public void ExtractTenantsFromLocations_WhenAllLocationsHaveNoTenant_ReturnsNullTenantMapping(
            [Frozen] IHarriTenantByLocationRepository tenantRepository,
            TenantExtractionService sut)
        {
            // Arrange - All locations return null (no tenant)
            var locations = new List<int> { 1111, 2222, 3333 };
            
            foreach (var location in locations)
            {
                Mock.Get(tenantRepository)
                    .Setup(repo => repo.GetTenantByLocation(location))
                    .Returns((HarriTenantByLocation)null);
            }

            // Act - This will fail because ExtractTenantsFromLocations method doesn't exist
            var result = sut.ExtractTenantsFromLocations(locations);

            // Assert - Should return single null tenant mapping
            result.Should().HaveCount(1, "all locations without tenants should result in single '(null)' tenant");
            result.Should().ContainKey("(null)")
                .WhoseValue.Should().BeEquivalentTo(locations,
                "all locations should be grouped under '(null)' key when no tenants exist");
        }

        /// <summary>
        /// Test edge case where all locations belong to the same tenant.
        /// Should return single tenant with all locations.
        /// 
        /// This test WILL FAIL initially because TenantExtractionService does not exist yet.
        /// </summary>
        [Theory]
        [AutoMoqData]
        public void ExtractTenantsFromLocations_WhenAllLocationsBelongToSameTenant_ReturnsSingleTenantMapping(
            [Frozen] IHarriTenantByLocationRepository tenantRepository,
            TenantExtractionService sut)
        {
            // Arrange - All locations belong to same tenant
            var locations = new List<int> { 1111, 2222, 3333 };
            var singleTenant = Guid.NewGuid();
            
            foreach (var location in locations)
            {
                Mock.Get(tenantRepository)
                    .Setup(repo => repo.GetTenantByLocation(location))
                    .Returns(new HarriTenantByLocation { TenantId = singleTenant, Location = location });
            }

            // Act - This will fail because ExtractTenantsFromLocations method doesn't exist
            var result = sut.ExtractTenantsFromLocations(locations);

            // Assert - Should return single tenant mapping
            result.Should().HaveCount(1, "all locations belonging to same tenant should result in single tenant mapping");
            result.Should().ContainKey(singleTenant.ToString())
                .WhoseValue.Should().BeEquivalentTo(locations,
                "all locations should be grouped under the single tenant");
        }

        /// <summary>
        /// Test that from tenant-location mappings, the service selects the first location per tenant.
        /// 
        /// This test WILL FAIL initially because SelectFirstLocationPerTenant method doesn't exist yet.
        /// </summary>
        [Theory]
        [AutoMoqData]
        public void SelectFirstLocationPerTenant_WhenGivenTenantLocationMappings_ReturnsFirstLocationPerTenant(
            TenantExtractionService sut)
        {
            // Arrange - Create tenant-location mappings
            var tenantLocationMappings = new Dictionary<string, List<int>>
            {
                { "(null)", new List<int> { 1234, 1235 } },
                { Guid.NewGuid().ToString(), new List<int> { 2345, 2346, 2347 } },
                { Guid.NewGuid().ToString(), new List<int> { 3456, 3457, 3458, 3459 } }
            };

            // Act - This will fail because SelectFirstLocationPerTenant method doesn't exist
            var result = sut.SelectFirstLocationPerTenant(tenantLocationMappings);

            // Assert - Should return first location for each tenant
            result.Should().HaveCount(3, "should return one location per tenant");
            
            result.Should().ContainValue(1234, "should select first location (1234) for '(null)' tenant");
            result.Should().ContainValue(2345, "should select first location (2345) for second tenant");
            result.Should().ContainValue(3456, "should select first location (3456) for third tenant");

            // Verify each tenant maps to their first location
            var nullTenant = tenantLocationMappings.Keys.First(k => k == "(null)");
            var secondTenant = tenantLocationMappings.Keys.Skip(1).First();
            var thirdTenant = tenantLocationMappings.Keys.Skip(2).First();
            
            result[nullTenant].Should().Be(1234, "null tenant should map to first location 1234");
            result[secondTenant].Should().Be(2345, "second tenant should map to first location 2345");
            result[thirdTenant].Should().Be(3456, "third tenant should map to first location 3456");
        }

        /// <summary>
        /// Test edge case where each tenant only has one location.
        /// Should return that same location for each tenant.
        /// 
        /// This test WILL FAIL initially because SelectFirstLocationPerTenant method doesn't exist yet.
        /// </summary>
        [Theory]
        [AutoMoqData]
        public void SelectFirstLocationPerTenant_WhenTenantHasSingleLocation_ReturnsTheSameLocation(
            TenantExtractionService sut)
        {
            // Arrange - Each tenant has only one location
            var tenantLocationMappings = new Dictionary<string, List<int>>
            {
                { "(null)", new List<int> { 1111 } },
                { Guid.NewGuid().ToString(), new List<int> { 2222 } },
                { Guid.NewGuid().ToString(), new List<int> { 3333 } }
            };

            // Act - This will fail because SelectFirstLocationPerTenant method doesn't exist
            var result = sut.SelectFirstLocationPerTenant(tenantLocationMappings);

            // Assert - Should return the single location for each tenant
            result.Should().HaveCount(3, "should return one location per tenant");
            
            result.Should().ContainValue(1111, "should return the single location 1111 for '(null)' tenant");
            result.Should().ContainValue(2222, "should return the single location 2222 for second tenant");
            result.Should().ContainValue(3333, "should return the single location 3333 for third tenant");

            // Verify mapping correctness
            foreach (var kvp in tenantLocationMappings)
            {
                var tenant = kvp.Key;
                var expectedLocation = kvp.Value.Single();
                result[tenant].Should().Be(expectedLocation, 
                    $"tenant {tenant} should map to their single location {expectedLocation}");
            }
        }

        /// <summary>
        /// Test empty location list handling.
        /// Should return empty dictionary when no locations provided.
        /// 
        /// This test WILL FAIL initially because ExtractTenantsFromLocations method doesn't exist yet.
        /// </summary>
        [Theory]
        [AutoMoqData]
        public void ExtractTenantsFromLocations_WhenGivenEmptyLocationList_ReturnsEmptyDictionary(
            [Frozen] IHarriTenantByLocationRepository tenantRepository,
            TenantExtractionService sut)
        {
            // Arrange - Empty location list
            var locations = new List<int>();

            // Act - This will fail because ExtractTenantsFromLocations method doesn't exist
            var result = sut.ExtractTenantsFromLocations(locations);

            // Assert - Should return empty dictionary
            result.Should().BeEmpty("empty location list should result in empty tenant mappings");
            
            // Verify repository was not called
            Mock.Get(tenantRepository)
                .Verify(repo => repo.GetTenantByLocation(It.IsAny<int>()), Times.Never,
                "repository should not be called when location list is empty");
        }

        /// <summary>
        /// Test null location list handling.
        /// Should throw ArgumentNullException when null locations provided.
        /// 
        /// This test WILL FAIL initially because ExtractTenantsFromLocations method doesn't exist yet.
        /// </summary>
        [Theory]
        [AutoMoqData]
        public void ExtractTenantsFromLocations_WhenGivenNullLocationList_ThrowsArgumentNullException(
            [Frozen] IHarriTenantByLocationRepository tenantRepository,
            TenantExtractionService sut)
        {
            // Arrange - Null location list
            List<int> locations = null;

            // Act & Assert - This will fail because ExtractTenantsFromLocations method doesn't exist
            sut.Invoking(service => service.ExtractTenantsFromLocations(locations))
                .Should().Throw<ArgumentNullException>()
                .WithMessage("*locations*", "should throw ArgumentNullException when locations parameter is null");
        }

        /// <summary>
        /// Test empty tenant mappings handling in SelectFirstLocationPerTenant.
        /// Should return empty dictionary when no tenant mappings provided.
        /// 
        /// This test WILL FAIL initially because SelectFirstLocationPerTenant method doesn't exist yet.
        /// </summary>
        [Theory]
        [AutoMoqData]
        public void SelectFirstLocationPerTenant_WhenGivenEmptyMappings_ReturnsEmptyDictionary(
            TenantExtractionService sut)
        {
            // Arrange - Empty tenant mappings
            var tenantLocationMappings = new Dictionary<string, List<int>>();

            // Act - This will fail because SelectFirstLocationPerTenant method doesn't exist
            var result = sut.SelectFirstLocationPerTenant(tenantLocationMappings);

            // Assert - Should return empty dictionary
            result.Should().BeEmpty("empty tenant mappings should result in empty location selections");
        }
    }
}