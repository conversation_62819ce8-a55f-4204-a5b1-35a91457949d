using AutoFixture;
using AutoFixture.AutoMoq;
using AutoFixture.Xunit2;
using System.Linq;

namespace Jitb.Employment.HarriValidateTenantTests
{
    /// <summary>
    /// AutoFixture attribute for automatic mock generation in xUnit tests for HarriValidateTenant
    /// </summary>
    public class AutoMoqDataAttribute : AutoDataAttribute
    {
        public AutoMoqDataAttribute() : base(Setup)
        {
        }

        private static IFixture Setup()
        {
            var fixture = new Fixture { RepeatCount = 1 }.Customize(new AutoMoqCustomization { ConfigureMembers = true });
            
            // Remove throwing behavior on recursion to avoid infinite loops
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            
            return fixture;
        }
    }

    /// <summary>
    /// AutoFixture attribute for parameterized tests with inline data
    /// </summary>
    public class InlineAutoMoqDataAttribute : InlineAutoDataAttribute
    {
        public InlineAutoMoqDataAttribute(params object[] values)
            : base(new AutoMoqDataAttribute(), values)
        {
        }
    }
}