﻿IF EXISTS (SELECT * FROM sys.objects WHERE type = 'TR' AND name = 'EmployeeLocationAuditTrigger')
BEGIN
	DROP TRIGGER [EmployeeLocationAuditTrigger]
END
GO
CREATE TRIGGER [EmployeeLocationAuditTrigger]
ON [EmployeeLocation]
After Update, Insert
AS
BEGIN
	SET NOCOUNT ON
	DECLARE @Action Varchar(15);
	SET @Action = 'Insert'
	IF EXISTS (SELECT * FROM DELETED)
	BEGIN
		SET @Action = 'Update';
	END
	INSERT INTO [dbo].[EmployeeLocationAudit]
			   ([AuditActionCode]
			   ,[AuditSource]
			   ,[AuditDateTimeStamp]
			   ,[EmployeeID]
			   ,[LocationNumber]
			   ,[PrimaryFlag]
			   ,[ActiveFlag]
			   ,[BeginDate]
			   ,[EndDate]
			   ,[MessageID])
		 SELECT
				@Action
			   ,NULL
			   ,GETDATE()
			   ,EmployeeID
			   ,LocationNumber
			   ,PrimaryFlag
			   ,ActiveFlag
			   ,BeginDate
			   ,EndDate
			   ,NULL
		 FROM INSERTED
	END
GO