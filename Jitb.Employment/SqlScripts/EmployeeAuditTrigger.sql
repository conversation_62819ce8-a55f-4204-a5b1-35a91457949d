﻿IF EXISTS (SELECT * FROM sys.objects WHERE type = 'TR' AND name = 'EmployeeAuditTrigger')
BEGIN
	DROP TRIGGER [EmployeeAuditTrigger]
END
GO
CREATE TRIGGER [EmployeeAuditTrigger]
ON [Employee]
After Update, Insert
AS
BEGIN
	SET NOCOUNT ON
	DECLARE @Action Varchar(15);
	SET @Action = 'Insert'
	IF EXISTS (SELECT * FROM DELETED)
	BEGIN
		SET @Action = 'Update';
	END
	INSERT INTO [dbo].[EmployeeAudit]
			   ([AuditActionCode]
			   ,[AuditSource]
			   ,[AuditDateTimeStamp]
			   ,[EmployeeID]
			   ,[BadgeID]
			   ,[Address1]
			   ,[Address2]
			   ,[City]
			   ,[StateCode]
			   ,[ZipCode]
			   ,[WorkPhoneNumber]
			   ,[HomePhoneNumber]
			   ,[CellPhoneNumber]
			   ,[NetworkID]
			   ,[FimEmailPreferenceCode]
			   ,[LawsonCompanyID]
			   ,[LawsonEmployeeID]
			   ,[<PERSON><PERSON>upervisorCode]
			   ,[Lawson<PERSON>upervisorID]
			   ,[<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>]
			   ,[LawsonEmpStatus]
			   ,[LawsonDepartmentCode]
			   ,[LawsonProcessLevelCode]
			   ,[LawsonCostCenter]
			   ,[eRestaurantEmployeeID]
			   ,[eRestaurantSupervisorID]
			   ,[HREmployeeIdentityID]
			   ,[HREmployeeID]
			   ,[HRSupervisorID]
			   ,[HRDepartmentCode]
			   ,[HRDepartment]
			   ,[HRCompanyBrand]
			   ,[CostCenterCode]
			   ,[EligibleForRehire]
			   ,[SupervisorEmployeeID]
			   ,[HireDate]
			   ,[PreviousHireDate]
			   ,[TerminationDate]
			   ,[AdjustedHireDate]
			   ,[EmailAddress]
			   ,[ExemptStatus]
			   ,[PayGrade]
			   ,[SalaryClass]
			   ,[BirthDate]
			   ,[Ssn]
			   ,[Sex]
			   ,[CurrentStatus]
			   ,[EmployeeType]
			   ,[MiddleInitial]
			   ,[FirstName]
			   ,[MiddleName]
			   ,[LastName]
			   ,[NickName]
			   ,[DateStamp]
			   ,[TrainingFlag]
			   ,[DomainName]
			   ,[JobCode]
			   ,[JobClass]
			   ,[DateTimeStamp]
			   ,[MessageID])
		 SELECT
			   @Action
			   ,NULL
			   ,GETDATE()
			   ,EmployeeID
			   ,BadgeID
			   ,Address1
			   ,Address2
			   ,City
			   ,StateCode
			   ,ZipCode
			   ,WorkPhoneNumber
			   ,HomePhoneNumber
			   ,CellPhoneNumber
			   ,NetworkID
			   ,FimEmailPreferenceCode
			   ,LawsonCompanyID
			   ,LawsonEmployeeID
			   ,LawsonSupervisorCode
			   ,LawsonSupervisorID
			   ,LawsonSuperKeyID
			   ,LawsonEmpStatus
			   ,LawsonDepartmentCode
			   ,LawsonProcessLevelCode
			   ,LawsonCostCenter
			   ,eRestaurantEmployeeID
			   ,eRestaurantSupervisorID
			   ,HREmployeeIdentityID
			   ,HREmployeeID
			   ,HRSupervisorID
			   ,HRDepartmentCode
			   ,HRDepartment
			   ,HRCompanyBrand
			   ,CostCenterCode
			   ,EligibleForRehire
			   ,SupervisorEmployeeID
			   ,HireDate
			   ,PreviousHireDate
			   ,TerminationDate
			   ,AdjustedHireDate
			   ,EmailAddress
			   ,ExemptStatus
			   ,PayGrade
			   ,SalaryClass
			   ,BirthDate
			   ,Ssn
			   ,Sex
			   ,CurrentStatus
			   ,EmployeeType
			   ,MiddleInitial
			   ,FirstName
			   ,MiddleName
			   ,LastName
			   ,NickName
			   ,DateTimeStamp
			   ,TrainingFlag
			   ,DomainName
			   ,JobCode
			   ,JobClass
			   ,DateTimeStamp
			   ,NULL
			FROM INSERTED


END
GO