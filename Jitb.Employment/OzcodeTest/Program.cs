﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OzcodeTest
{
    using System.Collections.ObjectModel;
    using System.Globalization;
    using System.Reflection;
    using Jitb.Employment.Domain.Concepts;

    class Program
    {
        static void Main(string[] args)
        {
            var user = new User
            {
                UserFrantrackerMappings = new List<UserFrantrackerMapping>
                {
                    new UserFrantrackerMapping
                    {
                        EisKey = 0,
                        Entities = new List<EntityTable>
                        {
                            new EntityTable
                            {
                                EntityId = 1020,
                                EntityTableKey = 4378,
                                JtkSecurityLevel = "5.5"
                            },
                            new EntityTable
                            {
                                EntityId = 1126,
                                EntityTableKey = 4379,
                                JtkSecurityLevel = "5.5"
                            },
                            new EntityTable
                            {
                                EntityId = 1128,
                                EntityTableKey = 4380,
                                JtkSecurityLevel = "5.5"
                            },
                            new EntityTable
                            {
                                EntityId = 1212,
                                EntityTableKey = 4381,
                                JtkSecurityLevel = "5.5"
                            },
                            new EntityTable
                            {
                                EntityId = 1213,
                                EntityTableKey = 4382,
                                JtkSecurityLevel = "5.5"
                            },
                            new EntityTable
                            {
                                EntityId = 2053,
                                EntityTableKey = 4383,
                                JtkSecurityLevel = "5.5"
                            },
                            new EntityTable
                            {
                                EntityId = 2337,
                                EntityTableKey = 4384,
                                JtkSecurityLevel = "5.5"
                            }
                        },
                        MappingActiveFlag = true,
                        RecordSourceKey = 4,
                        Type = "OPR",
                        UserFranTrackerMappingKey = 3145,
                        UseridKey = 995,
                        UserSourceSystem = new RecordSource
                        {
                            Id = 1,
                            SourceCode = "P",
                            SourceDesc = "FranTracker tblPeople"
                        }
                    }
                },
                CompanyName = " ",
                Employee = new Employee
                {
                    Address1 = "123 Main Street         ",
                    Address2 = "",
                    AdjustedHireDate = DateTime.Parse("5/1/1980 12:00:00 AM", CultureInfo.InvariantCulture),
                    AlternateEmailAddress = null,
                    BadgeId = 0,
                    BirthDate = DateTime.Parse("8/25/1954 12:00:00 AM", CultureInfo.InvariantCulture),
                    CellPhoneNumber = null,
                    City = "TEMPE             ",
                }
            };


            var securityLeval = user.UserFrantrackerMappings.SelectMany(x => x.Entities)
                .Where(y => y.JtkSecurityLevel != "")
                .First().JtkSecurityLevel;

            var f = Enumerable.Range(1, 10).Where(i => i % 2 == 0);
        }
    }
}
