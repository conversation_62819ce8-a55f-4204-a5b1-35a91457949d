<?xml version="1.0"?>
<Servers>
  <Server Name="CSNSBP01V"
          DeployPath="C:\services\employment"
          UserName="corp\nsbprod_svc"
          Password ="xxxxxxxxx"
          DeploymentType="NSB">
    <Service Name="Jitb.Employment.Audit.Endpoint" />
    <Service Name="Jitb.Employment.Endpoint" />
    <Service Name="Jitb.Employment.Fim.Endpoint" />
    <Service Name="Jitb.Employment.Lawson.Endpoint" />
    <Service Name="Jitb.Employment.Lawson.Sync.Endpoint" />
    <Service Name="Jitb.Employment.PointOfSale.Endpoint" />
    <Service Name="Jitb.Employment.Erestaurant.Endpoint" />
    <Service Name="Jitb.Employment.Timekeeping.Endpoint" />
    <Service Name="Jitb.Employment.Erestaurant.Sync.Endpoint" />
    <Service Name="Jitb.Employment.UltiPro.Endpoint" />
    <Service Name="Jitb.Employment.UltiPro.Sync.Endpoint" />
    <Service Name="Jitb.Employment.MessageAudit.Endpoint"/>
    <Service Name="Jitb.Employment.ActiveDirectory.Endpoint"/>
    <Service Name="Jitb.Employment.RoleManagement.Endpoint"/>
  </Server>
  <Server Name="CSNSBP02V"
          DeployPath="C:\services\employment"
          UserName="corp\nsbprod_svc"
          Password ="xxxxxxxxx"
          DeploymentType="NSB">
    <Service Name="Jitb.Employment.Audit.Endpoint"  />
    <Service Name="Jitb.Employment.Endpoint"  />
    <Service Name="Jitb.Employment.Fim.Endpoint"  />
    <Service Name="Jitb.Employment.Lawson.Endpoint"  />
    <Service Name="Jitb.Employment.Lawson.Sync.Endpoint"  />
    <Service Name="Jitb.Employment.PointOfSale.Endpoint"  />
    <Service Name="Jitb.Employment.Erestaurant.Sync.Endpoint" />
    <Service Name="Jitb.Employment.Timekeeping.Endpoint"  />
    <Service Name="Jitb.Employment.Erestaurant.Endpoint" />

    <Service Name="Jitb.Employment.UltiPro.Endpoint"  />
    <Service Name="Jitb.Employment.UltiPro.Sync.Endpoint"  />
    <Service Name="Jitb.Employment.ActiveDirectory.Endpoint"/>
    <Service Name="Jitb.Employment.RoleManagement.Endpoint"/>
  </Server>
  <Server Name="CSNSBP03V"
          DeployPath="C:\services\employment"
          UserName="corp\nsbprod_svc"
          Password ="xxxxxxxxx"
          DeploymentType="NSB">
    <Service Name="Jitb.Employment.Audit.Endpoint"  />
    <Service Name="Jitb.Employment.Endpoint"  />
    <Service Name="Jitb.Employment.Fim.Endpoint"  />
    <Service Name="Jitb.Employment.Lawson.Endpoint"  />
    <Service Name="Jitb.Employment.Erestaurant.Sync.Endpoint" />
    <Service Name="Jitb.Employment.Lawson.Sync.Endpoint"  />
    <Service Name="Jitb.Employment.PointOfSale.Endpoint"  />
    <Service Name="Jitb.Employment.Timekeeping.Endpoint"  />
    <Service Name="Jitb.Employment.UltiPro.Endpoint"  />
    <Service Name="Jitb.Employment.UltiPro.Sync.Endpoint"  />
    <Service Name="Jitb.Employment.ActiveDirectory.Endpoint"/>
    <Service Name="Jitb.Employment.RoleManagement.Endpoint"/>
  </Server>



  <Server Name="csboswebp01v"
           DeployPath="\\csboswebp01v\JIB Applications\JIB.Lawson.Services.WebServices"
           BackupLocation="\\csboswebp01v\JIB Applications\backups\JIB.Lawson.Services.WebServices"
           DeploymentType="XPATH">
    <Service Name="JIB.Lawson.Services.WebService"  />
  </Server>
  <Server Name="csboswebp02v"
          DeployPath="\\csboswebp02v\JIB Applications\JIB.Lawson.Services.WebServices"
          BackupLocation="\\csboswebp02v\JIB Applications\backups\JIB.Lawson.Services.WebServices"
          DeploymentType="XPATH">
    <Service Name="JIB.Lawson.Services.WebService"  />
  </Server>
  <Server Name="csboswebp03v"
          DeployPath="\\csboswebp03v\JIB Applications\JIB.Lawson.Services.WebServices"
          BackupLocation="\\csboswebp03v\JIB Applications\backups\JIB.Lawson.Services.WebServices"
          DeploymentType="XPATH">
    <Service Name="JIB.Lawson.Services.WebService"  />
  </Server>

  <Server Name="ebo-prd-web4"
         DeployPath="\\ebo-prd-web4\wwwroot\REST"
         BackupLocation="\\ebo-prd-web4\JIB Applications\backups\REST"
         DeploymentType="XPATH">
    <Service Name="JIB.JobApp.WebService"  />
  </Server>
 
</Servers>
