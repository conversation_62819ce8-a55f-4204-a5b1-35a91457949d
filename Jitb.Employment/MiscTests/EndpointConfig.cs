
namespace MiscTests
{
    using System.Threading.Tasks;
    using NServiceBus;


    using System.Threading.Tasks;

    using NServiceBus;

    /*
		This class configures this endpoint as a Server. More information about how to configure the NServiceBus host
		can be found here: http://particular.net/articles/the-nservicebus-host
	*/
    public class EndpointConfig : IConfigureThisEndpoint
    {
        public void Customize(EndpointConfiguration configuration)
        {
   //         configuration.DefaultJitbConfiguration();

        }
    }

    public class BusInjector : IWantToRunWhenEndpointStartsAndStops
    {
        public async Task Start(IMessageSession session)
        {
            await Task.FromResult(true);
        }

        public async Task Stop(IMessageSession session)
        {
            await Task.FromResult(true);
        }

    }
}