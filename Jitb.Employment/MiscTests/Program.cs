﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Jitb.Employment.Domain;


using NServiceBus;

using System.Threading.Tasks;


namespace MiscTests
{
    using Jitb.Employment.Domain.Repositories;

    class Program
    {
        static void Main(string[] args)
        {
            var x = new EmployeeRoleRepository();
            x.GetByEmployeeLocation(3);
            int i = 0;
        }
    }






    public class Startup : IWantToRunWhenEndpointStartsAndStops
    {

        private IEmployeeRepository _employeeRoleRepository;

        public Startup(IEmployeeRepository employeeRoleRepository)
        {
            this._employeeRoleRepository = employeeRoleRepository;
        }


        public async Task Start(IMessageSession session)
        {
            await Task.FromResult(true);
        }
        public async Task Stop(IMessageSession session)
        {
            await Task.FromResult(true);
        }
    }
}
