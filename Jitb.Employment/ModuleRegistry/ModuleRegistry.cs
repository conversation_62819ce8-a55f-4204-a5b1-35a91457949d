﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ModuleRegistry
{
    using Jitb.Employment.MSMQListener.Endpoint.CommandSenders;
    using StructureMap;

    public class ModuleRegistry : Registry
    {
        public ModuleRegistry()
        {
            For<ISendASpecificCommand>()
                .Add<NewBadgeid>();

            Scan(x =>
            {
                x.TheCallingAssembly();
                x.WithDefaultConventions();
            });
        }
    }
}
