# Harri API 501 Error Troubleshooting Guide

## Overview
This document provides guidance for troubleshooting 501 Not Implemented errors when calling the Harri API through the `CallHarriWebServiceProvider`.

## Enhanced Logging Features
The `CallHarriWebServiceProvider` now includes enhanced logging to help diagnose 501 errors:

- Full URL construction details
- Request and response headers  
- Tenant configuration information
- Response content for debugging
- Common causes and suggested solutions

## Common Causes and Solutions

### 1. Incorrect API Version
**Problem**: Using an unsupported API version
**Symptoms**: 501 error with version like "V3" when only V1/V2 are supported
**Solution**: 
- Check Harri API documentation for supported versions
- Use "V1" or "V2" instead of newer versions
- Look for log message: "Version 'X' doesn't start with 'v' or 'V'"

### 2. Invalid Endpoint Path
**Problem**: Endpoint doesn't exist or has wrong case sensitivity
**Symptoms**: 501 error on seemingly valid endpoints
**Solution**:
- Verify endpoint name in Harri API documentation
- Check case sensitivity (e.g., "Employees" vs "employees")
- Remove leading slashes from endpoints
- Look for log message: "Endpoint starts with '/'"

### 3. Unsupported HTTP Method
**Problem**: Using wrong HTTP method for endpoint
**Symptoms**: 501 error when endpoint exists but method is wrong
**Solution**:
- Verify HTTP method in API documentation
- Common patterns: GET for retrieval, POST for creation
- Check log message showing "HTTP Method: {method}"

### 4. URL Construction Issues
**Problem**: Malformed URLs due to double slashes or missing parts
**Symptoms**: 501 error with malformed URLs in logs
**Solution**:
- Check "Full URL being called" in logs
- Ensure no double slashes (e.g., "//api/")
- Verify base URL is correct: "https://gateway.harri.com/open-api-hub"

### 5. Server-Side Configuration
**Problem**: Harri server hasn't implemented the endpoint
**Symptoms**: 501 error on documented endpoints
**Solution**:
- Contact Harri support with full error details
- Provide tenant information and exact URL called
- Check if endpoint is in beta or requires special access

## Debugging Process

1. **Check Enhanced Logs**: Look for "501 Not Implemented error received" in logs
2. **Examine Full URL**: Verify the complete URL being called is correct
3. **Validate Parameters**: Check endpoint, method, version, and tenant config
4. **Test Known Endpoints**: Try a working endpoint first to verify connectivity
5. **Consult Documentation**: Check latest Harri API documentation for changes
6. **Contact Support**: If all else fails, provide full log details to Harri support

## Log Messages to Look For

- `"501 Not Implemented error received. URL: {url}"`
- `"Full URL being called: {fullUrl}"`
- `"Common causes of 501 errors:"`
- `"Endpoint starts with '/' which may cause URL construction issues"`
- `"Version '{version}' doesn't start with 'v' or 'V'"`

## Example Valid API Calls

- `GET https://gateway.harri.com/open-api-hub/api/v1/employees`
- `GET https://gateway.harri.com/open-api-hub/api/v1/locations`
- `POST https://gateway.harri.com/open-api-hub/api/v1/employees`

## Testing Strategy

1. Start with simple GET endpoints like "employees" or "locations"
2. Use version "V1" as it's most widely supported
3. Verify tenant configuration is correct
4. Check authentication tokens are valid
5. Gradually test more complex endpoints

## When to Contact Support

Contact Harri support when:
- All validation checks pass but 501 errors persist
- Documented endpoints return 501 errors
- Working endpoints suddenly start failing
- Need clarification on API version support

Include in support requests:
- Full error logs with 501 details
- Tenant ID and configuration
- Exact URL and parameters used
- Expected vs actual behavior