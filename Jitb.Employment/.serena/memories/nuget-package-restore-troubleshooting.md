# NuGet Package Restore Troubleshooting Guide

## Problem Description

When building the Jitb.Employment solution, encountered 92+ compilation errors due to missing NuGet packages. The errors indicated that packages were missing and needed to be restored.

### Symptoms
- Build failures with errors like: "This project references NuGet package(s) that are missing on this computer"
- Missing assemblies: AutoMapper, xunit.runner.visualstudio, Microsoft.CodeCoverage, etc.
- Error messages pointing to missing `.props` files in packages directory

## Root Cause Analysis

The solution uses the legacy `packages.config` format (not PackageReference) which requires different restore approaches:

1. **Legacy NuGet.exe**: The local `.nuget/NuGet.exe` was version 2.2.40207.9053 (very old)
   - This version doesn't support the `restore` command
   - Only supports: config, delete, help, install, list, pack, push, setApiKey, sources, spec, update

2. **MSBuild Integration**: The solution has NuGet targets configured but `RestorePackages` was set to `false`
   - Enabling with `/p:RestorePackages=true` didn't work due to old NuGet version

3. **Modern Tools Don't Support packages.config**:
   - `dotnet restore` only works with PackageReference format
   - Returns: "Nothing to do. None of the projects specified contain packages to restore."

## Solutions Attempted (Failed)

1. **MSBuild with RestorePackages=true**: Failed due to old NuGet.exe
2. **dotnet restore**: Not compatible with packages.config format
3. **Manual package installation**: Timed out with old NuGet.exe
4. **Searching for newer NuGet in VS installation**: Not found in expected locations

## Working Solution

### Step 1: Download Latest NuGet CLI
```bash
"/mnt/c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -Command "Invoke-WebRequest -Uri 'https://dist.nuget.org/win-x86-commandline/latest/nuget.exe' -OutFile './nuget-new.exe'"
```

### Step 2: Restore Packages
```bash
chmod +x nuget-new.exe
./nuget-new.exe restore Jitb.Employment.sln
```

### Step 3: Build Solution
```bash
"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe" Jitb.Employment.sln /p:Configuration=Debug
```

## Key Insights

1. **packages.config vs PackageReference**: Legacy format requires NuGet CLI, modern format uses dotnet CLI
2. **NuGet Version Matters**: Older versions (pre-3.0) don't support restore command
3. **Download Latest NuGet**: Always download from official source rather than relying on local/VS installations
4. **WSL Path Handling**: Use `/mnt/c/` prefix for Windows paths in WSL environment

## Prevention for Future

1. **Always check NuGet version first**: `./nuget.exe` (without arguments shows version and commands)
2. **Identify package format**: Look for `packages.config` files vs `<PackageReference>` in `.csproj`
3. **Have restore process documented**: Include restore steps in project documentation
4. **Consider migration**: Evaluate migrating from packages.config to PackageReference for better tooling support

## Build Commands for This Project

```bash
# Download latest NuGet if needed
"/mnt/c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -Command "Invoke-WebRequest -Uri 'https://dist.nuget.org/win-x86-commandline/latest/nuget.exe' -OutFile './nuget-new.exe'"

# Restore packages
chmod +x nuget-new.exe
./nuget-new.exe restore Jitb.Employment.sln

# Build solution
"/mnt/c/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/MSBuild.exe" Jitb.Employment.sln /p:Configuration=Debug
```

## Time Saved
This documentation should save 15-30 minutes of troubleshooting on future builds by providing the direct solution path.