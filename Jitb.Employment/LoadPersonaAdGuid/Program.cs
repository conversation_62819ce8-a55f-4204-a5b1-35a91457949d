﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Afterman.nRepo;
using Afterman.nRepo.DbPlatforms;
using Jitb.Employment.Domain.Providers;
using Jitb.Employment.Domain.Repositories;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.Domain.Repositories.Employment.Maps;
using LoadPersonaAdGuid.Providers;
using StructureMap;
using Jitb.ActiveDirectory.Commons;

namespace LoadPersonaAdGuid
{
    class Program
    {
        static void Main(string[] args)
        {
            //Start up Afterman
            var employmentConnectionString =
                ConfigurationManager.ConnectionStrings[DbAlias.Employment]?.ConnectionString;

            new Afterman.nRepo.nRepoConfiguration()
                .With(DbAlias.Employment,
                    Afterman.nRepo.Configure
                        .As
                        .NHibernate()
                        .AddMappingsForNamespaceOf<PersonaMap>()
                        .ConnectionString(employmentConnectionString)
                        .DefaultSchema("")
                        //               .UpdateSchema()
                        .ImplicitTransactions()
                        .ShowSql(true)
                        .Platform<MsSqlServer.Server2012Platform>())
                .Start();

            var container = new Container();
            container.Configure(_ =>
            {
                _.Scan(x =>
                {
                    x.TheCallingAssembly();
                    x.WithDefaultConventions();
                });
                _.Scan(x =>
                    {
                        x.AssemblyContainingType<Jitb.ActiveDirectory.Commons.ActiveDirectoryProvider>();
                        x.WithDefaultConventions();
                    }
                );
                _.Scan(x =>
                    {
                        x.AssemblyContainingType<PersonaRepository>();
                        x.WithDefaultConventions();
                    }
                );
                _.For<IMasterUnitOfWork>().Use<MasterUnitOfWork>();
            });

            var xx = container.WhatDoIHave();
            // container.Configure(x => x.For<ISyncPersonaToAdProvider>().Use(new SyncPersonaToAdProvider()));
            // container.Configure(x => x.For<IActiveDirectoryProvider>().Use(new ActiveDirectoryProvider()));

            var processPersonaProvider = container.GetInstance<IProcessPersonaProvider>();

            // var syncPersonaToAdProvider = new SyncPersonaToAdProvider();
            processPersonaProvider.SyncPersonaToAd();
        }
    }
}
