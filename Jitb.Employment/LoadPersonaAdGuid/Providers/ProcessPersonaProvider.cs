﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Jitb.ActiveDirectory.Commons;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Helpers;
using Jitb.Employment.Domain.Repositories.Employment;


namespace LoadPersonaAdGuid.Providers
{
    public interface IProcessPersonaProvider
    {
        void SyncPersonaToAd();
    }
    public class ProcessPersonaProvider : IProcessPersonaProvider
    {
        private readonly IPersonaRepository _personaRepository;
        private readonly IActiveDirectoryProvider _activeDirectoryProvider;

        public ProcessPersonaProvider(IPersonaRepository personaRepository, IActiveDirectoryProvider activeDirectoryProvider)
        {
            _personaRepository = personaRepository;
            _activeDirectoryProvider = activeDirectoryProvider;
        }

        public void SyncPersonaToAd()
        {
            var personas = _personaRepository.GetAll();
            foreach (var p in personas)
            {
                p.AdGuid = GetGuid(p);
                _personaRepository.Add(p);
            }
        }

        private Guid GetGuid(Persona persona)
        {
            var user = _activeDirectoryProvider.GetUserByExtensionAttribute3((int)persona.PersonaId);
            return user.Guid??Guid.Empty;
        }
    }
}
