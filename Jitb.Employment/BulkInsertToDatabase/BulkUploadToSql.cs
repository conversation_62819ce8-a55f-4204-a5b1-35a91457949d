﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BulkInsertToDatabase
{
    using System.Data;
    using System.Data.SqlClient;
    using System.IO;

    public class BulkUploadToSql

    {

        public List<MyRecord> internalStore;


        protected string tableName;

        protected DataTable dataTable = new DataTable();

        protected int recordCount;

        protected int commitBatchSize;
        protected string _connectionString;

        public BulkUploadToSql(

            string tableName,
            int commitBatchSize,
            string connectionString)

        {

            internalStore = new List<MyRecord>();

            this.tableName = tableName;

            this.dataTable = new DataTable(tableName);

            this.recordCount = 0;

            this.commitBatchSize = commitBatchSize;

            _connectionString = connectionString;

            // add columns to this data table

            InitializeStructures();

        }


//        public BulkUploadToSql() :
//
//            this("MyTableName", 1000)
//        {
//        }



        protected virtual void InitializeStructures()

        {

            this.dataTable.Columns.Add("TI", typeof(Int32));

            this.dataTable.Columns.Add("TS", typeof(string));
        }

        public static BulkUploadToSql Load(Stream dataSource)

        {

            // create a new object to return

//            BulkUploadToSql o = new BulkUploadToSql();

            // replace the code below

            // with your custom logic 

            for (int cnt = 0; cnt < 10000; cnt++)

            {

/*                MyRecord rec =

                    new MyRecord

                    (

                        cnt,

                        string.Format("string{0}", cnt)

                    );

                o.internalStore.Add(rec);*/

            }


//            return o;
            return null;
        }

        public void Flush()

        {

            // transfer data to the datatable

            foreach (MyRecord rec in this.internalStore)

            {

                this.PopulateDataRow(rec);

                if (this.recordCount >= this.commitBatchSize)

                    this.WriteToDatabase();

            }

            // write remaining records to the DB

            if (this.recordCount > 0)

                this.WriteToDatabase();

        }

        protected virtual void PopulateDataRow(MyRecord record)

        {

            DataRow row;

            // populate the values

            // using your custom logic

            row = this.dataTable.NewRow();


//            row[0] = record.TestInt;
//
//            row[1] = record.TestString;


            // add it to the base for final addition to the DB

            this.dataTable.Rows.Add(row);

            this.recordCount++;

        }
        private void WriteToDatabase()

        {

            // get your connection string



            // connect to SQL

            using (SqlConnection connection =

                new SqlConnection(this._connectionString))

            {

                // make sure to enable triggers

                // more on triggers in next post

                SqlBulkCopy bulkCopy =

                    new SqlBulkCopy

                    (

                        connection,

                        SqlBulkCopyOptions.TableLock |

                        SqlBulkCopyOptions.FireTriggers |

                        SqlBulkCopyOptions.UseInternalTransaction,

                        null

                    );

                // set the destination table name

                bulkCopy.DestinationTableName = this.tableName;

                connection.Open();


                // write the data in the "dataTable"

                bulkCopy.WriteToServer(dataTable);

                connection.Close();

            }

            // reset

            this.dataTable.Clear();

            this.recordCount = 0;

        }
    }
}
