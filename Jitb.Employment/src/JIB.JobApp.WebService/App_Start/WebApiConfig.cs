﻿namespace JIB.JobApp.WebService
{
    using JIB.JobApp.WebService.App_Start;
    using Jitb.Employment.Domain.Configuration;
    using NServiceBus.Logging;
    using Swashbuckle.Application;
    using System;
    using System.Configuration;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using System.Web.Http;

    public static class WebApiConfig
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(WebApiConfig));

        public static void Register(HttpConfiguration config)
        {
            // Web API configuration and services

            // Web API routes
            config.MapHttpAttributeRoutes();

            config.Routes.MapHttpRoute(
                name: "Default<PERSON><PERSON>",
                routeTemplate: "api/{controller}/{id}",
                defaults: new { id = RouteParameter.Optional }
            );

            // Redirect root to Swagger UI
            config.Routes.MapHttpRoute(
                name: "Swagger UI",
                routeTemplate: "",
                defaults: null,
                constraints: null,
                handler: new RedirectHandler(SwaggerDocsConfig.DefaultRootUrlResolver, "swagger/ui/index"));

            StructuremapWebApi.Start();
        }

        public static async Task ReplicateRequestToTestApi<T>(T item)
        {
            var typeName = item.GetType().Name;
            var logLinePrefix = $"{typeName} -";

            if (!ConfigurationContext.JobApp.ReplicateRequestToTestApi)
            {
                Log.Info("ReplicateRequestsToTestApi is disabled in web.config");
            }
            else
            {
                var url = $"{ConfigurationContext.JobApp.TestApiUrl}/{typeName}";
                try
                {
                    // Fire and forget.
                    Log.Info($"Begin POST to URL {url}");
                    await new HttpClient().PostAsJsonAsync(url, item);
                    Log.Info($"End POST to URL {url}");
                }
                catch (WebException ex)
                {
                    Log.Error($"WebException calling URL {url}", ex);
                }
                catch (Exception ex)
                {
                    Log.Error($"Exception calling URL {url}", ex);
                }
            }
        }
    }
}
