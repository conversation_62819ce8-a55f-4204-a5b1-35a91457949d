﻿<connectionStrings>
  <add name="InstanceMapping" connectionString="Data Source=.;Initial Catalog=dbnservicebus;integrated security=SSPI;enlist=false;" />
  <add name="MessageRouteMapping" connectionString="Data Source=.;Initial Catalog=dbNServiceBus;integrated security=SSPI;" />
  <add name="NServiceBus/Persistence" connectionString="Data Source=.;Initial Catalog=dbNServiceBus;integrated security=SSPI;enlist=false;" />
	<add name="Default" connectionString="data source=.;initial catalog=dbNserviceBus;integrated security=SSPI;enlist=false;" />

	<!--<add name="Default" connectionString="data source=dbnservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNserviceBus;integrated security=SSPI;enlist=false;" />-->
	<add name="ErestaurantIntegration" connectionString="Data Source=.;Initial Catalog=dbNServiceBus;Integrated Security=SSPI;enlist=false;" />
	<add name="LocationDb" connectionString="Data Source=.;Initial Catalog=dbNServiceBus;Integrated Security=SSPI;enlist=false;" />
	<!--<add name="Sitecenter" connectionString="Data Source=.;Initial Catalog=SitecenterEDW;Integrated Security=SSPI;enlist=false;" />-->
	<add name="Config" connectionString="data source=.;initial catalog=dbnservicebus;integrated security=true;enlist=false;" />
	<add name="CSNsbSqlAG-Lsnr" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;enlist=false;" />
	<add name="JibLocationEntityModel" connectionString="Data source=.;initial catalog=dbNServiceBus;integrated security=true;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
	<add name="HireEligibility" connectionString="Data Source=.;Initial Catalog=dbNServiceBus;Integrated Security=SSPI;enlist=false;" />
	<add name="Model_FIMFMADEMP_WPS" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
	<add name="ModelEmployeeAction" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  <!--<add name="dbJTK" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=tkt1-ora-lh)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=WBTST1)));User ID=*********1;Password=*********;"/>-->
	<!--<add name="Frantracker" connectionString="data source=cssqlt01v\vtest1;initial catalog=dbTracker;integrated security=SSPI;enlist=false;" />-->
  <add name="EmploymentLocation" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=true;enlist=false;" />
</connectionStrings>