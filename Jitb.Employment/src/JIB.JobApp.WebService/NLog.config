<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <variable name="endpointName" value="EmploymentJobAppWebService"/>
  <targets async="true">
    <target name="console"
            xsi:type="ColoredConsole"
            layout="${date} ${level:uppercase=true} ${machinename} ${windows-identity} ${processname} ${processid} ${threadid} ${logger} ${callsite:className=true:includeNamespace=true:fileName=true:includeSourcePath=true:methodName=true:cleanNamesOfAnonymousDelegates=true:skipFrames=0} ${callsite-linenumber:skipFrames=0} ${message} ${stacktrace:topFrames=10} ${exception:format=toString,Data:maxInnerExceptionLevel=10}"/>
    <target name="file"
            xsi:type="File"
            fileName="C:\JIB Logs\Employment\${var:endpointName}_${shortdate}.txt"
            archiveFileName="C:\JIB Logs\Employment\${var:endpointName}_{#}.txt"
            archiveNumbering="DateAndSequence"
            archiveAboveSize="1000000"
            archiveDateFormat="yyyy-MM-dd"
            maxArchiveFiles="10">
      <layout xsi:type="CsvLayout" delimiter="Tab">
        <column name="Date" layout="${date}" />
        <column name="Level" layout="${level:uppercase=true}" />
        <column name="Host" layout="${aspnet-Request-Host}" />
        <column name="WebSite" layout="${iis-site-name}" />
        <column name="HttpMethod" layout="${aspnet-Request-Method}" />
        <column name="Client" layout="${aspnet-Request-IP}" />
        <column name="Referrer" layout="${aspnet-Request-Referrer}" />
        <column name="UserAgent" layout="${aspnet-Request-UserAgent}" />
        <column name="Url" layout="${aspnet-Request-Url}" />
        <column name="QueryString" layout="${aspnet-Request-QueryString}" />
        <column name="StoreNumber" layout="${mdc:item=StoreNumber}" />
        <column name="Machine" layout="${machinename}" />
        <column name="Identity" layout="${windows-identity}" />
        <column name="Process" layout="${processname}" />
        <column name="ProcessId" layout="${processid}" />
        <column name="ThreadId" layout="${threadid}" />
        <column name="Logger" layout="${logger}" />
        <column name="CallSite" layout="${callsite:className=true:includeNamespace=true:fileName=true:includeSourcePath=true:methodName=true:cleanNamesOfAnonymousDelegates=true:skipFrames=0}" />
        <column name="Line" layout="${callsite-linenumber:skipFrames=0}" />
        <column name="Message" layout="${message}" />
        <column name="StackTrace" layout="${stacktrace:topFrames=10}" />
        <column name="Exception" layout="${exception:format=toString,Data:maxInnerExceptionLevel=10}" />
      </layout>
    </target>
    <target name="database"
            xsi:type="Database"
            connectionString="data source=dz-nservicebus.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;enlist=false;">
      <commandText>
        INSERT INTO [dbo].[${var:endpointName}] ([Date],[Level],[Host],[WebSite],[HttpMethod],[Client],[Referrer],[UserAgent],[Url],[QueryString],[StoreNumber],[Machine],[Identity],[Process],[ProcessId],[ThreadId],[Logger],[CallSite],[Line],[Message],[StackTrace],[Exception])
        VALUES                                  (@Date, @Level, @Host, @WebSite, @HttpMethod, @Client, @Referrer, @UserAgent, @Url, @QueryString, @StoreNumber, @Machine, @Identity, @Process, @ProcessId, @ThreadId, @Logger, @CallSite, @Line, @Message, @StackTrace, @Exception);
      </commandText>
      <parameter name="@Date" layout="${date}" />
      <parameter name="@Level" layout="${level:uppercase=true}" />
      <parameter name="@Host" layout="${aspnet-Request-Host}" />
      <parameter name="@WebSite" layout="${iis-site-name}" />
      <parameter name="@HttpMethod" layout="${aspnet-Request-Method}" />
      <parameter name="@Client" layout="${aspnet-Request-IP}" />
      <parameter name="@Referrer" layout="${aspnet-Request-Referrer}" />
      <parameter name="@UserAgent" layout="${aspnet-Request-UserAgent}" />
      <parameter name="@Url" layout="${aspnet-Request-Url}" />
      <parameter name="@QueryString" layout="${aspnet-Request-QueryString}" />
      <parameter name="@StoreNumber" layout="${mdc:item=StoreNumber}" />
      <parameter name="@Machine" layout="${machinename}" />
      <parameter name="@Identity" layout="${windows-identity}" />
      <parameter name="@Process" layout="${processname}" />
      <parameter name="@ProcessId" layout="${processid}" />
      <parameter name="@ThreadId" layout="${threadid}" />
      <parameter name="@Logger" layout="${logger}" />
      <parameter name="@CallSite" layout="${callsite:className=true:includeNamespace=true:fileName=true:includeSourcePath=true:methodName=true:cleanNamesOfAnonymousDelegates=true:skipFrames=0}" />
      <parameter name="@Line" layout="${callsite-linenumber:skipFrames=0}" />
      <parameter name="@Message" layout="${message}" />
      <parameter name="@StackTrace" layout="${stacktrace:topFrames=10}" />
      <parameter name="@Exception" layout="${exception:format=toString,Data:maxInnerExceptionLevel=10}" />
      <install-command>
        <text>
          IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = N'${var:endpointName}')
          BEGIN
          CREATE TABLE [dbo].[${var:endpointName}](
          [Id] [int] IDENTITY(1,1) NOT NULL,
          [Date] [datetime] NOT NULL,
          [Level] [varchar](50) NOT NULL,
          [Host] [varchar](50) NOT NULL,
          [WebSite] [varchar](50) NOT NULL,
          [HttpMethod] [varchar](50) NOT NULL,
          [Client] [varchar](50) NOT NULL,
          [Referrer] [varchar](255) NOT NULL,
          [UserAgent] [varchar](255) NOT NULL,
          [Url] [varchar](255) NOT NULL,
          [QueryString] [varchar](1000) NOT NULL,
          [StoreNumber] [varchar](50) NOT NULL,
          [Machine] [varchar](255) NOT NULL,
          [Identity] [varchar](255) NOT NULL,
          [Process] [varchar](255) NOT NULL,
          [ProcessId] [varchar](50) NOT NULL,
          [ThreadId] [varchar](50) NOT NULL,
          [Logger] [varchar](255) NOT NULL,
          [CallSite] [varchar](1000) NOT NULL,
          [Line] [varchar](50) NOT NULL,
          [Message] [varchar](max) NOT NULL,
          [StackTrace] [varchar](max) NOT NULL,
          [Exception] [varchar](max) NULL
          ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
          END
        </text>
      </install-command>
    </target>
  </targets>
  <rules>
    <logger name="Jitb.*"
            minlevel="Debug"
            writeTo="console" />
    <logger name="Jitb.*"
            minlevel="Debug"
            writeTo="file" />
    <logger name="Jitb.*"
            minlevel="Debug"
            writeTo="database" />

    <logger name="NServiceBus*"
            minlevel="Info"
            writeTo="console" />
    <logger name="NServiceBus*"
            minlevel="Info"
            writeTo="file" />
    <logger name="NServiceBus*"
            minlevel="Info"
            writeTo="database" />

    <logger name="NHibernate.cfg*"
            minlevel="Off"
            writeTo="console" />
    <logger name="NHibernate.cfg*"
            minlevel="Off"
            writeTo="file" />
    <logger name="NHibernate.cfg*"
            minlevel="Off"
            writeTo="database" />

    <logger name="NHibernate.Dialect*"
            minlevel="Off"
            writeTo="console" />
    <logger name="NHibernate.Dialect*"
            minlevel="Off"
            writeTo="file" />
    <logger name="NHibernate.Dialect*"
            minlevel="Off"
            writeTo="database" />
    
    <logger name="NHibernate.SQL"
            minlevel="Debug"
            writeTo="console" />
    <logger name="NHibernate.SQL"
            minlevel="Debug"
            writeTo="file" />
    <logger name="NHibernate.SQL"
            minlevel="Debug"
            writeTo="database" />
  </rules>
</nlog>
