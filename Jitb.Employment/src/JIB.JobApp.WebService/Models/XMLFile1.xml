<?xml version="1.0" encoding="iso-8859-1"?>
<XFM40.1>
	<FM40.1>
    <_PDL>LDV3</_PDL>
    <_TKN>FM40.1</_TKN>
    <_LFN>TRUE</_LFN>
    <TC>FM40.1</TC>
    <FC>A</FC>
    <I1>0</I1>
    <I2>0</I2>
    <USER-ID></USER-ID>
    <XMIT-HREMP-BLOCK>0</XMIT-HREMP-BLOCK>
    <XMIT-REQDED>0</XMIT-REQDED>
    <HREMP-COMPANY>0</HREMP-COMPANY>
    <HREMP-EMPLOYEE>0</HREMP-EMPLOYEE>
    <MGR-LOCAT-CODE>000669</MGR-LOCAT-CODE>
    <MGR-EMPLOYEE>2780</MGR-EMPLOYEE>
    <MGR-DATE>20180606</MGR-DATE>
    <MGR-TIME>07:24:30</MGR-TIME>
    <EMP-COMPANY>2353</EMP-COMPANY>
    <PRS-NAME>GULF COAST JACKS, INC.</PRS-NAME>
    <EMP-EMPLOYEE>3876</EMP-EMPLOYEE>
    <EFFECT-DATE>20180606</EFFECT-DATE>
    <EMP-EMP-STATUS>06</EMP-EMP-STATUS>
    <EMS-DESCRIPTION></EMS-DESCRIPTION>
		<ACTION>HIRE</ACTION>
		<MAIN-TAB>2</MAIN-TAB>
		<EMP-FICA-NBR>039-75-3032</EMP-FICA-NBR>
    <EMP-LAST-NAME>BATE</EMP-LAST-NAME>
    <EMP-FIRST-NAME>TIGANA</EMP-FIRST-NAME>
    <EMP-MIDDLE-NAME>AJOPI</EMP-MIDDLE-NAME>
    <PEM-LOCAT-CODE>000669</PEM-LOCAT-CODE>
    <ALT-LOCAT-CODE></ALT-LOCAT-CODE>
    <GM-LOCAT-CODE1></GM-LOCAT-CODE1>
    <GM-LOCAT-CODE2></GM-LOCAT-CODE2>
    <GM-LOCAT-CODE3></GM-LOCAT-CODE3>
    <GM-LOCAT-CODE4></GM-LOCAT-CODE4>
    <GM-LOCAT-CODE5></GM-LOCAT-CODE5>
    <GM-LOCAT-CODE6></GM-LOCAT-CODE6>
    <GM-LOCAT-CODE7></GM-LOCAT-CODE7>
    <GM-LOCAT-CODE8></GM-LOCAT-CODE8>
    <GM-LOCAT-CODE9></GM-LOCAT-CODE9>
    <GM-LOCAT-CODE10></GM-LOCAT-CODE10>
    <EMP-TERM-DATE></EMP-TERM-DATE>
    <EMP-DATE-HIRED>20180606</EMP-DATE-HIRED>
    <EMP-PAY-RATE>8.0000</EMP-PAY-RATE>
    <PAY-CARD-NBR></PAY-CARD-NBR>
    <EMP-JOB-CODE>RORH10</EMP-JOB-CODE>
    <JBC-DESCRIPTION>TEAM MEMBER</JBC-DESCRIPTION>
    <PEM-BIRTHDATE>19981020</PEM-BIRTHDATE>
    <PEM-SEX>M</PEM-SEX>
    <PEM-SEX-XLT>Male</PEM-SEX-XLT>
    <PEM-EEO-CLASS>B</PEM-EEO-CLASS>
    <CTC-DESCRIPTION></CTC-DESCRIPTION>
    <SHFTPAT-NAME>JIB SHIFT PATTERN</SHFTPAT-NAME>
    <EMP-FULL-NAME-CND>BATE, TIGANA A.</EMP-FULL-NAME-CND>
    <EMP-LAST-NAME-PRE></EMP-LAST-NAME-PRE>
    <EMP-NAME-SUFFIX></EMP-NAME-SUFFIX>
    <NAME-SUFFIX-DESC></NAME-SUFFIX-DESC>
    <EMP-NICK-NAME>TIGANA</EMP-NICK-NAME>
    <EMP-NAME-PREFIX></EMP-NAME-PREFIX>
    <NAME-PREFIX-DESC></NAME-PREFIX-DESC>
    <PEM-BEN-DATE-3></PEM-BEN-DATE-3>
    <HEU-TERM-REASON></HEU-TERM-REASON>
    <HEU-TERM-REASON-DESC></HEU-TERM-REASON-DESC>
    <HEU-ELIG-HIRE></HEU-ELIG-HIRE>
    <HEU-ELIG-HIRE-XLT></HEU-ELIG-HIRE-XLT>
    <EMP-ADJ-HIRE-DATE>20180606</EMP-ADJ-HIRE-DATE>
    <EMP-NEW-HIRE-DATE></EMP-NEW-HIRE-DATE>
    <EMP-ANNIVERS-DATE>20180606</EMP-ANNIVERS-DATE>
    <PEM-SENIOR-DATE>20180606</PEM-SENIOR-DATE>
    <EMP-FST-DAY-WORKED></EMP-FST-DAY-WORKED>
    <EMP-LAST-DAY-PAID></EMP-LAST-DAY-PAID>
    <ASSIGNMENT-TAB>0</ASSIGNMENT-TAB>
    <EMP-PROCESS-LEVEL>F2353</EMP-PROCESS-LEVEL>
    <PRS1-NAME></PRS1-NAME>
    <EMP-DEPARTMENT>00669</EMP-DEPARTMENT>
    <DPT-NAME></DPT-NAME>
    <EMP-WORK-COUNTRY>US</EMP-WORK-COUNTRY>
    <WORK-COUNTRY-DESC></WORK-COUNTRY-DESC>
    <EMP-SEC-LVL>9</EMP-SEC-LVL>
    <EMP-SEC-LOCATION>9999999999</EMP-SEC-LOCATION>
    <EMP-SUPERVISOR></EMP-SUPERVISOR>
    <SUP-NAME></SUP-NAME>
    <EMP-SUPER-IND></EMP-SUPER-IND>
    <IND-SUP-NAME></IND-SUP-NAME>
    <PEM-SECURITY-CODE>LEVEL 1</PEM-SECURITY-CODE>
    <PCO4-DESCRIPTION></PCO4-DESCRIPTION>
    <PEM-SECURITY-NBR>0000817570</PEM-SECURITY-NBR>
    <PEM-MAIL-GROUP>CALC-001</PEM-MAIL-GROUP>
    <PCO5-DESCRIPTION></PCO5-DESCRIPTION>
    <PEM-MB-NBR></PEM-MB-NBR>
    <PEM-CLOCK-NBR></PEM-CLOCK-NBR>
    <PAYGRP-NAME>JIB-BIWEEKLY</PAYGRP-NAME>
    <EMP-USER-LEVEL></EMP-USER-LEVEL>
    <EMP-POSITION></EMP-POSITION>
    <POS-DESCRIPTION></POS-DESCRIPTION>
    <EMP-UNION-CODE></EMP-UNION-CODE>
    <UNION-CODE-DESC></UNION-CODE-DESC>
    <PEM-BARGAIN-UNIT></PEM-BARGAIN-UNIT>
    <PCO3-DESCRIPTION></PCO3-DESCRIPTION>
    <EMP-WORK-SCHED></EMP-WORK-SCHED>
    <WORK-SCHED-DESC></WORK-SCHED-DESC>
    <POSITIONS></POSITIONS>
    <POSJOB-FLAG></POSJOB-FLAG>
    <MESSAGES-TAB>0</MESSAGES-TAB>
    <RETURN-CODE>1</RETURN-CODE>
    <RETURN-CODE-CAT></RETURN-CODE-CAT>
    <MSG-EDIT-NBR>1</MSG-EDIT-NBR>
    <MSG-JBX-WB-TYPE>HIRE</MSG-JBX-WB-TYPE>
    <MSG-FICA-MATCH>N</MSG-FICA-MATCH>
    <MSG-NAME-MATCH>N</MSG-NAME-MATCH>
    <MSG-GENDER-MATCH>N</MSG-GENDER-MATCH>
    <MSG-DOB-MATCH>N</MSG-DOB-MATCH>
    <MSG-ACTIVE>N</MSG-ACTIVE>
    <MSG-REHIRE>N</MSG-REHIRE>
    <CRT-ERROR-NBR></CRT-ERROR-NBR>
    <MSG-EDIT-MESSAGE1>Step 1 of the hire process is complete.</MSG-EDIT-MESSAGE1>
    <MSG-EDIT-MESSAGE2></MSG-EDIT-MESSAGE2>
    <MSG-EDIT-MESSAGE3></MSG-EDIT-MESSAGE3>
    <MSG-EDIT-MESSAGE4></MSG-EDIT-MESSAGE4>
    <MSG-EDIT-MESSAGE5></MSG-EDIT-MESSAGE5>
    <EMP-SALARY-CLASS>H</EMP-SALARY-CLASS>
    <EMP-SALARY-CLASS-XLT></EMP-SALARY-CLASS-XLT>
    <EMP-PAY-FREQUENCY>2</EMP-PAY-FREQUENCY>
    <EMP-PAY-FREQUENCY-XLT></EMP-PAY-FREQUENCY-XLT>
    <EMP-CURR-CODE>USD</EMP-CURR-CODE>
    <CURR-CODE-DESC></CURR-CODE-DESC>
    <ALT-PAY-RATES></ALT-PAY-RATES>
    <ALT-RATES-FLAG></ALT-RATES-FLAG>
    <EMP-SCHEDULE></EMP-SCHEDULE>
    <EMP-PAY-GRADE></EMP-PAY-GRADE>
    <EMP-PAY-STEP></EMP-PAY-STEP>
    <SCHEDULE-DESC></SCHEDULE-DESC>
    <COMP-ANALYSIS></COMP-ANALYSIS>
    <EMP-ANNUAL-HOURS>0</EMP-ANNUAL-HOURS>
    <EMP-NBR-FTE>.000</EMP-NBR-FTE>
    <EMP-SHIFT>1</EMP-SHIFT>
    <EMP-FTE-TOTAL>.000</EMP-FTE-TOTAL>
    <EMP-EXEMPT-EMP>N</EMP-EXEMPT-EMP>
    <EMP-EXEMPT-EMP-XLT></EMP-EXEMPT-EMP-XLT>
    <EMP-OT-PLAN-CODE></EMP-OT-PLAN-CODE>
    <PRO-DESCRIPTION></PRO-DESCRIPTION>
    <EMP-TIPPED>N</EMP-TIPPED>
    <EMP-TIPPED-XLT></EMP-TIPPED-XLT>
    <EMP-AUTO-DEPOSIT></EMP-AUTO-DEPOSIT>
    <EMP-AUTO-DEPOSIT-XLT></EMP-AUTO-DEPOSIT-XLT>
    <EMP-AUTO-TIME-REC></EMP-AUTO-TIME-REC>
    <EMP-AUTO-TIME-REC-XLT></EMP-AUTO-TIME-REC-XLT>
    <EMP-HM-DIST-CO>2353</EMP-HM-DIST-CO>
    <EMP-HM-ACCT-UNIT></EMP-HM-ACCT-UNIT>
    <EMP-HM-ACCOUNT></EMP-HM-ACCOUNT>
    <EMP-HM-SUB-ACCT></EMP-HM-SUB-ACCT>
    <GLM-DESCRIPTION></GLM-DESCRIPTION>
    <EMP-ACTIVITY></EMP-ACTIVITY>
    <EMP-ACCT-CATEGORY></EMP-ACCT-CATEGORY>
    <ACV-DESCRIPTION></ACV-DESCRIPTION>
    <PEM-WK-PHONE-CNTRY></PEM-WK-PHONE-CNTRY>
    <PEM-WK-PHONE-NBR></PEM-WK-PHONE-NBR>
    <PEM-WK-PHONE-EXT></PEM-WK-PHONE-EXT>
    <EMP-EMAIL-ADDRESS></EMP-EMAIL-ADDRESS>
    <PEM-COMP-CODE></PEM-COMP-CODE>
    <PEM-COMP-NBR></PEM-COMP-NBR>
    <PEM-HIRE-SOURCE></PEM-HIRE-SOURCE>
    <PCO6-DESCRIPTION></PCO6-DESCRIPTION>
    <PEM-FNCTN-GROUP>0</PEM-FNCTN-GROUP>
    <PEM-FNCTN-GROUP-XLT></PEM-FNCTN-GROUP-XLT>
    <PEM-EXCLUDE-FLAG></PEM-EXCLUDE-FLAG>
    <PEM-EXCLUDE-FLAG-XLT></PEM-EXCLUDE-FLAG-XLT>
    <PEM-BIRTH-CITY></PEM-BIRTH-CITY>
    <PEM-BIRTH-STATE></PEM-BIRTH-STATE>
    <PEM-BIRTH-CNTRY-CD></PEM-BIRTH-CNTRY-CD>
    <BIRTH-COUNTRY-DESC></BIRTH-COUNTRY-DESC>
    <PEM-MAIDEN-LST-NM></PEM-MAIDEN-LST-NM>
    <PEM-MAIDEN-FST-NM></PEM-MAIDEN-FST-NM>
    <PEM-MAIDEN-MI></PEM-MAIDEN-MI>
    <PEM-FORMER-LST-NM></PEM-FORMER-LST-NM>
    <PEM-FORMER-FST-NM></PEM-FORMER-FST-NM>
    <PEM-FORMER-MI></PEM-FORMER-MI>
    <PEM-TRUE-MAR-STAT></PEM-TRUE-MAR-STAT>
    <PEM-TRUE-MAR-STAT-XLT></PEM-TRUE-MAR-STAT-XLT>
    <PEM-HANDICAP-ID></PEM-HANDICAP-ID>
    <PEM-HANDICAP-ID-XLT></PEM-HANDICAP-ID-XLT>
    <PEM-DISABILITY></PEM-DISABILITY>
    <DISABILITY-DESC></DISABILITY-DESC>
    <PEM-RELIGION></PEM-RELIGION>
    <RELIGION-DESC></RELIGION-DESC>
    <PEM-LANGUAGE-CODE></PEM-LANGUAGE-CODE>
    <PEM-CONSENT></PEM-CONSENT>
    <PEM-CONSENT-XLT></PEM-CONSENT-XLT>
    <PEM-VISIBLE-MIN></PEM-VISIBLE-MIN>
    <PEM-VISIBLE-MIN-XLT></PEM-VISIBLE-MIN-XLT>
    <PEM-ABORIGINAL></PEM-ABORIGINAL>
    <PEM-ABORIGINAL-XLT></PEM-ABORIGINAL-XLT>
    <DATE-OF-DEATH></DATE-OF-DEATH>
    <EMP-ADDR1>10806 BISSONNET ST</EMP-ADDR1>
    <PEM-SUPP-ADDR1>10806 BISSONNET ST</PEM-SUPP-ADDR1>
    <EMP-ADDR2></EMP-ADDR2>
    <PEM-SUPP-ADDR2></PEM-SUPP-ADDR2>
    <EMP-ADDR3></EMP-ADDR3>
    <PEM-SUPP-ADDR3></PEM-SUPP-ADDR3>
    <EMP-ADDR4></EMP-ADDR4>
    <PEM-SUPP-ADDR4></PEM-SUPP-ADDR4>
    <EMP-CITY>HOUSTON</EMP-CITY>
    <PEM-SUPP-CITY>HOUSTON</PEM-SUPP-CITY>
    <EMP-STATE>TX</EMP-STATE>
    <PEM-SUPP-STATE>TX</PEM-SUPP-STATE>
    <EMP-ZIP>77099</EMP-ZIP>
    <PEM-SUPP-ZIP>77099</PEM-SUPP-ZIP>
    <PEM-SUPP-COUNTY>HARRIS</PEM-SUPP-COUNTY>
    <EMP-COUNTY>HARRIS</EMP-COUNTY>
    <EMP-COUNTRY-CODE>US</EMP-COUNTRY-CODE>
    <EMP-COUNTRY-DESC></EMP-COUNTRY-DESC>
    <PEM-SUPP-CNTRY-CD>US</PEM-SUPP-CNTRY-CD>
    <SUPP-COUNTRY-DESC></SUPP-COUNTRY-DESC>
    <PEM-HM-PHONE-CNTRY></PEM-HM-PHONE-CNTRY>
    <PEM-HM-PHONE-NBR>************</PEM-HM-PHONE-NBR>
    <PEM-SUPP-PHONE-CNT></PEM-SUPP-PHONE-CNT>
    <PEM-SUPP-PHONE-NBR>************</PEM-SUPP-PHONE-NBR>
    <MORE></MORE>
    <PEM-I9-DOC-TYPE1></PEM-I9-DOC-TYPE1>
    <PCO-DT-DESC1></PCO-DT-DESC1>
    <PEM-I9-DOC-TYPE2></PEM-I9-DOC-TYPE2>
    <PCO-DT-DESC2></PCO-DT-DESC2>
    <PEM-I9-DOC-DESCR1></PEM-I9-DOC-DESCR1>
    <PCO-DO-DESC1></PCO-DO-DESC1>
    <PEM-I9-DOC-DESCR2></PEM-I9-DOC-DESCR2>
    <PCO-DO-DESC2></PCO-DO-DESC2>
    <PEM-I9-DOC-NBR1></PEM-I9-DOC-NBR1>
    <PEM-I9-DOC-NBR2></PEM-I9-DOC-NBR2>
    <PEM-I9-DOC-EXP-DT1></PEM-I9-DOC-EXP-DT1>
    <PEM-I9-DOC-EXP-DT2></PEM-I9-DOC-EXP-DT2>
    <PEM-I9-STATUS></PEM-I9-STATUS>
    <PCO-WE-DESC></PCO-WE-DESC>
    <PEM-I9-ALIEN-NBR></PEM-I9-ALIEN-NBR>
    <PEM-I9-STA-EXP-DT></PEM-I9-STA-EXP-DT>
    <PEM-I9-ADMIT-NBR></PEM-I9-ADMIT-NBR>
    <PEM-I9-AUTHORIZE></PEM-I9-AUTHORIZE>
    <MORE-MSG></MORE-MSG>
    <USER-FIELDS></USER-FIELDS>
    <LINE-FCr0></LINE-FCr0>
    <HRU-FLD-NBRr0>0</HRU-FLD-NBRr0>
    <HRU-FIELD-TYPEr0></HRU-FIELD-TYPEr0>
    <HRU-FIELD-TYPEr0-XLT></HRU-FIELD-TYPEr0-XLT>
    <HRU-FIELD-NAMEr0></HRU-FIELD-NAMEr0>
    <VALUEr0></VALUEr0>
    <HRU-DESCRIPTIONr0></HRU-DESCRIPTIONr0>
    <HEU-CURRENCY-CODEr0></HEU-CURRENCY-CODEr0>
    <HRU-REQ-FLAGr0></HRU-REQ-FLAGr0>
    <HRU-REQ-FLAGr0-XLT></HRU-REQ-FLAGr0-XLT>
    <BASE-CURRENCY-TABr0></BASE-CURRENCY-TABr0>
    <HEU-BASE-AMOUNTr0></HEU-BASE-AMOUNTr0>
    <HEU-BASE-CURRENCYr0></HEU-BASE-CURRENCYr0>
    <BASE-FORMS-EXPr0></BASE-FORMS-EXPr0>
    <CALC-WINDOWr0></CALC-WINDOWr0>
    <HEU-BASE-NDr0></HEU-BASE-NDr0>
    <HEU-CURR-NDr0></HEU-CURR-NDr0>
    <HRU-FIELD-KEYr0></HRU-FIELD-KEYr0>
    <LINE-FCr1></LINE-FCr1>
    <HRU-FLD-NBRr1>0</HRU-FLD-NBRr1>
    <HRU-FIELD-TYPEr1></HRU-FIELD-TYPEr1>
    <HRU-FIELD-TYPEr1-XLT></HRU-FIELD-TYPEr1-XLT>
    <HRU-FIELD-NAMEr1></HRU-FIELD-NAMEr1>
    <VALUEr1></VALUEr1>
    <HRU-DESCRIPTIONr1></HRU-DESCRIPTIONr1>
    <HEU-CURRENCY-CODEr1></HEU-CURRENCY-CODEr1>
    <HRU-REQ-FLAGr1></HRU-REQ-FLAGr1>
    <HRU-REQ-FLAGr1-XLT></HRU-REQ-FLAGr1-XLT>
    <BASE-CURRENCY-TABr1></BASE-CURRENCY-TABr1>
    <HEU-BASE-AMOUNTr1></HEU-BASE-AMOUNTr1>
    <HEU-BASE-CURRENCYr1></HEU-BASE-CURRENCYr1>
    <BASE-FORMS-EXPr1></BASE-FORMS-EXPr1>
    <CALC-WINDOWr1></CALC-WINDOWr1>
    <HEU-BASE-NDr1></HEU-BASE-NDr1>
    <HEU-CURR-NDr1></HEU-CURR-NDr1>
    <HRU-FIELD-KEYr1></HRU-FIELD-KEYr1>
    <LINE-FCr2></LINE-FCr2>
    <HRU-FLD-NBRr2>0</HRU-FLD-NBRr2>
    <HRU-FIELD-TYPEr2></HRU-FIELD-TYPEr2>
    <HRU-FIELD-TYPEr2-XLT></HRU-FIELD-TYPEr2-XLT>
    <HRU-FIELD-NAMEr2></HRU-FIELD-NAMEr2>
    <VALUEr2></VALUEr2>
    <HRU-DESCRIPTIONr2></HRU-DESCRIPTIONr2>
    <HEU-CURRENCY-CODEr2></HEU-CURRENCY-CODEr2>
    <HRU-REQ-FLAGr2></HRU-REQ-FLAGr2>
    <HRU-REQ-FLAGr2-XLT></HRU-REQ-FLAGr2-XLT>
    <BASE-CURRENCY-TABr2></BASE-CURRENCY-TABr2>
    <HEU-BASE-AMOUNTr2></HEU-BASE-AMOUNTr2>
    <HEU-BASE-CURRENCYr2></HEU-BASE-CURRENCYr2>
    <BASE-FORMS-EXPr2></BASE-FORMS-EXPr2>
    <CALC-WINDOWr2></CALC-WINDOWr2>
    <HEU-BASE-NDr2></HEU-BASE-NDr2>
    <HEU-CURR-NDr2></HEU-CURR-NDr2>
    <HRU-FIELD-KEYr2></HRU-FIELD-KEYr2>
    <LINE-FCr3></LINE-FCr3>
    <HRU-FLD-NBRr3>0</HRU-FLD-NBRr3>
    <HRU-FIELD-TYPEr3></HRU-FIELD-TYPEr3>
    <HRU-FIELD-TYPEr3-XLT></HRU-FIELD-TYPEr3-XLT>
    <HRU-FIELD-NAMEr3></HRU-FIELD-NAMEr3>
    <VALUEr3></VALUEr3>
    <HRU-DESCRIPTIONr3></HRU-DESCRIPTIONr3>
    <HEU-CURRENCY-CODEr3></HEU-CURRENCY-CODEr3>
    <HRU-REQ-FLAGr3></HRU-REQ-FLAGr3>
    <HRU-REQ-FLAGr3-XLT></HRU-REQ-FLAGr3-XLT>
    <BASE-CURRENCY-TABr3></BASE-CURRENCY-TABr3>
    <HEU-BASE-AMOUNTr3></HEU-BASE-AMOUNTr3>
    <HEU-BASE-CURRENCYr3></HEU-BASE-CURRENCYr3>
    <BASE-FORMS-EXPr3></BASE-FORMS-EXPr3>
    <CALC-WINDOWr3></CALC-WINDOWr3>
    <HEU-BASE-NDr3></HEU-BASE-NDr3>
    <HEU-CURR-NDr3></HEU-CURR-NDr3>
    <HRU-FIELD-KEYr3></HRU-FIELD-KEYr3>
    <LINE-FCr4></LINE-FCr4>
    <HRU-FLD-NBRr4>0</HRU-FLD-NBRr4>
    <HRU-FIELD-TYPEr4></HRU-FIELD-TYPEr4>
    <HRU-FIELD-TYPEr4-XLT></HRU-FIELD-TYPEr4-XLT>
    <HRU-FIELD-NAMEr4></HRU-FIELD-NAMEr4>
    <VALUEr4></VALUEr4>
    <HRU-DESCRIPTIONr4></HRU-DESCRIPTIONr4>
    <HEU-CURRENCY-CODEr4></HEU-CURRENCY-CODEr4>
    <HRU-REQ-FLAGr4></HRU-REQ-FLAGr4>
    <HRU-REQ-FLAGr4-XLT></HRU-REQ-FLAGr4-XLT>
    <BASE-CURRENCY-TABr4></BASE-CURRENCY-TABr4>
    <HEU-BASE-AMOUNTr4></HEU-BASE-AMOUNTr4>
    <HEU-BASE-CURRENCYr4></HEU-BASE-CURRENCYr4>
    <BASE-FORMS-EXPr4></BASE-FORMS-EXPr4>
    <CALC-WINDOWr4></CALC-WINDOWr4>
    <HEU-BASE-NDr4></HEU-BASE-NDr4>
    <HEU-CURR-NDr4></HEU-CURR-NDr4>
    <HRU-FIELD-KEYr4></HRU-FIELD-KEYr4>
    <EMP-BSI-GROUP></EMP-BSI-GROUP>
    <EMP-EIC-STATUS>0</EMP-EIC-STATUS>
    <EMP-EIC-STATUS-XLT></EMP-EIC-STATUS-XLT>
    <DFT-MAR-STAT>1</DFT-MAR-STAT>
    <DFT-MAR-STAT-XLT></DFT-MAR-STAT-XLT>
    <DFT-EXEMPTS>0</DFT-EXEMPTS>
    <EMP-WORK-STATE></EMP-WORK-STATE>
    <STATE-BUTTON></STATE-BUTTON>
    <STATE-BTN-INDICATOR></STATE-BTN-INDICATOR>
    <RAILROAD-CODE>0</RAILROAD-CODE>
    <RAILROAD-CODE-XLT></RAILROAD-CODE-XLT>
    <EMP-TAX-FILTER>0</EMP-TAX-FILTER>
    <EMP-TAX-FILTER-XLT></EMP-TAX-FILTER-XLT>
    <EMP-REMOTE>0</EMP-REMOTE>
    <EMP-REMOTE-XLT></EMP-REMOTE-XLT>
    <EMP-TAX-CITY></EMP-TAX-CITY>
    <EMP-TAX-COUNTY></EMP-TAX-COUNTY>
    <EMP-TAX-SCHOOL></EMP-TAX-SCHOOL>
    <EMP-WORK-CITY></EMP-WORK-CITY>
    <EMP-WORK-COUNTY></EMP-WORK-COUNTY>
    <EMP-WORK-SCHOOL></EMP-WORK-SCHOOL>
    <EMP-TAX-PROVINCE></EMP-TAX-PROVINCE>
    <PPV-DESCRIPTION></PPV-DESCRIPTION>
    <EMP-BUS-NBR-GRP></EMP-BUS-NBR-GRP>
    <PBG-BUS-NBR></PBG-BUS-NBR>
    <EMP-QC-ENT-NBR-GRP></EMP-QC-ENT-NBR-GRP>
    <PQC-QC-ENT-NBR></PQC-QC-ENT-NBR>
    <EMP-WC-PROVINCE></EMP-WC-PROVINCE>
    <PPV-WC-DESCRIPTION></PPV-WC-DESCRIPTION>
    <EMP-WARN-FLAG></EMP-WARN-FLAG>
    <EMP-WARN-FLAG-XLT></EMP-WARN-FLAG-XLT>
    <EMP-STAND-HOURS>.00</EMP-STAND-HOURS>
    <EMP-STAND-AMT>.00</EMP-STAND-AMT>
    <EMP-CURR-CODE-3>USD</EMP-CURR-CODE-3>
    <EMP-ADD-ALLOW-PER>.000</EMP-ADD-ALLOW-PER>
    <EMP-ADD-ALLOW-HRS>.00</EMP-ADD-ALLOW-HRS>
    <EMP-ADD-ALLOW-AMT>0</EMP-ADD-ALLOW-AMT>
    <EMP-CURR-CODE-4>USD</EMP-CURR-CODE-4>
    <EMP-WC-STATE>TX</EMP-WC-STATE>
    <PSA2-DESCRIPTION></PSA2-DESCRIPTION>
    <EMP-EBE-AMOUNT>.00</EMP-EBE-AMOUNT>
    <EMP-SICK-PAY>.00</EMP-SICK-PAY>
    <EMP-MOVING-EXP>.00</EMP-MOVING-EXP>
    <EMP-RPT-INS-COST>.00</EMP-RPT-INS-COST>
    <EMP-PENSION-PLAN>N</EMP-PENSION-PLAN>
    <EMP-PENSION-PLAN-XLT></EMP-PENSION-PLAN-XLT>
    <PEM-BEN-DATE-1>20180606</PEM-BEN-DATE-1>
    <PEM-BEN-DATE-2></PEM-BEN-DATE-2>
    <PEM-BEN-DATE-4></PEM-BEN-DATE-4>
    <PEM-BEN-DATE-5></PEM-BEN-DATE-5>
    <PEM-BEN-SALARY-1>.00</PEM-BEN-SALARY-1>
    <EMP-CURR-CODE-2>USD</EMP-CURR-CODE-2>
    <CURR-CODE-2-DESC></CURR-CODE-2-DESC>
    <PEM-BEN-SALARY-2>9000.00</PEM-BEN-SALARY-2>
    <PEM-BEN-SALARY-3>9000.00</PEM-BEN-SALARY-3>
    <PEM-BEN-SALARY-4>.00</PEM-BEN-SALARY-4>
    <PEM-BEN-SALARY-5>.00</PEM-BEN-SALARY-5>
    <PEM-WORK-ZIP>77099</PEM-WORK-ZIP>
    <PEM-VETERAN>N</PEM-VETERAN>
    <EMP-DECEASED>N</EMP-DECEASED>
    <PEM-SMOKER></PEM-SMOKER>
    <PEM-SMOKER-XLT></PEM-SMOKER-XLT>
    <PEM-LIFE-STYLE-CR>0</PEM-LIFE-STYLE-CR>
    <PEM-NBR-HL-DEP>0</PEM-NBR-HL-DEP>
    <PEM-NBR-DN-DEP>0</PEM-NBR-DN-DEP>
    <PEM-PRIMARY-CARE></PEM-PRIMARY-CARE>
    <PCO7-DESCRIPTION></PCO7-DESCRIPTION>
    <ESTAB-PATIENT>0</ESTAB-PATIENT>
    <ESTAB-PATIENT-XLT></ESTAB-PATIENT-XLT>
    <PRIOR-MONTHS-COV>0</PRIOR-MONTHS-COV>
    <PEM-RELATED-EMP></PEM-RELATED-EMP>
    <PEM-RELATED-EMP-XLT></PEM-RELATED-EMP-XLT>
    <PEM-HL-COV-PROOF></PEM-HL-COV-PROOF>
    <PEM-HL-COV-PROOF-XLT></PEM-HL-COV-PROOF-XLT>
    <PEM-HL-VERIFY-DT></PEM-HL-VERIFY-DT>
    <PEM-DN-COV-PROOF></PEM-DN-COV-PROOF>
    <PEM-DN-COV-PROOF-XLT></PEM-DN-COV-PROOF-XLT>
    <PEM-DN-VERIFY-DT></PEM-DN-VERIFY-DT>
    <PEM-OWNER-FLAG></PEM-OWNER-FLAG>
    <PEM-OWNER-FLAG-XLT></PEM-OWNER-FLAG-XLT>
    <PEM-OFFICER></PEM-OFFICER>
    <PEM-OFFICER-XLT></PEM-OFFICER-XLT>
    <PEM-KEY-EMP-FLAG></PEM-KEY-EMP-FLAG>
    <PEM-KEY-EMP-FLAG-XLT></PEM-KEY-EMP-FLAG-XLT>
    <PEM-HIGH-COMP></PEM-HIGH-COMP>
    <PEM-HIGH-COMP-XLT></PEM-HIGH-COMP-XLT>
    <EMP-MAX-LIMIT-OVRD></EMP-MAX-LIMIT-OVRD>
    <EMP-MAX-LIMIT-OVRD-XLT></EMP-MAX-LIMIT-OVRD-XLT>
    <PEM-FAMILY-AGG></PEM-FAMILY-AGG>
    <FAMILY-AGG-NAME></FAMILY-AGG-NAME>
    <PEM-SPOUSE-EMP></PEM-SPOUSE-EMP>
    <PEM-SP-EMP-ADDR1></PEM-SP-EMP-ADDR1>
    <PEM-SP-EMP-ADDR2></PEM-SP-EMP-ADDR2>
    <PEM-SP-EMP-ADDR3></PEM-SP-EMP-ADDR3>
    <PEM-SP-EMP-ADDR4></PEM-SP-EMP-ADDR4>
    <PEM-SP-EMP-CITY></PEM-SP-EMP-CITY>
    <PEM-SP-EMP-STATE></PEM-SP-EMP-STATE>
    <PEM-SP-EMP-ZIP></PEM-SP-EMP-ZIP>
    <PEM-SP-EMP-COUNTRY></PEM-SP-EMP-COUNTRY>
    <SP-COUNTRY-DESC></SP-COUNTRY-DESC>
    <PEM-SP-EMP-PH-CNTR></PEM-SP-EMP-PH-CNTR>
    <PEM-SP-EMP-PH-NBR></PEM-SP-EMP-PH-NBR>
    <EDM-EFFECT-DATE></EDM-EFFECT-DATE>
    <EDM-END-DATE></EDM-END-DATE>
    <FM0-EDIT-NBR>1</FM0-EDIT-NBR>
    <FM0-EDIT-MESSAGE1>Step 1 of the hire process is complete.</FM0-EDIT-MESSAGE1>
    <FM0-EDIT-MESSAGE2></FM0-EDIT-MESSAGE2>
    <FM0-EDIT-MESSAGE3></FM0-EDIT-MESSAGE3>
    <FM0-EDIT-MESSAGE4></FM0-EDIT-MESSAGE4>
    <FM0-EDIT-MESSAGE5></FM0-EDIT-MESSAGE5>
    <FM0-JBX-WB-TYPE>HIRE</FM0-JBX-WB-TYPE>
    <FM0-FICA-MATCH>N</FM0-FICA-MATCH>
    <FM0-NAME-MATCH>N</FM0-NAME-MATCH>
    <FM0-GENDER-MATCH>N</FM0-GENDER-MATCH>
    <FM0-DOB-MATCH>N</FM0-DOB-MATCH>
    <FM0-ACTIVE>N</FM0-ACTIVE>
    <FM0-REHIRE>N</FM0-REHIRE>
    <Message>Hire complete; Continue</Message>
    <MsgNbr>000</MsgNbr>
    <StatusNbr>001</StatusNbr>
    <FldNbr>TC</FldNbr>
  </FM40.1>
</XFM40.1>