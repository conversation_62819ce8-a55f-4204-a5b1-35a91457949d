﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Afterman.nRepo" version="2021.9.15.2" targetFramework="net461" />
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net461" />
  <package id="AutoMapper" version="8.1.1" targetFramework="net461" />
  <package id="BouncyCastle.OpenPgp" version="1.8.1.1" targetFramework="net461" />
  <package id="Castle.Core" version="5.2.1" targetFramework="net461" />
  <package id="CommonServiceLocator" version="1.3" targetFramework="net461" />
  <package id="FluentNHibernate" version="2.0.3.0" targetFramework="net461" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net461" />
  <package id="Jitb.ActiveDirectory.Commons" version="2021.6.22.1" targetFramework="net461" />
  <package id="Jitb.Common.Contracts" version="2019.10.31.1" targetFramework="net461" />
  <package id="Jitb.CommonLibrary" version="2022.11.22.4" targetFramework="net461" />
  <package id="Jitb.NSB.Commons" version="2022.11.10.1" targetFramework="net461" />
  <package id="log4net" version="2.0.12" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights" version="2.4.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.4.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.4.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.4.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.4.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.4.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.4.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.TelemetryCorrelation" version="1.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.4" targetFramework="net461" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="1.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.Primitives" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Net.Compilers" version="1.0.0" targetFramework="net461" developmentDependency="true" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="12.0.1" targetFramework="net461" />
  <package id="NHibernate" version="4.0.4.4000" targetFramework="net461" />
  <package id="NLog" version="4.7.10" targetFramework="net461" />
  <package id="NLog.Web" version="4.5.1" targetFramework="net461" />
  <package id="NServiceBus" version="7.4.0" targetFramework="net461" />
  <package id="NServiceBus.AmazonSQS" version="4.4.1" targetFramework="net461" />
  <package id="NServiceBus.Callbacks" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.CustomChecks" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.Encryption.MessageProperty" version="2.0.1" targetFramework="net461" />
  <package id="NServiceBus.Heartbeat" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.Log4Net" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.Metrics" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.Metrics.ServiceControl" version="3.0.2" targetFramework="net461" />
  <package id="NServiceBus.Metrics.ServiceControl.Msmq" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.Newtonsoft.Json" version="2.2.0" targetFramework="net461" />
  <package id="NServiceBus.NHibernate" version="8.0.1" targetFramework="net461" />
  <package id="NServiceBus.NLog" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.Raw" version="3.2.1" targetFramework="net461" />
  <package id="NServiceBus.Router" version="3.9.0" targetFramework="net461" />
  <package id="NServiceBus.Router.Connector" version="3.9.1" targetFramework="net461" />
  <package id="NServiceBus.SagaAudit" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.StructureMap" version="7.0.0" targetFramework="net461" />
  <package id="NServiceBus.Transport.Msmq" version="1.1.1" targetFramework="net461" />
  <package id="OctoPack" version="3.6.5" targetFramework="net461" developmentDependency="true" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net461" />
  <package id="Remotion.Linq.EagerFetching" version="2.2.0" targetFramework="net461" />
  <package id="SSH.NET" version="2020.0.1" targetFramework="net461" />
  <package id="StructureMap" version="4.7.1" targetFramework="net461" />
  <package id="StructureMap.MVC5" version="3.0.4.125" targetFramework="net461" />
  <package id="structuremap.web" version="4.0.0.315" targetFramework="net461" />
  <package id="StructureMap.WebApi2" version="3.0.4.125" targetFramework="net461" />
  <package id="Swashbuckle" version="5.6.0" targetFramework="net461" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net461" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net461" />
  <package id="System.Data.SqlClient" version="4.4.3" targetFramework="net461" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.4.1" targetFramework="net461" />
  <package id="System.Diagnostics.EventLog" version="4.7.0" targetFramework="net461" />
  <package id="System.Memory" version="4.5.4" targetFramework="net461" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net461" />
  <package id="System.Reflection.Emit" version="4.7.0" targetFramework="net461" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.1" targetFramework="net461" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net461" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net461" />
  <package id="WebActivatorEx" version="2.1.0" targetFramework="net461" />
</packages>