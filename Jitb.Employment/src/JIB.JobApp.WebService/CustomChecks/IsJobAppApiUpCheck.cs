﻿using Jitb.Employment.Domain.Configuration;
using Jitb.Employment.Domain.Exceptions;
using Jitb.Employment.Domain.Integrations.Components.JobApp;
using NServiceBus.Logging;
using NServiceBus.CustomChecks;
using System;
using System.Threading.Tasks;

namespace JIB.JobApp.WebService.CustomChecks
{
    public class IsJobAppApiUpCheck
        : CustomCheck
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(IsJobAppApiUpCheck));
        private readonly IPing _ping;

        public IsJobAppApiUpCheck(IPing ping)
            : base("JobApp API Uptime", "Dependent Services", TimeSpan.FromMinutes(5))
        {
            _ping = ping;
        }

        public override Task<CheckResult> PerformCheck()
        {
            try
            {
                Log.Info($"JobApp API custom check is pinging URL {ConfigurationContext.JobApp.PingUrl}.");
                // As long as we don't get an exception we're good.
                _ping.PingJobAppApi(ConfigurationContext.JobApp.PingUrl);
                return CheckResult.Pass;
            }
            catch (JobAppApiInaccessibleException ex)
            {
                Log.Error("JobApp API custom check failed.", ex);
                return CheckResult.Failed(ex.Message);
            }
        }
    }
}
