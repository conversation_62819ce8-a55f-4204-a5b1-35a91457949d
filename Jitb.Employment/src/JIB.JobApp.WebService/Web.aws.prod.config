﻿<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <connectionStrings>
    <add name="InstanceMapping" connectionString="data source=dz-nservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;"
			   xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    <add name="MessageRouteMapping" connectionString="data source=dbnsb.prod-entsys.aws.jitb.net;initial catalog=dbNSB;integrated security=SSPI;"
			  xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    <add name="NServiceBus/Persistence" connectionString="data source=dz-nservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;"
			   xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    <add name="Default" connectionString="data source=dz-nservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;"
			   xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    <add name="HireEligibility" connectionString="data source=dz-nservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
  </connectionStrings>
  <appSettings>
    <add key="env" value="prod"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="ServiceControl/Queue" value="particular.servicecontrol@AW2NSBSCP01"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EncryptionEnabled" value="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="ReplicateRequestsToTestApi" value="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="TestApiUrl" value="https://jobapp-test.jackinthebox.com/api"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="PingApiUrl" value="https://jobapp.jackinthebox.com/api/HireProcess"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="SendToNServiceBus" value="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="EnableJsonRequestLogging" value="false"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="auditQueue" value="audit@AW2NSBSCP01"
        xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="errorQueue" value="error@AW2NSBSCP01"
        xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
  </system.web>
</configuration>
