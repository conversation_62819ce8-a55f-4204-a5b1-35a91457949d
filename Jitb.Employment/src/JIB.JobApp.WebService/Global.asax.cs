﻿using Jitb.Employment.Domain.Configuration;
using Jitb.Employment.Domain.Helpers;
using NServiceBus.Logging;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using System.Web.Routing;

namespace JIB.JobApp.WebService
{
    [ExcludeFromCodeCoverage]
    public class WebApiApplication : System.Web.HttpApplication
    {
        private static ILog Log = LogManager.GetLogger(typeof(WebApiApplication));

        protected void Application_Start()
        {
            LogManager.Use<NServiceBus.NLogFactory>();
            GlobalConfiguration.Configure(WebApiConfig.Register);
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            MvcHandler.DisableMvcResponseHeader = true;
        }

        protected void Application_Error()
        {
            var ex = Server.GetLastError().GetBaseException();

            Log.Error($"Error in web api: {ex}");

            Server.ClearError();
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            // Commented out because this can log PII.
            //if (ConfigurationContext.JobApp.EnableJsonRequestLogging)
            //    HttpHelper.LogHttpRequest(HttpContext.Current.Request);
        }
    }
}
