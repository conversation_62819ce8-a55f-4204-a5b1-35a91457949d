﻿<?xml version="1.0" encoding="utf-8"?>
<!-- For more information on using transformations 
     see the web.config examples at http://go.microsoft.com/fwlink/?LinkId=214134. -->
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <connectionStrings>
    <add name="NServiceBus/Persistence"
         connectionString="data source=CSERESTT01V;initial catalog=dbNServiceBus;integrated security=SSPI;"
         xdt:Transform="SetAttributes"
         xdt:Locator="Match(name)"/>
    <add name="Default"
         connectionString="data source=CSERESTT01V;initial catalog=dbNServiceBus;integrated security=SSPI;"
         xdt:Transform="SetAttributes"
         xdt:Locator="Match(name)"/>
    <add name="HireEligibility"
         connectionString="data source=CSERESTT01V;initial catalog=dbNServiceBus;integrated security=SSPI"
         xdt:Transform="SetAttributes"
         xdt:Locator="Match(name)"/>
  </connectionStrings>
  <appSettings>
    <add key="env" value="qa"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="ServiceControl/Queue" value="particular.servicecontrol@CSNSBSCT01V"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EncryptionEnabled" value="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="ReplicateRequestsToTestApi" value="false"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="TestApiUrl" value="https://csccapatost0115/api"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="PingApiUrl" value="https://jobapp-test.jackinthebox.com/api/HireProcess"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="SendToNServiceBus" value="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="EnableJsonRequestLogging" value="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="auditQueue" value="audit@csnsbsct01v"
        xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="errorQueue" value="error@csnsbsct01v"
        xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
  </system.web>
</configuration>