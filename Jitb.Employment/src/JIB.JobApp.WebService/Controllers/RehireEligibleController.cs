﻿using JIB.JobApp.WebService;
using Jitb.Employment.Contracts.Commands.Employment;
using Jitb.Employment.Domain.Commons;
using Jitb.Employment.Domain.Constants;
using Jitb.Employment.Domain.Models;
using Jitb.Employment.Domain.Providers;
using NServiceBus;
using NServiceBus.Logging;
using System;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace Jitb.Employment.Web.Api.Controllers
{
    public class RehireEligibleController : ApiController
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(RehireEligibleController));
        private readonly IEndpointInstance _endpointInstance;
        private readonly IHireApplicantDataValidator _hireApplicantDataValidator;
        private readonly IApplicantEligibilityVerifier _applicantEligibilityVerifier;

        public RehireEligibleController(IEndpointInstance endpointInstance
            , IHireApplicantDataValidator hireApplicantDataValidator
            , IApplicantEligibilityVerifier applicantEligibilityVerifier)
        {
            _endpointInstance = endpointInstance;
            _hireApplicantDataValidator = hireApplicantDataValidator;
            _applicantEligibilityVerifier = applicantEligibilityVerifier;
        }

        public IHttpActionResult Get()
        {
            var msg = "JobApp API RehireEligibleController is responding";
            Log.Info(msg);
            return Ok(msg);
        }

        public HttpResponseMessage Post(RehireEligible item)
        {
            // Fire and forget a call to qa/test/dev API to enable real-time test data.
#pragma warning disable 4014
            WebApiConfig.ReplicateRequestToTestApi(item);
#pragma warning restore 4014

            // Extracts the StoreNum from the item.Location to use in logging.
            int storeNum = 0;
            Int32.TryParse(item.Location, out storeNum);
            NLog.MappedDiagnosticsContext.Set("StoreNumber", storeNum);

            Log.Info("Start RehireEligibleController");

            HttpResponseMessage response = null;
            var result = new RehireEligibleResult();

            if (item != null)
            {
                Log.Info($"Unmodified RehireEligible item {item}");

                if (ModelState.IsValid)
                {
                    try
                    {
                        var ssn = _hireApplicantDataValidator.ValidateSSN(item.SourceSSN);
                        var maskedSsn = StringMasks.MaskSsn(ssn);

                        Log.Info($"After calling ValidateSSN() method ssn = {maskedSsn}.");

                        try
                        {
                            // Check for applicant eligibility (not as rigorous a check as HireEligibilityVerifier).
                            var eligibilityResult = _applicantEligibilityVerifier.IsApplicantEligibleToProceed(ssn);
                            if (eligibilityResult.IsEligible)
                            {
                                result.Eligible = JobApp.RESULT_YES;
                                result.Message = JobApp.RESULT_ELIGIBLE;

                                Log.Info($"Sending CheckForRehireEligibility command for ssn: {maskedSsn}");

                                var checkForRehireEligibilityCommand = new CheckForRehireEligibility()
                                {
                                    Ssn = ssn
                                };

                                try
                                {
                                    _endpointInstance.Send(checkForRehireEligibilityCommand)
                                        .GetAwaiter()
                                        .GetResult();
                                }
                                catch (Exception ex)
                                {
                                    Log.Error($"Application error: {ex.Message}", ex);
                                    result.Eligible = JobApp.RESULT_NO;
                                    result.Message = ex.Message;
                                }
                            }
                            else
                            {
                                result.Eligible = JobApp.RESULT_NO;
                                result.Message = JobApp.RESULT_NOT_ELIGIBLE;
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"Error validating against EIS db: {ex.Message}", ex);

                            result.Eligible = JobApp.RESULT_NO;
                            result.Message = JobApp.ERROR_PERSONNEL;
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"Error: {ex.Message}", ex);

                        result.Eligible = JobApp.RESULT_NO;
                        result.Message = JobApp.ERROR_PERSONNEL;
                    }
                    finally
                    {
                        response = Request.CreateResponse(HttpStatusCode.Created, result);
                    }
                }
                else // a required field is missing.
                {
                    response = Request.CreateErrorResponse(HttpStatusCode.BadRequest, ModelState);

                    var missingFields = "";
                    foreach (string modelkey in ModelState.Keys)
                    {
                        missingFields += $" {modelkey}";
                    }
                    Log.Info($"{(int)response.StatusCode} {response.ReasonPhrase}. Missing required field(s): {missingFields}");
                }

                Log.Info($"Final response result: {result}");
            }
            else
            {
                Log.Info($"Model Error: NULL item. Check payload for missing braces, quotes, or content");

                response = Request.CreateErrorResponse(HttpStatusCode.BadRequest, ModelState);
            }

            Log.Info("End RehireEligibleController");

            return response;
        }
    }
}
