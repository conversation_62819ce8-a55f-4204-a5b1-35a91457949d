﻿using JIB.JobApp.WebService;
using Jitb.CommonLibrary;
using Jitb.Employment.Contracts.Commands.CoreLifecycle;
using Jitb.Employment.Domain.Commons;
using Jitb.Employment.Domain.Configuration;
using Jitb.Employment.Domain.Constants;
using Jitb.Employment.Domain.Models;
using Jitb.Employment.Domain.Providers;
using NServiceBus;
using NServiceBus.Logging;
using System;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace Jitb.Employment.Web.Api.Controllers
{
    public class HireProcessController : ApiController
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(HireProcessController));
        private readonly IEndpointInstance _endpointInstance;
        private readonly IHireApplicantDataValidator _hireApplicantDataValidator;
        private readonly IHireEligibilityVerifier _hireEligibilityVerifier;

        public HireProcessController(IEndpointInstance endpointInstance
            , IHireApplicantDataValidator hireApplicantDataValidator
            , IHireEligibilityVerifier hireEligibilityVerifier)
        {
            _endpointInstance = endpointInstance;
            _hireApplicantDataValidator = hireApplicantDataValidator;
            _hireEligibilityVerifier = hireEligibilityVerifier;
        }

        public IHttpActionResult Get()
        {
            var msg = "JobApp API HireProcessController is responding";
            Log.Info(msg);
            return Ok(msg);
        }

        public HttpResponseMessage Post(HireProcess item)
        {
            // Fire and forget a call to qa/test/dev API to enable real-time test data.
#pragma warning disable 4014
            WebApiConfig.ReplicateRequestToTestApi(item);
#pragma warning restore 4014

            // Extracts the StoreNum from the item.Location to use in logging.
            int storeNum = 0;
            Int32.TryParse(item.Location, out storeNum);
            NLog.MappedDiagnosticsContext.Set("StoreNumber", storeNum);

            Log.Info("Start HireProcessController");

            HttpResponseMessage response = null;
            var result = new HireProcessResult();

            if (item != null)
            {
                Log.Info($"Unmodified HireProcess item before TransformPayload() - {item}");

                if (ModelState.IsValid)
                {
                    try
                    {
                        // Perform all validation and business logic of required fields.
                        item = _hireApplicantDataValidator.TransformPayload(item);

                        Log.Info($"Modified HireProcess item after TransformPayload() - {item}");

                        var ssn = item.SourceSSN;
                        var maskedSsn = StringMasks.MaskSsn(ssn);
                        var lastName = item.LastName;
                        var gender = item.EEOGender;
                        var dob = StringUtility.ConvertToDateOrNull(item.DateOfBirth).Value;

                        Log.Info("Check for hire/rehire eligibility.");
                        var eligibilityResult = _hireEligibilityVerifier.IsEligibleForHireOrRehire(ssn, lastName, gender, dob);
                        if (eligibilityResult.IsEligible)
                        {
                            result.Type = JobApp.RESULT_ACCEPT;
                            result.Message = JobApp.RESULT_HIRE_COMPLETE + ConfigurationContext.JobApp.MessageInformMgrToUseUltiPro;

                            Log.Info($"Sending PassThruHire command for ssn: {maskedSsn}");

                            var passThruHireCommand = new PassThruHire()
                            {
                                FirstName = item.FirstName,
                                MiddleName = item.MiddleName,
                                LastName = lastName,
                                Address1 = item.Address1,
                                Address2 = item.Address2,
                                City = item.City,
                                StateCode = item.State,
                                ZipCode = item.Zip,
                                HomePhoneNumber = item.Phone,
                                LocationCode = item.Location,
                                Ssn = ssn,
                                JobCode = item.Position,
                                RaceCode = item.EEORace,
                                GenderCode = gender,
                                BirthDate = item.DateOfBirth,
                                HiredDate = item.StartDate,
                                PayRate = item.Salary,
                                SalaryClass = item.SalaryType,
                                FederalW4ExemptStatus = item.FederalW4ExemptStatus,
                                FederalW4Status = item.FederalW4Status,
                                FederalW4ExemptAmount = item.FederalW4ExemptAmount,
                                FederalW4AdditionalAmount = item.FederalW4AdditionalAmount,
                                StateW4ExemptStatus = item.StateW4ExemptStatus,
                                StateW4Status = item.StateW4Status,
                                StateW4ExemptAmount = item.StateW4ExemptAmount,
                                StateW4AdditionalAmount = item.StateW4AdditionalAmount,
                                DirectDepositAccount = item.DirectDepositAccount,
                                DirectDepositRouting = item.DirectDepositRouting,
                                PayCardNumber = item.PayCardNumber,
                                LocationCodeAlternate = item.TrainingLocation,
                                EmergencyContactFirstName = item.EmergencyContactFirstName,
                                EmergencyContactLastName = item.EmergencyContactLastName,
                                EmergencyContactHomePhone = item.EmergencyContactHomePhone,
                                EmergencyContactCellPhone = item.EmergencyContactCellPhone,
                                FimEmailPreference = Core.Codes.Domain.RestaurantJackFIM,
                                PersonalEmail = item.EmployeeEmailAddress,
                            };

                            try
                            {
                                _endpointInstance.Send(passThruHireCommand)
                                    .GetAwaiter()
                                    .GetResult();
                            }
                            catch (Exception ex)
                            {
                                Log.Error($"Application error: {ex.Message}", ex);
                                result.Type = JobApp.RESULT_REJECT;
                                result.Message = ex.Message;
                            }
                        }
                        else
                        {
                            result.Type = JobApp.RESULT_REJECT;
                            result.Message = eligibilityResult.Message;
                        }

                        response = Request.CreateResponse(HttpStatusCode.Created, result);
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"Application error: {ex.Message}", ex);
                        result.Type = JobApp.RESULT_REJECT;
                        result.Message = ex.Message;
                    }
                }
                else // a required field is missing.
                {
                    response = Request.CreateErrorResponse(HttpStatusCode.BadRequest, ModelState);

                    var missingFields = "";
                    foreach (string modelkey in ModelState.Keys)
                    {
                        missingFields += $" {modelkey}";
                    }
                    Log.Info($"{(int)response.StatusCode} {response.ReasonPhrase}. Missing required field(s): {missingFields}");
                }

                Log.Info($"Final response result: {result}");
            }
            else
            {
                Log.Info($"Model Error: NULL item. Check payload for missing braces, quotes, or content");

                response = Request.CreateErrorResponse(HttpStatusCode.BadRequest, this.ModelState);
            }

            Log.Info("End HireProcessController");

            return response;
        }
    }
}
