﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3D4FE506-2443-47CE-B553-121BACB4FFCB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Jitb.Employment.Lawson.Web.Commons</RootNamespace>
    <AssemblyName>Jitb.Employment.Lawson.Web.Commons</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="JIB.Framework">
      <HintPath>..\JibGac\2.0\JIB.Framework.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Security">
      <HintPath>..\JibGac\2.0\JIB.Framework.Security.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.Employment.Contracts">
      <HintPath>..\..\..\..\..\Repos\NServiceBus Integrations\Jitb.Employment\src\Jitb.Employment.Contracts\bin\Debug\Jitb.Employment.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.NSB.Commons">
      <HintPath>..\..\..\..\..\Repos\NServiceBus Integrations\Jitb.Employment\src\Jitb.Employment.NServiceBus.Common\bin\Debug\Jitb.NSB.Commons.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Core">
      <HintPath>packages\NServiceBus.5.2.16\lib\net45\NServiceBus.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EmployeeInformation.cs" />
    <Compile Include="ISendCommandsToNServiceBusForAGivenAction.cs" />
    <Compile Include="LawsonActions.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="CommandSender.cs" />
    <Compile Include="ServiceBus.cs" />
    <Compile Include="StringUtility.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>