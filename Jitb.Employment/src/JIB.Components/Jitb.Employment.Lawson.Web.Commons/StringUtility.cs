﻿using System;

namespace Jitb.Employment.Lawson.Web.Commons
{
    public class StringUtility
    {
        private const string LawsonDateFormatShort = "yyyyMMdd";
        private const string LawsonDateFormatLong = "yyyyMMddhhmmss";

        public static DateTime ConvertToDate(string dateString)
        {
            return ConvertToDateOrNull(dateString) ?? DateTime.MinValue;
        }

        public static DateTime? ConvertToDateOrNull(string dateString)
        {
            if (string.IsNullOrWhiteSpace(dateString))
                return null;

            if (dateString.Length == LawsonDateFormatShort.Length)
                return DateTime.ParseExact(dateString, LawsonDateFormatShort, System.Globalization.CultureInfo.InvariantCulture);

            if (dateString.Length == LawsonDateFormatLong.Length)
                return DateTime.ParseExact(dateString, LawsonDateFormatLong, System.Globalization.CultureInfo.InvariantCulture);

            return null;
        }

        public static bool ConvertToBool(string boolString)
        {
            return ConvertToBoolOrNull(boolString) ?? false;
        }

        public static bool? ConvertToBoolOrNull(string boolString)
        {
            if (string.IsNullOrWhiteSpace(boolString))
                return null;

            bool result;
            if (bool.TryParse(boolString, out result))
                return result;

            return null;
        }

        public static int ConvertToInt(string intString)
        {
            return ConvertToIntOrNull(intString) ?? 0;
        }

        public static int? ConvertToIntOrNull(string intString)
        {
            if (string.IsNullOrWhiteSpace(intString))
                return null;

            int result;
            if (int.TryParse(intString, out result))
                return result;

            return null;
        }
    }
}
