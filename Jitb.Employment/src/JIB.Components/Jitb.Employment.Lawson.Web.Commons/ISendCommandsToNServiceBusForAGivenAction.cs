﻿using JIB.Lawson.Services.WebServices;
using Jitb.Employment.Contracts.Commands.Employment;

namespace Jitb.Employment.Lawson.Web.Commons
{
    public interface ISendCommandsToNServiceBusForAGivenAction
    {
        void MapAndSend(EmployeeInformation agsEmployee);
    }

    public class NsbPreHireSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {
            ServiceBus.Bus.Send<PreHireEmployee>(x =>
            {
                x.FirstName = agsEmployee.FirstName;
                x.MiddleName = agsEmployee.MiddleName;
                x.LastName = agsEmployee.LastName;
                x.Address1 = agsEmployee.Address1;
                x.Address2 = agsEmployee.Address2;
                x.City = agsEmployee.City;
                x.State = agsEmployee.State;
                x.ZIP = agsEmployee.ZIP;
                x.Phone = agsEmployee.Phone;
                x.LocationCode = agsEmployee.LocationCode;
                byte[] key = { 00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32 };
                byte[] iv = { 55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32 };
                x.FICANumber = JIB.Framework.Security.Encryption.DecryptRijndaelManaged(agsEmployee.FICANumber, key, iv).Trim();
                x.JobCode = agsEmployee.JobCode;
                x.RaceCode = agsEmployee.RaceCode;
                x.GenderCode = agsEmployee.GenderCode;
                x.BirthDate = agsEmployee.BirthDate;
                x.HiredDate = agsEmployee.HiredDate;
                x.PayRate = agsEmployee.PayRate;
                x.SalaryClass = agsEmployee.SalaryClass;
                x.FederalW4ExemptStatus = agsEmployee.FederalW4ExemptStatus;
                x.FederalW4Status = agsEmployee.FederalW4Status;
                x.FederalW4ExemptAmount = agsEmployee.FederalW4ExemptAmount;
                x.FederalW4AdditionalAmount = agsEmployee.FederalW4AdditionalAmount;
                x.StateW4ExemptStatus = agsEmployee.StateW4ExemptStatus;
                x.StateW4Status = agsEmployee.StateW4Status;
                x.StateW4ExemptAmount = agsEmployee.StateW4ExemptAmount;
                x.StateW4AdditionalAmount = agsEmployee.StateW4AdditionalAmount;
                x.DirectDepositAccount = agsEmployee.DirectDepositAccount;
                x.DirectDepositRouting = agsEmployee.DirectDepositRouting;
                x.PayCardNumber = agsEmployee.PayCardNumber;
                x.LocationCodeAlternate = agsEmployee.LocationCodeAlternate;
                x.EmergencyContactFirstName = agsEmployee.EmergencyContactFirstName;
                x.EmergencyContactLastName = agsEmployee.EmergencyContactLastName;
                x.EmergencyContactHomePhone = agsEmployee.EmergencyContactHomePhone;
                x.EmergencyContactCellPhone = agsEmployee.EmergencyContactCellPhone;
            });
        }
    }

    public class NsbHireSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {
            ServiceBus.Bus.Send<CreateNewHire>(x =>
            {
                x.BadgeId = StringUtility.ConvertToInt(agsEmployee.BadgeNumber);
                x.EntityId = agsEmployee.Company;
                x.EmployeeId = StringUtility.ConvertToInt(agsEmployee.EmployeeNumber);
                x.BirthDate = StringUtility.ConvertToDate(agsEmployee.BirthDate);
                x.ContactPhoneNumber = agsEmployee.Phone;
                x.EmploymentStatusCode = agsEmployee.StatusCode;
                x.FirstName = agsEmployee.FirstName;
                x.GenderCode = agsEmployee.GenderCode;
                x.HireDate = StringUtility.ConvertToDate(agsEmployee.HiredDate);
                x.HomeStoreNumber = agsEmployee.LocationCode;
                x.JobCode = agsEmployee.JobCode;
                x.LastName = agsEmployee.LastName;
                x.MiddleName = agsEmployee.MiddleName;
                x.PayRate = agsEmployee.PayRate;
                x.ResidenceCity = agsEmployee.City;
                x.ResidenceState = agsEmployee.State;
                x.Address1 = agsEmployee.Address1;
                x.Address2 = agsEmployee.Address2;
                x.ResidenceZip = agsEmployee.ZIP;
                byte[] key = { 00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32 };
                byte[] iv = { 55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32 };
                x.Ssn = JIB.Framework.Security.Encryption.DecryptRijndaelManaged(agsEmployee.FICANumber, key, iv).Trim();
                x.SupervisorBadgeId = agsEmployee.ManagerBadgeNumber;
                // No need to send anything back to Lawson.
                x.SendReplyWhenComplete = false;
            });
        }
    }

    public class NsbBorrowSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {
            ServiceBus.Bus.Send<BorrowEmployee>(x =>
            {
                x.BadgeId = StringUtility.ConvertToInt(agsEmployee.BadgeNumber);
                x.EntityId = agsEmployee.Company;
                x.EmployeeId = agsEmployee.EmployeeNumber;
                x.BorrowBeginDate = StringUtility.ConvertToDate(agsEmployee.BorrowStartDate);
                x.BorrowEndDate = StringUtility.ConvertToDate(agsEmployee.BorrowEndDate);
                x.BorrowToStoreNumber = StringUtility.ConvertToInt(agsEmployee.LocationCodeAlternate);
            });
        }
    }

    public class NsbBeginLeaveOfAbsenceSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {
            ServiceBus.Bus.Send<StartLeaveOfAbsence>(x =>
            {
                x.BadgeId = StringUtility.ConvertToInt(agsEmployee.BadgeNumber);
                x.EntityId = agsEmployee.Company;
                x.EmployeeId = agsEmployee.EmployeeNumber;
                x.LeaveOfAbsenceBeginDate = StringUtility.ConvertToDate(agsEmployee.LOABeginDate);
                x.LeaveOfAbsenceComment = string.Format("{0} {1} {2} {3}",
                    agsEmployee.Comment1, agsEmployee.Comment2, agsEmployee.Comment3, agsEmployee.Comment4);
                x.LeaveOfAbsenceEndDate = StringUtility.ConvertToDate(agsEmployee.LOAEndDate);
                x.LeaveOfAbsenceInjuryDate = StringUtility.ConvertToDateOrNull(agsEmployee.LOAInjuryDate);
                x.LeaveOfAbsenceLastWorkedDate = StringUtility.ConvertToDate(agsEmployee.LOAInjuryLastWorkedDate);
                x.LeaveOfAbsenceReturnedToWorkDate = StringUtility.ConvertToDateOrNull(agsEmployee.Company);
                x.LeaveOfAbsenceType = agsEmployee.LOAType;
            });
        }
    }

    public class NsbEndLeaveOfAbsenceSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {
            ServiceBus.Bus.Send<EndLeaveOfAbsence>(x =>
            {
                x.BadgeId = StringUtility.ConvertToInt(agsEmployee.BadgeNumber);
                x.EntityId = agsEmployee.Company;
                x.EmployeeId = agsEmployee.EmployeeNumber;
                x.LeaveOfAbsenceComment = string.Format("{0} {1} {2} {3}",
                    agsEmployee.Comment1, agsEmployee.Comment2, agsEmployee.Comment3, agsEmployee.Comment4);
                x.LeaveOfAbsenceEndDate = StringUtility.ConvertToDate(agsEmployee.LOAEndDate);
            });
        }
    }

    public class NsbTerminationSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {
            ServiceBus.Bus.Send<TerminateEmployee>(x =>
            {
                x.BadgeId = StringUtility.ConvertToInt(agsEmployee.BadgeNumber);
                x.EntityId = agsEmployee.Company;
                x.EmployeeId = agsEmployee.EmployeeNumber;
                x.TerminationAction = agsEmployee.TerminationAction;
                x.TerminationComment = string.Format("{0} {1} {2} {3}",
                    agsEmployee.Comment1, agsEmployee.Comment2, agsEmployee.Comment3, agsEmployee.Comment4);
                x.TerminationDate = StringUtility.ConvertToDate(agsEmployee.TerminationDate);
                x.TerminationEligibleForRehire = StringUtility.ConvertToBoolOrNull(agsEmployee.EligibleForRehireFlag);
                x.TerminationLastWorkedDate = StringUtility.ConvertToDate(agsEmployee.LastDateWorked);
                x.TerminationReason = agsEmployee.TerminationReason;
            });
        }
    }

    public class NsbTransferSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {
            ServiceBus.Bus.Send<TransferEmployee>(x =>
            {
                x.BadgeId = StringUtility.ConvertToInt(agsEmployee.BadgeNumber);
                x.EntityId = agsEmployee.Company;
                x.EmployeeId = agsEmployee.EmployeeNumber;
                x.TransferFromStoreNumber = agsEmployee.TransferFromStoreNumber;
                x.TransferInDate = StringUtility.ConvertToDate(agsEmployee.TransferInDate);
                x.TransferToStoreNumber = StringUtility.ConvertToInt(agsEmployee.TransferToStoreNumber);
            });
        }
    }

    public class NsbChangeAddressSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {

        }
    }

    public class NsbChangeJobSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {

        }
    }

    public class NsbChangeNameSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {

        }
    }

    public class NsbChangePayRateSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {

        }
    }

    public class NsbChangePhoneNumberSender : ISendCommandsToNServiceBusForAGivenAction
    {
        public void MapAndSend(EmployeeInformation agsEmployee)
        {

        }
    }
}