﻿namespace JIB.Lawson.Services.WebServices
{
    public class LawsonActions
    {
        public const string Ping = "PING";
        public const string PreHire = "PREHIRE";
        public const string Hire = "HIRE";
        public const string HirePart2 = "HIRE2";
        public const string Loan = "LOAN";
        public const string Borrow = "BORROW";
        public const string Terminate = "TERM";
        public const string TerminateCancel = "TERMCANCEL";
        public const string TransferIn = "TRANSFERIN";
        public const string TransferOut = "TRANSFEROUT";
        public const string LeaveOfAbsence = "LOA";
        public const string LeaveOfAbsenceEnd = "LOAEND";
        public const string PasswordChange = "PASSWORDCHANGE";
        public const string Snapshot = "SNAPSHOT";
        public const string Update = "UPDATE";
        public const string Query = "EMPQUERY";
        public const string RestQuery = "RESTQUERY";
    }

    public class LawsonActionUpdateFlags
    {
        public bool AddressChange { get; set; }
        public bool JobChange { get; set; }
        public bool NameChange { get; set; }
        public bool PayRateChange { get; set; }
        public bool PhoneNumberChange { get; set; }
    }
}
