﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NServiceBus;

namespace Jitb.Employment.Lawson.Web.Commons
{
    public static class ServiceBus
    {
        public static ISendOnlyBus Bus { get; private set; }
        private static readonly object padlock = new object();

        public static void Init()
        {
            if (Bus != null) return;
            lock (padlock)
            {
                if (Bus != null) return;

                Bus = new NsbConfiguration().Config();
            }
        }
    }

    public class NsbConfiguration
    {
        public ISendOnlyBus Config()
        {
            var configuration = new BusConfiguration();
            configuration.DefaultJitbConfiguration();
            return Bus.CreateSendOnly(configuration);
        }
    }
}