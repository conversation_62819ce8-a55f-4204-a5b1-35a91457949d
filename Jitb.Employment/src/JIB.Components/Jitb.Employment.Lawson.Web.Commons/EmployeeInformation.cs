﻿namespace JIB.Lawson.Services.WebServices
{
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://JackInTheBox.com/webservices/", IsNullable = false)]
    public struct EmployeeInformation
    {
        // MSMQ data...
        public string SourceApplication;

        // Flag data...
        // 1=Send, 0=Don't Send...
        public int SnapshotSendEmail;

        // Hire Manager Flag...
        public int HireManagerFlag;

        // General data...
        public string RequestID;
        public string SnapShotRequestID;
        public byte[] FICANumber;
        public string LocationCode;
        public string LocationState;
        public string LocationCodeAlternate;
        public string HomeLocationCode;
        public string JobCode;
        public string JobCodePrevious;
        public string LastName;
        public string FirstName;
        public string MiddleName;
        public string HiredDate;
        public string HiredDateOriginal;
        public string BirthDate;
        public string StatusCode;
        public string RaceCode;
        public string GenderCode;
        public decimal PayRate;
        public decimal PayRatePrevious;
        public string EmployeeNumber;
        public string Company;
        public string PayCardNumber;
        public string PayCardNumberPrevious;
        public string PayCardNumEncrypted;
        public string PayCardNumPreviousEncrypted;
        public string JobEffectiveDate;
        public string PayEffectiveDate;
        public string Address1;
        public string Address2;
        public string City;
        public string State;
        public string ZIP;
        public string Phone;
        public string WorkExperienceDate;
        public string TeamLeaderMeritRating;
        public string SuperKey;
        public string BorrowedIndicator;
        public string ClockType;

        // Electronic Signature Authentication Data...
        public string BadgeNumber;
        public string Password;
        public string ManagerBadgeNumber;
        public string ManagerPassword;

        // Training Data
        public int EligibleForTrainingCode;
        public int SurveyFlag;

        // I9 Data
        public string I9ExpirationDate;
        public string Citizen;
        public string NonCitizen;
        public string Maiden;
        public string Permanent;
        public string Admission;
        public string I9ADescription;
        public string Doc1Number;
        public string ExpirationDate1;
        public string Alien;
        public string AlienNumber;
        public string AlienExpirationDate;
        public string IssueWho1;
        public string IssueWho2;
        public string Doc2Number;
        public string ExpirationDate2;
        public string I9BDescription;
        public string I9CDescription;
        public string I9FormVersion;
        public string I9FormDate;
        public string I9FormExpirationDate;
        public string ManagerI9SignoffDateTime;
        public string EmployeeI9SignoffDateTime;

        // W4 Data
        public string W4FormVersion;
        public string W4FormDate;
        public string ManagerW4SignoffDateTime;
        public string EmployeeW4SignoffDateTime;

        // General Manager Data
        public string EmployerID;
        public string GMHomeLocation;
        public string GMLocation01;
        public string GMLocation02;
        public string GMLocation03;
        public string GMLocation04;
        public string GMLocation05;
        public string GMLocation06;
        public string GMLocation07;
        public string GMLocation08;
        public string GMLocation09;
        public string GMLocation10;

        // Review Data
        public string Points;
        public string Score;
        public string ReviewDate;
        public string NoReview;

        public string Comment1;
        public string Comment2;
        public string Comment3;
        public string Comment4;


        // Termination data...
        public string TerminationDate;
        public string TerminationDatePrevious;
        public string LastDateWorked;
        public string TerminationAction;
        public string TerminationReason;
        public string EligibleForRehireFlag;

        // Borrow data...
        public string BorrowFromStoreNumber;
        public string BorrowStartDate;
        public string BorrowEndDate;
        public string BorrowForNewTraining;

        // Transfer In data...
        public string TransferInDate;
        public string TransferFromStoreNumber;

        // Transfer Out data...
        public string TransferToDate;
        public string TransferToStoreNumber;

        // Leave of Absence data...
        public string LOABeginDate;
        public string LOAEndDate;
        public string LOAType;
        public string LOAFlag;
        public string LOAWorkRelatedInjuryFlag;
        public string LOAInjuryDate;
        public string LOAInjuryLastWorkedDate;
        public string LOAInjuryFirstLightDutyDate;
        public string LOAInjuryFirstFullDutyDate;
        public string LOAReturnedFromDate;

        // Application return data...
        public int ApplicationReturnValue;
        public string ApplicationReturnMessage1;
        public string ApplicationReturnMessage2;
        public string ApplicationReturnMessage3;
        public string ApplicationReturnMessage4;
        public string ApplicationReturnMessage5;

        // Version stamping...
        public string PersonnelVersion;

        // Biometric Data for Timeclock...
        public byte[] Biometric01;
        public byte[] Biometric02;

        // Workbrain Clock return data...
        public string ClockEmployeeID;					// Workbrain Employee.EMP_ID
        public string ClockEmployeeNumber;				// Workbrain Employee.EMP_NAME
        public string ClockStatus;						// Workbrain Employee.EMP_STATUS
        public string ClockEnfSch;						// Workbrain Employee.Val4
        public string ClockClassification;				// Workbrain Employee.Val2
        public string ClockReaderGroupName;				// Workbrain Employee_Reader_Group.RDRGRP_ID
        public string ClockMemberType;					// "emp"

        // JobApp Fields
        public string DirectDepositAccount;
        public string DirectDepositRouting;
        public string FederalW4ExemptStatus;
        public string FederalW4Status;
        public string FederalW4ExemptAmount;
        public string FederalW4AdditionalAmount;
        public string StateW4ExemptStatus;
        public string StateW4Status;
        public string StateW4ExemptAmount;
        public string StateW4AdditionalAmount;
        public decimal StateW4PercentArizona;
        public string EmergencyContactFirstName;
        public string EmergencyContactLastName;
        public string EmergencyContactHomePhone;
        public string EmergencyContactCellPhone;
        public string SalaryClass;
    }
}
