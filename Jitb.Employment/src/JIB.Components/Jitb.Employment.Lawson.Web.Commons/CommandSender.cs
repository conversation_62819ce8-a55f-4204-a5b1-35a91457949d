﻿using JIB.Lawson.Services.WebServices;

namespace Jitb.Employment.Lawson.Web.Commons
{
    public class CommandSender
    {
        public const string LogLineFormat_SendAttempt = "NServiceBus attempting Bus.Send: {0}";
        public const string LogLineFormat_SendComplete = "NServiceBus Bus.Send is complete";
        public const string LogLineFormat_SendException = "NServiceBus exception: {0}";

        public static void DetermineNsbSender(string action, EmployeeInformation employeeInfo)
        {
            DetermineNsbSender(action, null, employeeInfo);
        }

        public static void DetermineNsbSender(string action, LawsonActionUpdateFlags lawsonActionUpdateFlags, EmployeeInformation employeeInfo)
        {
            switch (action.ToUpper())
            {
                case LawsonActions.PreHire:
                    new NsbPreHireSender().MapAndSend(employeeInfo);
                    break;
                case LawsonActions.Hire:
                    new NsbHireSender().MapAndSend(employeeInfo);
                    break;
                case LawsonActions.Borrow:
                    new NsbBorrowSender().MapAndSend(employeeInfo);
                    break;
                case LawsonActions.LeaveOfAbsence:
                    new NsbBeginLeaveOfAbsenceSender().MapAndSend(employeeInfo);
                    break;
                case LawsonActions.LeaveOfAbsenceEnd:
                    new NsbEndLeaveOfAbsenceSender().MapAndSend(employeeInfo);
                    break;
                case LawsonActions.Terminate:
                    new NsbTerminationSender().MapAndSend(employeeInfo);
                    break;
                case LawsonActions.TransferIn:
                    new NsbTransferSender().MapAndSend(employeeInfo);
                    break;
                case LawsonActions.Update:
                    if (lawsonActionUpdateFlags.AddressChange)
                        new NsbChangeAddressSender().MapAndSend(employeeInfo);
                    if (lawsonActionUpdateFlags.JobChange)
                        new NsbChangeJobSender().MapAndSend(employeeInfo);
                    if (lawsonActionUpdateFlags.NameChange)
                        new NsbChangeNameSender().MapAndSend(employeeInfo);
                    if (lawsonActionUpdateFlags.PayRateChange)
                        new NsbChangePayRateSender().MapAndSend(employeeInfo);
                    if (lawsonActionUpdateFlags.PhoneNumberChange)
                        new NsbChangePhoneNumberSender().MapAndSend(employeeInfo);
                    break;
            }
        }
    }
}
