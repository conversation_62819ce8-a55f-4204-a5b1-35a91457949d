﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <ProductVersion>8.0.30319</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{23E2ECF5-AFF1-4782-8C35-A5CC3D81A9C5}</ProjectGuid>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon />
    <AssemblyKeyContainerName />
    <AssemblyName>JIB.Lawson.Services.WebServices</AssemblyName>
    <AssemblyOriginatorKeyFile />
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>Library</OutputType>
    <RootNamespace>JIB.Lawson.Services.WebServices</RootNamespace>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
    <StartupObject />
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <UseIISExpress>true</UseIISExpress>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <OldToolsVersion>4.0</OldToolsVersion>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile />
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile />
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn />
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile />
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile />
    <DebugSymbols>false</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn />
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>none</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="JIB.Framework">
      <Name>JIB.Framework</Name>
      <HintPath>..\JibGac\2.0\JIB.Framework.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Caching">
      <HintPath>..\JibGac\2.0\JIB.Framework.Caching.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Persistence">
      <Name>JIB.Framework.Persistence</Name>
      <HintPath>..\JibGac\2.0\JIB.Framework.Persistence.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Security">
      <Name>JIB.Framework.Security</Name>
      <HintPath>..\JibGac\2.0\JIB.Framework.Security.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Services">
      <Name>JIB.Framework.Services</Name>
      <HintPath>..\JibGac\2.0\JIB.Framework.Services.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Lawson.Data, Version=4.0.5073.15054, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>App_Code\JIB.Lawson.Data.dll</HintPath>
    </Reference>
    <Reference Include="JIB.WebServiceAudit.Data, Version=2.0.5073.15054, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>App_Code\JIB.WebServiceAudit.Data.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Workbrain.Data, Version=1.0.5073.15053, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>App_Code\JIB.Workbrain.Data.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Workbrain.Services, Version=2.0.5073.15054, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>App_Code\JIB.Workbrain.Services.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.Employment.Contracts, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\Repos\NServiceBus Integrations\Jitb.Employment\src\Jitb.Employment.Contracts\bin\Debug\Jitb.Employment.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.Employment.Lawson.Web.Commons">
      <HintPath>..\..\..\..\..\Repos\Jitb.Employment.Lawson.Web.Commons\bin\Debug\Jitb.Employment.Lawson.Web.Commons.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.NSB.Commons">
      <HintPath>..\..\..\..\..\Repos\NServiceBus Integrations\Jitb.Employment\src\Jitb.Employment.NServiceBus.Common\bin\Debug\Jitb.NSB.Commons.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Core, Version=6.0.0.0, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TestJobAppWebSvc\packages\NServiceBus.6.0.0\lib\net452\NServiceBus.Core.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap">
      <HintPath>..\..\..\..\..\Repos\NServiceBus Integrations\Jitb.Employment\src\Jitb.Employment.NServiceBus.Common\bin\Debug\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.configuration" />
    <Reference Include="System.Data">
      <Name>System.Data</Name>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Messaging" />
    <Reference Include="System.Web">
      <Name>System.Web</Name>
    </Reference>
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services">
      <Name>System.Web.Services</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Employee.asmx.cs">
      <DependentUpon>Employee.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Encryption.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Enumerations.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Content Include="App_Code\JIB.Lawson.Data.dll" />
    <Content Include="App_Code\JIB.WebServiceAudit.Data.dll" />
    <Content Include="App_Code\JIB.Workbrain.Data.dll" />
    <Content Include="App_Code\JIB.Workbrain.Services.dll" />
    <Content Include="Employee.asmx">
      <SubType>Form</SubType>
    </Content>
    <Content Include="Global.asax">
      <SubType>Component</SubType>
    </Content>
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <EmbeddedResource Include="Employee.asmx.resx">
      <DependentUpon>Employee.asmx.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Global.asax.resx">
      <DependentUpon>Global.asax.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent />
    <PostBuildEvent />
  </PropertyGroup>
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>40262</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:40263/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>