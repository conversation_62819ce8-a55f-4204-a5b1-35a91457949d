Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.21005.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JIB.Lawson.Services.WebServices", "JIB.Lawson.Services.WebServices.csproj", "{23E2ECF5-AFF1-4782-8C35-A5CC3D81A9C5}"
EndProject
Global
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 2
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://tfserver:8080/tfs/backofficesystems
		SccLocalPath0 = .
		SccProjectUniqueName1 = JIB.Lawson.Services.WebServices.csproj
		SccLocalPath1 = .
	EndGlobalSection
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{23E2ECF5-AFF1-4782-8C35-A5CC3D81A9C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23E2ECF5-AFF1-4782-8C35-A5CC3D81A9C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23E2ECF5-AFF1-4782-8C35-A5CC3D81A9C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23E2ECF5-AFF1-4782-8C35-A5CC3D81A9C5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
