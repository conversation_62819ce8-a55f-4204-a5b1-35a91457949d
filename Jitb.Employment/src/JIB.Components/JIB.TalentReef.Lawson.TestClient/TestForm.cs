﻿using JIB.JobApp.Services.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace JIB.TalentReef.Lawson.TestClient
{
    public partial class TestForm : Form
    {
        bool useLocal = false;

        public TestForm()
        {
            InitializeComponent();
        }

        async Task InvokeRestApi<TRequestModel, TResponseModel>(string requestUri, TRequestModel item)
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri("http://csccapatost0115/TEST/");
            if (!useLocal)
                client.BaseAddress = new Uri("https://jobapp.jackinthebox.com/TEST/");
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            try
            {
                var response = await client.PostAsJsonAsync(requestUri, item);
                response.EnsureSuccessStatusCode();
                var result = await response.Content.ReadAsAsync<TResponseModel>();

                MessageBox.Show(string.Format("{0}{1}\n\n\n{2}", client.BaseAddress, requestUri, result));
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void buttonTestRehireEligible_Click(object sender, EventArgs e)
        {
            var requestUri = "api/RehireEligible";

            // Create a new HireProcess item (i.e. new Employee)
            var item = new RehireEligibile
            {
                Location = "003702", //EmployeeInfo.LocationCode
                SourceSSN = "***********", //EmployeeInfo.FICANumber
            };

            InvokeRestApi<RehireEligibile, RehireEligibleResult>(requestUri, item);
        }

        private void buttonHireProcess_Click(object sender, EventArgs e)
        {
            var requestUri = "api/HireProcess";

            // Create a new HireProcess item (i.e. new Employee)
            var item = new HireProcess
            {
                FirstName = "Dead", //EmployeeInfo.FirstName
                MiddleName = "N/A", //EmployeeInfo.MiddleName
                LastName = "Pool", //EmployeeInfo.LastName
                Address1 = "924 gross rd", //EmployeeInfo.Address1
                Address2 = "927", //EmployeeInfo.Address2
                City = "Mesquite", //EmployeeInfo.City
                State = "TX", //EmployeeInfo.State
                Zip = "75149", //EmployeeInfo.ZIP
                Phone = "4697205512", //EmployeeInfo.Phone
                Location = "002001", //EmployeeInfo.LocationCode
                SourceSSN = "***********", //EmployeeInfo.FICANumber
                Position = "QDRH65", //EmployeeInfo.JobCode
                EEORace = "Two or more Races", //EmployeeInfo.RaceCode
                EEOGender = "Male", //EmployeeInfo.GenderCode
                DateOfBirth = "1994-11-21 00:00:00.0", //EmployeeInfo.BirthDate
                StartDate = "2016-10-18 00:00:00.0", //EmployeeInfo.HiredDate
                Salary = 8.50M, //EmployeeInfo.PayRate
                SalaryType = "H", //EmployeeInfo.SalaryClass
                FederalW4Status = "S", //EmployeeInfo.FederalW4Status
                FederalW4ExemptAmount = "3", //EmployeeInfo.FederalW4ExemptAmount
                FederalW4AdditionalAmount = "0", //EmployeeInfo.FederalW4AdditionalAmount
                FederalW4ExemptStatus = "0", //EmployeeInfo.FederalW4ExemptStatus
                StateW4ExemptStatus = "N/A", //EmployeeInfo.StateW4ExemptStatus
                StateW4ExemptAmount = "N/A", //EmployeeInfo.StateW4ExemptAmount
                StateW4AdditionalAmount = "N/A", //EmployeeInfo.StateW4AdditionalAmount
                StateW4Status = "N/A", //EmployeeInfo.StateW4Status
                DirectDepositRouting = "N/A", //EmployeeInfo.DirectDepositRouting
                DirectDepositAccount = "N/A", //EmployeeInfo.DirectDepositAccount
                PayCardNumber = "****************", //EmployeeInfo.PayCardNumber
                TrainingLocation = "N/A", //EmployeeInfo.LocationCodeAlternate
                EmergencyContactFirstName = "Tia", //EmployeeInfo.EmergencyContactFirstName
                EmergencyContactLastName = "freemon", //EmployeeInfo.EmergencyContactLastName
                EmergencyContactHomePhone = "************", //EmployeeInfo.EmergencyContactHomePhone
                EmergencyContactCellPhone = "N/A", //EmployeeInfo.EmergencyContactCellPhone
            };

            InvokeRestApi<HireProcess, HireProcessResult>(requestUri, item);
        }
    }
}
