﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace JIB.TalentReef.Lawson.TestClient
{
    class RestService
    {
        static bool useLocal = false;

        public static async Task InvokeRestApi<TRequestModel, TResponseModel>(string requestUri, TRequestModel item)
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri("http://localhost:54676/TEST/");
            if (!useLocal)
                client.BaseAddress = new Uri("https://jobapp.jackinthebox.com/TEST/");
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            try
            {
                var response = await client.PostAsJsonAsync(requestUri, item);
                response.EnsureSuccessStatusCode();
                var result = await response.Content.ReadAsAsync<TResponseModel>();

                //MessageBox.Show(string.Format("{0}{1}\n\n\n{2}", client.BaseAddress, requestUri, result));
            }
            catch (Exception ex)
            {
                //MessageBox.Show(ex.Message);
            }
        }

    }
}
