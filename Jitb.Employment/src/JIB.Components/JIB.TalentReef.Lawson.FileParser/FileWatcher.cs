﻿using JIB.JobApp.Services.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JIB.TalentReef.Lawson.FileParser
{
    class FileWatcher
    {
        FileSystemWatcher watcher;

        public void Start()
        {
            var folderPath = @"C:\JIB Data\TalentReef";
            var filter = "*.csv";
            watcher = new FileSystemWatcher(folderPath, filter);
            watcher.Created += watcher_Created;
            watcher.EnableRaisingEvents = true;
        }

        public void watcher_Created(object sender, FileSystemEventArgs e)
        {
            string mgs = string.Format("File {0} | {1}", e.FullPath, e.ChangeType);
            var list = QdobaReportSummary.ParseFile(e.FullPath);
            //foreach (var item in list)
            //{
                var requestUri = "api/RehireEligible";
                //RestService.InvokeRestApi<HireProcess, HireProcessMap>(requestUri, item);
                requestUri = "api/HireProcess";
                RestService.InvokeRestApi<HireProcess, HireProcessResult>(requestUri, list[0]);
            //}
        }
    }
}
