﻿using JIB.JobApp.Services.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace JIB.TalentReef.Lawson.FileParser
{
    public class HireProcessMap
    {
        public HireProcessMap(string[] fields, bool setDefaultStrings)
        {
            _hireProcess = new HireProcess()
            {
                LastName = fields[0],
                FirstName = fields[1],
                MiddleName = fields[2],
                Position = fields[3],
                Location = fields[4],
                SourceSSN = fields[5],
                EEOGender = fields[6],
                DateOfBirth = fields[7],
                StartDate = fields[8],
                Salary = Convert.ToDecimal(fields[9]),
                SalaryType = fields[10],
                Phone = fields[11],
                Address1 = fields[12],
                Address2 = fields[13],
                City = fields[14],
                State = fields[15],
                Zip = fields[16],
                EEORace = fields[17],
                PayCardNumber = fields[18],
                DirectDepositRouting = fields[19],
                DirectDepositAccount = fields[20],
                FederalW4Status = fields[21],
                FederalW4ExemptAmount = fields[22],
                FederalW4AdditionalAmount = fields[23],
                FederalW4ExemptStatus = fields[24],
                StateW4Status = fields[25],
                StateW4ExemptAmount = fields[26],
                StateW4AdditionalAmount = fields[27],
                StateW4ExemptStatus = fields[28],
            };

            if (setDefaultStrings)
                this.SetDefaultStrings();
        }

        HireProcess _hireProcess;
        public HireProcess HireProcess
        {
            get { return _hireProcess; }
        }

        public void SetDefaultStrings()
        {
            var props = _hireProcess.GetType().GetProperties();
            foreach (var prop in props)
            {
                if (prop.PropertyType == typeof(string))
                {
                    var val = prop.GetValue(_hireProcess);
                    if (val == null || string.IsNullOrWhiteSpace((string)val))
                        prop.SetValue(_hireProcess, "N/A");
                }
            }
        }
    }
}
