﻿using JIB.JobApp.Services.Models;
using Microsoft.VisualBasic.FileIO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JIB.TalentReef.Lawson.FileParser
{
    public class QdobaReportSummary
    {
        public static List<HireProcess> ParseFile(string fullPath)
        {
            var _hireProcessList = new List<HireProcess>();

            using (var parser = new TextFieldParser(fullPath))
            {
                parser.TextFieldType = FieldType.Delimited;
                parser.SetDelimiters(",");
                bool firstLine = true;
                while (!parser.EndOfData)
                {
                    // Process each row.
                    try
                    {
                        var fields = parser.ReadFields();

                        // Skip header row.
                        if (firstLine)
                        {
                            firstLine = false;
                            continue;
                        }

                        var hireProcessMap = new HireProcessMap(fields, true);
                        _hireProcessList.Add(hireProcessMap.HireProcess);
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }

            return _hireProcessList;
        }
    }
}
