﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.31101.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JIB.Personnel.Store.Data", "..\..\..\Personnel\DEV\JIB.Personnel.Store.Data\JIB.Personnel.Store.Data.csproj", "{8B40CBB2-7328-4F78-BA22-A46F5FE11C69}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JIB.Personnel.Lawson.TestClient", "..\JIB.Personnel.Lawson.TestHarness\JIB.Personnel.Lawson.TestClient.csproj", "{269DE2A2-E433-4174-91C5-95A3D7D9B06B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JIB.Lawson.Services.WebServices", "..\JIB.Lawson.Services.WebServices\JIB.Lawson.Services.WebServices.csproj", "{7DE1FCD8-CE00-461C-A24D-619DC5319FD2}"
EndProject
Global
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 4
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://tfserver:8080/tfs/backofficesystems
		SccLocalPath0 = .
		SccProjectUniqueName1 = ..\\..\\..\\Personnel\\DEV\\JIB.Personnel.Store.Data\\JIB.Personnel.Store.Data.csproj
		SccProjectName1 = ../../../Personnel/DEV/JIB.Personnel.Store.Data
		SccLocalPath1 = ..\\..\\..\\Personnel\\DEV\\JIB.Personnel.Store.Data
		SccProjectUniqueName2 = ..\\JIB.Lawson.Services.WebServices\\JIB.Lawson.Services.WebServices.csproj
		SccProjectName2 = ../JIB.Lawson.Services.WebServices
		SccLocalPath2 = ..\\JIB.Lawson.Services.WebServices
		SccProjectUniqueName3 = ..\\JIB.Personnel.Lawson.TestHarness\\JIB.Personnel.Lawson.TestClient.csproj
		SccProjectName3 = ../JIB.Personnel.Lawson.TestHarness
		SccLocalPath3 = ..\\JIB.Personnel.Lawson.TestHarness
	EndGlobalSection
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8B40CBB2-7328-4F78-BA22-A46F5FE11C69}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B40CBB2-7328-4F78-BA22-A46F5FE11C69}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B40CBB2-7328-4F78-BA22-A46F5FE11C69}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B40CBB2-7328-4F78-BA22-A46F5FE11C69}.Release|Any CPU.Build.0 = Release|Any CPU
		{269DE2A2-E433-4174-91C5-95A3D7D9B06B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{269DE2A2-E433-4174-91C5-95A3D7D9B06B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{269DE2A2-E433-4174-91C5-95A3D7D9B06B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{269DE2A2-E433-4174-91C5-95A3D7D9B06B}.Release|Any CPU.Build.0 = Release|Any CPU
		{7DE1FCD8-CE00-461C-A24D-619DC5319FD2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DE1FCD8-CE00-461C-A24D-619DC5319FD2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DE1FCD8-CE00-461C-A24D-619DC5319FD2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DE1FCD8-CE00-461C-A24D-619DC5319FD2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
