﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace JIB.Personnel.Lawson.TestClient.Lawson.Employee {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="EmployeeSoap", Namespace="http://JackInTheBox.com/webservices/")]
    public partial class Employee : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private HeaderStore headerStoreValueField;
        
        private System.Threading.SendOrPostCallback PingOperationCompleted;
        
        private System.Threading.SendOrPostCallback HireOrRehireOperationCompleted;
        
        private System.Threading.SendOrPostCallback PasswordChangeOperationCompleted;
        
        private System.Threading.SendOrPostCallback HirePart2OperationCompleted;
        
        private System.Threading.SendOrPostCallback LoanOperationCompleted;
        
        private System.Threading.SendOrPostCallback BorrowOperationCompleted;
        
        private System.Threading.SendOrPostCallback TerminateOperationCompleted;
        
        private System.Threading.SendOrPostCallback TerminateCancelOperationCompleted;
        
        private System.Threading.SendOrPostCallback TransferInOperationCompleted;
        
        private System.Threading.SendOrPostCallback TransferOutOperationCompleted;
        
        private System.Threading.SendOrPostCallback LeaveOfAbsenceOperationCompleted;
        
        private System.Threading.SendOrPostCallback LeaveOfAbsenceEndOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpdateOperationCompleted;
        
        private System.Threading.SendOrPostCallback QueryOperationCompleted;
        
        private System.Threading.SendOrPostCallback QueryStoreOperationCompleted;
        
        private System.Threading.SendOrPostCallback SnapshotOperationCompleted;
        
        private System.Threading.SendOrPostCallback AuthenticateOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public Employee() {
            this.Url = global::JIB.Personnel.Lawson.TestClient.Properties.Settings.Default.JIB_Personnel_Lawson_TestClient_Lawson_Employee;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public HeaderStore HeaderStoreValue {
            get {
                return this.headerStoreValueField;
            }
            set {
                this.headerStoreValueField = value;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event PingCompletedEventHandler PingCompleted;
        
        /// <remarks/>
        public event HireOrRehireCompletedEventHandler HireOrRehireCompleted;
        
        /// <remarks/>
        public event PasswordChangeCompletedEventHandler PasswordChangeCompleted;
        
        /// <remarks/>
        public event HirePart2CompletedEventHandler HirePart2Completed;
        
        /// <remarks/>
        public event LoanCompletedEventHandler LoanCompleted;
        
        /// <remarks/>
        public event BorrowCompletedEventHandler BorrowCompleted;
        
        /// <remarks/>
        public event TerminateCompletedEventHandler TerminateCompleted;
        
        /// <remarks/>
        public event TerminateCancelCompletedEventHandler TerminateCancelCompleted;
        
        /// <remarks/>
        public event TransferInCompletedEventHandler TransferInCompleted;
        
        /// <remarks/>
        public event TransferOutCompletedEventHandler TransferOutCompleted;
        
        /// <remarks/>
        public event LeaveOfAbsenceCompletedEventHandler LeaveOfAbsenceCompleted;
        
        /// <remarks/>
        public event LeaveOfAbsenceEndCompletedEventHandler LeaveOfAbsenceEndCompleted;
        
        /// <remarks/>
        public event UpdateCompletedEventHandler UpdateCompleted;
        
        /// <remarks/>
        public event QueryCompletedEventHandler QueryCompleted;
        
        /// <remarks/>
        public event QueryStoreCompletedEventHandler QueryStoreCompleted;
        
        /// <remarks/>
        public event SnapshotCompletedEventHandler SnapshotCompleted;
        
        /// <remarks/>
        public event AuthenticateCompletedEventHandler AuthenticateCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/Ping", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void Ping(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("Ping", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void PingAsync(EmployeeInformation employeeInfo) {
            this.PingAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void PingAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.PingOperationCompleted == null)) {
                this.PingOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPingOperationCompleted);
            }
            this.InvokeAsync("Ping", new object[] {
                        employeeInfo}, this.PingOperationCompleted, userState);
        }
        
        private void OnPingOperationCompleted(object arg) {
            if ((this.PingCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PingCompleted(this, new PingCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/HireOrRehire", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void HireOrRehire(ref EmployeeInformation employeeInfo, bool addEmployeeToWorkbrain) {
            object[] results = this.Invoke("HireOrRehire", new object[] {
                        employeeInfo,
                        addEmployeeToWorkbrain});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void HireOrRehireAsync(EmployeeInformation employeeInfo, bool addEmployeeToWorkbrain) {
            this.HireOrRehireAsync(employeeInfo, addEmployeeToWorkbrain, null);
        }
        
        /// <remarks/>
        public void HireOrRehireAsync(EmployeeInformation employeeInfo, bool addEmployeeToWorkbrain, object userState) {
            if ((this.HireOrRehireOperationCompleted == null)) {
                this.HireOrRehireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHireOrRehireOperationCompleted);
            }
            this.InvokeAsync("HireOrRehire", new object[] {
                        employeeInfo,
                        addEmployeeToWorkbrain}, this.HireOrRehireOperationCompleted, userState);
        }
        
        private void OnHireOrRehireOperationCompleted(object arg) {
            if ((this.HireOrRehireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HireOrRehireCompleted(this, new HireOrRehireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/PasswordChange", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void PasswordChange(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("PasswordChange", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void PasswordChangeAsync(EmployeeInformation employeeInfo) {
            this.PasswordChangeAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void PasswordChangeAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.PasswordChangeOperationCompleted == null)) {
                this.PasswordChangeOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPasswordChangeOperationCompleted);
            }
            this.InvokeAsync("PasswordChange", new object[] {
                        employeeInfo}, this.PasswordChangeOperationCompleted, userState);
        }
        
        private void OnPasswordChangeOperationCompleted(object arg) {
            if ((this.PasswordChangeCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PasswordChangeCompleted(this, new PasswordChangeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/HirePart2", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void HirePart2(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("HirePart2", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void HirePart2Async(EmployeeInformation employeeInfo) {
            this.HirePart2Async(employeeInfo, null);
        }
        
        /// <remarks/>
        public void HirePart2Async(EmployeeInformation employeeInfo, object userState) {
            if ((this.HirePart2OperationCompleted == null)) {
                this.HirePart2OperationCompleted = new System.Threading.SendOrPostCallback(this.OnHirePart2OperationCompleted);
            }
            this.InvokeAsync("HirePart2", new object[] {
                        employeeInfo}, this.HirePart2OperationCompleted, userState);
        }
        
        private void OnHirePart2OperationCompleted(object arg) {
            if ((this.HirePart2Completed != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HirePart2Completed(this, new HirePart2CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/Loan", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void Loan(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("Loan", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void LoanAsync(EmployeeInformation employeeInfo) {
            this.LoanAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void LoanAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.LoanOperationCompleted == null)) {
                this.LoanOperationCompleted = new System.Threading.SendOrPostCallback(this.OnLoanOperationCompleted);
            }
            this.InvokeAsync("Loan", new object[] {
                        employeeInfo}, this.LoanOperationCompleted, userState);
        }
        
        private void OnLoanOperationCompleted(object arg) {
            if ((this.LoanCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.LoanCompleted(this, new LoanCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/Borrow", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void Borrow(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("Borrow", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void BorrowAsync(EmployeeInformation employeeInfo) {
            this.BorrowAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void BorrowAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.BorrowOperationCompleted == null)) {
                this.BorrowOperationCompleted = new System.Threading.SendOrPostCallback(this.OnBorrowOperationCompleted);
            }
            this.InvokeAsync("Borrow", new object[] {
                        employeeInfo}, this.BorrowOperationCompleted, userState);
        }
        
        private void OnBorrowOperationCompleted(object arg) {
            if ((this.BorrowCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.BorrowCompleted(this, new BorrowCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/Terminate", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void Terminate(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("Terminate", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void TerminateAsync(EmployeeInformation employeeInfo) {
            this.TerminateAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void TerminateAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.TerminateOperationCompleted == null)) {
                this.TerminateOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTerminateOperationCompleted);
            }
            this.InvokeAsync("Terminate", new object[] {
                        employeeInfo}, this.TerminateOperationCompleted, userState);
        }
        
        private void OnTerminateOperationCompleted(object arg) {
            if ((this.TerminateCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TerminateCompleted(this, new TerminateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/TerminateCancel", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void TerminateCancel(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("TerminateCancel", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void TerminateCancelAsync(EmployeeInformation employeeInfo) {
            this.TerminateCancelAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void TerminateCancelAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.TerminateCancelOperationCompleted == null)) {
                this.TerminateCancelOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTerminateCancelOperationCompleted);
            }
            this.InvokeAsync("TerminateCancel", new object[] {
                        employeeInfo}, this.TerminateCancelOperationCompleted, userState);
        }
        
        private void OnTerminateCancelOperationCompleted(object arg) {
            if ((this.TerminateCancelCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TerminateCancelCompleted(this, new TerminateCancelCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/TransferIn", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void TransferIn(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("TransferIn", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void TransferInAsync(EmployeeInformation employeeInfo) {
            this.TransferInAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void TransferInAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.TransferInOperationCompleted == null)) {
                this.TransferInOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTransferInOperationCompleted);
            }
            this.InvokeAsync("TransferIn", new object[] {
                        employeeInfo}, this.TransferInOperationCompleted, userState);
        }
        
        private void OnTransferInOperationCompleted(object arg) {
            if ((this.TransferInCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TransferInCompleted(this, new TransferInCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/TransferOut", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void TransferOut(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("TransferOut", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void TransferOutAsync(EmployeeInformation employeeInfo) {
            this.TransferOutAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void TransferOutAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.TransferOutOperationCompleted == null)) {
                this.TransferOutOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTransferOutOperationCompleted);
            }
            this.InvokeAsync("TransferOut", new object[] {
                        employeeInfo}, this.TransferOutOperationCompleted, userState);
        }
        
        private void OnTransferOutOperationCompleted(object arg) {
            if ((this.TransferOutCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TransferOutCompleted(this, new TransferOutCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/LeaveOfAbsence", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void LeaveOfAbsence(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("LeaveOfAbsence", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void LeaveOfAbsenceAsync(EmployeeInformation employeeInfo) {
            this.LeaveOfAbsenceAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void LeaveOfAbsenceAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.LeaveOfAbsenceOperationCompleted == null)) {
                this.LeaveOfAbsenceOperationCompleted = new System.Threading.SendOrPostCallback(this.OnLeaveOfAbsenceOperationCompleted);
            }
            this.InvokeAsync("LeaveOfAbsence", new object[] {
                        employeeInfo}, this.LeaveOfAbsenceOperationCompleted, userState);
        }
        
        private void OnLeaveOfAbsenceOperationCompleted(object arg) {
            if ((this.LeaveOfAbsenceCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.LeaveOfAbsenceCompleted(this, new LeaveOfAbsenceCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/LeaveOfAbsenceEnd", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void LeaveOfAbsenceEnd(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("LeaveOfAbsenceEnd", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void LeaveOfAbsenceEndAsync(EmployeeInformation employeeInfo) {
            this.LeaveOfAbsenceEndAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void LeaveOfAbsenceEndAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.LeaveOfAbsenceEndOperationCompleted == null)) {
                this.LeaveOfAbsenceEndOperationCompleted = new System.Threading.SendOrPostCallback(this.OnLeaveOfAbsenceEndOperationCompleted);
            }
            this.InvokeAsync("LeaveOfAbsenceEnd", new object[] {
                        employeeInfo}, this.LeaveOfAbsenceEndOperationCompleted, userState);
        }
        
        private void OnLeaveOfAbsenceEndOperationCompleted(object arg) {
            if ((this.LeaveOfAbsenceEndCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.LeaveOfAbsenceEndCompleted(this, new LeaveOfAbsenceEndCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/Update", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void Update(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("Update", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void UpdateAsync(EmployeeInformation employeeInfo) {
            this.UpdateAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void UpdateAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.UpdateOperationCompleted == null)) {
                this.UpdateOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpdateOperationCompleted);
            }
            this.InvokeAsync("Update", new object[] {
                        employeeInfo}, this.UpdateOperationCompleted, userState);
        }
        
        private void OnUpdateOperationCompleted(object arg) {
            if ((this.UpdateCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpdateCompleted(this, new UpdateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/Query", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void Query(ref EmployeeInformation employeeInfo) {
            object[] results = this.Invoke("Query", new object[] {
                        employeeInfo});
            employeeInfo = ((EmployeeInformation)(results[0]));
        }
        
        /// <remarks/>
        public void QueryAsync(EmployeeInformation employeeInfo) {
            this.QueryAsync(employeeInfo, null);
        }
        
        /// <remarks/>
        public void QueryAsync(EmployeeInformation employeeInfo, object userState) {
            if ((this.QueryOperationCompleted == null)) {
                this.QueryOperationCompleted = new System.Threading.SendOrPostCallback(this.OnQueryOperationCompleted);
            }
            this.InvokeAsync("Query", new object[] {
                        employeeInfo}, this.QueryOperationCompleted, userState);
        }
        
        private void OnQueryOperationCompleted(object arg) {
            if ((this.QueryCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.QueryCompleted(this, new QueryCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/QueryStore", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void QueryStore(ref EmployeeInformation employeeInfo, [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)] ref EmployeeInformation[] employeeInfos) {
            object[] results = this.Invoke("QueryStore", new object[] {
                        employeeInfo,
                        employeeInfos});
            employeeInfo = ((EmployeeInformation)(results[0]));
            employeeInfos = ((EmployeeInformation[])(results[1]));
        }
        
        /// <remarks/>
        public void QueryStoreAsync(EmployeeInformation employeeInfo, EmployeeInformation[] employeeInfos) {
            this.QueryStoreAsync(employeeInfo, employeeInfos, null);
        }
        
        /// <remarks/>
        public void QueryStoreAsync(EmployeeInformation employeeInfo, EmployeeInformation[] employeeInfos, object userState) {
            if ((this.QueryStoreOperationCompleted == null)) {
                this.QueryStoreOperationCompleted = new System.Threading.SendOrPostCallback(this.OnQueryStoreOperationCompleted);
            }
            this.InvokeAsync("QueryStore", new object[] {
                        employeeInfo,
                        employeeInfos}, this.QueryStoreOperationCompleted, userState);
        }
        
        private void OnQueryStoreOperationCompleted(object arg) {
            if ((this.QueryStoreCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.QueryStoreCompleted(this, new QueryStoreCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapHeaderAttribute("HeaderStoreValue")]
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/Snapshot", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void Snapshot(ref EmployeeInformation employeeInfo, [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)] ref EmployeeInformation[] employeeInfos) {
            object[] results = this.Invoke("Snapshot", new object[] {
                        employeeInfo,
                        employeeInfos});
            employeeInfo = ((EmployeeInformation)(results[0]));
            employeeInfos = ((EmployeeInformation[])(results[1]));
        }
        
        /// <remarks/>
        public void SnapshotAsync(EmployeeInformation employeeInfo, EmployeeInformation[] employeeInfos) {
            this.SnapshotAsync(employeeInfo, employeeInfos, null);
        }
        
        /// <remarks/>
        public void SnapshotAsync(EmployeeInformation employeeInfo, EmployeeInformation[] employeeInfos, object userState) {
            if ((this.SnapshotOperationCompleted == null)) {
                this.SnapshotOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSnapshotOperationCompleted);
            }
            this.InvokeAsync("Snapshot", new object[] {
                        employeeInfo,
                        employeeInfos}, this.SnapshotOperationCompleted, userState);
        }
        
        private void OnSnapshotOperationCompleted(object arg) {
            if ((this.SnapshotCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SnapshotCompleted(this, new SnapshotCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://JackInTheBox.com/webservices/Authenticate", RequestNamespace="http://JackInTheBox.com/webservices/", ResponseNamespace="http://JackInTheBox.com/webservices/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool Authenticate(string badgeNumber, string password) {
            object[] results = this.Invoke("Authenticate", new object[] {
                        badgeNumber,
                        password});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void AuthenticateAsync(string badgeNumber, string password) {
            this.AuthenticateAsync(badgeNumber, password, null);
        }
        
        /// <remarks/>
        public void AuthenticateAsync(string badgeNumber, string password, object userState) {
            if ((this.AuthenticateOperationCompleted == null)) {
                this.AuthenticateOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAuthenticateOperationCompleted);
            }
            this.InvokeAsync("Authenticate", new object[] {
                        badgeNumber,
                        password}, this.AuthenticateOperationCompleted, userState);
        }
        
        private void OnAuthenticateOperationCompleted(object arg) {
            if ((this.AuthenticateCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.AuthenticateCompleted(this, new AuthenticateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://JackInTheBox.com/webservices/")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://JackInTheBox.com/webservices/", IsNullable=false)]
    public partial class HeaderStore : System.Web.Services.Protocols.SoapHeader {
        
        private string iPAddressField;
        
        private byte[] encryptedBytesField;
        
        private System.Xml.XmlAttribute[] anyAttrField;
        
        /// <remarks/>
        public string IPAddress {
            get {
                return this.iPAddressField;
            }
            set {
                this.iPAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] EncryptedBytes {
            get {
                return this.encryptedBytesField;
            }
            set {
                this.encryptedBytesField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyAttributeAttribute()]
        public System.Xml.XmlAttribute[] AnyAttr {
            get {
                return this.anyAttrField;
            }
            set {
                this.anyAttrField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://JackInTheBox.com/webservices/")]
    public partial class EmployeeInformation {
        
        private string sourceApplicationField;
        
        private int snapshotSendEmailField;
        
        private int hireManagerFlagField;
        
        private string requestIDField;
        
        private string snapShotRequestIDField;
        
        private byte[] fICANumberField;
        
        private string locationCodeField;
        
        private string locationStateField;
        
        private string locationCodeAlternateField;
        
        private string homeLocationCodeField;
        
        private string jobCodeField;
        
        private string jobCodePreviousField;
        
        private string lastNameField;
        
        private string firstNameField;
        
        private string middleNameField;
        
        private string hiredDateField;
        
        private string hiredDateOriginalField;
        
        private string birthDateField;
        
        private string statusCodeField;
        
        private string raceCodeField;
        
        private string genderCodeField;
        
        private decimal payRateField;
        
        private decimal payRatePreviousField;
        
        private string employeeNumberField;
        
        private string companyField;
        
        private string payCardNumberField;
        
        private string payCardNumberPreviousField;
        
        private string payCardNumEncryptedField;
        
        private string payCardNumPreviousEncryptedField;
        
        private string jobEffectiveDateField;
        
        private string payEffectiveDateField;
        
        private string address1Field;
        
        private string address2Field;
        
        private string cityField;
        
        private string stateField;
        
        private string zIPField;
        
        private string phoneField;
        
        private string workExperienceDateField;
        
        private string teamLeaderMeritRatingField;
        
        private string superKeyField;
        
        private string borrowedIndicatorField;
        
        private string clockTypeField;
        
        private string badgeNumberField;
        
        private string passwordField;
        
        private string managerBadgeNumberField;
        
        private string managerPasswordField;
        
        private int eligibleForTrainingCodeField;
        
        private int surveyFlagField;
        
        private string i9ExpirationDateField;
        
        private string citizenField;
        
        private string nonCitizenField;
        
        private string maidenField;
        
        private string permanentField;
        
        private string admissionField;
        
        private string i9ADescriptionField;
        
        private string doc1NumberField;
        
        private string expirationDate1Field;
        
        private string alienField;
        
        private string alienNumberField;
        
        private string alienExpirationDateField;
        
        private string issueWho1Field;
        
        private string issueWho2Field;
        
        private string doc2NumberField;
        
        private string expirationDate2Field;
        
        private string i9BDescriptionField;
        
        private string i9CDescriptionField;
        
        private string i9FormVersionField;
        
        private string i9FormDateField;
        
        private string i9FormExpirationDateField;
        
        private string managerI9SignoffDateTimeField;
        
        private string employeeI9SignoffDateTimeField;
        
        private string w4FormVersionField;
        
        private string w4FormDateField;
        
        private string managerW4SignoffDateTimeField;
        
        private string employeeW4SignoffDateTimeField;
        
        private string employerIDField;
        
        private string gMHomeLocationField;
        
        private string gMLocation01Field;
        
        private string gMLocation02Field;
        
        private string gMLocation03Field;
        
        private string gMLocation04Field;
        
        private string gMLocation05Field;
        
        private string gMLocation06Field;
        
        private string gMLocation07Field;
        
        private string gMLocation08Field;
        
        private string gMLocation09Field;
        
        private string gMLocation10Field;
        
        private string pointsField;
        
        private string scoreField;
        
        private string reviewDateField;
        
        private string noReviewField;
        
        private string comment1Field;
        
        private string comment2Field;
        
        private string comment3Field;
        
        private string comment4Field;
        
        private string terminationDateField;
        
        private string terminationDatePreviousField;
        
        private string lastDateWorkedField;
        
        private string terminationActionField;
        
        private string terminationReasonField;
        
        private string eligibleForRehireFlagField;
        
        private string borrowFromStoreNumberField;
        
        private string borrowStartDateField;
        
        private string borrowEndDateField;
        
        private string borrowForNewTrainingField;
        
        private string transferInDateField;
        
        private string transferFromStoreNumberField;
        
        private string transferToDateField;
        
        private string transferToStoreNumberField;
        
        private string lOABeginDateField;
        
        private string lOAEndDateField;
        
        private string lOATypeField;
        
        private string lOAFlagField;
        
        private string lOAWorkRelatedInjuryFlagField;
        
        private string lOAInjuryDateField;
        
        private string lOAInjuryLastWorkedDateField;
        
        private string lOAInjuryFirstLightDutyDateField;
        
        private string lOAInjuryFirstFullDutyDateField;
        
        private string lOAReturnedFromDateField;
        
        private int applicationReturnValueField;
        
        private string applicationReturnMessage1Field;
        
        private string applicationReturnMessage2Field;
        
        private string applicationReturnMessage3Field;
        
        private string applicationReturnMessage4Field;
        
        private string applicationReturnMessage5Field;
        
        private string personnelVersionField;
        
        private byte[] biometric01Field;
        
        private byte[] biometric02Field;
        
        private string clockEmployeeIDField;
        
        private string clockEmployeeNumberField;
        
        private string clockStatusField;
        
        private string clockEnfSchField;
        
        private string clockClassificationField;
        
        private string clockReaderGroupNameField;
        
        private string clockMemberTypeField;
        
        /// <remarks/>
        public string SourceApplication {
            get {
                return this.sourceApplicationField;
            }
            set {
                this.sourceApplicationField = value;
            }
        }
        
        /// <remarks/>
        public int SnapshotSendEmail {
            get {
                return this.snapshotSendEmailField;
            }
            set {
                this.snapshotSendEmailField = value;
            }
        }
        
        /// <remarks/>
        public int HireManagerFlag {
            get {
                return this.hireManagerFlagField;
            }
            set {
                this.hireManagerFlagField = value;
            }
        }
        
        /// <remarks/>
        public string RequestID {
            get {
                return this.requestIDField;
            }
            set {
                this.requestIDField = value;
            }
        }
        
        /// <remarks/>
        public string SnapShotRequestID {
            get {
                return this.snapShotRequestIDField;
            }
            set {
                this.snapShotRequestIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] FICANumber {
            get {
                return this.fICANumberField;
            }
            set {
                this.fICANumberField = value;
            }
        }
        
        /// <remarks/>
        public string LocationCode {
            get {
                return this.locationCodeField;
            }
            set {
                this.locationCodeField = value;
            }
        }
        
        /// <remarks/>
        public string LocationState {
            get {
                return this.locationStateField;
            }
            set {
                this.locationStateField = value;
            }
        }
        
        /// <remarks/>
        public string LocationCodeAlternate {
            get {
                return this.locationCodeAlternateField;
            }
            set {
                this.locationCodeAlternateField = value;
            }
        }
        
        /// <remarks/>
        public string HomeLocationCode {
            get {
                return this.homeLocationCodeField;
            }
            set {
                this.homeLocationCodeField = value;
            }
        }
        
        /// <remarks/>
        public string JobCode {
            get {
                return this.jobCodeField;
            }
            set {
                this.jobCodeField = value;
            }
        }
        
        /// <remarks/>
        public string JobCodePrevious {
            get {
                return this.jobCodePreviousField;
            }
            set {
                this.jobCodePreviousField = value;
            }
        }
        
        /// <remarks/>
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        public string MiddleName {
            get {
                return this.middleNameField;
            }
            set {
                this.middleNameField = value;
            }
        }
        
        /// <remarks/>
        public string HiredDate {
            get {
                return this.hiredDateField;
            }
            set {
                this.hiredDateField = value;
            }
        }
        
        /// <remarks/>
        public string HiredDateOriginal {
            get {
                return this.hiredDateOriginalField;
            }
            set {
                this.hiredDateOriginalField = value;
            }
        }
        
        /// <remarks/>
        public string BirthDate {
            get {
                return this.birthDateField;
            }
            set {
                this.birthDateField = value;
            }
        }
        
        /// <remarks/>
        public string StatusCode {
            get {
                return this.statusCodeField;
            }
            set {
                this.statusCodeField = value;
            }
        }
        
        /// <remarks/>
        public string RaceCode {
            get {
                return this.raceCodeField;
            }
            set {
                this.raceCodeField = value;
            }
        }
        
        /// <remarks/>
        public string GenderCode {
            get {
                return this.genderCodeField;
            }
            set {
                this.genderCodeField = value;
            }
        }
        
        /// <remarks/>
        public decimal PayRate {
            get {
                return this.payRateField;
            }
            set {
                this.payRateField = value;
            }
        }
        
        /// <remarks/>
        public decimal PayRatePrevious {
            get {
                return this.payRatePreviousField;
            }
            set {
                this.payRatePreviousField = value;
            }
        }
        
        /// <remarks/>
        public string EmployeeNumber {
            get {
                return this.employeeNumberField;
            }
            set {
                this.employeeNumberField = value;
            }
        }
        
        /// <remarks/>
        public string Company {
            get {
                return this.companyField;
            }
            set {
                this.companyField = value;
            }
        }
        
        /// <remarks/>
        public string PayCardNumber {
            get {
                return this.payCardNumberField;
            }
            set {
                this.payCardNumberField = value;
            }
        }
        
        /// <remarks/>
        public string PayCardNumberPrevious {
            get {
                return this.payCardNumberPreviousField;
            }
            set {
                this.payCardNumberPreviousField = value;
            }
        }
        
        /// <remarks/>
        public string PayCardNumEncrypted {
            get {
                return this.payCardNumEncryptedField;
            }
            set {
                this.payCardNumEncryptedField = value;
            }
        }
        
        /// <remarks/>
        public string PayCardNumPreviousEncrypted {
            get {
                return this.payCardNumPreviousEncryptedField;
            }
            set {
                this.payCardNumPreviousEncryptedField = value;
            }
        }
        
        /// <remarks/>
        public string JobEffectiveDate {
            get {
                return this.jobEffectiveDateField;
            }
            set {
                this.jobEffectiveDateField = value;
            }
        }
        
        /// <remarks/>
        public string PayEffectiveDate {
            get {
                return this.payEffectiveDateField;
            }
            set {
                this.payEffectiveDateField = value;
            }
        }
        
        /// <remarks/>
        public string Address1 {
            get {
                return this.address1Field;
            }
            set {
                this.address1Field = value;
            }
        }
        
        /// <remarks/>
        public string Address2 {
            get {
                return this.address2Field;
            }
            set {
                this.address2Field = value;
            }
        }
        
        /// <remarks/>
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        public string State {
            get {
                return this.stateField;
            }
            set {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        public string ZIP {
            get {
                return this.zIPField;
            }
            set {
                this.zIPField = value;
            }
        }
        
        /// <remarks/>
        public string Phone {
            get {
                return this.phoneField;
            }
            set {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        public string WorkExperienceDate {
            get {
                return this.workExperienceDateField;
            }
            set {
                this.workExperienceDateField = value;
            }
        }
        
        /// <remarks/>
        public string TeamLeaderMeritRating {
            get {
                return this.teamLeaderMeritRatingField;
            }
            set {
                this.teamLeaderMeritRatingField = value;
            }
        }
        
        /// <remarks/>
        public string SuperKey {
            get {
                return this.superKeyField;
            }
            set {
                this.superKeyField = value;
            }
        }
        
        /// <remarks/>
        public string BorrowedIndicator {
            get {
                return this.borrowedIndicatorField;
            }
            set {
                this.borrowedIndicatorField = value;
            }
        }
        
        /// <remarks/>
        public string ClockType {
            get {
                return this.clockTypeField;
            }
            set {
                this.clockTypeField = value;
            }
        }
        
        /// <remarks/>
        public string BadgeNumber {
            get {
                return this.badgeNumberField;
            }
            set {
                this.badgeNumberField = value;
            }
        }
        
        /// <remarks/>
        public string Password {
            get {
                return this.passwordField;
            }
            set {
                this.passwordField = value;
            }
        }
        
        /// <remarks/>
        public string ManagerBadgeNumber {
            get {
                return this.managerBadgeNumberField;
            }
            set {
                this.managerBadgeNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ManagerPassword {
            get {
                return this.managerPasswordField;
            }
            set {
                this.managerPasswordField = value;
            }
        }
        
        /// <remarks/>
        public int EligibleForTrainingCode {
            get {
                return this.eligibleForTrainingCodeField;
            }
            set {
                this.eligibleForTrainingCodeField = value;
            }
        }
        
        /// <remarks/>
        public int SurveyFlag {
            get {
                return this.surveyFlagField;
            }
            set {
                this.surveyFlagField = value;
            }
        }
        
        /// <remarks/>
        public string I9ExpirationDate {
            get {
                return this.i9ExpirationDateField;
            }
            set {
                this.i9ExpirationDateField = value;
            }
        }
        
        /// <remarks/>
        public string Citizen {
            get {
                return this.citizenField;
            }
            set {
                this.citizenField = value;
            }
        }
        
        /// <remarks/>
        public string NonCitizen {
            get {
                return this.nonCitizenField;
            }
            set {
                this.nonCitizenField = value;
            }
        }
        
        /// <remarks/>
        public string Maiden {
            get {
                return this.maidenField;
            }
            set {
                this.maidenField = value;
            }
        }
        
        /// <remarks/>
        public string Permanent {
            get {
                return this.permanentField;
            }
            set {
                this.permanentField = value;
            }
        }
        
        /// <remarks/>
        public string Admission {
            get {
                return this.admissionField;
            }
            set {
                this.admissionField = value;
            }
        }
        
        /// <remarks/>
        public string I9ADescription {
            get {
                return this.i9ADescriptionField;
            }
            set {
                this.i9ADescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string Doc1Number {
            get {
                return this.doc1NumberField;
            }
            set {
                this.doc1NumberField = value;
            }
        }
        
        /// <remarks/>
        public string ExpirationDate1 {
            get {
                return this.expirationDate1Field;
            }
            set {
                this.expirationDate1Field = value;
            }
        }
        
        /// <remarks/>
        public string Alien {
            get {
                return this.alienField;
            }
            set {
                this.alienField = value;
            }
        }
        
        /// <remarks/>
        public string AlienNumber {
            get {
                return this.alienNumberField;
            }
            set {
                this.alienNumberField = value;
            }
        }
        
        /// <remarks/>
        public string AlienExpirationDate {
            get {
                return this.alienExpirationDateField;
            }
            set {
                this.alienExpirationDateField = value;
            }
        }
        
        /// <remarks/>
        public string IssueWho1 {
            get {
                return this.issueWho1Field;
            }
            set {
                this.issueWho1Field = value;
            }
        }
        
        /// <remarks/>
        public string IssueWho2 {
            get {
                return this.issueWho2Field;
            }
            set {
                this.issueWho2Field = value;
            }
        }
        
        /// <remarks/>
        public string Doc2Number {
            get {
                return this.doc2NumberField;
            }
            set {
                this.doc2NumberField = value;
            }
        }
        
        /// <remarks/>
        public string ExpirationDate2 {
            get {
                return this.expirationDate2Field;
            }
            set {
                this.expirationDate2Field = value;
            }
        }
        
        /// <remarks/>
        public string I9BDescription {
            get {
                return this.i9BDescriptionField;
            }
            set {
                this.i9BDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string I9CDescription {
            get {
                return this.i9CDescriptionField;
            }
            set {
                this.i9CDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string I9FormVersion {
            get {
                return this.i9FormVersionField;
            }
            set {
                this.i9FormVersionField = value;
            }
        }
        
        /// <remarks/>
        public string I9FormDate {
            get {
                return this.i9FormDateField;
            }
            set {
                this.i9FormDateField = value;
            }
        }
        
        /// <remarks/>
        public string I9FormExpirationDate {
            get {
                return this.i9FormExpirationDateField;
            }
            set {
                this.i9FormExpirationDateField = value;
            }
        }
        
        /// <remarks/>
        public string ManagerI9SignoffDateTime {
            get {
                return this.managerI9SignoffDateTimeField;
            }
            set {
                this.managerI9SignoffDateTimeField = value;
            }
        }
        
        /// <remarks/>
        public string EmployeeI9SignoffDateTime {
            get {
                return this.employeeI9SignoffDateTimeField;
            }
            set {
                this.employeeI9SignoffDateTimeField = value;
            }
        }
        
        /// <remarks/>
        public string W4FormVersion {
            get {
                return this.w4FormVersionField;
            }
            set {
                this.w4FormVersionField = value;
            }
        }
        
        /// <remarks/>
        public string W4FormDate {
            get {
                return this.w4FormDateField;
            }
            set {
                this.w4FormDateField = value;
            }
        }
        
        /// <remarks/>
        public string ManagerW4SignoffDateTime {
            get {
                return this.managerW4SignoffDateTimeField;
            }
            set {
                this.managerW4SignoffDateTimeField = value;
            }
        }
        
        /// <remarks/>
        public string EmployeeW4SignoffDateTime {
            get {
                return this.employeeW4SignoffDateTimeField;
            }
            set {
                this.employeeW4SignoffDateTimeField = value;
            }
        }
        
        /// <remarks/>
        public string EmployerID {
            get {
                return this.employerIDField;
            }
            set {
                this.employerIDField = value;
            }
        }
        
        /// <remarks/>
        public string GMHomeLocation {
            get {
                return this.gMHomeLocationField;
            }
            set {
                this.gMHomeLocationField = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation01 {
            get {
                return this.gMLocation01Field;
            }
            set {
                this.gMLocation01Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation02 {
            get {
                return this.gMLocation02Field;
            }
            set {
                this.gMLocation02Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation03 {
            get {
                return this.gMLocation03Field;
            }
            set {
                this.gMLocation03Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation04 {
            get {
                return this.gMLocation04Field;
            }
            set {
                this.gMLocation04Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation05 {
            get {
                return this.gMLocation05Field;
            }
            set {
                this.gMLocation05Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation06 {
            get {
                return this.gMLocation06Field;
            }
            set {
                this.gMLocation06Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation07 {
            get {
                return this.gMLocation07Field;
            }
            set {
                this.gMLocation07Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation08 {
            get {
                return this.gMLocation08Field;
            }
            set {
                this.gMLocation08Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation09 {
            get {
                return this.gMLocation09Field;
            }
            set {
                this.gMLocation09Field = value;
            }
        }
        
        /// <remarks/>
        public string GMLocation10 {
            get {
                return this.gMLocation10Field;
            }
            set {
                this.gMLocation10Field = value;
            }
        }
        
        /// <remarks/>
        public string Points {
            get {
                return this.pointsField;
            }
            set {
                this.pointsField = value;
            }
        }
        
        /// <remarks/>
        public string Score {
            get {
                return this.scoreField;
            }
            set {
                this.scoreField = value;
            }
        }
        
        /// <remarks/>
        public string ReviewDate {
            get {
                return this.reviewDateField;
            }
            set {
                this.reviewDateField = value;
            }
        }
        
        /// <remarks/>
        public string NoReview {
            get {
                return this.noReviewField;
            }
            set {
                this.noReviewField = value;
            }
        }
        
        /// <remarks/>
        public string Comment1 {
            get {
                return this.comment1Field;
            }
            set {
                this.comment1Field = value;
            }
        }
        
        /// <remarks/>
        public string Comment2 {
            get {
                return this.comment2Field;
            }
            set {
                this.comment2Field = value;
            }
        }
        
        /// <remarks/>
        public string Comment3 {
            get {
                return this.comment3Field;
            }
            set {
                this.comment3Field = value;
            }
        }
        
        /// <remarks/>
        public string Comment4 {
            get {
                return this.comment4Field;
            }
            set {
                this.comment4Field = value;
            }
        }
        
        /// <remarks/>
        public string TerminationDate {
            get {
                return this.terminationDateField;
            }
            set {
                this.terminationDateField = value;
            }
        }
        
        /// <remarks/>
        public string TerminationDatePrevious {
            get {
                return this.terminationDatePreviousField;
            }
            set {
                this.terminationDatePreviousField = value;
            }
        }
        
        /// <remarks/>
        public string LastDateWorked {
            get {
                return this.lastDateWorkedField;
            }
            set {
                this.lastDateWorkedField = value;
            }
        }
        
        /// <remarks/>
        public string TerminationAction {
            get {
                return this.terminationActionField;
            }
            set {
                this.terminationActionField = value;
            }
        }
        
        /// <remarks/>
        public string TerminationReason {
            get {
                return this.terminationReasonField;
            }
            set {
                this.terminationReasonField = value;
            }
        }
        
        /// <remarks/>
        public string EligibleForRehireFlag {
            get {
                return this.eligibleForRehireFlagField;
            }
            set {
                this.eligibleForRehireFlagField = value;
            }
        }
        
        /// <remarks/>
        public string BorrowFromStoreNumber {
            get {
                return this.borrowFromStoreNumberField;
            }
            set {
                this.borrowFromStoreNumberField = value;
            }
        }
        
        /// <remarks/>
        public string BorrowStartDate {
            get {
                return this.borrowStartDateField;
            }
            set {
                this.borrowStartDateField = value;
            }
        }
        
        /// <remarks/>
        public string BorrowEndDate {
            get {
                return this.borrowEndDateField;
            }
            set {
                this.borrowEndDateField = value;
            }
        }
        
        /// <remarks/>
        public string BorrowForNewTraining {
            get {
                return this.borrowForNewTrainingField;
            }
            set {
                this.borrowForNewTrainingField = value;
            }
        }
        
        /// <remarks/>
        public string TransferInDate {
            get {
                return this.transferInDateField;
            }
            set {
                this.transferInDateField = value;
            }
        }
        
        /// <remarks/>
        public string TransferFromStoreNumber {
            get {
                return this.transferFromStoreNumberField;
            }
            set {
                this.transferFromStoreNumberField = value;
            }
        }
        
        /// <remarks/>
        public string TransferToDate {
            get {
                return this.transferToDateField;
            }
            set {
                this.transferToDateField = value;
            }
        }
        
        /// <remarks/>
        public string TransferToStoreNumber {
            get {
                return this.transferToStoreNumberField;
            }
            set {
                this.transferToStoreNumberField = value;
            }
        }
        
        /// <remarks/>
        public string LOABeginDate {
            get {
                return this.lOABeginDateField;
            }
            set {
                this.lOABeginDateField = value;
            }
        }
        
        /// <remarks/>
        public string LOAEndDate {
            get {
                return this.lOAEndDateField;
            }
            set {
                this.lOAEndDateField = value;
            }
        }
        
        /// <remarks/>
        public string LOAType {
            get {
                return this.lOATypeField;
            }
            set {
                this.lOATypeField = value;
            }
        }
        
        /// <remarks/>
        public string LOAFlag {
            get {
                return this.lOAFlagField;
            }
            set {
                this.lOAFlagField = value;
            }
        }
        
        /// <remarks/>
        public string LOAWorkRelatedInjuryFlag {
            get {
                return this.lOAWorkRelatedInjuryFlagField;
            }
            set {
                this.lOAWorkRelatedInjuryFlagField = value;
            }
        }
        
        /// <remarks/>
        public string LOAInjuryDate {
            get {
                return this.lOAInjuryDateField;
            }
            set {
                this.lOAInjuryDateField = value;
            }
        }
        
        /// <remarks/>
        public string LOAInjuryLastWorkedDate {
            get {
                return this.lOAInjuryLastWorkedDateField;
            }
            set {
                this.lOAInjuryLastWorkedDateField = value;
            }
        }
        
        /// <remarks/>
        public string LOAInjuryFirstLightDutyDate {
            get {
                return this.lOAInjuryFirstLightDutyDateField;
            }
            set {
                this.lOAInjuryFirstLightDutyDateField = value;
            }
        }
        
        /// <remarks/>
        public string LOAInjuryFirstFullDutyDate {
            get {
                return this.lOAInjuryFirstFullDutyDateField;
            }
            set {
                this.lOAInjuryFirstFullDutyDateField = value;
            }
        }
        
        /// <remarks/>
        public string LOAReturnedFromDate {
            get {
                return this.lOAReturnedFromDateField;
            }
            set {
                this.lOAReturnedFromDateField = value;
            }
        }
        
        /// <remarks/>
        public int ApplicationReturnValue {
            get {
                return this.applicationReturnValueField;
            }
            set {
                this.applicationReturnValueField = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationReturnMessage1 {
            get {
                return this.applicationReturnMessage1Field;
            }
            set {
                this.applicationReturnMessage1Field = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationReturnMessage2 {
            get {
                return this.applicationReturnMessage2Field;
            }
            set {
                this.applicationReturnMessage2Field = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationReturnMessage3 {
            get {
                return this.applicationReturnMessage3Field;
            }
            set {
                this.applicationReturnMessage3Field = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationReturnMessage4 {
            get {
                return this.applicationReturnMessage4Field;
            }
            set {
                this.applicationReturnMessage4Field = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationReturnMessage5 {
            get {
                return this.applicationReturnMessage5Field;
            }
            set {
                this.applicationReturnMessage5Field = value;
            }
        }
        
        /// <remarks/>
        public string PersonnelVersion {
            get {
                return this.personnelVersionField;
            }
            set {
                this.personnelVersionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Biometric01 {
            get {
                return this.biometric01Field;
            }
            set {
                this.biometric01Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Biometric02 {
            get {
                return this.biometric02Field;
            }
            set {
                this.biometric02Field = value;
            }
        }
        
        /// <remarks/>
        public string ClockEmployeeID {
            get {
                return this.clockEmployeeIDField;
            }
            set {
                this.clockEmployeeIDField = value;
            }
        }
        
        /// <remarks/>
        public string ClockEmployeeNumber {
            get {
                return this.clockEmployeeNumberField;
            }
            set {
                this.clockEmployeeNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ClockStatus {
            get {
                return this.clockStatusField;
            }
            set {
                this.clockStatusField = value;
            }
        }
        
        /// <remarks/>
        public string ClockEnfSch {
            get {
                return this.clockEnfSchField;
            }
            set {
                this.clockEnfSchField = value;
            }
        }
        
        /// <remarks/>
        public string ClockClassification {
            get {
                return this.clockClassificationField;
            }
            set {
                this.clockClassificationField = value;
            }
        }
        
        /// <remarks/>
        public string ClockReaderGroupName {
            get {
                return this.clockReaderGroupNameField;
            }
            set {
                this.clockReaderGroupNameField = value;
            }
        }
        
        /// <remarks/>
        public string ClockMemberType {
            get {
                return this.clockMemberTypeField;
            }
            set {
                this.clockMemberTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void PingCompletedEventHandler(object sender, PingCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PingCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PingCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void HireOrRehireCompletedEventHandler(object sender, HireOrRehireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HireOrRehireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HireOrRehireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void PasswordChangeCompletedEventHandler(object sender, PasswordChangeCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PasswordChangeCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PasswordChangeCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void HirePart2CompletedEventHandler(object sender, HirePart2CompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HirePart2CompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HirePart2CompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void LoanCompletedEventHandler(object sender, LoanCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class LoanCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal LoanCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void BorrowCompletedEventHandler(object sender, BorrowCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BorrowCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal BorrowCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void TerminateCompletedEventHandler(object sender, TerminateCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TerminateCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TerminateCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void TerminateCancelCompletedEventHandler(object sender, TerminateCancelCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TerminateCancelCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TerminateCancelCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void TransferInCompletedEventHandler(object sender, TransferInCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TransferInCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TransferInCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void TransferOutCompletedEventHandler(object sender, TransferOutCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TransferOutCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TransferOutCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void LeaveOfAbsenceCompletedEventHandler(object sender, LeaveOfAbsenceCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class LeaveOfAbsenceCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal LeaveOfAbsenceCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void LeaveOfAbsenceEndCompletedEventHandler(object sender, LeaveOfAbsenceEndCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class LeaveOfAbsenceEndCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal LeaveOfAbsenceEndCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void UpdateCompletedEventHandler(object sender, UpdateCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpdateCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpdateCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void QueryCompletedEventHandler(object sender, QueryCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class QueryCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal QueryCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void QueryStoreCompletedEventHandler(object sender, QueryStoreCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class QueryStoreCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal QueryStoreCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
        
        /// <remarks/>
        public EmployeeInformation[] employeeInfos {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation[])(this.results[1]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void SnapshotCompletedEventHandler(object sender, SnapshotCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SnapshotCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SnapshotCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmployeeInformation employeeInfo {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation)(this.results[0]));
            }
        }
        
        /// <remarks/>
        public EmployeeInformation[] employeeInfos {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmployeeInformation[])(this.results[1]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    public delegate void AuthenticateCompletedEventHandler(object sender, AuthenticateCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.6.1055.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AuthenticateCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal AuthenticateCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591