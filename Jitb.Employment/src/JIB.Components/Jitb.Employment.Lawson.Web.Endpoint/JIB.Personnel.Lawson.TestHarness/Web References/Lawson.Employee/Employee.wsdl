<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://JackInTheBox.com/webservices/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://JackInTheBox.com/webservices/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://JackInTheBox.com/webservices/">
      <s:element name="Ping">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="EmployeeInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="SourceApplication" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SnapshotSendEmail" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="HireManagerFlag" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="RequestID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SnapShotRequestID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="FICANumber" type="s:base64Binary" />
          <s:element minOccurs="0" maxOccurs="1" name="LocationCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LocationState" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LocationCodeAlternate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HomeLocationCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="JobCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="JobCodePrevious" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LastName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="FirstName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="MiddleName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HiredDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HiredDateOriginal" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BirthDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="StatusCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="RaceCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GenderCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PayRate" type="s:decimal" />
          <s:element minOccurs="1" maxOccurs="1" name="PayRatePrevious" type="s:decimal" />
          <s:element minOccurs="0" maxOccurs="1" name="EmployeeNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Company" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PayCardNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PayCardNumberPrevious" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PayCardNumEncrypted" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PayCardNumPreviousEncrypted" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="JobEffectiveDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PayEffectiveDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Address1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Address2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="City" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="State" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ZIP" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Phone" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="WorkExperienceDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TeamLeaderMeritRating" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SuperKey" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BorrowedIndicator" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ClockType" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BadgeNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Password" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ManagerBadgeNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ManagerPassword" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="EligibleForTrainingCode" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="SurveyFlag" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="I9ExpirationDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Citizen" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="NonCitizen" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Maiden" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Permanent" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Admission" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="I9ADescription" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Doc1Number" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExpirationDate1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Alien" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AlienNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AlienExpirationDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="IssueWho1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="IssueWho2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Doc2Number" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExpirationDate2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="I9BDescription" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="I9CDescription" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="I9FormVersion" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="I9FormDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="I9FormExpirationDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ManagerI9SignoffDateTime" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EmployeeI9SignoffDateTime" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="W4FormVersion" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="W4FormDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ManagerW4SignoffDateTime" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EmployeeW4SignoffDateTime" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EmployerID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMHomeLocation" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation01" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation02" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation03" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation04" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation05" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation06" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation07" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation08" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation09" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GMLocation10" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Points" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Score" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReviewDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="NoReview" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Comment1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Comment2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Comment3" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Comment4" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TerminationDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TerminationDatePrevious" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LastDateWorked" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TerminationAction" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TerminationReason" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EligibleForRehireFlag" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BorrowFromStoreNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BorrowStartDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BorrowEndDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BorrowForNewTraining" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransferInDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransferFromStoreNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransferToDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransferToStoreNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOABeginDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAEndDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAType" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAFlag" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAWorkRelatedInjuryFlag" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAInjuryDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAInjuryLastWorkedDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAInjuryFirstLightDutyDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAInjuryFirstFullDutyDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LOAReturnedFromDate" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ApplicationReturnValue" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationReturnMessage1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationReturnMessage2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationReturnMessage3" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationReturnMessage4" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationReturnMessage5" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PersonnelVersion" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Biometric01" type="s:base64Binary" />
          <s:element minOccurs="0" maxOccurs="1" name="Biometric02" type="s:base64Binary" />
          <s:element minOccurs="0" maxOccurs="1" name="ClockEmployeeID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ClockEmployeeNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ClockStatus" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ClockEnfSch" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ClockClassification" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ClockReaderGroupName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ClockMemberType" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="PingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="HeaderStore" type="tns:HeaderStore" />
      <s:complexType name="HeaderStore">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="IPAddress" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EncryptedBytes" type="s:base64Binary" />
        </s:sequence>
        <s:anyAttribute />
      </s:complexType>
      <s:element name="HireOrRehire">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
            <s:element minOccurs="1" maxOccurs="1" name="addEmployeeToWorkbrain" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="HireOrRehireResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PasswordChange">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PasswordChangeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="HirePart2">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="HirePart2Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Loan">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LoanResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Borrow">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BorrowResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Terminate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TerminateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TerminateCancel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TerminateCancelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TransferIn">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TransferInResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TransferOut">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TransferOutResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LeaveOfAbsence">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LeaveOfAbsenceResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LeaveOfAbsenceEnd">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LeaveOfAbsenceEndResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Update">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Query">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="QueryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="QueryStore">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
            <s:element minOccurs="0" maxOccurs="1" name="employeeInfos" type="tns:ArrayOfEmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfEmployeeInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="EmployeeInformation" type="tns:EmployeeInformation" />
        </s:sequence>
      </s:complexType>
      <s:element name="QueryStoreResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
            <s:element minOccurs="0" maxOccurs="1" name="employeeInfos" type="tns:ArrayOfEmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Snapshot">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
            <s:element minOccurs="0" maxOccurs="1" name="employeeInfos" type="tns:ArrayOfEmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SnapshotResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="employeeInfo" type="tns:EmployeeInformation" />
            <s:element minOccurs="0" maxOccurs="1" name="employeeInfos" type="tns:ArrayOfEmployeeInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Authenticate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="badgeNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AuthenticateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AuthenticateResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="PingSoapIn">
    <wsdl:part name="parameters" element="tns:Ping" />
  </wsdl:message>
  <wsdl:message name="PingSoapOut">
    <wsdl:part name="parameters" element="tns:PingResponse" />
  </wsdl:message>
  <wsdl:message name="PingHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="HireOrRehireSoapIn">
    <wsdl:part name="parameters" element="tns:HireOrRehire" />
  </wsdl:message>
  <wsdl:message name="HireOrRehireSoapOut">
    <wsdl:part name="parameters" element="tns:HireOrRehireResponse" />
  </wsdl:message>
  <wsdl:message name="HireOrRehireHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="PasswordChangeSoapIn">
    <wsdl:part name="parameters" element="tns:PasswordChange" />
  </wsdl:message>
  <wsdl:message name="PasswordChangeSoapOut">
    <wsdl:part name="parameters" element="tns:PasswordChangeResponse" />
  </wsdl:message>
  <wsdl:message name="PasswordChangeHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="HirePart2SoapIn">
    <wsdl:part name="parameters" element="tns:HirePart2" />
  </wsdl:message>
  <wsdl:message name="HirePart2SoapOut">
    <wsdl:part name="parameters" element="tns:HirePart2Response" />
  </wsdl:message>
  <wsdl:message name="HirePart2HeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="LoanSoapIn">
    <wsdl:part name="parameters" element="tns:Loan" />
  </wsdl:message>
  <wsdl:message name="LoanSoapOut">
    <wsdl:part name="parameters" element="tns:LoanResponse" />
  </wsdl:message>
  <wsdl:message name="LoanHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="BorrowSoapIn">
    <wsdl:part name="parameters" element="tns:Borrow" />
  </wsdl:message>
  <wsdl:message name="BorrowSoapOut">
    <wsdl:part name="parameters" element="tns:BorrowResponse" />
  </wsdl:message>
  <wsdl:message name="BorrowHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="TerminateSoapIn">
    <wsdl:part name="parameters" element="tns:Terminate" />
  </wsdl:message>
  <wsdl:message name="TerminateSoapOut">
    <wsdl:part name="parameters" element="tns:TerminateResponse" />
  </wsdl:message>
  <wsdl:message name="TerminateHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="TerminateCancelSoapIn">
    <wsdl:part name="parameters" element="tns:TerminateCancel" />
  </wsdl:message>
  <wsdl:message name="TerminateCancelSoapOut">
    <wsdl:part name="parameters" element="tns:TerminateCancelResponse" />
  </wsdl:message>
  <wsdl:message name="TerminateCancelHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="TransferInSoapIn">
    <wsdl:part name="parameters" element="tns:TransferIn" />
  </wsdl:message>
  <wsdl:message name="TransferInSoapOut">
    <wsdl:part name="parameters" element="tns:TransferInResponse" />
  </wsdl:message>
  <wsdl:message name="TransferInHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="TransferOutSoapIn">
    <wsdl:part name="parameters" element="tns:TransferOut" />
  </wsdl:message>
  <wsdl:message name="TransferOutSoapOut">
    <wsdl:part name="parameters" element="tns:TransferOutResponse" />
  </wsdl:message>
  <wsdl:message name="TransferOutHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="LeaveOfAbsenceSoapIn">
    <wsdl:part name="parameters" element="tns:LeaveOfAbsence" />
  </wsdl:message>
  <wsdl:message name="LeaveOfAbsenceSoapOut">
    <wsdl:part name="parameters" element="tns:LeaveOfAbsenceResponse" />
  </wsdl:message>
  <wsdl:message name="LeaveOfAbsenceHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="LeaveOfAbsenceEndSoapIn">
    <wsdl:part name="parameters" element="tns:LeaveOfAbsenceEnd" />
  </wsdl:message>
  <wsdl:message name="LeaveOfAbsenceEndSoapOut">
    <wsdl:part name="parameters" element="tns:LeaveOfAbsenceEndResponse" />
  </wsdl:message>
  <wsdl:message name="LeaveOfAbsenceEndHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="UpdateSoapIn">
    <wsdl:part name="parameters" element="tns:Update" />
  </wsdl:message>
  <wsdl:message name="UpdateSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="QuerySoapIn">
    <wsdl:part name="parameters" element="tns:Query" />
  </wsdl:message>
  <wsdl:message name="QuerySoapOut">
    <wsdl:part name="parameters" element="tns:QueryResponse" />
  </wsdl:message>
  <wsdl:message name="QueryHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="QueryStoreSoapIn">
    <wsdl:part name="parameters" element="tns:QueryStore" />
  </wsdl:message>
  <wsdl:message name="QueryStoreSoapOut">
    <wsdl:part name="parameters" element="tns:QueryStoreResponse" />
  </wsdl:message>
  <wsdl:message name="QueryStoreHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="SnapshotSoapIn">
    <wsdl:part name="parameters" element="tns:Snapshot" />
  </wsdl:message>
  <wsdl:message name="SnapshotSoapOut">
    <wsdl:part name="parameters" element="tns:SnapshotResponse" />
  </wsdl:message>
  <wsdl:message name="SnapshotHeaderStore">
    <wsdl:part name="HeaderStore" element="tns:HeaderStore" />
  </wsdl:message>
  <wsdl:message name="AuthenticateSoapIn">
    <wsdl:part name="parameters" element="tns:Authenticate" />
  </wsdl:message>
  <wsdl:message name="AuthenticateSoapOut">
    <wsdl:part name="parameters" element="tns:AuthenticateResponse" />
  </wsdl:message>
  <wsdl:portType name="EmployeeSoap">
    <wsdl:operation name="Ping">
      <wsdl:input message="tns:PingSoapIn" />
      <wsdl:output message="tns:PingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="HireOrRehire">
      <wsdl:input message="tns:HireOrRehireSoapIn" />
      <wsdl:output message="tns:HireOrRehireSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PasswordChange">
      <wsdl:input message="tns:PasswordChangeSoapIn" />
      <wsdl:output message="tns:PasswordChangeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="HirePart2">
      <wsdl:input message="tns:HirePart2SoapIn" />
      <wsdl:output message="tns:HirePart2SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Loan">
      <wsdl:input message="tns:LoanSoapIn" />
      <wsdl:output message="tns:LoanSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Borrow">
      <wsdl:input message="tns:BorrowSoapIn" />
      <wsdl:output message="tns:BorrowSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Terminate">
      <wsdl:input message="tns:TerminateSoapIn" />
      <wsdl:output message="tns:TerminateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TerminateCancel">
      <wsdl:input message="tns:TerminateCancelSoapIn" />
      <wsdl:output message="tns:TerminateCancelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TransferIn">
      <wsdl:input message="tns:TransferInSoapIn" />
      <wsdl:output message="tns:TransferInSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TransferOut">
      <wsdl:input message="tns:TransferOutSoapIn" />
      <wsdl:output message="tns:TransferOutSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="LeaveOfAbsence">
      <wsdl:input message="tns:LeaveOfAbsenceSoapIn" />
      <wsdl:output message="tns:LeaveOfAbsenceSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="LeaveOfAbsenceEnd">
      <wsdl:input message="tns:LeaveOfAbsenceEndSoapIn" />
      <wsdl:output message="tns:LeaveOfAbsenceEndSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Update">
      <wsdl:input message="tns:UpdateSoapIn" />
      <wsdl:output message="tns:UpdateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Query">
      <wsdl:input message="tns:QuerySoapIn" />
      <wsdl:output message="tns:QuerySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="QueryStore">
      <wsdl:input message="tns:QueryStoreSoapIn" />
      <wsdl:output message="tns:QueryStoreSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Snapshot">
      <wsdl:input message="tns:SnapshotSoapIn" />
      <wsdl:output message="tns:SnapshotSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Authenticate">
      <wsdl:input message="tns:AuthenticateSoapIn" />
      <wsdl:output message="tns:AuthenticateSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="EmployeeSoap" type="tns:EmployeeSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Ping">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/Ping" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:PingHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HireOrRehire">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/HireOrRehire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:HireOrRehireHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PasswordChange">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/PasswordChange" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:PasswordChangeHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HirePart2">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/HirePart2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:HirePart2HeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Loan">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/Loan" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:LoanHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Borrow">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/Borrow" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:BorrowHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Terminate">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/Terminate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:TerminateHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TerminateCancel">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/TerminateCancel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:TerminateCancelHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferIn">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/TransferIn" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:TransferInHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferOut">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/TransferOut" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:TransferOutHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LeaveOfAbsence">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/LeaveOfAbsence" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:LeaveOfAbsenceHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LeaveOfAbsenceEnd">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/LeaveOfAbsenceEnd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:LeaveOfAbsenceEndHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Update">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/Update" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:UpdateHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Query">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/Query" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:QueryHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryStore">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/QueryStore" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:QueryStoreHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Snapshot">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/Snapshot" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
        <soap:header message="tns:SnapshotHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Authenticate">
      <soap:operation soapAction="http://JackInTheBox.com/webservices/Authenticate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="EmployeeSoap12" type="tns:EmployeeSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Ping">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/Ping" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:PingHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HireOrRehire">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/HireOrRehire" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:HireOrRehireHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PasswordChange">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/PasswordChange" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:PasswordChangeHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HirePart2">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/HirePart2" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:HirePart2HeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Loan">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/Loan" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:LoanHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Borrow">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/Borrow" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:BorrowHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Terminate">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/Terminate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:TerminateHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TerminateCancel">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/TerminateCancel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:TerminateCancelHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferIn">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/TransferIn" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:TransferInHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferOut">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/TransferOut" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:TransferOutHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LeaveOfAbsence">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/LeaveOfAbsence" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:LeaveOfAbsenceHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LeaveOfAbsenceEnd">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/LeaveOfAbsenceEnd" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:LeaveOfAbsenceEndHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Update">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/Update" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:UpdateHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Query">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/Query" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:QueryHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryStore">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/QueryStore" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:QueryStoreHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Snapshot">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/Snapshot" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
        <soap12:header message="tns:SnapshotHeaderStore" part="HeaderStore" use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Authenticate">
      <soap12:operation soapAction="http://JackInTheBox.com/webservices/Authenticate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Employee">
    <wsdl:port name="EmployeeSoap" binding="tns:EmployeeSoap">
      <soap:address location="http://localhost:50351/Employee.asmx" />
    </wsdl:port>
    <wsdl:port name="EmployeeSoap12" binding="tns:EmployeeSoap12">
      <soap12:address location="http://localhost:50351/Employee.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>