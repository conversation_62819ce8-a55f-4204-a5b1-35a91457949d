﻿using JIB.Framework.Security.Services.WebServices.AuthenticationHeaders;
using JIB.Personnel.Store.Data.PersistenceLayer;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Numerics;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace JIB.Personnel.Lawson.TestClient
{
    public partial class TestForm : Form
    {
        const string MsgBoxFormatCommon = "{0}\n\nWeb service:\n{1}";
        const string MsgBoxFormatLawson = "{0}: {1}\n{2}\n{3}\n{4}\n{5}\n\nWeb service:\n{6}";
        const string MsgBoxFormatException = "Exception:\n{0}\n\nStackTrace:\n{1}\n\nWeb service:\n{2}";

        int requestIdIncrementer = 303;

        public TestForm()
        {
            InitializeComponent();
        }

        #region Fields
        byte[] key = { 00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32 };
        byte[] iv = { 55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32 };
        const string MethodPing = "PING";
        const string ProcessIDAdd = "C71E3D50-7C47-4e10-8C17-A6D0FF3EFDAA";
        const string ProcessIDTerminate = "F89ACF36-1B95-456b-A33C-10AA7885F859";
        const string ProcessIDTerminateCancel = "2F15D484-AB24-450d-9C5F-E924477AA484";
        const string ProcessIDLoan = "2A796025-97DA-486c-8483-737FC6E3D4E7";
        const string ProcessIDBorrow = "53988D11-DF28-4430-8A1C-E00663B3A6AD";
        const string ProcessIDTransferIn = "8DF7A220-6012-40ea-84D9-B9B5697D3532";
        const string ProcessIDTransferOut = "B2D00062-4021-48d4-AF2C-571B268CB77A";
        const string ProcessIDLeaveOfAbsence = "E8D45406-40E1-451c-828E-A51D6F95E494";
        const string ProcessIDLeaveOfAbsenceEnd = "********-62B1-4965-AE16-080E79A31398";
        const string ProcessIDPasswordChange = "343AF2AB-1DE0-4076-BD05-561BE46A530F";
        const string ProcessIDUpdate = "D4A47467-3F15-4687-940D-41AB26452ABF";
        internal enum ApplicationReturnValue : int
        {
            Failure = 0,
            Success = 1,
            AuthenticationError = 2
        }
        internal class HeaderInfo
        {
            internal string ProcessID { get; set; }
            internal string RequestID { get; set; }
            internal string StoreNumber { get; set; }
            internal string Method { get; set; }
            internal string OperatorEmployeeNumber { get; set; }
        }
        #endregion

        #region Methods

        private void InvokeWebSvc(HeaderInfo headerInfo)
        {
            Lawson.Employee.EmployeeInformation employeeInfo = new Lawson.Employee.EmployeeInformation();
            if (headerInfo.Method != MethodPing)
            {
                headerInfo.RequestID = requestIdIncrementer++.ToString("0000");
                MapEmployee(ref employeeInfo, ref headerInfo);
            }

            switch (headerInfo.Method)
            {
                case MethodPing:
                    headerInfo.ProcessID = ProcessIDAdd;
                    break;

                case Pe2law.TranTypeValues.Hire:
                case Pe2law.TranTypeValues.HireManager:
                case Pe2law.TranTypeValues.Hire2:
                case Pe2law.TranTypeValues.HireDeprecated:
                    headerInfo.ProcessID = ProcessIDAdd;
                    break;

                case Pe2law.TranTypeValues.TransferIn:
                    headerInfo.ProcessID = ProcessIDTransferIn;
                    break;

                case Pe2law.TranTypeValues.TransferOut:
                    headerInfo.ProcessID = ProcessIDTransferOut;
                    break;

                case Pe2law.TranTypeValues.Loan:
                    headerInfo.ProcessID = ProcessIDLoan;
                    break;

                case Pe2law.TranTypeValues.Borrow:
                    headerInfo.ProcessID = ProcessIDBorrow;
                    break;

                case Pe2law.TranTypeValues.LeaveOfAbsence:
                    headerInfo.ProcessID = ProcessIDLeaveOfAbsence;
                    break;

                case Pe2law.TranTypeValues.LeaveOfAbsenceEnd:
                    headerInfo.ProcessID = ProcessIDLeaveOfAbsenceEnd;
                    break;

                case Pe2law.TranTypeValues.Terminate:
                    headerInfo.ProcessID = ProcessIDTerminate;
                    break;

                case Pe2law.TranTypeValues.TerminateCancel:
                    headerInfo.ProcessID = ProcessIDTerminateCancel;
                    break;

                case Pe2law.TranTypeValues.PasswordChange:
                    headerInfo.ProcessID = ProcessIDPasswordChange;
                    break;

                case Pe2law.TranTypeValues.Update:
                    headerInfo.ProcessID = ProcessIDUpdate;
                    break;
            }

            StoreAuthenticator storeAuthenticator = new StoreAuthenticator();
            Lawson.Employee.HeaderStore storeHeader = new Lawson.Employee.HeaderStore();
            storeHeader.EncryptedBytes = storeAuthenticator.Encrypt(headerInfo.ProcessID, headerInfo.StoreNumber, headerInfo.OperatorEmployeeNumber, key, iv);
            storeHeader.IPAddress = JIB.Framework.Store.ComputerIPAddress;
            Lawson.Employee.Employee employeeWebService = new Lawson.Employee.Employee();
            employeeWebService.HeaderStoreValue = storeHeader;

            try
            {
                switch (headerInfo.Method)
                {
                    case MethodPing:
                        employeeWebService.Ping(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.Hire:
                    case Pe2law.TranTypeValues.HireManager:
                    case Pe2law.TranTypeValues.HireDeprecated:
                        bool addEmployeeToWorkbrainFlag = false;
                        employeeWebService.HireOrRehire(ref employeeInfo, addEmployeeToWorkbrainFlag);
                        break;

                    case Pe2law.TranTypeValues.Hire2:
                        employeeWebService.HirePart2(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.TransferIn:
                        employeeWebService.TransferIn(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.TransferOut:
                        employeeWebService.TransferOut(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.Loan:
                        employeeWebService.Loan(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.Borrow:
                        employeeWebService.Borrow(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.LeaveOfAbsence:
                        employeeWebService.LeaveOfAbsence(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.LeaveOfAbsenceEnd:
                        employeeWebService.LeaveOfAbsenceEnd(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.Terminate:
                        employeeWebService.Terminate(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.TerminateCancel:
                        employeeWebService.TerminateCancel(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.PasswordChange:
                        employeeWebService.PasswordChange(ref employeeInfo);
                        break;

                    case Pe2law.TranTypeValues.Update:
                        employeeWebService.Update(ref employeeInfo);
                        break;

                    default:
                        throw new SystemException("Invalid Transaction Type: " + headerInfo.Method);
                }

                MessageBox.Show(string.Format(MsgBoxFormatLawson, headerInfo.Method,
                    Enum.GetName(typeof(ApplicationReturnValue), employeeInfo.ApplicationReturnValue),
                    employeeInfo.ApplicationReturnMessage1,
                    employeeInfo.ApplicationReturnMessage2,
                    employeeInfo.ApplicationReturnMessage3,
                    employeeInfo.ApplicationReturnMessage4,
                    Properties.Settings.Default.JIB_Personnel_Lawson_TestClient_Lawson_Employee));
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format(MsgBoxFormatException,
                    ex.Message,
                    ex.StackTrace,
                    Properties.Settings.Default.JIB_Personnel_Lawson_TestClient_Lawson_Employee));
            }
        }

        private void MapEmployee(ref Lawson.Employee.EmployeeInformation employeeInfo, ref HeaderInfo headerInfo)
        {
            string databaseFile = @"C:\JIB Data\Personnel\PE2LAW.DBF";
            string connectionString = @"Provider=vfpoledb;Data Source=" + databaseFile + ";Exclusive=false;Mode=ReadWrite|Share Deny None;Collating Sequence=MACHINE;Password=";
            JIB.Framework.Persistence.OleDb persistence = new JIB.Framework.Persistence.OleDb(connectionString);
            JIB.Personnel.Store.Data.BusinessLayer.Pe2law pe2law = new JIB.Personnel.Store.Data.BusinessLayer.Pe2law(persistence, headerInfo.RequestID);
            headerInfo.OperatorEmployeeNumber = pe2law.MgrLawID.ToString("000000000");
            headerInfo.Method = pe2law.TranType;

            if (pe2law.TranType != JIB.Personnel.Store.Data.PersistenceLayer.Pe2law.TranTypeValues.Hire)
            {
                // Add the passwords to the structure...
                // .. this sends string.Empty if nothing was passed as args...
                //employeeInfo.ManagerPassword = JIB.Personnel.Store.Console.LawsonShell.Encryption.Encrypt(managerPassword);
                //employeeInfo.Password = JIB.Personnel.Store.Console.LawsonShell.Encryption.Encrypt(employeePassword);
            }

            employeeInfo.RequestID = headerInfo.RequestID;

            // PE version...
            employeeInfo.PersonnelVersion = pe2law.PEVer;

            // Hire and general data...
            employeeInfo.Company = pe2law.Company.ToString();
            employeeInfo.PayCardNumber = pe2law.PayCardNo;
            employeeInfo.LocationCode = headerInfo.StoreNumber;
            employeeInfo.LocationCodeAlternate = pe2law.AltLoc;
            string scrambledSS = (long.Parse(pe2law.SS.Replace("-", "")) - 149986).ToString("***********");
            employeeInfo.FICANumber = JIB.Framework.Security.Encryption.EncryptRijndaelManaged(scrambledSS, key, iv);
            employeeInfo.FirstName = pe2law.FirstName;
            employeeInfo.LastName = pe2law.LastName;
            employeeInfo.MiddleName = pe2law.Middle;
            employeeInfo.JobCode = pe2law.JobCode;
            employeeInfo.GenderCode = pe2law.GenderCode;
            //Logging.WriteLog(logFile, "pe2law.EEOCode: " + pe2law.EEOCode);
            employeeInfo.RaceCode = pe2law.EEOCode;
            employeeInfo.PayRate = pe2law.HourlyPay;
            DateTime scrambledBD = pe2law.BirthDate.AddYears(3).AddMonths(5).AddDays(12);
            employeeInfo.BirthDate = ConvertDateToString(scrambledBD);
            employeeInfo.HiredDate = ConvertDateToString(pe2law.HireDate);
            employeeInfo.HiredDateOriginal = ConvertDateToString(pe2law.OrigDate);

            // For a manager, set the HireManagerFlag...
            employeeInfo.HireManagerFlag = 0;
            if (pe2law.TranType == JIB.Personnel.Store.Data.PersistenceLayer.Pe2law.TranTypeValues.HireManager)
                employeeInfo.HireManagerFlag = 1;

            if (!string.IsNullOrEmpty(ConvertDateToString(pe2law.JobEffDate)))
                employeeInfo.JobEffectiveDate = ConvertDateToString(DateTime.Today);
            if (!string.IsNullOrEmpty(ConvertDateToString(pe2law.PayEffDate)))
                employeeInfo.PayEffectiveDate = ConvertDateToString(DateTime.Today);
            employeeInfo.Address1 = pe2law.Address1;
            employeeInfo.Address2 = pe2law.Address2;
            employeeInfo.City = pe2law.City;
            employeeInfo.State = pe2law.State;
            employeeInfo.ZIP = pe2law.Zip;
            employeeInfo.Phone = pe2law.Phone;
            employeeInfo.WorkExperienceDate = ConvertDateToString(pe2law.WkExpDate);

            // I9 Data...
            employeeInfo.I9ExpirationDate = ConvertDateToString(pe2law.I9ExpDate);
            employeeInfo.Citizen = pe2law.Citizen;
            employeeInfo.NonCitizen = pe2law.NonCitizen;
            employeeInfo.Maiden = pe2law.Maiden;
            employeeInfo.Permanent = pe2law.Permanent;
            employeeInfo.Admission = pe2law.Admission;
            employeeInfo.I9ADescription = pe2law.I9A_Desc;
            employeeInfo.Doc1Number = pe2law.Doc1_No;
            employeeInfo.ExpirationDate1 = ConvertDateToString(pe2law.Exp_DT1);
            employeeInfo.Alien = pe2law.Alien;
            employeeInfo.AlienNumber = pe2law.Alien_No;
            employeeInfo.AlienExpirationDate = ConvertDateToString(pe2law.Alien_Exp);
            employeeInfo.IssueWho1 = pe2law.IssueWho1;
            employeeInfo.IssueWho2 = pe2law.IssueWho2;
            employeeInfo.Doc2Number = pe2law.Doc2_No;
            employeeInfo.ExpirationDate2 = ConvertDateToString(pe2law.Exp_DT2);
            employeeInfo.I9BDescription = pe2law.I9B_Desc;
            employeeInfo.I9CDescription = pe2law.I9C_Desc;
            employeeInfo.I9FormVersion = pe2law.I9FormVer;
            employeeInfo.I9FormDate = ConvertDateToString(pe2law.I9FormDate);
            employeeInfo.I9FormExpirationDate = ConvertDateToString(pe2law.I9FrmExpDt);

            DateTime signoffDateTime = new DateTime();

            if (pe2law.MgrI9Time.Length == 8)
            {
                signoffDateTime = new DateTime(pe2law.MgrI9Date.Year, pe2law.MgrI9Date.Month, pe2law.MgrI9Date.Day, Convert.ToInt32(pe2law.MgrI9Time.Substring(0, 2)), Convert.ToInt32(pe2law.MgrI9Time.Substring(3, 2)), Convert.ToInt32(pe2law.MgrI9Time.Substring(6, 2)));
            }
            employeeInfo.ManagerI9SignoffDateTime = ConvertDateToString(signoffDateTime);

            signoffDateTime = new DateTime();
            if (pe2law.EmpI9Time.Length == 8)
            {
                signoffDateTime = new DateTime(pe2law.EmpI9Date.Year, pe2law.EmpI9Date.Month, pe2law.EmpI9Date.Day, Convert.ToInt32(pe2law.EmpI9Time.Substring(0, 2)), Convert.ToInt32(pe2law.EmpI9Time.Substring(3, 2)), Convert.ToInt32(pe2law.EmpI9Time.Substring(6, 2)));
            }
            employeeInfo.EmployeeI9SignoffDateTime = ConvertDateToString(signoffDateTime);

            // W4 Data...
            employeeInfo.W4FormVersion = pe2law.W4FormVer;
            employeeInfo.W4FormDate = ConvertDateToString(pe2law.W4FormDate);

            signoffDateTime = new DateTime();
            if (pe2law.MgrW4Time.Length == 8)
            {
                signoffDateTime = new DateTime(pe2law.MgrW4Date.Year, pe2law.MgrW4Date.Month, pe2law.MgrW4Date.Day, Convert.ToInt32(pe2law.MgrW4Time.Substring(0, 2)), Convert.ToInt32(pe2law.MgrW4Time.Substring(3, 2)), Convert.ToInt32(pe2law.MgrW4Time.Substring(6, 2)));
            }
            employeeInfo.ManagerW4SignoffDateTime = ConvertDateToString(signoffDateTime);

            signoffDateTime = new DateTime();
            if (pe2law.EmpW4Time.Length == 8)
            {
                signoffDateTime = new DateTime(pe2law.EmpW4Date.Year, pe2law.EmpW4Date.Month, pe2law.EmpW4Date.Day, Convert.ToInt32(pe2law.EmpW4Time.Substring(0, 2)), Convert.ToInt32(pe2law.EmpW4Time.Substring(3, 2)), Convert.ToInt32(pe2law.EmpW4Time.Substring(6, 2)));
            }
            employeeInfo.EmployeeW4SignoffDateTime = ConvertDateToString(signoffDateTime);

            // General Manager Data...
            employeeInfo.EmployerID = pe2law.EmployerID;
            employeeInfo.GMHomeLocation = pe2law.GMHome;
            employeeInfo.GMLocation01 = pe2law.GMLoc1;
            employeeInfo.GMLocation02 = pe2law.GMLoc2;
            employeeInfo.GMLocation03 = pe2law.GMLoc3;
            employeeInfo.GMLocation04 = pe2law.GMLoc4;
            employeeInfo.GMLocation05 = pe2law.GMLoc5;
            employeeInfo.GMLocation06 = pe2law.GMLoc6;
            employeeInfo.GMLocation07 = pe2law.GMLoc7;
            employeeInfo.GMLocation08 = pe2law.GMLoc8;
            employeeInfo.GMLocation09 = pe2law.GMLoc9;
            employeeInfo.GMLocation10 = pe2law.GMLoc10;

            // Review Data...
            employeeInfo.Points = pe2law.Points;
            employeeInfo.Score = pe2law.Score;
            employeeInfo.ReviewDate = ConvertDateToString(pe2law.ReviewDate);
            employeeInfo.NoReview = pe2law.NoReview;

            employeeInfo.BadgeNumber = pe2law.BadgeID;
            employeeInfo.EmployeeNumber = pe2law.EmpNo.ToString();
            if (pe2law.TranType == JIB.Personnel.Store.Data.PersistenceLayer.Pe2law.TranTypeValues.Hire)
                employeeInfo.EmployeeNumber = "0";
            employeeInfo.ManagerBadgeNumber = pe2law.MgrBadgeID;

            // Terminate data...
            employeeInfo.LastDateWorked = ConvertDateToString(pe2law.LastDate);
            employeeInfo.TerminationAction = pe2law.TermAction;
            employeeInfo.TerminationReason = pe2law.TermReason;
            if (!string.IsNullOrEmpty(ConvertDateToString(pe2law.TermDate)))
                employeeInfo.TerminationDate = ConvertDateToString(DateTime.Today);
            employeeInfo.TerminationDatePrevious = ConvertDateToString(pe2law.PTermDT);
            employeeInfo.EligibleForRehireFlag = pe2law.EligRehire;

            // Termination comments and Injury LOA comments...
            employeeInfo.Comment1 = pe2law.Comment1;
            employeeInfo.Comment2 = pe2law.Comment2;
            employeeInfo.Comment3 = pe2law.Comment3;
            employeeInfo.Comment4 = pe2law.Comment4;

            // Transfer In data...
            employeeInfo.TransferInDate = ConvertDateToString(pe2law.TransInDT);
            employeeInfo.TransferFromStoreNumber = pe2law.TransFrom;
            employeeInfo.Address1 = pe2law.Address1;
            employeeInfo.Address2 = pe2law.Address2;
            employeeInfo.City = pe2law.City;
            employeeInfo.State = pe2law.State;
            employeeInfo.ZIP = pe2law.Zip;
            employeeInfo.Phone = pe2law.Phone;

            // Transfer Out data...
            employeeInfo.TransferToDate = ConvertDateToString(pe2law.TransToDT);
            employeeInfo.TransferToStoreNumber = pe2law.TransTo;

            // Borrow data...
            employeeInfo.BorrowFromStoreNumber = pe2law.BorFrom;
            employeeInfo.BorrowStartDate = ConvertDateToString(pe2law.BorStartDT);
            employeeInfo.BorrowEndDate = ConvertDateToString(pe2law.BorEndDate);

            // Leave of Absence additional data...
            employeeInfo.LOABeginDate = ConvertDateToString(pe2law.BegLOA_DT);
            employeeInfo.LOAEndDate = ConvertDateToString(pe2law.EndLOA_DT);
            employeeInfo.LOAType = pe2law.LOA_TX;
            employeeInfo.LOAWorkRelatedInjuryFlag = pe2law.Wrk_Injure;
            employeeInfo.LOAInjuryDate = ConvertDateToString(pe2law.Injure_DT);
            employeeInfo.LOAInjuryLastWorkedDate = ConvertDateToString(pe2law.InjLast_DT);
            employeeInfo.LOAInjuryFirstLightDutyDate = ConvertDateToString(pe2law.InjLit_DT);
            employeeInfo.LOAInjuryFirstFullDutyDate = ConvertDateToString(pe2law.InjFull_DT);
            employeeInfo.LOAReturnedFromDate = ConvertDateToString(pe2law.RetLOA_DT);

            // Additional Update data...
            employeeInfo.JobCodePrevious = pe2law.OldJobCode;
            employeeInfo.PayRatePrevious = pe2law.OldPay;
            // Do not send PayCard info from proxy client to web service plain text any longer.
            employeeInfo.PayCardNumberPrevious = pe2law.OldPayCard;
            // Leave plain text field blank/null and use new encrypted field.
            //employeeInfo.PayCardNumPreviousEncrypted = Encryption.Encrypt(pe2law.OldPayCard);
        }

        private static string ConvertDateToString(DateTime dateTime)
        {
            string convertDateToString = string.Empty;

            try
            {
                if (dateTime != new DateTime())
                {
                    convertDateToString = dateTime.ToString("yyyyMMddHHmmss");
                }
            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }

            return convertDateToString;
        }
        #endregion

        private void buttonPingAction_Click(object sender, EventArgs e)
        {
            InvokeWebSvc(new HeaderInfo() { StoreNumber = "000013", Method = MethodPing });
        }

        private void buttonNextAction_Click(object sender, EventArgs e)
        {
            InvokeWebSvc(new HeaderInfo() { StoreNumber = "000150" });
        }
    }
}
