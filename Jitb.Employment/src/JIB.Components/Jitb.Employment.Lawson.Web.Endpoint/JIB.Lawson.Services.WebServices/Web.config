﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="MessageForwardingInCaseOfFaultConfig" type="NServiceBus.Config.MessageForwardingInCaseOfFaultConfig, NServiceBus.Core" />
    <section name="UnicastBusConfig" type="NServiceBus.Config.UnicastBusConfig, NServiceBus.Core" />
    <section name="AuditConfig" type="NServiceBus.Config.AuditConfig, NServiceBus.Core" />
    <section name="TransportConfig" type="NServiceBus.Config.TransportConfig, NServiceBus.Core" />
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
    <section name="MasterNodeConfig" type="NServiceBus.Config.MasterNodeConfig, NServiceBus.Core" />
  </configSections>
  <system.web>
    <compilation debug="true" targetFramework="4.5.2" />
    <httpRuntime targetFramework="4.5" />
  </system.web>
  <connectionStrings>
    <add name="NServiceBus/Persistence" connectionString="Data Source=cssqlt01v\vtest1;Initial Catalog=dbNServiceBus;User Id=***********;Password=***********;" />
    <add name="Default" connectionString="Data Source=cssqlt01v\vtest1;Initial Catalog=dbNServiceBus;User Id=***********;Password=***********;" />
  </connectionStrings>
  <appSettings>
    <add key="LogFile" value="C:\JIB Logs\Lawson\%StoreNumber%.JIB.Lawson.Services.WebServices.log" />
    <add key="LogFileExceptions" value="C:\JIB Logs\Lawson\%StoreNumber%.JIB.Lawson.Services.WebServices.Exceptions.log" />
    <add key="SqlConnectStringAudit" value="Server=cssqlt01v\vtest1;Initial Catalog=WebServiceAudit;User Id=apps_user;Password=*******;" />
    <add key="SqlConnectStringLocation" value="Server=cssqlt01v\vtest1;Initial Catalog=dbLocation;User Id=locationread;Password=read;" />
    <add key="SqlConnectStringWorkbrain" value="Server=timekp-test-sql;Initial Catalog=RSWB01DV;User Id=*********;Password=*********;" />
    <add key="OracleConnectStringLawson" value="Provider=OraOLEDB.Oracle;Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=hris.corp.jitb.net)(PORT=1525))(CONNECT_DATA=(SID=LTR1)));User ID=ad;Password=********;" />
    <add key="OracleConnectStringWorkbrain" value="Provider=OraOLEDB.Oracle;Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=tkt1-ora-lh.corp.jitb.net)(PORT=1521))(CONNECT_DATA=(SID=WBTST1)));User Id=*********;Password=*********" />
    <add key="PublishStores" value="000084,003045,000013,003012,000099,009803,009804,009821" />
    <add key="PublishEnabled" value="false" />
    <add key="PublishMSMQName" value="FormatName:direct=os:JACKSOPS\private$\JIB.Lawson.Services.WebServices.EmployeeTransaction" />
    <add key="SnapshotEmailStore" value="false" />
    <add key="SnapshotEmailJobClasses" value="RORM36,RORM40,DVQS23,DVQS25,RORM10,RORM15,RORM20,RORM25,RORM30,DVQS20,DVQS15,RORM27,RORM32" />
    <add key="SnapshotEmailSubject" value="**TEST** Badge Number Report **TEST**" />
    <add key="SnapshotEmailFromAddress" value="<EMAIL>" />
    <add key="SnapshotEmailSmtpServer" value="jnotes5.intfmi.com" />
    <add key="SnapshotEmailStoreAddressIdentifier" value="@restaurant.jackinthebox.com" />
    <add key="FranchiseConversion.Email.JobClasses" value="RORM10,RORM15,RORM20,RORM23,RORM25,RORM27,RORM30,RORM32,RORM33,RORM36" />
    <add key="FranchiseConversion.Email.Subject" value="**TEST** Franchise Conversion Badge Number Report for Location %StoreNumber% in Region %Region% Area %Area%" />
    <add key="FranchiseConversion.Email.ToAddressTest" value="" />
    <add key="FranchiseConversion.Email.CcAddress" value="<EMAIL>" />
    <add key="FranchiseConversion.Email.FromAddress" value="<EMAIL>" />
    <add key="FranchiseConversion.Email.SmtpServer" value="notes3.intfmi.com" />
    <add key="FranchiseConversion.Email.StoreAddressIdentifier" value="/JBX/FMI" />
    <add key="IPRejectedIfMissing" value="false" />
    <!--Lab values are 010164-010168-->
    <add key="IPRejectionList" value="010130-010163,010169-999999" />
    <add key="AuthenticationEnabled" value="false" />
    <!--Delimit replacement phrases with |-->
    <add key="ReplacementPhrases" value="; OK to continue||Warning-||;OK To cont||; OK To cont|" />
    <add key="MessageException" value="An Unexpected Error was Returned" />
    <add key="MessageUnauthorized" value="Your Configuration Settings are Incorrect.  Please Contact your Support Help Desk." />
    <add key="MessageLawsonUnavailable" value="Lawson is Unavailable" />
    <add key="MessageLawsonUnexpectedResult" value="Lawson Returned an Unexpected Result" />
    <add key="Message.AuthenticationError.Manager" value="Manager's password is incorrect." />
    <add key="Message.AuthenticationError.Employee" value="Employee's password is incorrect." />
    <add key="Message.AuthenticationError.Both" value="Both Manager's and Employee's password are incorrect." />
    <add key="Message.AuthenticationError.Instructions" value="  Please re-enter your passwords.  If you need to re-create your password, exit Personnel and go create a new password with the Password Reset program." />
    <add key="LawsonIPAddress" value="pers-tst-web:90" />
    <add key="LawsonProxyID" value="webops" />
    <add key="LawsonProxyPassword" value="webops" />
    <add key="LawsonDataArea" value="LDV3" />
    <add key="LawsonBaseUrl" value="http://lawdev.corp.jitb.net:94/sso/SSOServlet" />
    <add key="LawsonTransactionUrl" value="http://lawdev.corp.jitb.net:94/servlet/Router/Transaction/Erp" />
    <add key="LawsonEmployeeAddFormTag" value="FM40.1" />
    <add key="LawsonEmployeeAddXMLNavigatorRoot" value="/XFM40.1/FM40.1/" />
    <add key="LawsonEmployeeHirePart2FormTag" value="FM41.1" />
    <add key="LawsonEmployeeHirePart2XMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeBorrowFormTag" value="FM41.1" />
    <add key="LawsonEmployeeBorrowXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeTerminateFormTag" value="FM41.1" />
    <add key="LawsonEmployeeTerminateXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeTerminateCancelFormTag" value="FM41.1" />
    <add key="LawsonEmployeeTerminateCancelXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeTransferInFormTag" value="FM41.1" />
    <add key="LawsonEmployeeTransferInXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeTransferOutFormTag" value="FM41.1" />
    <add key="LawsonEmployeeTransferOutXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeLeaveOfAbsenceFormTag" value="FM41.1" />
    <add key="LawsonEmployeeLeaveOfAbsenceXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeLeaveOfAbsenceEndFormTag" value="FM41.1" />
    <add key="LawsonEmployeeLeaveOfAbsenceEndXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeUpdateFormTag" value="FM41.1" />
    <add key="LawsonEmployeeUpdateXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeeQueryFormTag" value="FM45.1" />
    <add key="LawsonEmployeeQueryXMLNavigatorRoot" value="/XFM45.1/FM45.1/" />
    <add key="LawsonRestQueryFormTag" value="FM45.1" />
    <add key="LawsonRestQueryXMLNavigatorRoot" value="/XFM45.1/FM45.1/" />
    <add key="LawsonEmployeeSnapshotFormTag" value="FM41.1" />
    <add key="LawsonEmployeeSnapshotXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="LawsonEmployeePasswordChangeFormTag" value="FM41.1" />
    <add key="LawsonEmployeePasswordChangeXMLNavigatorRoot" value="/XFM41.1/FM41.1/" />
    <add key="StopTransmittingPlainTextPayCard" value="false" />
    <add key="CheckWorkbrainForLastDayWorked" value="false" />
    <add key="SendToNServiceBus" value="true" />
    <add key="CheckIPRejectionList" value="false" />
  </appSettings>
  <TransportConfig MaximumConcurrencyLevel="5" MaxRetries="5"  />
  <MessageForwardingInCaseOfFaultConfig ErrorQueue="error" />
  <UnicastBusConfig>
    <MessageEndpointMappings>
      <!-- Employment.Endpoint -->
      <add Namespace="Jitb.Employment.Contracts.Commands.Employment" Assembly="Jitb.Employment.Contracts" Endpoint="Jitb.Employment.Endpoint@CSNSBT01V" />
      <add Namespace="Jitb.Employment.Contracts.Events.Employment" Assembly="Jitb.Employment.Contracts" Endpoint="Jitb.Employment.Endpoint@CSNSBT01V" />
      <add Namespace="Jitb.Employment.Contracts.Messages.Employment" Assembly="Jitb.Employment.Contracts" Endpoint="Jitb.Employment.Endpoint@CSNSBT01V" />
    </MessageEndpointMappings>
  </UnicastBusConfig>
  <AuditConfig QueueName="audit" />
</configuration>