﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7DE1FCD8-CE00-461C-A24D-619DC5319FD2}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>JIB.Lawson.Services.WebServices</RootNamespace>
    <AssemblyName>JIB.Lawson.Services.WebServices</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="JIB.Framework">
      <HintPath>..\JibGac\2.0\JIB.Framework.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Caching, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7ba95ead34ac8c9d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\4.0\JibGac\2.0\JIB.Framework.Caching.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Persistence">
      <HintPath>..\JibGac\2.0\JIB.Framework.Persistence.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Security">
      <HintPath>..\JibGac\2.0\JIB.Framework.Security.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Framework.Services">
      <HintPath>..\JibGac\2.0\JIB.Framework.Services.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Lawson.Data, Version=4.0.5955.17337, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>App_Code\JIB.Lawson.Data.dll</HintPath>
    </Reference>
    <Reference Include="JIB.WebServiceAudit.Data, Version=2.0.5955.17337, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>App_Code\JIB.WebServiceAudit.Data.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Workbrain.Data, Version=1.0.5955.17337, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>App_Code\JIB.Workbrain.Data.dll</HintPath>
    </Reference>
    <Reference Include="JIB.Workbrain.Services, Version=2.0.5955.17338, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>App_Code\JIB.Workbrain.Services.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.Employment.Contracts">
      <HintPath>..\..\..\..\..\Repos\NServiceBus Integrations\Jitb.Employment\src\Jitb.Employment.Contracts\bin\Debug\Jitb.Employment.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.Employment.Lawson.Web.Commons">
      <HintPath>..\..\..\..\..\Repos\Jitb.Employment.Lawson.Web.Commons\bin\Debug\Jitb.Employment.Lawson.Web.Commons.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.NSB.Commons">
      <HintPath>..\..\..\..\..\Repos\NServiceBus Integrations\Jitb.Employment\src\Jitb.Employment.NServiceBus.Common\bin\Debug\Jitb.NSB.Commons.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="NServiceBus.Core">
      <HintPath>packages\NServiceBus.5.2.16\lib\net45\NServiceBus.Core.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap">
      <HintPath>..\..\..\..\..\Repos\NServiceBus Integrations\Jitb.Employment\src\Jitb.Employment.NServiceBus.Common\bin\Debug\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Messaging" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Code\JIB.Lawson.Data.dll" />
    <Content Include="App_Code\JIB.WebServiceAudit.Data.dll" />
    <Content Include="App_Code\JIB.Workbrain.Data.dll" />
    <Content Include="App_Code\JIB.Workbrain.Services.dll" />
    <Content Include="Employee.asmx" />
    <Content Include="Global.asax" />
    <Content Include="Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Employee.asmx.cs">
      <DependentUpon>Employee.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Encryption.cs" />
    <Compile Include="Enumerations.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>50351</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:50351/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>