﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.XPath;
using System.Net.Mail;
using System.IO;
using System.Security;
using System.Security.Permissions;
using System.Text;
using JIB.Framework.Utilities;
using JIB.Framework.Security.Services.WebServices.AuthenticationHeaders;
using JIB.Framework.Persistence;

using JIB.WebServiceAudit.Data.BusinessLayer;

using JIB.Workbrain.Services;
using JIB.Workbrain.Data.BusinessLayer.Oracle;
using System.Collections.Generic;
using System.Configuration;
using Jitb.Employment.Lawson.Web.Commons;

namespace JIB.Lawson.Services.WebServices
{
    /// <summary>
    /// Summary description for Employee
    /// </summary>
    [WebService(Namespace = "http://JackInTheBox.com/webservices/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [System.ComponentModel.ToolboxItem(false)]
    // To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
    // [System.Web.Script.Services.ScriptService]
    public class Employee : System.Web.Services.WebService
    {
        #region Constructor

        public Employee()
        {
            //CODEGEN: This call is required by the ASP.NET Web Services Designer
            InitializeComponent();
        }

        #endregion

        #region Component Designer generated code

        //Required by the Web Services Designer 
        private IContainer components = null;

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
        }

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion

        #region Private Parts

        public HeaderStore storeHeaderFromClient;
        private const string UnauthorizedAccess = "Unauthorized Access";
        private const int ZipCodeLengthToLawson = 5;
        private System.Net.CookieContainer CookieContainer = new System.Net.CookieContainer();

        #endregion

        #region Lawson Action Constants

        // Lawson Action constants...
        private const string LawsonActionPing = "PING";
        internal const string LawsonActionHire = "HIRE";
        internal const string LawsonActionHirePart2 = "HIRE2";
        private const string LawsonActionLoan = "LOAN";
        private const string LawsonActionBorrow = "BORROW";
        private const string LawsonActionTerminate = "TERM";
        private const string LawsonActionTerminateCancel = "TERMCANCEL";
        private const string LawsonActionTransferIn = "TRANSFERIN";
        private const string LawsonActionTransferOut = "TRANSFEROUT";
        internal const string LawsonActionLeaveOfAbsence = "LOA";
        private const string LawsonActionLeaveOfAbsenceEnd = "LOAEND";
        private const string LawsonActionPasswordChange = "PASSWORDCHANGE";
        private const string LawsonActionSnapshot = "SNAPSHOT";
        private const string LawsonActionUpdate = "UPDATE";
        private const string LawsonActionQuery = "EMPQUERY";
        private const string LawsonActionRestQuery = "RESTQUERY";

        #endregion

        #region Process IDs

        internal const string ProcessIDAdd = "C71E3D50-7C47-4e10-8C17-A6D0FF3EFDAA";
        const string ProcessIDTerminate = "F89ACF36-1B95-456b-A33C-10AA7885F859";
        const string ProcessIDTerminateCancel = "2F15D484-AB24-450d-9C5F-E924477AA484";
        const string ProcessIDLoan = "2A796025-97DA-486c-8483-737FC6E3D4E7";
        const string ProcessIDBorrow = "53988D11-DF28-4430-8A1C-E00663B3A6AD";
        const string ProcessIDTransferIn = "8DF7A220-6012-40ea-84D9-B9B5697D3532";
        const string ProcessIDTransferOut = "B2D00062-4021-48d4-AF2C-571B268CB77A";
        internal const string ProcessIDLeaveOfAbsence = "E8D45406-40E1-451c-828E-A51D6F95E494";
        const string ProcessIDLeaveOfAbsenceEnd = "********-62B1-4965-AE16-080E79A31398";
        const string ProcessIDPasswordChange = "343AF2AB-1DE0-4076-BD05-561BE46A530F";
        const string ProcessIDUpdate = "D4A47467-3F15-4687-940D-41AB26452ABF";
        const string ProcessIDSnapshot = "04BA1432-17AE-4680-BE81-1424BF0B5182";
        const string ProcessIDPing = "C71E3D50-7C47-4e10-8C17-A6D0FF3EFDAA";
        const string ProcessIDQuery = "7BB152D8-6E50-41c1-96B1-D6C2559417AA";

        #endregion

        #region Ping
        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void Ping(ref EmployeeInformation employeeInfo)
        {
            const string EVENT = "ADD";
            const string METHOD = "A";

            byte[] key = { 00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32 };
            byte[] iv = { 55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32 };

            string processID = "C71E3D50-7C47-4e10-8C17-A6D0FF3EFDAA";

            string logFile = System.Configuration.ConfigurationManager.AppSettings["LogFile"];
            if (logFile != null)
            {
                if (logFile.Length == 0)
                {
                    logFile = null;
                }
            }
            string logFileExceptions = System.Configuration.ConfigurationManager.AppSettings["LogFileExceptions"];
            if (logFileExceptions != null)
            {
                if (logFileExceptions.Length == 0)
                {
                    logFileExceptions = null;
                }
            }

            try
            {
                StoreAuthenticator storeAuthenticator = new StoreAuthenticator();
                storeAuthenticator.Decrypt(storeHeaderFromClient.EncryptedBytes, key, iv);

                // Set the log file to the store number to avoid log meshing...
                if (logFile != null)
                {
                    logFile = logFile.Replace("%StoreNumber%", storeAuthenticator.StoreNumber);
                }
                if (logFileExceptions != null)
                {
                    logFileExceptions = logFileExceptions.Replace("%StoreNumber%", storeAuthenticator.StoreNumber);
                }

                Logging.WriteLog(logFile, "PING - Start");

                Logging.WriteLog(logFile, "Assembly Version: " + AssemblyVersion);
                Logging.WriteLog(logFile, "Decrypted Header Store Number: " + storeAuthenticator.StoreNumber);
                Logging.WriteLog(logFile, "Decrypted Header Operator Employee Number: " + storeAuthenticator.EmployeeNumber);
                Logging.WriteLog(logFile, "Decrypted Header processID: " + storeAuthenticator.ProcessID);

                if (storeAuthenticator.ProcessID == processID)
                {
                    #region Invoke Lawson AGS and Get Response

                    string form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeAddFormTag"];
                    string lawsonBaseUrl = System.Configuration.ConfigurationManager.AppSettings["LawsonBaseUrl"];
                    string lawsonTransactionUrl = System.Configuration.ConfigurationManager.AppSettings["LawsonTransactionUrl"];
                    string lawsonProxyID = System.Configuration.ConfigurationManager.AppSettings["LawsonProxyID"];
                    string lawsonProxyPassword = System.Configuration.ConfigurationManager.AppSettings["LawsonProxyPassword"];
                    string lawsonDataArea = System.Configuration.ConfigurationManager.AppSettings["LawsonDataArea"];

                    System.Text.StringBuilder agsCall = new System.Text.StringBuilder(1000);
                    // base url...
                    agsCall.Append(lawsonBaseUrl);
                    // login parms...
                    agsCall.Append("?_action=LOGIN&_fromLoginPage=TRUE&_ssoUser=");
                    agsCall.Append(lawsonProxyID);
                    agsCall.Append("&_ssoPass=");
                    agsCall.Append(lawsonProxyPassword);
                    agsCall.Append("&_ssoOrigUrl=");

                    System.Text.StringBuilder originalUrl = new StringBuilder(500);
                    // Transaction url...
                    originalUrl.Append(lawsonTransactionUrl);
                    originalUrl.Append("?_PDL=");
                    originalUrl.Append(lawsonDataArea);

                    originalUrl.Append("&_TKN=");
                    originalUrl.Append(form);

                    originalUrl.Append("&_LFN=ALL");
                    originalUrl.Append("&_RTN=DATA");
                    originalUrl.Append("&_TDS=IGNORE");
                    originalUrl.Append("&_OUT=XML");

                    originalUrl.Append("&_EVT=");
                    originalUrl.Append(EVENT);

                    originalUrl.Append("&FC=");
                    originalUrl.Append(METHOD);

                    // Add data fields...
                    originalUrl.Append("&ACTION=");
                    originalUrl.Append(LawsonActionPing);

                    // Add terminator...
                    originalUrl.Append("&_EOT=TRUE");

                    originalUrl = ConvertSpecialToHex(originalUrl);
                    agsCall.Append(originalUrl);

                    // Add trailer stuff...
                    agsCall.Append("&_serviceName=SSOP");

                    Logging.WriteLog(logFile, "AGS Call: " + agsCall.ToString());

                    System.Net.HttpWebRequest webRequest = (System.Net.HttpWebRequest)System.Net.HttpWebRequest.Create(agsCall.ToString());
                    webRequest.Accept = "text/xml,application/xml,text/html";
                    webRequest.UserAgent = "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; InfoPath.1)";
                    webRequest.Method = "GET";
                    webRequest.CookieContainer = CookieContainer;

                    System.Net.HttpWebResponse webResponse = null;

                    try
                    {
                        webResponse = (System.Net.HttpWebResponse)webRequest.GetResponse();
                        Logging.WriteLog(logFile, "AGS Call Complete");
                    }

                    catch (Exception e)
                    {
                        Logging.WriteLog(logFile, e.Message);
                        Logging.WriteLog(logFileExceptions, e.Message);
                        Logging.WriteLog(logFile, "Exception: Lawson is Unavailable");
                        Logging.WriteLog(logFileExceptions, "Exception: Lawson is Unavailable");
                        throw new SystemException("Exception: Lawson is Unavailable", e);
                    }

                    #endregion

                    #region Put Web Response in XML Document

                    string lawsonReturnMessage = "";
                    int lawsonReturnValue = 0;

                    try
                    {
                        string navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeAddXMLNavigatorRoot"];

                        Logging.WriteLog(logFile, "XML Root from config: " + navigatorRoot);
                        System.Xml.XmlTextReader xmlTextReader = new System.Xml.XmlTextReader(webResponse.GetResponseStream());
                        Logging.WriteLog(logFile, "XML Text Reader Read: " + xmlTextReader);

                        XPathDocument configDocument = new XPathDocument(xmlTextReader);
                        Logging.WriteLog(logFile, "XML Document Loaded");
                        XPathNavigator configNav = configDocument.CreateNavigator();

                        // Lawson error and message...
                        XPathNodeIterator configIter = configNav.Select(navigatorRoot + "MsgNbr");
                        string elementValue = GetElementValue(configIter);
                        lawsonReturnValue = System.Convert.ToInt32(elementValue);
                        Logging.WriteLog(logFile, "lawsonReturnValue: " + lawsonReturnValue);

                        configIter = configNav.Select(navigatorRoot + "Message");
                        elementValue = GetElementValue(configIter);
                        lawsonReturnMessage = elementValue;
                        Logging.WriteLog(logFile, "lawsonReturnMessage: " + lawsonReturnMessage);
                    }

                    catch (Exception e)
                    {
                        Logging.WriteLog(logFile, "Exception: Lawson returned an unexpected result");
                        Logging.WriteLog(logFileExceptions, "Exception: Lawson returned an unexpected result");
                        throw new SystemException("Exception: Lawson returned an unexpected result", e);
                    }

                    finally
                    {
                        employeeInfo.ApplicationReturnValue = lawsonReturnValue;
                        employeeInfo.ApplicationReturnMessage1 = lawsonReturnMessage;
                    }
                    #endregion
                }
                else
                {
                    employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                    employeeInfo.ApplicationReturnMessage1 = UnauthorizedAccess;
                }
            }

            catch (Exception e)
            {
                employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                employeeInfo.ApplicationReturnMessage1 = e.Message;
                Logging.WriteLog(logFile, "Exception: " + e.Message);
                Logging.WriteLog(logFileExceptions, "Exception: " + e.Message);
            }

            finally
            {
                Logging.WriteLog(logFile, "Ping - Complete");
            }


        }

        #endregion

        #region HireOrRehire Employee
        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void HireOrRehire(ref EmployeeInformation employeeInfo, bool addEmployeeToWorkbrain)
        {
            ProcessHireorRehire(ref employeeInfo, addEmployeeToWorkbrain);
        }
        internal void ProcessHireorRehire(ref EmployeeInformation employeeInfo, bool addEmployeeToWorkbrain)
        {
            const string EVENT = "ADD";
            const string METHOD = "A";
            const string ConceptJack = "J";

            SqlClient persistenceWebServiceAudit = null;
            JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog webServiceLog = null;

            byte[] key = { 00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32 };
            byte[] iv = { 55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32 };

            string processID = ProcessIDAdd;

            employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
            employeeInfo.ApplicationReturnMessage1 = "";
            employeeInfo.ApplicationReturnMessage2 = "";
            employeeInfo.ApplicationReturnMessage3 = "";
            employeeInfo.ApplicationReturnMessage4 = "";
            employeeInfo.ApplicationReturnMessage5 = "";

            string logFile = System.Configuration.ConfigurationManager.AppSettings["LogFile"];
            if (logFile != null)
            {
                if (logFile.Length == 0)
                {
                    logFile = null;
                }
            }
            string logFileExceptions = System.Configuration.ConfigurationManager.AppSettings["LogFileExceptions"];
            if (logFileExceptions != null)
            {
                if (logFileExceptions.Length == 0)
                {
                    logFileExceptions = null;
                }
            }

            try
            {
                string messageException = System.Configuration.ConfigurationManager.AppSettings["MessageException"];
                string messageUnauthorized = System.Configuration.ConfigurationManager.AppSettings["MessageUnauthorized"];
                string messageLawsonUnavailable = System.Configuration.ConfigurationManager.AppSettings["MessageLawsonUnavailable"];
                string messageLawsonUnexpectedResult = System.Configuration.ConfigurationManager.AppSettings["MessageLawsonUnexpectedResult"];
                string replacementPhraseFromConfig = System.Configuration.ConfigurationManager.AppSettings["ReplacementPhrases"];
                string[] replacementPhrases = replacementPhraseFromConfig.Split("|".ToCharArray());

                string ipRejectedIfMissingFlag = System.Configuration.ConfigurationManager.AppSettings["IPRejectedIfMissing"];
                bool ipRejectedIfMissing = true;
                if (ipRejectedIfMissingFlag != null)
                {
                    if (ipRejectedIfMissingFlag.Length > 0)
                    {
                        if (string.Compare(ipRejectedIfMissingFlag, "false", true) == 0)
                        {
                            ipRejectedIfMissing = false;
                        }
                    }
                }

                string sqlConnectStringAudit = System.Configuration.ConfigurationManager.AppSettings["SqlConnectStringAudit"];

                persistenceWebServiceAudit = new SqlClient(sqlConnectStringAudit);
                webServiceLog = new JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog(persistenceWebServiceAudit);

                webServiceLog.StartDate = DateTime.Now;
                webServiceLog.AssemblyVersion = AssemblyVersion;

                StoreAuthenticator storeAuthenticator = new StoreAuthenticator();
                storeAuthenticator.Decrypt(storeHeaderFromClient.EncryptedBytes, key, iv);

                // Set the log file to the store number to avoid log meshing...
                if (logFile != null)
                {
                    logFile = logFile.Replace("%StoreNumber%", storeAuthenticator.StoreNumber);
                }
                if (logFileExceptions != null)
                {
                    logFileExceptions = logFileExceptions.Replace("%StoreNumber%", storeAuthenticator.StoreNumber);
                }

                Logging.WriteLog(logFile, "HIRE - Start");

                Logging.WriteLog(logFile, "Assembly Version: " + AssemblyVersion);
                Logging.WriteLog(logFile, "Decrypted Header Store Number: " + storeAuthenticator.StoreNumber);
                Logging.WriteLog(logFile, "Decrypted Header Operator Employee Number: " + storeAuthenticator.EmployeeNumber);
                Logging.WriteLog(logFile, "Decrypted Header processID: " + storeAuthenticator.ProcessID);

                webServiceLog.CompanyNumber = employeeInfo.Company;
                webServiceLog.StoreNumber = storeAuthenticator.StoreNumber;
                webServiceLog.ServiceCode = (short)JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog.ServiceCodeValues.EmployeeAdd;
                webServiceLog.LawsonNumber = storeAuthenticator.EmployeeNumber;

                // Check IP Authentication...
                Logging.WriteLog(logFile, "StoreAuthenticator IP: " + storeHeaderFromClient.IPAddress);
                bool authenticateIP = AttemptToAuthenticateIP(logFile);

                if (ipRejectedIfMissing == false)
                {
                    if (authenticateIP == false)
                    {
                        if (storeHeaderFromClient.IPAddress == null)
                        {
                            authenticateIP = true;
                        }
                        else
                        {
                            if (storeHeaderFromClient.IPAddress.Length == 0)
                            {
                                authenticateIP = true;
                            }
                        }
                    }
                }

                if (authenticateIP == true)
                {
                    if (storeAuthenticator.ProcessID == processID)
                    {
                        Logging.WriteLog(logFile, "Parameters...");
                        Logging.WriteLog(logFile, "RequestID: " + employeeInfo.RequestID);

                        #region Invoke Lawson AGS and Get Response

                        string form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeAddFormTag"];
                        string lawsonBaseUrl = System.Configuration.ConfigurationManager.AppSettings["LawsonBaseUrl"];
                        string lawsonTransactionUrl = System.Configuration.ConfigurationManager.AppSettings["LawsonTransactionUrl"];
                        string lawsonProxyID = System.Configuration.ConfigurationManager.AppSettings["LawsonProxyID"];
                        string lawsonProxyPassword = System.Configuration.ConfigurationManager.AppSettings["LawsonProxyPassword"];
                        string lawsonDataArea = System.Configuration.ConfigurationManager.AppSettings["LawsonDataArea"];

                        System.Text.StringBuilder agsCall = new System.Text.StringBuilder(1000);
                        // base url...
                        agsCall.Append(lawsonBaseUrl);
                        // login parms...
                        agsCall.Append("?_action=LOGIN&_fromLoginPage=TRUE&_ssoUser=");
                        agsCall.Append(lawsonProxyID);
                        agsCall.Append("&_ssoPass=");
                        agsCall.Append(lawsonProxyPassword);
                        agsCall.Append("&_ssoOrigUrl=");

                        System.Text.StringBuilder originalUrl = new StringBuilder(500);
                        // Transaction url...
                        originalUrl.Append(lawsonTransactionUrl);
                        originalUrl.Append("?_PDL=");
                        originalUrl.Append(lawsonDataArea);

                        originalUrl.Append("&_TKN=");
                        originalUrl.Append(form);

                        originalUrl.Append("&_LFN=ALL");
                        originalUrl.Append("&_RTN=DATA");
                        originalUrl.Append("&_TDS=IGNORE");
                        originalUrl.Append("&_OUT=XML");

                        originalUrl.Append("&_EVT=");
                        originalUrl.Append(EVENT);

                        originalUrl.Append("&FC=");
                        originalUrl.Append(METHOD);

                        // Add data fields...
                        originalUrl.Append("&ACTION=");
                        originalUrl.Append(LawsonActionHire);

                        if (employeeInfo.EmployeeNumber != null)
                        {
                            if (employeeInfo.EmployeeNumber.Length > 0)
                            {
                                originalUrl.Append("&EMP-EMPLOYEE=");
                                originalUrl.Append(employeeInfo.EmployeeNumber);
                            }
                        }

                        originalUrl.Append("&EMP-FICA-NBR=");
                        string socialSecurityNumber = JIB.Framework.Security.Encryption.DecryptRijndaelManaged(employeeInfo.FICANumber, key, iv).Trim();
                        originalUrl.Append(socialSecurityNumber);

                        originalUrl.Append("&EMP-LAST-NAME=");
                        originalUrl.Append(employeeInfo.LastName);

                        originalUrl.Append("&EMP-FIRST-NAME=");
                        originalUrl.Append(employeeInfo.FirstName);

                        originalUrl.Append("&EMP-MIDDLE-NAME=");
                        originalUrl.Append(employeeInfo.MiddleName);

                        originalUrl.Append("&PEM-LOCAT-CODE=");
                        originalUrl.Append(employeeInfo.LocationCode);

                        if (employeeInfo.BorrowFromStoreNumber == null)
                        {
                            employeeInfo.BorrowFromStoreNumber = string.Empty;
                        }
                        if (employeeInfo.BorrowFromStoreNumber.Length > 0)
                        {
                            originalUrl.Append("&ALT-LOCAT-CODE=");
                            originalUrl.Append(employeeInfo.BorrowFromStoreNumber);
                        }

                        if (employeeInfo.JobCode == null)
                        {
                            employeeInfo.JobCode = string.Empty;
                        }
                        if (employeeInfo.JobCode.Length > 0)
                        {
                            originalUrl.Append("&EMP-JOB-CODE=");
                            originalUrl.Append(employeeInfo.JobCode);
                        }

                        if (employeeInfo.PayRate > 0)
                        {
                            originalUrl.Append("&EMP-PAY-RATE=");
                            originalUrl.Append(employeeInfo.PayRate);
                        }

                        // Hire pay card
                        if (employeeInfo.PayCardNumber == null)
                        {
                            employeeInfo.PayCardNumber = string.Empty;
                        }
                        if (employeeInfo.PayCardNumEncrypted == null)
                        {
                            employeeInfo.PayCardNumEncrypted = string.Empty;
                        }
                        if (employeeInfo.PayCardNumber.Length > 0)
                        {
                            Logging.WriteLog(logFile, "Client is still sending clear text PayCardNumber during Hire");
                            // Web service must still support old proxy clients so continue to evaluate these fields.
                            originalUrl.Append("&PAY-CARD-NBR=");
                            originalUrl.Append(employeeInfo.PayCardNumber);
                        }
                        else if (employeeInfo.PayCardNumEncrypted.Length > 0)
                        {
                            Logging.WriteLog(logFile, string.Format("Client is sending PayCardNumberEncrypted {0} during Hire", employeeInfo.PayCardNumEncrypted));
                            // New proxy client will only send PayCard data via encrypted fields. Lawson AGS expects it decrypted.
                            string payCardNum = Encryption.Decrypt(employeeInfo.PayCardNumEncrypted);
                            originalUrl.Append("&PAY-CARD-NBR=");
                            originalUrl.Append(payCardNum);
                        }

                        if (employeeInfo.GenderCode == null)
                        {
                            employeeInfo.GenderCode = string.Empty;
                        }
                        if (employeeInfo.GenderCode.Length > 0)
                        {
                            originalUrl.Append("&PEM-SEX=");
                            originalUrl.Append(employeeInfo.GenderCode);
                        }

                        if (employeeInfo.RaceCode == null)
                        {
                            employeeInfo.RaceCode = string.Empty;
                        }
                        if (employeeInfo.RaceCode.Length > 0)
                        {
                            originalUrl.Append("&PEM-EEO-CLASS=");
                            originalUrl.Append(employeeInfo.RaceCode);
                        }

                        originalUrl.Append("&PEM-BIRTHDATE=");
                        originalUrl.Append(ConvertStringToDate(employeeInfo.BirthDate).ToString("yyyyMMdd"));

                        if (ConvertStringToDate(employeeInfo.HiredDate) != new System.DateTime())
                        {
                            originalUrl.Append("&EMP-DATE-HIRED=");
                            originalUrl.Append(ConvertStringToDate(employeeInfo.HiredDate).ToString("yyyyMMdd"));
                        }

                        originalUrl.Append("&EFFECT-DATE=");
                        originalUrl.Append(System.DateTime.Now.ToString("yyyyMMdd"));

                        if (storeAuthenticator.EmployeeNumber.Length > 0)
                        {
                            originalUrl.Append("&MGR-EMPLOYEE=");
                            originalUrl.Append(storeAuthenticator.EmployeeNumber);
                        }

                        // MGR-LOCAT-CODE is either the calling location or the GM Location...
                        string mgrLocationCode = string.Empty;
                        if (employeeInfo.LocationCode != null)
                        {
                            if (employeeInfo.LocationCode.Length > 0)
                            {
                                mgrLocationCode = employeeInfo.LocationCode;
                            }
                        }
                        // Override with the GM Home if passed...
                        if (employeeInfo.GMHomeLocation != null)
                        {
                            if (employeeInfo.GMHomeLocation.Length > 0)
                            {
                                mgrLocationCode = employeeInfo.GMHomeLocation;
                            }
                        }
                        if (mgrLocationCode.Length > 0)
                        {
                            originalUrl.Append("&MGR-LOCAT-CODE=");
                            originalUrl.Append(mgrLocationCode);
                        }

                        // GM location codes...
                        if (employeeInfo.GMLocation01 != null)
                        {
                            if (employeeInfo.GMLocation01.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE1=");
                                originalUrl.Append(employeeInfo.GMLocation01);
                            }
                        }
                        if (employeeInfo.GMLocation02 != null)
                        {
                            if (employeeInfo.GMLocation02.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE2=");
                                originalUrl.Append(employeeInfo.GMLocation02);
                            }
                        }
                        if (employeeInfo.GMLocation03 != null)
                        {
                            if (employeeInfo.GMLocation03.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE3=");
                                originalUrl.Append(employeeInfo.GMLocation03);
                            }
                        }
                        if (employeeInfo.GMLocation04 != null)
                        {
                            if (employeeInfo.GMLocation04.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE4=");
                                originalUrl.Append(employeeInfo.GMLocation04);
                            }
                        }
                        if (employeeInfo.GMLocation05 != null)
                        {
                            if (employeeInfo.GMLocation05.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE5=");
                                originalUrl.Append(employeeInfo.GMLocation05);
                            }
                        }
                        if (employeeInfo.GMLocation06 != null)
                        {
                            if (employeeInfo.GMLocation06.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE6=");
                                originalUrl.Append(employeeInfo.GMLocation06);
                            }
                        }
                        if (employeeInfo.GMLocation07 != null)
                        {
                            if (employeeInfo.GMLocation07.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE7=");
                                originalUrl.Append(employeeInfo.GMLocation07);
                            }
                        }
                        if (employeeInfo.GMLocation08 != null)
                        {
                            if (employeeInfo.GMLocation08.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE8=");
                                originalUrl.Append(employeeInfo.GMLocation08);
                            }
                        }
                        if (employeeInfo.GMLocation09 != null)
                        {
                            if (employeeInfo.GMLocation09.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE9=");
                                originalUrl.Append(employeeInfo.GMLocation09);
                            }
                        }
                        if (employeeInfo.GMLocation10 != null)
                        {
                            if (employeeInfo.GMLocation10.Length > 0)
                            {
                                originalUrl.Append("&GM-LOCAT-CODE10=");
                                originalUrl.Append(employeeInfo.GMLocation10);
                            }
                        }

                        originalUrl.Append("&MGR-DATE=");
                        originalUrl.Append(DateTime.Now.ToString("yyyyMMdd"));

                        originalUrl.Append("&MGR-TIME=");
                        originalUrl.Append(DateTime.Now.ToString("HH:mm:ss"));

                        // Add terminator...
                        originalUrl.Append("&_EOT=TRUE");

                        originalUrl.Replace("#", string.Empty);
                        originalUrl = ConvertSpecialToHex(originalUrl);
                        agsCall.Append(originalUrl);

                        // Add trailer stuff...
                        agsCall.Append("&_serviceName=SSOP");

                        Logging.WriteLog(logFile, "AGS Call: " + agsCall.ToString());

                        System.Net.HttpWebRequest webRequest = (System.Net.HttpWebRequest)System.Net.HttpWebRequest.Create(agsCall.ToString());
                        webRequest.Accept = "text/xml,application/xml,text/html";
                        webRequest.UserAgent = "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; InfoPath.1)";
                        webRequest.Method = "GET";
                        webRequest.CookieContainer = CookieContainer;

                        System.Net.HttpWebResponse webResponse = null;

                        try
                        {
                            webResponse = (System.Net.HttpWebResponse)webRequest.GetResponse();
                            Logging.WriteLog(logFile, "AGS Call Complete");
                        }

                        catch (Exception e)
                        {
                            Logging.WriteLog(logFile, "Exception: " + messageLawsonUnavailable);
                            Logging.WriteLog(logFileExceptions, "Exception: " + messageLawsonUnavailable);
                            throw new SystemException(messageLawsonUnavailable, e);
                        }

                        #endregion

                        #region Put Web Response in XML Document, Get Results and Add To Workbrain

                        int returnCode = (int)ApplicationReturnValue.Failure;
                        int lawsonReturnValue = 0;
                        string applicationReturnMessage1 = "";
                        string applicationReturnMessage2 = "";
                        string applicationReturnMessage3 = "";
                        string applicationReturnMessage4 = "";
                        string applicationReturnMessage5 = "";
                        string lawsonReturnMessage = "";

                        try
                        {
                            string navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeAddXMLNavigatorRoot"];

                            Logging.WriteLog(logFile, "XML Root from config: " + navigatorRoot);
                            System.Xml.XmlTextReader xmlTextReader = new System.Xml.XmlTextReader(webResponse.GetResponseStream());
                            Logging.WriteLog(logFile, "XML Text Reader Read: " + xmlTextReader);

                            //						System.Xml.XmlTextReader xmlTextReaderDump = null;
                            //						xmlTextReaderDump = xmlTextReader;
                            //						while (xmlTextReader.Read())
                            //						{
                            //							switch (xmlTextReaderDump.NodeType)
                            //							{
                            //								case System.Xml.XmlNodeType.Element: // The node is an element.
                            //									Logging.WriteLog(logFile, "<" + xmlTextReaderDump.Name);
                            //									Logging.WriteLog(logFile, ">");
                            //									break;
                            //								case System.Xml.XmlNodeType.Text: //Display the text in each element.
                            //									Logging.WriteLog(logFile, xmlTextReaderDump.Value);
                            //									break;
                            //								case System.Xml.XmlNodeType.EndElement: //Display the end of the element.
                            //									Logging.WriteLog(logFile, "</" + xmlTextReaderDump.Name);
                            //									Logging.WriteLog(logFile, ">");
                            //									break;
                            //							}
                            //						}

                            XPathDocument configDocument = new XPathDocument(xmlTextReader);
                            Logging.WriteLog(logFile, "XML Document Loaded");
                            XPathNavigator configNav = configDocument.CreateNavigator();

                            employeeInfo.Company = "";
                            XPathNodeIterator configIter = configNav.Select(navigatorRoot + "EMP-COMPANY");
                            Logging.WriteLog(logFile, "Company Found");
                            string elementValue = GetElementValue(configIter);
                            employeeInfo.Company = elementValue;

                            employeeInfo.EmployeeNumber = "";
                            configIter = configNav.Select(navigatorRoot + "EMP-EMPLOYEE");
                            elementValue = GetElementValue(configIter);
                            employeeInfo.EmployeeNumber = elementValue;

                            configIter = configNav.Select(navigatorRoot + "EMP-FICA-NBR");
                            elementValue = GetElementValue(configIter);
                            employeeInfo.FICANumber = JIB.Framework.Security.Encryption.EncryptRijndaelManaged(elementValue, key, iv); ;

                            employeeInfo.BadgeNumber = "";
                            configIter = configNav.Select(navigatorRoot + "PEM-SECURITY-NBR");
                            elementValue = GetElementValue(configIter);
                            employeeInfo.BadgeNumber = elementValue;

                            // This will be a 21 (hire) or 22 (rehire)...
                            employeeInfo.StatusCode = "";
                            configIter = configNav.Select(navigatorRoot + "EMP-EMP-STATUS");
                            elementValue = GetElementValue(configIter);
                            employeeInfo.StatusCode = elementValue;

                            // Custom return code - 1=success, might be empty...
                            configIter = configNav.Select(navigatorRoot + "RETURN-CODE");
                            elementValue = GetElementValue(configIter);
                            returnCode = (int)ApplicationReturnValue.Failure;
                            if (elementValue.Length > 0)
                            {
                                returnCode = System.Convert.ToInt32(elementValue);
                            }

                            configIter = configNav.Select(navigatorRoot + "EMP-DATE-HIRED");
                            elementValue = GetElementValue(configIter);
                            if (elementValue.Length > 0)
                            {
                                Logging.WriteLog(logFile, "EMP-DATE-HIRED: " + elementValue);
                                employeeInfo.HiredDate = ConvertDateToString(ConvertStringToDate(elementValue));
                            }
                            else
                            {
                                employeeInfo.HiredDate = ConvertDateToString(new DateTime());
                            }

                            configIter = configNav.Select(navigatorRoot + "EMP-ADJ-HIRE-DATE");
                            elementValue = GetElementValue(configIter);
                            if (elementValue.Length > 0)
                            {
                                Logging.WriteLog(logFile, "EMP-ADJ-HIRE-DATE: " + elementValue);
                                employeeInfo.HiredDateOriginal = ConvertDateToString(ConvertStringToDate(elementValue));
                            }
                            else
                            {
                                employeeInfo.HiredDateOriginal = ConvertDateToString(new DateTime());
                            }

                            configIter = configNav.Select(navigatorRoot + "EMP-TERM-DATE");
                            elementValue = GetElementValue(configIter);
                            if (elementValue.Length > 0)
                            {
                                Logging.WriteLog(logFile, "EMP-TERM-DATE: " + elementValue);
                                employeeInfo.TerminationDate = ConvertDateToString(ConvertStringToDate(elementValue));
                            }
                            else
                            {
                                employeeInfo.TerminationDate = ConvertDateToString(new DateTime());
                            }

                            // Lawson error and message...
                            configIter = configNav.Select(navigatorRoot + "MsgNbr");
                            elementValue = GetElementValue(configIter);
                            lawsonReturnValue = System.Convert.ToInt32(elementValue);

                            configIter = configNav.Select(navigatorRoot + "Message");
                            elementValue = GetElementValue(configIter);
                            lawsonReturnMessage = elementValue;

                            // Matrix error and message...
                            configIter = configNav.Select(navigatorRoot + "FM0-EDIT-NBR");
                            elementValue = GetElementValue(configIter);
                            int applicationReturnValue = System.Convert.ToInt32(elementValue);

                            configIter = configNav.Select(navigatorRoot + "FM0-EDIT-MESSAGE1");
                            elementValue = GetElementValue(configIter);
                            applicationReturnMessage1 = elementValue;

                            configIter = configNav.Select(navigatorRoot + "FM0-EDIT-MESSAGE2");
                            elementValue = GetElementValue(configIter);
                            applicationReturnMessage2 = elementValue;

                            configIter = configNav.Select(navigatorRoot + "FM0-EDIT-MESSAGE3");
                            elementValue = GetElementValue(configIter);
                            applicationReturnMessage3 = elementValue;

                            employeeInfo.ApplicationReturnMessage4 = "";
                            configIter = configNav.Select(navigatorRoot + "FM0-EDIT-MESSAGE4");
                            elementValue = GetElementValue(configIter);
                            applicationReturnMessage4 = elementValue;

                            employeeInfo.ApplicationReturnMessage5 = "";
                            configIter = configNav.Select(navigatorRoot + "FM0-EDIT-MESSAGE5");
                            elementValue = GetElementValue(configIter);
                            applicationReturnMessage5 = elementValue;

                            // Publish the transaction...
                            if (returnCode == (int)ApplicationReturnValue.Success)
                            {
                                if (employeeInfo.HireManagerFlag == 1)
                                {
                                    Logging.WriteLog(logFile, "Publishing Transaction");
                                    Publish(LawsonActionHire, employeeInfo, logFile);
                                    Logging.WriteLog(logFile, "Publishing Complete");
                                }

                                string lawsonAction = LawsonActionHire;
                                AttemptSendToNServiceBus(lawsonAction, employeeInfo, logFile, logFileExceptions);
                            }

                            #region Add Records To Workbrain

                            // Invoke the Workbrain API to add the person to the Workbrain database...
                            if (addEmployeeToWorkbrain == true)
                            {
                                if (returnCode == (int)ApplicationReturnValue.Success)
                                {
                                    Logging.WriteLog(logFile, "Lawson hire successful...adding to Workbrain");

                                    HRRefresh.Employee employee = new JIB.Workbrain.Services.HRRefresh.Employee();

                                    employee.StartDate = DateTime.Now.Date;
                                    // Fix this later...Workbrain requirement...
                                    employee.StartDate = Convert.ToDateTime("02/01/2005");

                                    // Need leading zeroes...
                                    employee.Company = System.Convert.ToInt32(employeeInfo.Company).ToString("0000");
                                    employee.LawsonID = System.Convert.ToInt32(employeeInfo.EmployeeNumber).ToString("*********");

                                    employee.NameLast = employeeInfo.LastName;
                                    employee.NameFirst = employeeInfo.FirstName;

                                    // Shift...
                                    configIter = configNav.Select(navigatorRoot + "SHFTPAT-NAME");
                                    elementValue = GetElementValue(configIter);
                                    employee.Shift = elementValue;

                                    // Calc Group...
                                    configIter = configNav.Select(navigatorRoot + "PEM-MAIL-GROUP");
                                    elementValue = GetElementValue(configIter);
                                    employee.CalcGroup = elementValue;

                                    // Pay Group...
                                    configIter = configNav.Select(navigatorRoot + "PAYGRP-NAME");
                                    elementValue = GetElementValue(configIter);
                                    employee.PayGroup = elementValue;

                                    employee.HireDate = ConvertStringToDate(employeeInfo.HiredDate);

                                    // Get the Adjusted Hired Date...format is YYYYMMDD
                                    configIter = configNav.Select(navigatorRoot + "EMP-ADJ-HIRE-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length == 8)
                                    {
                                        employee.AdjustedHireDate = ConvertStringToDate(elementValue);
                                    }
                                    else
                                    {
                                        employee.AdjustedHireDate = ConvertStringToDate(employeeInfo.HiredDate);
                                    }
                                    employee.BirthDate = ConvertStringToDate(employeeInfo.BirthDate);

                                    // Status="A" for a hire...
                                    employee.Status = "A";

                                    // FICA Number...
                                    configIter = configNav.Select(navigatorRoot + "EMP-FICA-NBR");
                                    elementValue = GetElementValue(configIter);
                                    employee.FICANumber = elementValue;

                                    // Security Code and Supervisor Code...
                                    configIter = configNav.Select(navigatorRoot + "PEM-SECURITY-CODE");
                                    elementValue = GetElementValue(configIter);
                                    employee.SecurityCode = elementValue;
                                    switch (employee.SecurityCode)
                                    {
                                        case "LEVEL 3":
                                        case "LEVEL 4":
                                            employee.SupervisorCode = 1;
                                            break;

                                        default:
                                            employee.SupervisorCode = 0;
                                            break;
                                    }

                                    employee.Val4 = "T";

                                    employee.BadgeNumber = employeeInfo.BadgeNumber;

                                    employee.EDLAPercentage = 1000;
                                    employee.EDLADocketName = "NULL";
                                    employee.EDLAHourTypeName = "UNPAID";
                                    employee.EDLAJobName = employeeInfo.JobCode;
                                    employee.EDLATimeCodeName = "UAT";
                                    employee.EDLAProjectName = "NULL";
                                    string storeNumber = ConceptJack + System.Convert.ToInt32(storeAuthenticator.StoreNumber).ToString("0000");
                                    employee.EDLADepartmentName = storeNumber;
                                    employee.JobCode = employeeInfo.JobCode;
                                    employee.LocationCode = storeNumber;

                                    // build the User Defined Field Values hash table...
                                    System.Collections.Hashtable userDefinedFieldValues = new Hashtable();
                                    configIter = configNav.Select(navigatorRoot + "EMP-PAY-RATE");
                                    elementValue = GetElementValue(configIter);
                                    decimal payRate = System.Convert.ToDecimal(elementValue);
                                    userDefinedFieldValues.Add("PAYRATE", payRate.ToString("*********.00"));
                                    employee.UserDefinedFieldValues = userDefinedFieldValues;

                                    Logging.WriteLog(logFile, "Before Invoking Workbrain Hire");

                                    HRRefresh.Hire(ref employee, logFile);

                                    Logging.WriteLog(logFile, "After Invoking Workbrain Hire");

                                    // Load up the Workbrain Clock values...
                                    employeeInfo.ClockEmployeeID = employee.EmpID.ToString();
                                    employeeInfo.ClockEmployeeNumber = employee.Company + employee.LawsonID;
                                    employeeInfo.ClockStatus = employee.Status;
                                    employeeInfo.ClockEnfSch = employee.Val4;
                                    employeeInfo.ClockClassification = employee.SupervisorCode.ToString();
                                    employeeInfo.ClockReaderGroupName = employee.ReaderGroupID.ToString();
                                    employeeInfo.ClockMemberType = "emp";
                                }
                            }

                            #endregion

                        }

                        catch (Exception e)
                        {
                            Logging.WriteLog(logFile, "Exception: " + e.Message);
                            Logging.WriteLog(logFileExceptions, "Exception: " + e.Message);
                            Logging.WriteLog(logFile, "Exception: " + messageLawsonUnexpectedResult);
                            Logging.WriteLog(logFileExceptions, "Exception: " + messageLawsonUnexpectedResult);
                            throw new SystemException(messageLawsonUnexpectedResult, e);
                        }

                        // Wipe out structure return values and messages...

                        switch (returnCode)
                        {
                            case (int)ApplicationReturnValue.Failure:
                                // Request Failed...

                                // Check for a Lawson error...
                                if (lawsonReturnValue == (int)LawsonMsgNbr.MatrixViolation)
                                {
                                    // Matrix violation...
                                    employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                                    employeeInfo.ApplicationReturnMessage1 = applicationReturnMessage1;
                                    employeeInfo.ApplicationReturnMessage2 = applicationReturnMessage2;
                                    employeeInfo.ApplicationReturnMessage3 = applicationReturnMessage3;
                                    employeeInfo.ApplicationReturnMessage4 = applicationReturnMessage4;
                                    employeeInfo.ApplicationReturnMessage5 = applicationReturnMessage5;
                                }
                                else
                                {
                                    // NOT a matrix violation...assume a Lawson native error...
                                    employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                                    employeeInfo.ApplicationReturnMessage1 = lawsonReturnMessage;
                                }
                                break;

                            case (int)ApplicationReturnValue.Success:
                                employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Success;
                                employeeInfo.ApplicationReturnMessage1 = applicationReturnMessage1;
                                employeeInfo.ApplicationReturnMessage2 = applicationReturnMessage2;
                                employeeInfo.ApplicationReturnMessage3 = applicationReturnMessage3;
                                employeeInfo.ApplicationReturnMessage4 = applicationReturnMessage4;
                                employeeInfo.ApplicationReturnMessage5 = applicationReturnMessage5;

                                break;

                            default:
                                employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                                employeeInfo.ApplicationReturnMessage1 = messageException;
                                break;
                        }

                        #endregion

                        // Reform messages returned...
                        employeeInfo.ApplicationReturnMessage1 = ReformMessage(employeeInfo.ApplicationReturnMessage1, replacementPhrases);
                        employeeInfo.ApplicationReturnMessage2 = ReformMessage(employeeInfo.ApplicationReturnMessage2, replacementPhrases);
                        employeeInfo.ApplicationReturnMessage3 = ReformMessage(employeeInfo.ApplicationReturnMessage3, replacementPhrases);
                        employeeInfo.ApplicationReturnMessage4 = ReformMessage(employeeInfo.ApplicationReturnMessage4, replacementPhrases);
                        employeeInfo.ApplicationReturnMessage5 = ReformMessage(employeeInfo.ApplicationReturnMessage5, replacementPhrases);

                        Logging.WriteLog(logFile, "Returned From Lawson");
                        Logging.WriteLog(logFile, "Employee Number: " + employeeInfo.EmployeeNumber);
                        Logging.WriteLog(logFile, "Employee Status Code: " + employeeInfo.StatusCode.ToString());
                        Logging.WriteLog(logFile, "Company: " + employeeInfo.Company);
                        Logging.WriteLog(logFile, "Badge Number: " + employeeInfo.BadgeNumber);

                        Logging.WriteLog(logFile, "Application Return Number: " + employeeInfo.ApplicationReturnValue);
                        Logging.WriteLog(logFile, "Application Return Message 1: " + employeeInfo.ApplicationReturnMessage1);
                        Logging.WriteLog(logFile, "Application Return Message 2: " + employeeInfo.ApplicationReturnMessage2);
                        Logging.WriteLog(logFile, "Application Return Message 3: " + employeeInfo.ApplicationReturnMessage3);
                        Logging.WriteLog(logFile, "Application Return Message 4: " + employeeInfo.ApplicationReturnMessage4);
                        Logging.WriteLog(logFile, "Application Return Message 5: " + employeeInfo.ApplicationReturnMessage5);
                    }
                    else
                    {
                        Logging.WriteLog(logFile, "ProcessID appears to be wrong.");
                        employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                        employeeInfo.ApplicationReturnMessage1 = UnauthorizedAccess;
                    }
                }
                else
                {
                    Logging.WriteLog(logFile, "IP appears out of range");
                    employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                    employeeInfo.ApplicationReturnMessage1 = messageUnauthorized;
                }
            }

            catch (Exception e)
            {
                employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                employeeInfo.ApplicationReturnMessage1 = e.Message;
                Logging.WriteLog(logFile, "Exception: " + e.Message);
                Logging.WriteLog(logFileExceptions, "Exception: " + e.Message);
            }

            finally
            {
                webServiceLog.CompanyNumber = employeeInfo.Company;
                webServiceLog.ResultCode = (short)employeeInfo.ApplicationReturnValue;
                webServiceLog.ResultMessage = JIB.Framework.Utilities.Strings.LeftSafe(employeeInfo.ApplicationReturnMessage1, 100);

                webServiceLog.EndDate = DateTime.Now;
                webServiceLog.Save();

                persistenceWebServiceAudit.Dispose();
            }

            Logging.WriteLog(logFile, "HIRE - Complete");

        }

        #endregion

        #region Password Change

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void PasswordChange(ref EmployeeInformation employeeInfo)
        {
            employeeInfo.ApplicationReturnValue = (int)JIB.Framework.ReturnValues.Success;
        }
        //			// http://10.100.133.121:90/cgi-lawson/ags.exe?_PDL=DEV&_TKN=FX54&_LFN=ALL&_RTN=DATA&_TDS=IGNORE&_OUT=xml&_EVT=CHG&FC=C&FTO-TRN-TYPE=PASSWRD&FTO-TRN-KEY=28477618&FTO-COMPANY=1&FTO-EMPLOYEE=284778&FTO-LOCAT-CODE=000003&FTO-TRAN-STATUS=:)$8Q>~*!&FTO-EFFECTIVE-DATE=20041209&FTO-EFFECTIVE-TIME=155101&FTO-USER1=JON284778&FTO-USER2=KENNY100&_EOT=TRUE
        //			const string FORM = "JX54";
        //			const string EVENT = "CHG";
        //			const string METHOD = "C";
        //
        //			SqlClient persistenceWebServiceAudit = null;
        //			JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog webServiceLog = null;
        //			
        //			byte[] key = {00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32};
        //			byte[] iv = {55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32};
        //
        //			string processID = "343AF2AB-1DE0-4076-BD05-561BE46A530F";
        //
        //			//			string logFile = @"c:\LawsonService.txt";
        //
        //			try
        //			{
        //				string lawsonIPAddress = System.Configuration.ConfigurationManager.AppSettings["LawsonIPAddress"];
        //				string lawsonProxyID = System.Configuration.ConfigurationManager.AppSettings["LawsonProxyID"];
        //				string lawsonProxyPassword = System.Configuration.ConfigurationManager.AppSettings["LawsonProxyPassword"];
        //				string lawsonDataArea = System.Configuration.ConfigurationManager.AppSettings["LawsonDataArea"];
        //				string sqlConnectStringAudit = System.Configuration.ConfigurationManager.AppSettings["SqlConnectStringAudit"];
        //
        //				persistenceWebServiceAudit = new SqlClient(sqlConnectStringAudit);
        //				webServiceLog = new JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog(persistenceWebServiceAudit);
        //				
        //				webServiceLog.StartDate = DateTime.Now;
        //				webServiceLog.AssemblyVersion = AssemblyVersion;
        //
        //				StoreAuthenticator storeAuthenticator = new StoreAuthenticator();
        //				storeAuthenticator.Decrypt(storeHeaderFromClient.EncryptedBytes, key, iv);
        //
        //				webServiceLog.CompanyNumber = employeeInfo.Company;
        //				webServiceLog.StoreNumber = storeAuthenticator.StoreNumber;
        //				webServiceLog.ServiceCode = (short) JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog.ServiceCodes.EmployeeChangePassword;
        //				webServiceLog.LawsonNumber = storeAuthenticator.EmployeeNumber;
        //
        //				if (storeAuthenticator.ProcessID == processID)
        //				{
        //
        //					#region Invoke Lawson AGS and Get Response
        //
        //					System.Net.HttpWebRequest webRequest;
        //					System.Net.HttpWebResponse webResponse;
        //					System.Text.StringBuilder agsCall;
        //
        //					agsCall = new System.Text.StringBuilder(500);
        //					agsCall.Append(@"http://" + lawsonIPAddress + "/cgi-lawson/ags.exe?");
        //
        //					agsCall.Append("_PDL=");
        //					agsCall.Append(lawsonDataArea);
        //
        //					agsCall.Append("&_TKN=");
        //					agsCall.Append(FORM);
        //
        //					agsCall.Append("&_LFN=ALL");
        //					agsCall.Append("&_RTN=DATA");
        //					agsCall.Append("&_TDS=IGNORE");
        //					agsCall.Append("&_OUT=XML");
        //
        //					agsCall.Append("&_EVT=");
        //					agsCall.Append(EVENT);
        //
        //					agsCall.Append("&FC=");
        //					agsCall.Append(METHOD);
        //
        //					// Add data fields...
        //					//					&FTO-TRN-TYPE=PASSWRD
        //					//					&FTO-TRN-KEY=28477618
        //					//					&FTO-TRAN-STATUS=:)$8Q>~*!
        //					//					&FTO-COMPANY=1
        //					//					&FTO-EMPLOYEE=284778
        //					//					&FTO-LOCAT-CODE=000003
        //					//					&FTO-EFFECTIVE-DATE=20041209
        //					//					&FTO-EFFECTIVE-TIME=155101
        //					//					&FTO-USER1=JON284778
        //					//					&FTO-USER2=KENNY100
        //
        //					agsCall.Append("&FTO-TRN-TYPE=PASSWRD");
        //					agsCall.Append("&FTO-TRN-KEY=28477618");
        //					agsCall.Append("&FTO-TRAN-STATUS=:)$8Q>~*!");
        //
        //					agsCall.Append("&FTO-COMPANY=");
        //					agsCall.Append(employeeInfo.Company.ToString());
        //
        //					agsCall.Append("&FTO-EMPLOYEE=");
        //					agsCall.Append(employeeInfo.EmployeeNumber);
        //
        //					agsCall.Append("&FTO-LOCAT-CODE=");
        //					agsCall.Append(employeeInfo.LocationCode);
        //
        //					agsCall.Append("&FTO-EFFECTIVE-DATE=");
        //					agsCall.Append(System.DateTime.Now.ToString("yyyyMMdd"));
        //
        //					agsCall.Append("&FTO-EFFECTIVE-TIME=");
        //					agsCall.Append(System.DateTime.Now.ToString("HHmmss"));
        //
        //					agsCall.Append("&FTO-USER1=");
        //					agsCall.Append(employeeInfo.BadgeNumber);
        //
        //					// Convert the encrypted password to a hex string - URL and text file friendly...
        //					string hexPassword = JIB.Framework.Utilities.Hex.ToString(employeeInfo.Password);
        //					agsCall.Append("&FTO-USER2=");
        //					agsCall.Append(hexPassword);
        //
        //					// Add terminator...
        //					agsCall.Append("&_EOT=TRUE");
        //
        //					webRequest = (System.Net.HttpWebRequest) System.Net.HttpWebRequest.Create(agsCall.ToString());
        //					webRequest.Credentials = new System.Net.NetworkCredential(lawsonProxyID, lawsonProxyPassword);
        //					webResponse = (System.Net.HttpWebResponse) webRequest.GetResponse();
        //
        //					#endregion
        //
        //					#region Put Web Response in XML Document and Find Returned Field Names
        //
        //					const string NavigatorRoot = "/XJX54.1/JX54.1/";
        //					System.Xml.XmlTextReader xmlTextReader;
        //
        //					xmlTextReader = new System.Xml.XmlTextReader(webResponse.GetResponseStream());
        //					XPathDocument configDocument;
        //					XPathNavigator configNav;
        //					XPathNodeIterator configIter;
        //					string elementValue;
        //
        //					configDocument = new XPathDocument(xmlTextReader);
        //					configNav = configDocument.CreateNavigator();
        //
        //					configIter = configNav.Select(NavigatorRoot + "Message");
        //					elementValue = GetElementValue(configIter);
        //					employeeInfo.ApplicationReturnMessage1 = elementValue;
        //
        //					configIter = configNav.Select(NavigatorRoot + "MsgNbr");
        //					elementValue = GetElementValue(configIter);
        //					employeeInfo.ApplicationReturnValue = System.Convert.ToInt32(elementValue);
        //
        //					configIter = configNav.Select(NavigatorRoot + "FTO-USER2");
        //					elementValue = GetElementValue(configIter);
        //					employeeInfo.Password = elementValue;
        //
        //					#endregion
        //
        //				}
        //				else
        //				{
        //					employeeInfo.ApplicationReturnValue = (int) JIB.Framework.ReturnValues.Failure;
        //					employeeInfo.ApplicationReturnMessage1 = UnauthorizedAccess;
        //				}
        //			}
        //
        //			catch(Exception e)
        //			{
        //				throw new SystemException(e.Message, e);
        //			}
        //
        //			finally
        //			{
        //				webServiceLog.ResultCode = (int) employeeInfo.ApplicationReturnValue;
        //				webServiceLog.ResultMessage = JIB.Framework.Utilities.Strings.LeftSafe(employeeInfo.ApplicationReturnMessage1, 100);
        //				webServiceLog.EndDate = DateTime.Now;
        //				webServiceLog.Save();
        //
        //				persistenceWebServiceAudit.Dispose();
        //			}
        //		}

        #endregion

        #region WebMethods Declarations for HirePart2, Loan, Borrow, Terminate, TerminateCancel, TransferIn, TransferOut, LOA, LOAEND

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void HirePart2(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionHirePart2, ProcessIDAdd, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void Loan(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionLoan, ProcessIDLoan, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void Borrow(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionBorrow, ProcessIDBorrow, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void Terminate(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionTerminate, ProcessIDTerminate, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void TerminateCancel(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionTerminateCancel, ProcessIDTerminateCancel, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void TransferIn(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionTransferIn, ProcessIDTransferIn, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void TransferOut(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionTransferOut, ProcessIDTransferOut, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void LeaveOfAbsence(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionLeaveOfAbsence, ProcessIDLeaveOfAbsence, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void LeaveOfAbsenceEnd(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionLeaveOfAbsenceEnd, ProcessIDLeaveOfAbsenceEnd, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void Update(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionUpdate, ProcessIDUpdate, true);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void Query(ref EmployeeInformation employeeInfo)
        {
            ProcessAction(ref employeeInfo, LawsonActionQuery, ProcessIDQuery, false);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void QueryStore(ref EmployeeInformation employeeInfo, ref EmployeeInformation[] employeeInfos)
        {
            ProcessAction(ref employeeInfo, ref employeeInfos, LawsonActionRestQuery, ProcessIDQuery, false);
        }

        [WebMethod]
        [SoapHeader("storeHeaderFromClient")]
        public void Snapshot(ref EmployeeInformation employeeInfo, ref EmployeeInformation[] employeeInfos)
        {
            ProcessAction(ref employeeInfo, ref employeeInfos, LawsonActionSnapshot, ProcessIDSnapshot, true);
        }

        [WebMethod]
        public bool Authenticate(string badgeNumber, string password)
        {
            return ValidatePassword(badgeNumber, password);
        }

        #endregion

        #region ProcessAction Method - Used for all FM41 Transactions

        private DateTime LawsonNullDate = new DateTime(1700, 1, 1);

        internal enum AuthenticationResults
        {
            Authenticated = 1,
            ManagerPasswordIncorrect = 2,
            EmployeePasswordIncorrect = 3,
            BothPasswordsIncorrect = 4
        }

        internal void ProcessAction(ref EmployeeInformation employeeInfo, string action, string processID, bool logTransaction)
        {
            EmployeeInformation[] employeeInfos = new EmployeeInformation[0];
            ProcessAction(ref employeeInfo, ref employeeInfos, action, processID, logTransaction);
        }
        internal void ProcessAction(ref EmployeeInformation employeeInfo, ref EmployeeInformation[] employeeInfos, string action, string processID, bool logTransaction)
        {
            const string EVENT = "ADD";
            const string METHOD = "A";

            const int PersonnelMessageMaximumLength = 128;
            const int CompanyJackInTheBox = 1;

            SqlClient persistenceWebServiceAudit = null;
            JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog webServiceLog = null;
            //			SqlClient persistenceFoundationGallery = null;

            byte[] key = { 00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32 };
            byte[] iv = { 55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32 };

            employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
            employeeInfo.ApplicationReturnMessage1 = "";
            employeeInfo.ApplicationReturnMessage2 = "";
            employeeInfo.ApplicationReturnMessage3 = "";
            employeeInfo.ApplicationReturnMessage4 = "";
            employeeInfo.ApplicationReturnMessage5 = "";

            bool publishEnabled = true;

            string logFile = System.Configuration.ConfigurationManager.AppSettings["LogFile"];
            if (logFile != null)
            {
                if (logFile.Length == 0)
                {
                    logFile = null;
                }
            }
            string logFileExceptions = System.Configuration.ConfigurationManager.AppSettings["LogFileExceptions"];
            if (logFileExceptions != null)
            {
                if (logFileExceptions.Length == 0)
                {
                    logFileExceptions = null;
                }
            }

            try
            {
                string socialSecurityNumber = string.Empty;

                string messageException = System.Configuration.ConfigurationManager.AppSettings["MessageException"];
                string messageUnauthorized = System.Configuration.ConfigurationManager.AppSettings["MessageUnauthorized"];
                string messageLawsonUnavailable = System.Configuration.ConfigurationManager.AppSettings["MessageLawsonUnavailable"];
                string messageLawsonUnexpectedResult = System.Configuration.ConfigurationManager.AppSettings["MessageLawsonUnexpectedResult"];
                string replacementPhraseFromConfig = System.Configuration.ConfigurationManager.AppSettings["ReplacementPhrases"];
                string[] replacementPhrases = replacementPhraseFromConfig.Split("|".ToCharArray());

                string ipRejectedIfMissingFlag = System.Configuration.ConfigurationManager.AppSettings["IPRejectedIfMissing"];
                bool ipRejectedIfMissing = true;
                if (ipRejectedIfMissingFlag != null)
                {
                    if (ipRejectedIfMissingFlag.Length > 0)
                    {
                        if (string.Compare(ipRejectedIfMissingFlag, "false", true) == 0)
                        {
                            ipRejectedIfMissing = false;
                        }
                    }
                }

                // Don't add these for the Query transaction...
                if (string.Compare(action, LawsonActionQuery, true) != 0)
                {
                    string sqlConnectString = System.Configuration.ConfigurationManager.AppSettings["SqlConnectStringAudit"];

                    persistenceWebServiceAudit = new SqlClient(sqlConnectString);
                    webServiceLog = new JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog(persistenceWebServiceAudit);

                    webServiceLog.StartDate = DateTime.Now;
                    webServiceLog.AssemblyVersion = AssemblyVersion;
                    webServiceLog.Custom01 = employeeInfo.PersonnelVersion;

                    //					sqlConnectString = null;
                    //					sqlConnectString = System.Configuration.ConfigurationManager.AppSettings["SqlConnectStringFoundationGallery"];
                    //					if (sqlConnectString != null)
                    //					{
                    //						if (sqlConnectString.Trim().Length > 0)
                    //						{
                    //							persistenceFoundationGallery = new SqlClient(sqlConnectString);
                    //						}
                    //					}
                }

                StoreAuthenticator storeAuthenticator = new StoreAuthenticator();
                storeAuthenticator.Decrypt(storeHeaderFromClient.EncryptedBytes, key, iv);

                // Set the log file to the store number to avoid log meshing...
                if (logFile != null)
                {
                    logFile = logFile.Replace("%StoreNumber%", storeAuthenticator.StoreNumber);
                }
                if (logFileExceptions != null)
                {
                    logFileExceptions = logFileExceptions.Replace("%StoreNumber%", storeAuthenticator.StoreNumber);
                }

                // One line log entry for CBT querys.
                if (logTransaction == false)
                {
                    if (string.Compare(action, LawsonActionQuery, true) == 0)
                    {
                        Logging.WriteLog(logFile, action + " - Start (" + employeeInfo.BadgeNumber + ")");
                    }
                    if (string.Compare(action, LawsonActionRestQuery, true) == 0)
                    {
                        Logging.WriteLog(logFile, action + " - Start (" + storeAuthenticator.StoreNumber + ")");
                    }
                    logFile = null;

                    // Changing the version so that all exceptions will be logged.
                    //logFileExceptions = null;
                }
                Logging.WriteLog(logFile, action + " - Start");

                Logging.WriteLog(logFile, "Assembly Version: " + AssemblyVersion);
                Logging.WriteLog(logFile, "Decrypted Header Store Number: " + storeAuthenticator.StoreNumber);
                Logging.WriteLog(logFile, "Decrypted Header Operator Employee Number: " + storeAuthenticator.EmployeeNumber);
                Logging.WriteLog(logFile, "Decrypted Header processID: " + storeAuthenticator.ProcessID);
                Logging.WriteLog(logFile, "Publish Enabled: " + publishEnabled.ToString());

                // Don't add these for the Query transaction or the RestQuery transaction...
                bool addLog = true;
                if (string.Compare(action, LawsonActionQuery, true) == 0)
                {
                    addLog = false;
                }
                if (string.Compare(action, LawsonActionRestQuery, true) == 0)
                {
                    addLog = false;
                }
                if (addLog == true)
                {
                    webServiceLog.CompanyNumber = employeeInfo.Company;
                    webServiceLog.StoreNumber = storeAuthenticator.StoreNumber;
                    webServiceLog.ServiceCode = (short)JIB.WebServiceAudit.Data.BusinessLayer.WebServiceLog.ServiceCodeValues.EmployeeAdd;
                    webServiceLog.LawsonNumber = storeAuthenticator.EmployeeNumber;
                }

                // Check IP Authentication...
                Logging.WriteLog(logFile, "StoreAuthenticator IP: " + storeHeaderFromClient.IPAddress);
                bool authenticateIP = AttemptToAuthenticateIP(logFile);

                if (ipRejectedIfMissing == false)
                {
                    if (authenticateIP == false)
                    {
                        if (storeHeaderFromClient.IPAddress == null)
                        {
                            authenticateIP = true;
                        }
                        else
                        {
                            if (storeHeaderFromClient.IPAddress.Length == 0)
                            {
                                authenticateIP = true;
                            }
                        }
                    }
                }

                // Auto authenticate Query transaction...
                if (string.Compare(action, LawsonActionQuery, true) == 0)
                {
                    authenticateIP = true;
                }
                if (string.Compare(action, LawsonActionRestQuery, true) == 0)
                {
                    authenticateIP = true;
                }

                if (authenticateIP == true)
                {
                    if (storeAuthenticator.ProcessID == processID)
                    {
                        Logging.WriteLog(logFile, "Parameters...");
                        Logging.WriteLog(logFile, "RequestID: " + employeeInfo.RequestID);

                        // Authenticate Manager and Employee Passwords, If Necessary...
                        // .. if the employee password exists, do the authentication...
                        AuthenticationResults authenticationResults = AuthenticationResults.Authenticated;
                        if (employeeInfo.Password == null)
                        {
                            employeeInfo.Password = string.Empty;
                        }
                        Logging.WriteLog(logFile, "employeeInfo.BadgeNumber: " + employeeInfo.BadgeNumber);
                        //						Logging.WriteLog(logFile, "employeeInfo.Password: " + employeeInfo.Password);
                        Logging.WriteLog(logFile, "employeeInfo.ManagerBadgeNumber: " + employeeInfo.ManagerBadgeNumber);
                        //						Logging.WriteLog(logFile, "employeeInfo.ManagerPassword: " + employeeInfo.ManagerPassword);

                        // Check the peversion...
                        switch (action)
                        {
                            case LawsonActionQuery:
                            case LawsonActionRestQuery:
                                break;

                            default:
                                Logging.WriteLog(logFile, "Original employeeInfo.PersonnelVersion: " + employeeInfo.PersonnelVersion);
                                // Strip out the non-numeric characters - leave the decimal...
                                employeeInfo.PersonnelVersion = System.Text.RegularExpressions.Regex.Replace(employeeInfo.PersonnelVersion, "[^0-9,.]", "");
                                Logging.WriteLog(logFile, "After employeeInfo.PersonnelVersion: " + employeeInfo.PersonnelVersion);
                                decimal peVer = Convert.ToDecimal(employeeInfo.PersonnelVersion);
                                if (peVer >= 9.98M)
                                {
                                    if (employeeInfo.Password.Length > 0)
                                    {
                                        authenticationResults = ValidateEncryptedPasswords(logFile, employeeInfo.ManagerBadgeNumber, employeeInfo.ManagerPassword, employeeInfo.BadgeNumber, employeeInfo.Password);
                                    }
                                }
                                break;
                        }

                        string authenticationEnabled = System.Configuration.ConfigurationManager.AppSettings["AuthenticationEnabled"];
                        if (authenticationEnabled != null)
                        {
                            if (authenticationEnabled.Length > 0)
                            {
                                if (string.Compare(authenticationEnabled, "false", true) == 0)
                                {
                                    authenticationResults = AuthenticationResults.Authenticated;
                                }
                            }
                        }

                        // Auto authenticate the Query...
                        if (string.Compare(action, LawsonActionQuery, true) == 0)
                        {
                            authenticationResults = AuthenticationResults.Authenticated;
                        }
                        if (string.Compare(action, LawsonActionRestQuery, true) == 0)
                        {
                            authenticationResults = AuthenticationResults.Authenticated;
                        }

                        // Check authentication results and send message if incorrect...
                        if (authenticationResults == AuthenticationResults.Authenticated)
                        {
                            if (ValidTransactionDate(ref employeeInfo, action, logFile, logFileExceptions) == true)
                            {
                                #region Invoke Lawson AGS and Get Response

                                string form = string.Empty;

                                switch (action.ToUpper())
                                {
                                    case LawsonActionHirePart2:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeHirePart2FormTag"];
                                        break;
                                    case LawsonActionBorrow:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeBorrowFormTag"];
                                        break;
                                    case LawsonActionTerminate:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeTerminateFormTag"];
                                        break;
                                    case LawsonActionTerminateCancel:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeTerminateCancelFormTag"];
                                        break;
                                    case LawsonActionTransferIn:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeTransferInFormTag"];
                                        break;
                                    case LawsonActionTransferOut:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeTransferOutFormTag"];
                                        break;
                                    case LawsonActionLeaveOfAbsence:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeLeaveOfAbsenceFormTag"];
                                        break;
                                    case LawsonActionLeaveOfAbsenceEnd:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeLeaveOfAbsenceEndFormTag"];
                                        break;
                                    case LawsonActionUpdate:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeUpdateFormTag"];
                                        break;
                                    case LawsonActionQuery:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeQueryFormTag"];
                                        break;
                                    case LawsonActionRestQuery:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonRestQueryFormTag"];
                                        break;
                                    case LawsonActionSnapshot:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeSnapshotFormTag"];
                                        break;
                                    case LawsonActionPasswordChange:
                                        form = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeePasswordChangeFormTag"];
                                        break;
                                }
                                string lawsonBaseUrl = System.Configuration.ConfigurationManager.AppSettings["LawsonBaseUrl"];
                                string lawsonTransactionUrl = System.Configuration.ConfigurationManager.AppSettings["LawsonTransactionUrl"];
                                string lawsonProxyID = System.Configuration.ConfigurationManager.AppSettings["LawsonProxyID"];
                                string lawsonProxyPassword = System.Configuration.ConfigurationManager.AppSettings["LawsonProxyPassword"];
                                string lawsonDataArea = System.Configuration.ConfigurationManager.AppSettings["LawsonDataArea"];
                                LawsonActionUpdateFlags lawsonActionUpdateFlags = new LawsonActionUpdateFlags();

                                System.Text.StringBuilder agsCall = new System.Text.StringBuilder(1000);
                                // base url...
                                agsCall.Append(lawsonBaseUrl);
                                // login parms...
                                agsCall.Append("?_action=LOGIN&_fromLoginPage=TRUE&_ssoUser=");
                                agsCall.Append(lawsonProxyID);
                                agsCall.Append("&_ssoPass=");
                                agsCall.Append(lawsonProxyPassword);
                                agsCall.Append("&_ssoOrigUrl=");

                                System.Text.StringBuilder originalUrl = new StringBuilder(500);
                                // Transaction url...
                                originalUrl.Append(lawsonTransactionUrl);
                                originalUrl.Append("?_PDL=");
                                originalUrl.Append(lawsonDataArea);

                                originalUrl.Append("&_TKN=");
                                originalUrl.Append(form);

                                originalUrl.Append("&_LFN=ALL");
                                originalUrl.Append("&_RTN=DATA");
                                originalUrl.Append("&_TDS=IGNORE");
                                originalUrl.Append("&_OUT=XML");

                                originalUrl.Append("&_EVT=");
                                originalUrl.Append(EVENT);

                                originalUrl.Append("&FC=");
                                originalUrl.Append(METHOD);

                                originalUrl.Append("&ACTION=");
                                originalUrl.Append(action);

                                // Don't add these for the Query transaction...
                                bool fullTransaction = true;
                                if (string.Compare(action, LawsonActionQuery, true) == 0)
                                {
                                    fullTransaction = false;
                                }
                                if (string.Compare(action, LawsonActionRestQuery, true) == 0)
                                {
                                    fullTransaction = false;
                                }
                                if (fullTransaction == true)
                                {
                                    // Stuff we send for all actions...
                                    if (employeeInfo.Company.Length > 0)
                                    {
                                        originalUrl.Append("&COMPANY=");
                                        originalUrl.Append(employeeInfo.Company);
                                    }

                                    if (employeeInfo.PersonnelVersion != null)
                                    {
                                        if (employeeInfo.PersonnelVersion.Length > 0)
                                        {
                                            originalUrl.Append("&PRS-VERSION=");
                                            originalUrl.Append(employeeInfo.PersonnelVersion);
                                        }
                                    }

                                    if (employeeInfo.EmployeeNumber.Length > 0)
                                    {
                                        originalUrl.Append("&EMPLOYEE=");
                                        originalUrl.Append(employeeInfo.EmployeeNumber);
                                    }

                                    if (employeeInfo.FICANumber.Length > 0)
                                    {
                                        originalUrl.Append("&FICA-NBR=");
                                        socialSecurityNumber = JIB.Framework.Security.Encryption.DecryptRijndaelManaged(employeeInfo.FICANumber, key, iv).Trim();
                                        originalUrl.Append(socialSecurityNumber);
                                    }

                                    if (ConvertStringToDate(employeeInfo.BirthDate) != new DateTime())
                                    {
                                        originalUrl.Append("&BIRTHDATE=");
                                        originalUrl.Append(ConvertStringToDate(employeeInfo.BirthDate).ToString("yyyyMMdd"));
                                    }

                                    if (employeeInfo.GenderCode.Length > 0)
                                    {
                                        originalUrl.Append("&SEX=");
                                        originalUrl.Append(employeeInfo.GenderCode);
                                    }

                                    if (employeeInfo.LastName.Length > 0)
                                    {
                                        originalUrl.Append("&LAST-NAME=");
                                        originalUrl.Append(employeeInfo.LastName);
                                    }

                                    if (employeeInfo.FirstName.Length > 0)
                                    {
                                        originalUrl.Append("&FIRST-NAME=");
                                        originalUrl.Append(employeeInfo.FirstName);
                                    }

                                    if (employeeInfo.MiddleName.Length > 0)
                                    {
                                        originalUrl.Append("&MIDDLE-NAME=");
                                        originalUrl.Append(employeeInfo.MiddleName);
                                    }

                                    if (storeAuthenticator.EmployeeNumber.Length > 0)
                                    {
                                        originalUrl.Append("&MGR-EMPLOYEE=");
                                        originalUrl.Append(storeAuthenticator.EmployeeNumber);
                                    }

                                    if (employeeInfo.Points != null)
                                    {
                                        if (employeeInfo.Points.Length > 0)
                                        {
                                            originalUrl.Append("&UPD-PPAT-POINTS=");
                                            originalUrl.Append(employeeInfo.Points);
                                        }
                                    }

                                    if (employeeInfo.Score != null)
                                    {
                                        if (employeeInfo.Score.Length > 0)
                                        {
                                            originalUrl.Append("&UPD-PPAT-SCORE=");
                                            originalUrl.Append(employeeInfo.Score);
                                        }
                                    }
                                }

                                if (employeeInfo.LocationCode.Length > 0)
                                {
                                    originalUrl.Append("&MGR-LOCAT-CODE=");
                                    originalUrl.Append(employeeInfo.LocationCode);
                                }

                                // GM location codes...
                                if (employeeInfo.GMHomeLocation != null)
                                {
                                    if (employeeInfo.GMHomeLocation.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-HM-LOCAT-CODE=");
                                        originalUrl.Append(employeeInfo.GMHomeLocation);
                                    }
                                }
                                if (employeeInfo.GMLocation01 != null)
                                {
                                    if (employeeInfo.GMLocation01.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE1=");
                                        originalUrl.Append(employeeInfo.GMLocation01);
                                    }
                                }
                                if (employeeInfo.GMLocation02 != null)
                                {
                                    if (employeeInfo.GMLocation02.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE2=");
                                        originalUrl.Append(employeeInfo.GMLocation02);
                                    }
                                }
                                if (employeeInfo.GMLocation03 != null)
                                {
                                    if (employeeInfo.GMLocation03.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE3=");
                                        originalUrl.Append(employeeInfo.GMLocation03);
                                    }
                                }
                                if (employeeInfo.GMLocation04 != null)
                                {
                                    if (employeeInfo.GMLocation04.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE4=");
                                        originalUrl.Append(employeeInfo.GMLocation04);
                                    }
                                }
                                if (employeeInfo.GMLocation05 != null)
                                {
                                    if (employeeInfo.GMLocation05.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE5=");
                                        originalUrl.Append(employeeInfo.GMLocation05);
                                    }
                                }
                                if (employeeInfo.GMLocation06 != null)
                                {
                                    if (employeeInfo.GMLocation06.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE6=");
                                        originalUrl.Append(employeeInfo.GMLocation06);
                                    }
                                }
                                if (employeeInfo.GMLocation07 != null)
                                {
                                    if (employeeInfo.GMLocation07.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE7=");
                                        originalUrl.Append(employeeInfo.GMLocation07);
                                    }
                                }
                                if (employeeInfo.GMLocation08 != null)
                                {
                                    if (employeeInfo.GMLocation08.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE8=");
                                        originalUrl.Append(employeeInfo.GMLocation08);
                                    }
                                }
                                if (employeeInfo.GMLocation09 != null)
                                {
                                    if (employeeInfo.GMLocation09.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE9=");
                                        originalUrl.Append(employeeInfo.GMLocation09);
                                    }
                                }
                                if (employeeInfo.GMLocation10 != null)
                                {
                                    if (employeeInfo.GMLocation10.Length > 0)
                                    {
                                        originalUrl.Append("&UPD-GM-LOCAT-CODE10=");
                                        originalUrl.Append(employeeInfo.GMLocation10);
                                    }
                                }

                                originalUrl.Append("&MGR-DATE=");
                                originalUrl.Append(DateTime.Now.ToString("yyyyMMdd"));

                                originalUrl.Append("&MGR-TIME=");
                                originalUrl.Append(DateTime.Now.ToString("HH:mm:ss"));

                                // Now do stuff specific to the action...
                                switch (action.ToUpper())
                                {
                                    case LawsonActionHirePart2:
                                        // Address data...
                                        if (employeeInfo.Address1.Length > 0)
                                        {
                                            originalUrl.Append("&HIRE-ADDR1=");
                                            originalUrl.Append(employeeInfo.Address1);

                                            lawsonActionUpdateFlags.AddressChange = true;
                                        }

                                        // Always send ADDR2...
                                        originalUrl.Append("&HIRE-ADDR2=");
                                        originalUrl.Append(employeeInfo.Address2);

                                        if (employeeInfo.City.Length > 0)
                                        {
                                            originalUrl.Append("&HIRE-CITY=");
                                            originalUrl.Append(employeeInfo.City);

                                            lawsonActionUpdateFlags.AddressChange = true;
                                        }

                                        if (employeeInfo.State.Length > 0)
                                        {
                                            originalUrl.Append("&HIRE-STATE=");
                                            originalUrl.Append(employeeInfo.State);

                                            lawsonActionUpdateFlags.AddressChange = true;
                                        }

                                        if (employeeInfo.ZIP.Length > 0)
                                        {
                                            originalUrl.Append("&HIRE-ZIP=");
                                            originalUrl.Append(JIB.Framework.Utilities.Strings.LeftSafe(employeeInfo.ZIP, ZipCodeLengthToLawson));

                                            lawsonActionUpdateFlags.AddressChange = true;
                                        }

                                        if (employeeInfo.Phone.Length > 0)
                                        {
                                            originalUrl.Append("&HIRE-HM-PHONE-NBR=");
                                            originalUrl.Append(ConvertPhoneNumber(employeeInfo.Phone));

                                            lawsonActionUpdateFlags.PhoneNumberChange = true;
                                        }

                                        AddI9DataToAGSCall(employeeInfo, ref originalUrl);
                                        break;

                                    case LawsonActionBorrow:
                                        if (employeeInfo.BorrowFromStoreNumber.Length > 0)
                                        {
                                            originalUrl.Append("&BORROW-FR-LOCAT-CODE=");
                                            originalUrl.Append(employeeInfo.BorrowFromStoreNumber);
                                        }

                                        if (ConvertStringToDate(employeeInfo.BorrowStartDate) != new DateTime())
                                        {
                                            originalUrl.Append("&BORROW-FR-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.BorrowStartDate).ToString("yyyyMMdd"));
                                        }

                                        if (ConvertStringToDate(employeeInfo.BorrowEndDate) != new DateTime())
                                        {
                                            originalUrl.Append("&BORROW-TO-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.BorrowEndDate).ToString("yyyyMMdd"));
                                        }

                                        if (employeeInfo.LocationCodeAlternate.Length > 0)
                                        {
                                            originalUrl.Append("&BORROW-ALT-LOCAT=");
                                            originalUrl.Append(employeeInfo.LocationCodeAlternate);
                                        }

                                        if (employeeInfo.BadgeNumber.Length > 0)
                                        {
                                            originalUrl.Append("&BADGE-ID=");
                                            originalUrl.Append(employeeInfo.BadgeNumber);
                                        }
                                        break;

                                    case LawsonActionTerminate:
                                        if (employeeInfo.TerminationAction.Length > 0)
                                        {
                                            originalUrl.Append("&TERM-ACTION=");
                                            originalUrl.Append(employeeInfo.TerminationAction);
                                        }

                                        if (ConvertStringToDate(employeeInfo.TerminationDate) != new DateTime())
                                        {
                                            originalUrl.Append("&TERM-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.TerminationDate).ToString("yyyyMMdd"));
                                        }

                                        if (employeeInfo.TerminationReason.Length > 0)
                                        {
                                            originalUrl.Append("&TERM-REASON=");
                                            originalUrl.Append(employeeInfo.TerminationReason);
                                        }

                                        if (ConvertStringToDate(employeeInfo.LastDateWorked) != new DateTime())
                                        {
                                            originalUrl.Append("&LAST-DAY-WORKD=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.LastDateWorked).ToString("yyyyMMdd"));
                                        }

                                        if (employeeInfo.EligibleForRehireFlag.Length > 0)
                                        {
                                            originalUrl.Append("&ELIG-4-REHIRE=");
                                            originalUrl.Append(employeeInfo.EligibleForRehireFlag);
                                        }

                                        if (employeeInfo.Comment1.Length > 0)
                                        {
                                            originalUrl.Append("&TERM-COMMENT1=");
                                            originalUrl.Append(employeeInfo.Comment1);
                                        }

                                        if (employeeInfo.Comment2.Length > 0)
                                        {
                                            originalUrl.Append("&TERM-COMMENT2=");
                                            originalUrl.Append(employeeInfo.Comment2);
                                        }

                                        if (employeeInfo.Comment3.Length > 0)
                                        {
                                            originalUrl.Append("&TERM-COMMENT3=");
                                            originalUrl.Append(employeeInfo.Comment3);
                                        }

                                        if (employeeInfo.Comment4.Length > 0)
                                        {
                                            originalUrl.Append("&TERM-COMMENT4=");
                                            originalUrl.Append(employeeInfo.Comment4);
                                        }
                                        break;

                                    case LawsonActionTransferIn:
                                        if (storeAuthenticator.StoreNumber.Length > 0)
                                        {
                                            originalUrl.Append("&XFRIN-IN-LOCATION=");
                                            originalUrl.Append(storeAuthenticator.StoreNumber);
                                        }

                                        if (employeeInfo.TransferFromStoreNumber.Length > 0)
                                        {
                                            originalUrl.Append("&XFRIN-OUT-LOCATION=");
                                            originalUrl.Append(employeeInfo.TransferFromStoreNumber);
                                        }

                                        if (ConvertStringToDate(employeeInfo.TransferInDate) != new DateTime())
                                        {
                                            originalUrl.Append("&XFRIN-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.TransferInDate).ToString("yyyyMMdd"));
                                        }

                                        if (employeeInfo.Address1.Length > 0)
                                        {
                                            originalUrl.Append("&XFRIN-ADDR1=");
                                            originalUrl.Append(employeeInfo.Address1);
                                        }

                                        // Always send ADDR2...
                                        originalUrl.Append("&XFRIN-ADDR2=");
                                        originalUrl.Append(employeeInfo.Address2);

                                        if (employeeInfo.City.Length > 0)
                                        {
                                            originalUrl.Append("&XFRIN-CITY=");
                                            originalUrl.Append(employeeInfo.City);
                                        }

                                        if (employeeInfo.State.Length > 0)
                                        {
                                            originalUrl.Append("&XFRIN-STATE=");
                                            originalUrl.Append(employeeInfo.State);
                                        }

                                        if (employeeInfo.ZIP.Length > 0)
                                        {
                                            originalUrl.Append("&XFRIN-ZIP=");
                                            originalUrl.Append(JIB.Framework.Utilities.Strings.LeftSafe(employeeInfo.ZIP, ZipCodeLengthToLawson));
                                        }

                                        if (employeeInfo.Phone.Length > 0)
                                        {
                                            originalUrl.Append("&XFRIN-HM-PHONE-NBR=");
                                            originalUrl.Append(ConvertPhoneNumber(employeeInfo.Phone));
                                        }

                                        if (employeeInfo.BadgeNumber.Length > 0)
                                        {
                                            originalUrl.Append("&BADGE-ID=");
                                            originalUrl.Append(employeeInfo.BadgeNumber);
                                        }
                                        break;

                                    case LawsonActionTransferOut:
                                        if (storeAuthenticator.StoreNumber.Length > 0)
                                        {
                                            originalUrl.Append("&XFROUT-OUT-LOCATION=");
                                            originalUrl.Append(storeAuthenticator.StoreNumber);
                                        }

                                        if (employeeInfo.TransferToStoreNumber.Length > 0)
                                        {
                                            originalUrl.Append("&XFROUT-IN-LOCATION=");
                                            originalUrl.Append(employeeInfo.TransferToStoreNumber);
                                        }

                                        if (ConvertStringToDate(employeeInfo.TransferToDate) != new DateTime())
                                        {
                                            originalUrl.Append("&XFROUT-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.TransferToDate).ToString("yyyyMMdd"));
                                        }
                                        break;

                                    case LawsonActionLeaveOfAbsence:
                                        if (employeeInfo.LOAType.Length > 0)
                                        {
                                            originalUrl.Append("&LOA-TYPE=");
                                            originalUrl.Append(employeeInfo.LOAType);
                                        }

                                        if (ConvertStringToDate(employeeInfo.LOABeginDate) != new DateTime())
                                        {
                                            originalUrl.Append("&LOA-BEGIN-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.LOABeginDate).ToString("yyyyMMdd"));
                                        }

                                        if (ConvertStringToDate(employeeInfo.LastDateWorked) != new DateTime())
                                        {
                                            originalUrl.Append("&LOA-LAST-DAY-WORKD=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.LastDateWorked).ToString("yyyyMMdd"));
                                        }

                                        if (ConvertStringToDate(employeeInfo.LOAEndDate) != new DateTime())
                                        {
                                            originalUrl.Append("&LOA-END-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.LOAEndDate).ToString("yyyyMMdd"));
                                        }

                                        if (ConvertStringToDate(employeeInfo.LOAReturnedFromDate) != new DateTime())
                                        {
                                            originalUrl.Append("&LOA-RET-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.LOAReturnedFromDate).ToString("yyyyMMdd"));
                                        }

                                        if (ConvertStringToDate(employeeInfo.LOAInjuryDate) != new DateTime())
                                        {
                                            originalUrl.Append("&LOA-INJ-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.LOAInjuryDate).ToString("yyyyMMdd"));
                                        }

                                        if (employeeInfo.Comment1.Length > 0)
                                        {
                                            originalUrl.Append("&LOA-REASON1=");
                                            originalUrl.Append(employeeInfo.Comment1);
                                        }

                                        if (employeeInfo.Comment2.Length > 0)
                                        {
                                            originalUrl.Append("&LOA-REASON2=");
                                            originalUrl.Append(employeeInfo.Comment2);
                                        }

                                        if (employeeInfo.Comment3.Length > 0)
                                        {
                                            originalUrl.Append("&LOA-REASON3=");
                                            originalUrl.Append(employeeInfo.Comment3);
                                        }

                                        if (employeeInfo.Comment4.Length > 0)
                                        {
                                            originalUrl.Append("&LOA-REASON4=");
                                            originalUrl.Append(employeeInfo.Comment4);
                                        }
                                        break;

                                    case LawsonActionLeaveOfAbsenceEnd:
                                        if (ConvertStringToDate(employeeInfo.LOAReturnedFromDate) != new DateTime())
                                        {
                                            originalUrl.Append("&LOAEND-END-DATE=");
                                            originalUrl.Append(ConvertStringToDate(employeeInfo.LOAReturnedFromDate).ToString("yyyyMMdd"));
                                        }

                                        if (employeeInfo.Comment1.Length > 0)
                                        {
                                            originalUrl.Append("&LOAEND-REASON1=");
                                            originalUrl.Append(employeeInfo.Comment1);
                                        }

                                        if (employeeInfo.Comment2.Length > 0)
                                        {
                                            originalUrl.Append("&LOAEND-REASON2=");
                                            originalUrl.Append(employeeInfo.Comment2);
                                        }

                                        if (employeeInfo.Comment3.Length > 0)
                                        {
                                            originalUrl.Append("&LOAEND-REASON3=");
                                            originalUrl.Append(employeeInfo.Comment3);
                                        }

                                        if (employeeInfo.Comment4.Length > 0)
                                        {
                                            originalUrl.Append("&LOAEND-REASON4=");
                                            originalUrl.Append(employeeInfo.Comment4);
                                        }
                                        break;

                                    case LawsonActionUpdate:
                                        // Address data...
                                        if (employeeInfo.Address1.Length > 0)
                                        {
                                            originalUrl.Append("&UPD-ADDR1=");
                                            originalUrl.Append(employeeInfo.Address1);

                                            lawsonActionUpdateFlags.AddressChange = true;
                                        }

                                        // Always send ADDR2...
                                        originalUrl.Append("&UPD-ADDR2=");
                                        originalUrl.Append(employeeInfo.Address2);

                                        if (employeeInfo.City.Length > 0)
                                        {
                                            originalUrl.Append("&UPD-CITY=");
                                            originalUrl.Append(employeeInfo.City);

                                            lawsonActionUpdateFlags.AddressChange = true;
                                        }

                                        if (employeeInfo.State.Length > 0)
                                        {
                                            originalUrl.Append("&UPD-STATE=");
                                            originalUrl.Append(employeeInfo.State);

                                            lawsonActionUpdateFlags.AddressChange = true;
                                        }

                                        if (employeeInfo.ZIP.Length > 0)
                                        {
                                            originalUrl.Append("&UPD-ZIP=");
                                            originalUrl.Append(JIB.Framework.Utilities.Strings.LeftSafe(employeeInfo.ZIP, ZipCodeLengthToLawson));

                                            lawsonActionUpdateFlags.AddressChange = true;
                                        }

                                        if (employeeInfo.Phone.Length > 0)
                                        {
                                            originalUrl.Append("&UPD-HM-PHONE-NBR=");
                                            originalUrl.Append(ConvertPhoneNumber(employeeInfo.Phone));

                                            lawsonActionUpdateFlags.PhoneNumberChange = true;
                                        }

                                        if (employeeInfo.NoReview != null)
                                        {
                                            if (employeeInfo.NoReview.Length > 0)
                                            {
                                                if (string.Compare(employeeInfo.NoReview, "Y", true) == 0)
                                                {
                                                    originalUrl.Append("&UPD-NO-REVIEW=");
                                                    originalUrl.Append(employeeInfo.NoReview.ToUpper());
                                                }
                                            }
                                        }

                                        // Job code, prev, new
                                        if (employeeInfo.JobCode.Length > 0)
                                        {
                                            if (string.Compare(employeeInfo.JobCode, employeeInfo.JobCodePrevious, true) != 0)
                                            {
                                                originalUrl.Append("&UPD-JOB-CODE-DATE=");
                                                originalUrl.Append(ConvertStringToDate(employeeInfo.JobEffectiveDate).ToString("yyyyMMdd"));

                                                originalUrl.Append("&UPD-JOB-CODE-PREV=");
                                                originalUrl.Append(employeeInfo.JobCodePrevious);

                                                originalUrl.Append("&UPD-JOB-CODE-NEW=");
                                                originalUrl.Append(employeeInfo.JobCode);

                                                lawsonActionUpdateFlags.JobChange = true;
                                            }
                                        }

                                        // Pay rate, prev, new
                                        if (employeeInfo.PayRate > 0)
                                        {
                                            if (employeeInfo.PayRate != employeeInfo.PayRatePrevious)
                                            {
                                                originalUrl.Append("&UPD-HRLY-PAY-DATE=");
                                                originalUrl.Append(ConvertStringToDate(employeeInfo.PayEffectiveDate).ToString("yyyyMMdd"));

                                                originalUrl.Append("&UPD-HRLY-PAY-PREV=");
                                                originalUrl.Append(employeeInfo.PayRatePrevious);

                                                originalUrl.Append("&UPD-HRLY-PAY-NEW=");
                                                originalUrl.Append(employeeInfo.PayRate);

                                                lawsonActionUpdateFlags.PayRateChange = true;
                                            }
                                        }

                                        // Update pay card, prev, new
                                        if (!string.IsNullOrWhiteSpace(employeeInfo.PayCardNumber))
                                        {
                                            Logging.WriteLog(logFile, "Client is sending clear text PayCardNumber during Update");
                                            // Web service must still support old proxy clients so continue to evaluate these fields.
                                            if (string.Compare(employeeInfo.PayCardNumber, employeeInfo.PayCardNumberPrevious, true) != 0)
                                            {
                                                originalUrl.Append("&UPD-PAY-CARD-NBR-PREV=");
                                                originalUrl.Append(employeeInfo.PayCardNumberPrevious);

                                                originalUrl.Append("&UPD-PAY-CARD-NBR-NEW=");
                                                originalUrl.Append(employeeInfo.PayCardNumber);
                                            }
                                        }
                                        else if (!string.IsNullOrWhiteSpace(employeeInfo.PayCardNumEncrypted))
                                        {
                                            Logging.WriteLog(logFile, "Client is sending PayCardNumEncrypted during Update");
                                            // New proxy client will only send PayCard data via encrypted fields. Lawson AGS expects it decrypted.
                                            if (string.Compare(employeeInfo.PayCardNumEncrypted, employeeInfo.PayCardNumPreviousEncrypted, true) != 0)
                                            {
                                                Logging.WriteLog(logFile, "Client sent employeeInfo.PayCardNumPreviousEncrypted: " + employeeInfo.PayCardNumPreviousEncrypted);
                                                string payCardNumPrev = Encryption.Decrypt(employeeInfo.PayCardNumPreviousEncrypted);
                                                originalUrl.Append("&UPD-PAY-CARD-NBR-PREV=");
                                                originalUrl.Append(payCardNumPrev);

                                                Logging.WriteLog(logFile, "Client sent employeeInfo.PayCardNumEncrypted: " + employeeInfo.PayCardNumEncrypted);
                                                string payCardNum = Encryption.Decrypt(employeeInfo.PayCardNumEncrypted);
                                                originalUrl.Append("&UPD-PAY-CARD-NBR-NEW=");
                                                originalUrl.Append(payCardNum);
                                            }
                                        }

                                        // Only do these for Franchises...
                                        if (employeeInfo.Company.Length > 0)
                                        {
                                            if (JIB.Framework.Utilities.Strings.IsNumeric(employeeInfo.Company) == true)
                                            {
                                                int company = int.Parse(employeeInfo.Company);
                                                if (company > CompanyJackInTheBox)
                                                {
                                                    if (employeeInfo.LastName.Length > 0)
                                                    {
                                                        originalUrl.Append("&UPD-LAST-NAME=");
                                                        originalUrl.Append(employeeInfo.LastName);

                                                        lawsonActionUpdateFlags.NameChange = true;
                                                    }

                                                    if (employeeInfo.FirstName.Length > 0)
                                                    {
                                                        originalUrl.Append("&UPD-FIRST-NAME=");
                                                        originalUrl.Append(employeeInfo.FirstName);

                                                        lawsonActionUpdateFlags.NameChange = true;
                                                    }

                                                    if (employeeInfo.MiddleName.Length > 0)
                                                    {
                                                        originalUrl.Append("&UPD-MIDDLE-NAME=");
                                                        originalUrl.Append(employeeInfo.MiddleName);

                                                        lawsonActionUpdateFlags.NameChange = true;
                                                    }

                                                    if (socialSecurityNumber.Length > 0)
                                                    {
                                                        originalUrl.Append("&UPD-FICA-NBR=");
                                                        originalUrl.Append(socialSecurityNumber);
                                                    }

                                                    if (ConvertStringToDate(employeeInfo.BirthDate) != new DateTime())
                                                    {
                                                        originalUrl.Append("&UPD-BIRTHDATE=");
                                                        originalUrl.Append(ConvertStringToDate(employeeInfo.BirthDate).ToString("yyyyMMdd"));
                                                    }

                                                    if (employeeInfo.GenderCode.Length > 0)
                                                    {
                                                        originalUrl.Append("&UPD-SEX=");
                                                        originalUrl.Append(employeeInfo.GenderCode);
                                                    }

                                                    if (employeeInfo.RaceCode.Length > 0)
                                                    {
                                                        originalUrl.Append("&UPD-EEO-CLASS=");
                                                        originalUrl.Append(employeeInfo.RaceCode);
                                                    }

                                                    if (ConvertStringToDate(employeeInfo.HiredDate) != new DateTime())
                                                    {
                                                        originalUrl.Append("&UPD-DATE-HIRED=");
                                                        originalUrl.Append(ConvertStringToDate(employeeInfo.HiredDate).ToString("yyyyMMdd"));
                                                    }
                                                }
                                            }
                                        }

                                        AddI9DataToAGSCall(employeeInfo, ref originalUrl);
                                        break;

                                    case LawsonActionQuery:
                                        if (JIB.Framework.Utilities.Strings.IsNumeric(employeeInfo.BadgeNumber) == true)
                                        {
                                            originalUrl.Append("&BADGE-ID=");
                                            originalUrl.Append(employeeInfo.BadgeNumber);
                                        }
                                        else
                                        {
                                            originalUrl.Append("&NETWORK-ID=");
                                            originalUrl.Append(employeeInfo.BadgeNumber);
                                        }
                                        break;

                                    case LawsonActionSnapshot:
                                        originalUrl.Append("&MGR-LOCAT-CODE=");
                                        originalUrl.Append(storeAuthenticator.StoreNumber);

                                        break;
                                }

                                // Add terminator...
                                originalUrl.Append("&_EOT=TRUE");

                                // Convert...
                                originalUrl = ConvertSpecialToHex(originalUrl);
                                agsCall.Append(originalUrl);

                                // Add trailer stuff...
                                agsCall.Append("&_serviceName=SSOP");

                                Logging.WriteLog(logFile, "AGS Call: " + agsCall.ToString());

                                System.Net.HttpWebRequest webRequest = (System.Net.HttpWebRequest)System.Net.HttpWebRequest.Create(agsCall.ToString());
                                webRequest.Accept = "text/xml,application/xml,text/html";
                                webRequest.UserAgent = "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; InfoPath.1)";
                                webRequest.Method = "GET";
                                webRequest.CookieContainer = CookieContainer;

                                System.Net.HttpWebResponse webResponse = null;

                                try
                                {
                                    webResponse = (System.Net.HttpWebResponse)webRequest.GetResponse();
                                    Logging.WriteLog(logFile, "AGS Call Complete");
                                }

                                catch (Exception e)
                                {
                                    Logging.WriteLog(logFile, "Exception: " + messageLawsonUnavailable);
                                    Logging.WriteLog(logFileExceptions, "Exception: " + messageLawsonUnavailable);
                                    throw new SystemException(messageLawsonUnavailable, e);
                                }

                                #endregion

                                #region Put Web Response in XML Document, Get Results

                                int returnCode = (int)ApplicationReturnValue.Failure;
                                string returnCodeCategory = string.Empty;
                                int lawsonReturnValue = 0;
                                string applicationReturnMessage1 = "";
                                string applicationReturnMessage2 = "";
                                string applicationReturnMessage3 = "";
                                string applicationReturnMessage4 = "";
                                string applicationReturnMessage5 = "";
                                string lawsonReturnMessage = "";

                                JIB.Framework.Persistence.OleDb persistenceLawson = null;

                                try
                                {
                                    string navigatorRoot = string.Empty;
                                    switch (action.ToUpper())
                                    {
                                        case LawsonActionHirePart2:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeHirePart2XMLNavigatorRoot"];
                                            break;
                                        case LawsonActionBorrow:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeBorrowXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionTerminate:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeTerminateXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionTerminateCancel:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeTerminateCancelXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionTransferIn:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeTransferInXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionTransferOut:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeTransferOutXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionLeaveOfAbsence:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeLeaveOfAbsenceXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionLeaveOfAbsenceEnd:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeLeaveOfAbsenceEndXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionUpdate:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeUpdateXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionQuery:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeQueryXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionRestQuery:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonRestQueryXMLNavigatorRoot"];
                                            break;
                                        case LawsonActionSnapshot:
                                            navigatorRoot = System.Configuration.ConfigurationManager.AppSettings["LawsonEmployeeSnapshotXMLNavigatorRoot"];
                                            break;
                                    }

                                    Logging.WriteLog(logFile, "XML Root from config: " + navigatorRoot);
                                    System.Xml.XmlTextReader xmlTextReader = new System.Xml.XmlTextReader(webResponse.GetResponseStream());
                                    Logging.WriteLog(logFile, "XML Text Reader Read: " + xmlTextReader);

                                    //						System.Xml.XmlTextReader xmlTextReaderDump = null;
                                    //						xmlTextReaderDump = xmlTextReader;
                                    //						while (xmlTextReader.Read())
                                    //						{
                                    //							switch (xmlTextReaderDump.NodeType)
                                    //							{
                                    //								case System.Xml.XmlNodeType.Element: // The node is an element.
                                    //									Logging.WriteLog(logFile, "<" + xmlTextReaderDump.Name);
                                    //									Logging.WriteLog(logFile, ">");
                                    //									break;
                                    //								case System.Xml.XmlNodeType.Text: //Display the text in each element.
                                    //									Logging.WriteLog(logFile, xmlTextReaderDump.Value);
                                    //									break;
                                    //								case System.Xml.XmlNodeType.EndElement: //Display the end of the element.
                                    //									Logging.WriteLog(logFile, "</" + xmlTextReaderDump.Name);
                                    //									Logging.WriteLog(logFile, ">");
                                    //									break;
                                    //							}
                                    //						}

                                    XPathDocument configDocument = new XPathDocument(xmlTextReader);
                                    Logging.WriteLog(logFile, "XML Document Loaded");
                                    XPathNavigator configNav = configDocument.CreateNavigator();

                                    XPathNodeIterator configIter = null;
                                    string elementValue = null;

                                    // Custom return code - 1=success, might be empty...
                                    configIter = configNav.Select(navigatorRoot + "RETURN-CODE");
                                    elementValue = GetElementValue(configIter);
                                    returnCode = (int)ApplicationReturnValue.Failure;
                                    if (elementValue.Length > 0)
                                    {
                                        returnCode = System.Convert.ToInt32(elementValue);
                                    }

                                    configIter = configNav.Select(navigatorRoot + "RETURN-CODE-CAT");
                                    elementValue = GetElementValue(configIter);
                                    returnCodeCategory = elementValue;

                                    //						 Lawson error and message...
                                    configIter = configNav.Select(navigatorRoot + "MsgNbr");
                                    elementValue = GetElementValue(configIter);
                                    lawsonReturnValue = System.Convert.ToInt32(elementValue);

                                    configIter = configNav.Select(navigatorRoot + "Message");
                                    elementValue = GetElementValue(configIter);
                                    lawsonReturnMessage = elementValue;

                                    // Returned values...
                                    configIter = configNav.Select(navigatorRoot + "WEB-EMPLOYEE");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.EmployeeNumber = elementValue;

                                    Logging.WriteLog(logFile, "WEB-EMPLOYEE: " + employeeInfo.EmployeeNumber);

                                    configIter = configNav.Select(navigatorRoot + "WEB-COMPANY");
                                    if (configIter != null)
                                    {
                                        elementValue = GetElementValue(configIter);
                                        if (elementValue != null)
                                        {
                                            employeeInfo.Company = elementValue;
                                        }
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-SUPER-KEY");
                                    if (configIter != null)
                                    {
                                        elementValue = GetElementValue(configIter);
                                        if (elementValue != null)
                                        {
                                            employeeInfo.SuperKey = elementValue;
                                        }
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-HOME-LOCAT-CODE");
                                    if (configIter != null)
                                    {
                                        elementValue = GetElementValue(configIter);
                                        if (elementValue != null)
                                        {
                                            employeeInfo.HomeLocationCode = elementValue;
                                        }
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-EMP-STATUS");
                                    if (configIter != null)
                                    {
                                        elementValue = GetElementValue(configIter);
                                        if (elementValue != null)
                                        {
                                            employeeInfo.StatusCode = elementValue;
                                        }
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-FICA-NBR");
                                    elementValue = GetElementValue(configIter);
                                    Logging.WriteLog(logFile, "WEB-FICA-NBR: " + elementValue);
                                    if (string.Compare(elementValue, "***********", true) == 0)
                                    {
                                        employeeInfo.FICANumber = JIB.Framework.Security.Encryption.EncryptRijndaelManaged(string.Empty, key, iv);
                                    }
                                    else
                                    {
                                        employeeInfo.FICANumber = JIB.Framework.Security.Encryption.EncryptRijndaelManaged(elementValue, key, iv);
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-LAST-NAME");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.LastName = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-FIRST-NAME");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.FirstName = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-MIDDLE-NAME");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.MiddleName = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-ADDR1");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Address1 = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-ADDR2");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Address2 = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-CITY");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.City = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-STATE");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.State = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-ZIP");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.ZIP = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-PHONE");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Phone = ConvertPhoneNumber(elementValue, true);

                                    configIter = configNav.Select(navigatorRoot + "WEB-JOB-CODE");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.JobCode = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-BORROWED-FLAG");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.BorrowedIndicator = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-EMP-PAY-RATE");
                                    elementValue = GetElementValue(configIter);
                                    Logging.WriteLog(logFile, "WEB-EMP-PAY-RATE: " + elementValue);
                                    if (Strings.IsNumeric(elementValue) == true)
                                    {
                                        employeeInfo.PayRate = Convert.ToDecimal(elementValue);
                                    }
                                    else
                                    {
                                        employeeInfo.PayRate = 0;
                                    }
                                    Logging.WriteLog(logFile, "employeeInfo.PayRate: " + employeeInfo.PayRate.ToString());

                                    configIter = configNav.Select(navigatorRoot + "WEB-PAY-EFF-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.PayEffectiveDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.PayEffectiveDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-BIRTHDATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.BirthDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.BirthDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-HIRE-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.HiredDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.HiredDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-ORIG-HIRE-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.HiredDateOriginal = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-PPAT-POINTS");
                                    elementValue = GetElementValue(configIter);
                                    if (Strings.IsNumeric(elementValue) == true)
                                    {
                                        employeeInfo.Points = elementValue.ToString();
                                    }
                                    else
                                    {
                                        employeeInfo.Points = "0";
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-PPAT-SCORE");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Score = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-JOB-CODE-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.JobEffectiveDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.JobEffectiveDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-BADGE-ID");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.BadgeNumber = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-SEX");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.GenderCode = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-PAY-CARD-NBR");
                                    // PayCard data will still return from Lawson AGS clear text so do not attempt to decrypt.
                                    elementValue = GetElementValue(configIter);
                                    bool stopTransmittingPlainTextPayCard = false;
                                    stopTransmittingPlainTextPayCard = bool.Parse(System.Configuration.ConfigurationManager.AppSettings["StopTransmittingPlainTextPayCard"]);
                                    // Continue to populate this field to satisfy old proxy clients that expect clear text PayCard data until it's verified they no longer use it.
                                    if (!stopTransmittingPlainTextPayCard)
                                    {
                                        Logging.WriteLog(logFile, "StopTransmittingPlainTextPayCard is disabled in web.config");
                                        employeeInfo.PayCardNumber = elementValue;
                                    }
                                    else
                                    {
                                        employeeInfo.PayCardNumber = string.Empty;
                                    }
                                    // Encrypt when sending back to new proxy clients. If both fields (encrypted and clear text) are somehow populated this will not harm old or new clients.
                                    //Logging.WriteLog(logFile, "Server will always return PayCardNumberEncrypted if it exists, regardless of configuration");
                                    employeeInfo.PayCardNumEncrypted = Encryption.Encrypt(elementValue);

                                    configIter = configNav.Select(navigatorRoot + "WEB-EEO-CLASS");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.RaceCode = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-TRANSFER-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.TransferInDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.TransferInDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-TERM-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.TerminationDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.TerminationDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-PREV-TERM-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.TerminationDatePrevious = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.TerminationDatePrevious = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-WK-EXP-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.WorkExperienceDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.WorkExperienceDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-EXP-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.I9ExpirationDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.I9ExpirationDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-MAIDEN-NAME");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Maiden = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-CITIZEN");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Citizen = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-NON-CITIZEN");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.NonCitizen = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-PERMANENT");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Permanent = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-ALIEN");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Alien = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-ADMISSION");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Admission = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-DOC1-NBR");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Doc1Number = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-DOC1-EXP-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.ExpirationDate1 = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.ExpirationDate1 = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-DOC2-NBR");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.Doc2Number = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-DOC2-EXP-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.ExpirationDate2 = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.ExpirationDate2 = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-ALIEN-NBR");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.AlienNumber = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-ALIEN-EXP-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.AlienExpirationDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.AlienExpirationDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-ISSUE-AUTH-1");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.IssueWho1 = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-ISSUE-AUTH-2");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.IssueWho2 = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-I9A-DESC");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.I9ADescription = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-I9B-DESC");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.I9BDescription = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-I9-I9C-DESC");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.I9CDescription = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-LOA-FLAG");
                                    elementValue = GetElementValue(configIter);
                                    employeeInfo.LOAType = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-SURVEY-FLAG");
                                    elementValue = GetElementValue(configIter);
                                    // On rejected transactions, this may not be set, so default...
                                    employeeInfo.SurveyFlag = 0;
                                    if (elementValue != null)
                                    {
                                        if (elementValue.Length > 0)
                                        {
                                            if (JIB.Framework.Utilities.Strings.IsNumeric(elementValue) == true)
                                            {
                                                employeeInfo.SurveyFlag = Convert.ToInt32(elementValue);
                                            }
                                        }
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-CBT-FLAG");
                                    elementValue = GetElementValue(configIter);
                                    // On rejected transactions, this may not be set, so default...
                                    employeeInfo.EligibleForTrainingCode = 0;
                                    if (elementValue != null)
                                    {
                                        if (elementValue.Length > 0)
                                        {
                                            if (JIB.Framework.Utilities.Strings.IsNumeric(elementValue) == true)
                                            {
                                                employeeInfo.EligibleForTrainingCode = Convert.ToInt32(elementValue);
                                            }
                                        }
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-CLOCK-TYPE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.ClockType = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.ClockType = elementValue;
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-LOA-BEGIN-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.LOABeginDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.LOABeginDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-LOA-END-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.LOAEndDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.LOAEndDate = ConvertDateToString(new DateTime());
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-LOA-INJURY-DATE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue.Length > 0)
                                    {
                                        employeeInfo.LOAInjuryDate = ConvertDateToString(ConvertStringToDate(elementValue));
                                    }
                                    else
                                    {
                                        employeeInfo.LOAInjuryDate = ConvertDateToString(new DateTime());
                                    }

                                    // Get the GM Fields if the fields are there...
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-HM-LOCAT-CODE");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMHomeLocation = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMHomeLocation = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE1");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation01 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation01 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE2");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation02 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation02 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE3");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation03 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation03 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE4");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation04 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation04 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE5");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation05 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation05 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE6");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation06 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation06 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE7");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation07 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation07 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE8");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation08 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation08 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE9");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation09 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation09 = elementValue;
                                    }
                                    configIter = configNav.Select(navigatorRoot + "WEB-GM-LOCAT-CODE10");
                                    elementValue = GetElementValue(configIter);
                                    if (elementValue == null)
                                    {
                                        employeeInfo.GMLocation10 = string.Empty;
                                    }
                                    else
                                    {
                                        employeeInfo.GMLocation10 = elementValue;
                                    }

                                    // Matrix error and message...
                                    configIter = configNav.Select(navigatorRoot + "MSG-EDIT-NBR");
                                    elementValue = GetElementValue(configIter);
                                    int applicationReturnValue = 0;
                                    if (Strings.IsNumeric(elementValue) == true)
                                    {
                                        applicationReturnValue = System.Convert.ToInt32(elementValue);
                                    }
                                    else
                                    {
                                        applicationReturnValue = 0;
                                    }

                                    configIter = configNav.Select(navigatorRoot + "WEB-EDIT-MESSAGE1");
                                    elementValue = GetElementValue(configIter);
                                    applicationReturnMessage1 = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-EDIT-MESSAGE2");
                                    elementValue = GetElementValue(configIter);
                                    applicationReturnMessage2 = elementValue;

                                    configIter = configNav.Select(navigatorRoot + "WEB-EDIT-MESSAGE3");
                                    elementValue = GetElementValue(configIter);
                                    applicationReturnMessage3 = elementValue;

                                    employeeInfo.ApplicationReturnMessage4 = "";
                                    configIter = configNav.Select(navigatorRoot + "WEB-EDIT-MESSAGE4");
                                    elementValue = GetElementValue(configIter);
                                    applicationReturnMessage4 = elementValue;

                                    employeeInfo.ApplicationReturnMessage5 = "";
                                    configIter = configNav.Select(navigatorRoot + "WEB-EDIT-MESSAGE5");
                                    elementValue = GetElementValue(configIter);
                                    applicationReturnMessage5 = elementValue;

                                    // Reset some of these..
                                    employeeInfo.StatusCode = "0";
                                    switch (action)
                                    {
                                        case LawsonActionTerminateCancel:
                                            employeeInfo.TerminationAction = string.Empty;
                                            employeeInfo.TerminationDate = ConvertDateToString(new DateTime());
                                            employeeInfo.TerminationReason = string.Empty;
                                            employeeInfo.LastDateWorked = ConvertDateToString(new DateTime());
                                            break;
                                    }

                                    // Publish the transaction...
                                    if (returnCode == (int)ApplicationReturnValue.Success)
                                    {
                                        switch (action.ToUpper())
                                        {
                                            case LawsonActionBorrow:
                                            case LawsonActionHirePart2:
                                            case LawsonActionLeaveOfAbsence:
                                            case LawsonActionLeaveOfAbsenceEnd:
                                            case LawsonActionLoan:
                                            case LawsonActionPasswordChange:
                                            case LawsonActionTerminate:
                                            case LawsonActionTerminateCancel:
                                            case LawsonActionTransferIn:
                                            case LawsonActionTransferOut:
                                            case LawsonActionUpdate:
                                                Logging.WriteLog(logFile, "Publishing Transaction");
                                                Publish(action.ToUpper(), employeeInfo, logFile);
                                                Logging.WriteLog(logFile, "Publishing Complete");

                                                string lawsonAction = action.ToUpper();
                                                AttemptSendToNServiceBus(lawsonAction, lawsonActionUpdateFlags, employeeInfo, logFile, logFileExceptions);
                                                break;
                                        }
                                    }

                                    #region Commented Out Foundation Gallery Code

                                    // Add, Update or get the Biometric data...
                                    // Only do this if we have a connection to the biometric database...for compatibility...
                                    //								if (persistenceFoundationGallery != null)
                                    //								{
                                    //									if (returnCode == (int) ApplicationReturnValue.Success)
                                    //									{
                                    //										Logging.WriteLog(logFile, "Getting Biometric Data");
                                    //										// This defaults the NewFlag to true...
                                    //										JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery);
                                    //
                                    //										// See if we have a super key...default to a negative combo...
                                    //										string superKeyCompany = employeeInfo.Company.PadLeft(4, '0');
                                    //										string superKeyEmployeeNumber = employeeInfo.EmployeeNumber.PadLeft(9, '0');
                                    //										Logging.WriteLog(logFile, "Using Company: " + superKeyCompany + " EmpNumber: " + superKeyEmployeeNumber);
                                    //										long superKeyTemp = -long.Parse(superKeyCompany + superKeyEmployeeNumber);
                                    //										long superKeyLawson = 0;
                                    //										if (employeeInfo.SuperKey != null)
                                    //										{
                                    //											if (employeeInfo.SuperKey.Length > 0)
                                    //											{
                                    //												Logging.WriteLog(logFile, "SuperKey from Lawson: " + employeeInfo.SuperKey);
                                    //												superKeyLawson = long.Parse(employeeInfo.SuperKey);
                                    //											}
                                    //										}
                                    //
                                    //										Logging.WriteLog(logFile, "superKeyTemp: " + superKeyTemp.ToString());
                                    //										Logging.WriteLog(logFile, "superKeyLawson: " + superKeyLawson.ToString());
                                    //
                                    //										switch (action)
                                    //										{
                                    //											case LawsonActionHirePart2:
                                    //											case LawsonActionUpdate:
                                    //												//
                                    //												// This adds/updates the biometric in the database...
                                    //												//
                                    //
                                    //												// Only do this if we have a database connection...
                                    //												if (persistenceFoundationGallery != null)
                                    //												{
                                    //													// Save the Biometric data...if we have it...
                                    //													if (employeeInfo.Biometric01 != null)
                                    //													{
                                    //														if (employeeInfo.Biometric01.Length > 0)
                                    //														{
                                    //
                                    //															// If Lawson has a superkey then try to find the temp and delete it...
                                    //															if (superKeyLawson != 0)
                                    //															{
                                    //																// Try to find the temp super key...
                                    //																employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery, superKeyTemp);
                                    //																if (employee.NewFlag == false)
                                    //																{
                                    //																	employee.Delete();
                                    //																}
                                    //															}
                                    //														
                                    //															// Now use the best superkey...
                                    //															long superKey = superKeyLawson;
                                    //															if (superKey == 0)
                                    //															{
                                    //																superKey = superKeyTemp;
                                    //															}
                                    //															Logging.WriteLog(logFile, "Adding record for super key: " + superKey.ToString());
                                    //															// Save it in the database...
                                    //															employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery, superKey);
                                    //															employee.StatusCode = (short)JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee.StatusCodeValues.Active;
                                    //															Logging.WriteLog(logFile, "Got Employee from FG");
                                    //															if (employeeInfo.Biometric01 != null)
                                    //															{
                                    //																Logging.WriteLog(logFile, "Serializing Biometric01 length: " + employeeInfo.Biometric01.Length.ToString());
                                    //																employee.Biometric01 = SerializeBytes(employeeInfo.Biometric01);
                                    //															}
                                    //															if (employeeInfo.Biometric02 != null)
                                    //															{
                                    //																Logging.WriteLog(logFile, "Serializing Biometric02 length: " + employeeInfo.Biometric02.Length.ToString());
                                    //																employee.Biometric02 = SerializeBytes(employeeInfo.Biometric02);
                                    //															}
                                    //															Logging.WriteLog(logFile, "Saving FG Data");
                                    //															employee.Save();
                                    //															Logging.WriteLog(logFile, "Saved");
                                    //														}
                                    //													}
                                    //												}
                                    //												break;
                                    //
                                    //											case LawsonActionBorrow:
                                    //											case LawsonActionLeaveOfAbsenceEnd:
                                    //											case LawsonActionTransferIn:
                                    //												//
                                    //												// This gets the biometric and sends it back to the store...
                                    //												//
                                    //
                                    //												// Only do this if we have a database connection...
                                    //												if (persistenceFoundationGallery != null)
                                    //												{
                                    //													// Return the Biometric data...
                                    //													// .. first try the Lawson superkey - if it is zero, don't bother...
                                    //													// .. the NewFlag of employee was set by default above so this is safe...
                                    //													if (superKeyLawson != 0)
                                    //													{
                                    //														employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery, superKeyLawson);
                                    //													}
                                    //													if (employee.NewFlag == true)
                                    //													{
                                    //														// Try the temporary superkey...
                                    //														employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery, superKeyTemp);
                                    //													}
                                    //													if (employee.NewFlag == false)
                                    //													{
                                    //														employeeInfo.Biometric01 = DeSerializeBytes(employee.Biometric01);
                                    //														employeeInfo.Biometric02 = DeSerializeBytes(employee.Biometric02);
                                    //													}
                                    //												}
                                    //												break;
                                    //
                                    //											case LawsonActionTerminate:
                                    //												//
                                    //												// This sets the Status Code to Inactive...
                                    //												//
                                    //
                                    //												// Only do this if we have a database connection...
                                    //												if (persistenceFoundationGallery != null)
                                    //												{
                                    //													// Return the Biometric data...
                                    //													if (superKeyLawson != 0)
                                    //													{
                                    //														employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery, superKeyLawson);
                                    //													}
                                    //													if (employee.NewFlag == true)
                                    //													{
                                    //														// Try the temporary superkey...
                                    //														employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery, superKeyTemp);
                                    //													}
                                    //													if (employee.NewFlag == false)
                                    //													{
                                    //														employee.StatusCode = (short)JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee.StatusCodeValues.Inactive;
                                    //														employee.Save();
                                    //													}
                                    //												}
                                    //												break;
                                    //
                                    //											case LawsonActionTerminateCancel:
                                    //												//
                                    //												// This sets the Status Code to Active...
                                    //												//
                                    //
                                    //												// Only do this if we have a database connection...
                                    //												if (persistenceFoundationGallery != null)
                                    //												{
                                    //													// Return the Biometric data...
                                    //													if (superKeyLawson != 0)
                                    //													{
                                    //														employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery, superKeyLawson);
                                    //													}
                                    //													if (employee.NewFlag == true)
                                    //													{
                                    //														// Try the temporary superkey...
                                    //														employee = new JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee(persistenceFoundationGallery, superKeyTemp);
                                    //													}
                                    //													if (employee.NewFlag == false)
                                    //													{
                                    //														employee.StatusCode = (short)JIB.FoundationGallery.Biometric.Data.BusinessLayer.Employee.StatusCodeValues.Active;
                                    //														employee.Save();
                                    //														// Return the biometrics...
                                    //														employeeInfo.Biometric01 = DeSerializeBytes(employee.Biometric01);
                                    //														employeeInfo.Biometric02 = DeSerializeBytes(employee.Biometric02);
                                    //													}
                                    //												}
                                    //												break;
                                    //										}
                                    //									}
                                    //								}
                                    #endregion

                                    switch (action)
                                    {
                                        case LawsonActionRestQuery:
                                            configIter = null;
                                            elementValue = null;

                                            System.Collections.ArrayList items = new ArrayList();
                                            // Lawson may return duplicate entries, so they are filtered out here...
                                            System.Collections.Specialized.ListDictionary employeeIDs = new System.Collections.Specialized.ListDictionary();

                                            int index = -1;
                                            bool done = false;
                                            while (done == false)
                                            {
                                                index++;
                                                Logging.WriteLog(logFile, "Index: " + index.ToString());
                                                configIter = configNav.Select(navigatorRoot + "WEB-REST-EMPLOYEEr" + index.ToString());
                                                elementValue = GetElementValue(configIter);
                                                if (Convert.ToInt32(elementValue) == 0)
                                                {
                                                    done = true;
                                                }
                                                else
                                                {
                                                    if (employeeIDs.Contains(elementValue) == false)
                                                    {
                                                        EmployeeInformation employeeInfoItem = new EmployeeInformation();

                                                        Logging.WriteLog(logFile, "EmployeeID: " + elementValue);

                                                        employeeInfoItem.EmployeeNumber = elementValue;

                                                        configIter = configNav.Select(navigatorRoot + "WEB-REST-SUPER-KEYr" + index.ToString());
                                                        elementValue = GetElementValue(configIter);
                                                        employeeInfoItem.SuperKey = elementValue;

                                                        configIter = configNav.Select(navigatorRoot + "WEB-REST-LAST-NAMEr" + index.ToString());
                                                        elementValue = GetElementValue(configIter);
                                                        employeeInfoItem.LastName = elementValue;

                                                        configIter = configNav.Select(navigatorRoot + "WEB-REST-FIRST-NAMEr" + index.ToString());
                                                        elementValue = GetElementValue(configIter);
                                                        employeeInfoItem.FirstName = elementValue;

                                                        configIter = configNav.Select(navigatorRoot + "WEB-REST-MIDDLE-INITr" + index.ToString());
                                                        elementValue = GetElementValue(configIter);
                                                        employeeInfoItem.MiddleName = elementValue;

                                                        configIter = configNav.Select(navigatorRoot + "WEB-REST-BADGE-IDr" + index.ToString());
                                                        elementValue = GetElementValue(configIter);
                                                        employeeInfoItem.BadgeNumber = elementValue;

                                                        configIter = configNav.Select(navigatorRoot + "WEB-REST-JOB-CODEr" + index.ToString());
                                                        elementValue = GetElementValue(configIter);
                                                        employeeInfoItem.JobCode = elementValue;

                                                        configIter = configNav.Select(navigatorRoot + "WEB-REST-HIRE-DATEr" + index.ToString());
                                                        elementValue = GetElementValue(configIter);
                                                        employeeInfoItem.HiredDate = ConvertDateToString(ConvertStringToDate(elementValue));

                                                        items.Add(employeeInfoItem);

                                                        Logging.WriteLog(logFile, "Adding to Dictionary: " + employeeInfoItem.EmployeeNumber);

                                                        employeeIDs.Add(employeeInfoItem.EmployeeNumber, null);
                                                    }

                                                    if (index == 98)
                                                    {
                                                        done = true;
                                                    }
                                                }
                                            }

                                            employeeInfos = new EmployeeInformation[items.Count];
                                            index = -1;
                                            foreach (EmployeeInformation item in items)
                                            {
                                                index++;
                                                employeeInfos[index] = item;
                                            }

                                            break;
                                        case LawsonActionSnapshot:
                                            // Only do this if the return code is success...
                                            if (returnCode == (int)ApplicationReturnValue.Success)
                                            {
                                                // Get the results from the Lawson table...
                                                string connect = System.Configuration.ConfigurationManager.AppSettings["OracleConnectStringLawson"];
                                                persistenceLawson = new OleDb(connect);

                                                JIB.Lawson.Data.BusinessLayer.FMLawPerss lawsonEmployees = new JIB.Lawson.Data.BusinessLayer.FMLawPerss(persistenceLawson, JIB.Lawson.Data.BusinessLayer.FMLawPers.CollectionSources.ForLocation(Convert.ToDecimal(employeeInfo.Company), storeAuthenticator.StoreNumber));
                                                employeeInfos = new EmployeeInformation[lawsonEmployees.Count];
                                                index = -1;
                                                foreach (JIB.Lawson.Data.BusinessLayer.FMLawPers lawsonEmployee in lawsonEmployees)
                                                {
                                                    index++;
                                                    EmployeeInformation empInfo = new EmployeeInformation();
                                                    empInfo.Company = lawsonEmployee.COMPANY.ToString();
                                                    empInfo.LocationCode = lawsonEmployee.LOCAT_CODE;
                                                    empInfo.LocationState = lawsonEmployee.LOCAT_STATE;
                                                    empInfo.LocationCodeAlternate = lawsonEmployee.ALT_LOCAT_CODE;
                                                    empInfo.EmployeeNumber = lawsonEmployee.EMPLOYEE.ToString("*********");
                                                    empInfo.FICANumber = JIB.Framework.Security.Encryption.EncryptRijndaelManaged(lawsonEmployee.FICA_NBR, key, iv);
                                                    empInfo.StatusCode = lawsonEmployee.EMP_STATUS;
                                                    empInfo.LastName = lawsonEmployee.LAST_NAME;
                                                    empInfo.FirstName = lawsonEmployee.FIRST_NAME;
                                                    empInfo.MiddleName = lawsonEmployee.MIDDLE_NAME;
                                                    empInfo.Address1 = lawsonEmployee.ADDR1;
                                                    empInfo.Address2 = lawsonEmployee.ADDR2;
                                                    empInfo.City = lawsonEmployee.CITY;
                                                    empInfo.State = lawsonEmployee.STATE;
                                                    empInfo.ZIP = lawsonEmployee.ZIP;
                                                    empInfo.Phone = ConvertPhoneNumber(lawsonEmployee.HM_PHONE_NBR, true);
                                                    empInfo.JobCode = lawsonEmployee.JOB_CODE;
                                                    empInfo.PayRate = lawsonEmployee.PAY_RATE;
                                                    empInfo.PayEffectiveDate = ConvertDateToString(AdjustDate(lawsonEmployee.PAY_EFF_DATE));
                                                    empInfo.BirthDate = ConvertDateToString(AdjustDate(lawsonEmployee.BIRTHDATE));
                                                    empInfo.HiredDate = ConvertDateToString(AdjustDate(lawsonEmployee.HIRE_DATE));
                                                    empInfo.HiredDateOriginal = ConvertDateToString(AdjustDate(lawsonEmployee.ORIG_HIRE_DATE));
                                                    empInfo.Points = lawsonEmployee.PPAT_POINTS.ToString();
                                                    empInfo.Score = lawsonEmployee.PPAT_SCORE;
                                                    empInfo.ReviewDate = ConvertDateToString(AdjustDate(lawsonEmployee.NEXT_REV_DATE));
                                                    empInfo.JobEffectiveDate = ConvertDateToString(AdjustDate(lawsonEmployee.JOB_CODE_DATE));
                                                    empInfo.BadgeNumber = lawsonEmployee.BADGE_ID;
                                                    empInfo.GenderCode = lawsonEmployee.SEX;
                                                    empInfo.RaceCode = lawsonEmployee.EEO_CLASS;
                                                    empInfo.WorkExperienceDate = ConvertDateToString(AdjustDate(lawsonEmployee.WK_EXP_DATE));
                                                    empInfo.I9ExpirationDate = ConvertDateToString(AdjustDate(lawsonEmployee.I9_EXP_DATE));
                                                    empInfo.LOAType = lawsonEmployee.LOA_FLAG;
                                                    empInfo.LOABeginDate = ConvertDateToString(AdjustDate(lawsonEmployee.LOA_BEGIN_DATE));
                                                    empInfo.LOAEndDate = ConvertDateToString(AdjustDate(lawsonEmployee.LOA_END_DATE));
                                                    empInfo.LOAInjuryDate = ConvertDateToString(AdjustDate(lawsonEmployee.LOA_INJ_DATE));
                                                    empInfo.LOAReturnedFromDate = ConvertDateToString(AdjustDate(lawsonEmployee.LOA_RET_DATE));
                                                    empInfo.LOAInjuryLastWorkedDate = ConvertDateToString(AdjustDate(lawsonEmployee.LOA_LS_WK_DATE));
                                                    empInfo.LOAInjuryFirstLightDutyDate = ConvertDateToString(AdjustDate(lawsonEmployee.LOA_LIGHT_DATE));
                                                    empInfo.LOAInjuryFirstFullDutyDate = ConvertDateToString(AdjustDate(lawsonEmployee.LOA_FULL_DATE));
                                                    // This "snapshot" appears to be emailed rather than returned via HTTP so should not encrypt PayCard data here.
                                                    empInfo.PayCardNumber = JIB.Framework.Utilities.Strings.LeftSafe(lawsonEmployee.PAY_CARD_NBR, 16);

                                                    employeeInfos[index] = empInfo;
                                                }

                                                // Send Email...
                                                bool sendEmailToStore = false;
                                                string sendEmail = System.Configuration.ConfigurationManager.AppSettings["SnapshotEmailStore"];
                                                if (sendEmail == null)
                                                {
                                                    sendEmail = string.Empty;
                                                }
                                                if (sendEmail.Length > 0)
                                                {
                                                    if (string.Compare(sendEmail, "true", true) == 0)
                                                    {
                                                        sendEmailToStore = true;
                                                    }
                                                }

                                                // Override a true with the structure parameter from the store...
                                                if (employeeInfo.SnapshotSendEmail == 0)
                                                {
                                                    sendEmailToStore = false;
                                                }

                                                if (sendEmailToStore == true)
                                                {
                                                    SendEmailToStore(storeAuthenticator.StoreNumber, employeeInfos);
                                                }
                                            }
                                            break;
                                    }

                                }

                                catch (Exception e)
                                {
                                    Logging.WriteLog(logFile, "Exception: " + e.Message);
                                    Logging.WriteLog(logFileExceptions, "Exception: " + e.Message);
                                    Logging.WriteLog(logFile, "Exception: " + messageLawsonUnexpectedResult);
                                    Logging.WriteLog(logFileExceptions, "Exception: " + messageLawsonUnexpectedResult);
                                    throw new SystemException(messageLawsonUnexpectedResult, e);
                                }

                                if (persistenceLawson != null)
                                {
                                    persistenceLawson.Dispose();
                                }

                                Logging.WriteLog(logFile, "RETURN-CODE: " + returnCode.ToString());
                                Logging.WriteLog(logFile, "RETURN-CODE-CAT: " + returnCodeCategory);
                                Logging.WriteLog(logFile, "MsgNbr: " + lawsonReturnValue);
                                Logging.WriteLog(logFile, "Message: " + lawsonReturnMessage);
                                Logging.WriteLog(logFile, "WEB-EDIT-MESSAGE1 " + applicationReturnMessage1);
                                Logging.WriteLog(logFile, "WEB-EDIT-MESSAGE2 " + applicationReturnMessage2);
                                Logging.WriteLog(logFile, "WEB-EDIT-MESSAGE3 " + applicationReturnMessage3);
                                Logging.WriteLog(logFile, "WEB-EDIT-MESSAGE4 " + applicationReturnMessage4);
                                Logging.WriteLog(logFile, "WEB-EDIT-MESSAGE5 " + applicationReturnMessage5);

                                // Wipe out structure return values and messages...

                                switch (returnCode)
                                {
                                    case (int)ApplicationReturnValue.Failure:
                                        // Request Failed...

                                        // Check for a Lawson error...
                                        if (lawsonReturnValue == (int)LawsonMsgNbr.MatrixViolation && returnCodeCategory.Length > 0)
                                        {
                                            // Matrix violation...
                                            employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                                            employeeInfo.ApplicationReturnMessage1 = applicationReturnMessage1;
                                            employeeInfo.ApplicationReturnMessage2 = applicationReturnMessage2;
                                            employeeInfo.ApplicationReturnMessage3 = applicationReturnMessage3;
                                            employeeInfo.ApplicationReturnMessage4 = applicationReturnMessage4;
                                            employeeInfo.ApplicationReturnMessage5 = applicationReturnMessage5;
                                        }
                                        else
                                        {
                                            // NOT a matrix violation...assume a Lawson native error...
                                            employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                                            employeeInfo.ApplicationReturnMessage1 = lawsonReturnMessage;
                                        }
                                        break;

                                    case (int)ApplicationReturnValue.Success:
                                        employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Success;
                                        employeeInfo.ApplicationReturnMessage1 = applicationReturnMessage1;
                                        employeeInfo.ApplicationReturnMessage2 = applicationReturnMessage2;
                                        employeeInfo.ApplicationReturnMessage3 = applicationReturnMessage3;
                                        employeeInfo.ApplicationReturnMessage4 = applicationReturnMessage4;
                                        employeeInfo.ApplicationReturnMessage5 = applicationReturnMessage5;
                                        break;

                                    default:
                                        employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                                        employeeInfo.ApplicationReturnMessage1 = messageException;
                                        break;
                                }

                                #endregion

                                // Reform messages returned...
                                employeeInfo.ApplicationReturnMessage1 = ReformMessage(employeeInfo.ApplicationReturnMessage1, replacementPhrases);
                                employeeInfo.ApplicationReturnMessage2 = ReformMessage(employeeInfo.ApplicationReturnMessage2, replacementPhrases);
                                employeeInfo.ApplicationReturnMessage3 = ReformMessage(employeeInfo.ApplicationReturnMessage3, replacementPhrases);
                                employeeInfo.ApplicationReturnMessage4 = ReformMessage(employeeInfo.ApplicationReturnMessage4, replacementPhrases);
                                employeeInfo.ApplicationReturnMessage5 = ReformMessage(employeeInfo.ApplicationReturnMessage5, replacementPhrases);


                                Logging.WriteLog(logFile, "Returned From Lawson");
                                Logging.WriteLog(logFile, "Employee Number: " + employeeInfo.EmployeeNumber);
                                Logging.WriteLog(logFile, "Employee Status Code: " + employeeInfo.StatusCode);
                                Logging.WriteLog(logFile, "Company: " + employeeInfo.Company);
                                Logging.WriteLog(logFile, "Badge Number: " + employeeInfo.BadgeNumber);

                                Logging.WriteLog(logFile, "Application Return Number: " + employeeInfo.ApplicationReturnValue);
                                Logging.WriteLog(logFile, "Application Return Message 1: " + employeeInfo.ApplicationReturnMessage1);
                                Logging.WriteLog(logFile, "Application Return Message 2: " + employeeInfo.ApplicationReturnMessage2);
                                Logging.WriteLog(logFile, "Application Return Message 3: " + employeeInfo.ApplicationReturnMessage3);
                                Logging.WriteLog(logFile, "Application Return Message 4: " + employeeInfo.ApplicationReturnMessage4);
                                Logging.WriteLog(logFile, "Application Return Message 5: " + employeeInfo.ApplicationReturnMessage5);
                            }
                            else
                            {
                                employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                            }
                        }
                        else
                        {
                            // The manager and/or employee is not authenticated...send back the response...
                            employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.AuthenticationError;
                            switch (authenticationResults)
                            {
                                case AuthenticationResults.ManagerPasswordIncorrect:
                                    employeeInfo.ApplicationReturnMessage1 = System.Configuration.ConfigurationManager.AppSettings["Message.AuthenticationError.Manager"];
                                    break;

                                case AuthenticationResults.EmployeePasswordIncorrect:
                                    employeeInfo.ApplicationReturnMessage1 = System.Configuration.ConfigurationManager.AppSettings["Message.AuthenticationError.Employee"];
                                    break;

                                case AuthenticationResults.BothPasswordsIncorrect:
                                    employeeInfo.ApplicationReturnMessage1 = System.Configuration.ConfigurationManager.AppSettings["Message.AuthenticationError.Both"];
                                    break;
                            }

                            // Add the instructions text...
                            string instructions = System.Configuration.ConfigurationManager.AppSettings["Message.AuthenticationError.Instructions"];
                            // Split the message into message 2 and 3...
                            employeeInfo.ApplicationReturnMessage2 = JIB.Framework.Utilities.Strings.LeftSafe(instructions, PersonnelMessageMaximumLength);
                            if (instructions.Length > PersonnelMessageMaximumLength)
                            {
                                employeeInfo.ApplicationReturnMessage3 = instructions.Substring(PersonnelMessageMaximumLength);
                            }
                        }
                    }
                    else
                    {
                        Logging.WriteLog(logFile, "ProcessID appears to be wrong.");
                        employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                        employeeInfo.ApplicationReturnMessage1 = UnauthorizedAccess;
                    }
                }
                else
                {
                    Logging.WriteLog(logFile, "IP appears out of range");
                    employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                    employeeInfo.ApplicationReturnMessage1 = messageUnauthorized;
                }
            }

            catch (Exception e)
            {
                employeeInfo.ApplicationReturnValue = (int)ApplicationReturnValue.Failure;
                employeeInfo.ApplicationReturnMessage1 = e.Message;
                Logging.WriteLog(logFile, "Exception: " + e.Message);
                Logging.WriteLog(logFileExceptions, "Exception: " + e.Message);
            }

            finally
            {
                if (string.Compare(action, LawsonActionQuery, true) != 0)
                {
                    webServiceLog.CompanyNumber = employeeInfo.Company;
                    webServiceLog.ResultCode = (short)employeeInfo.ApplicationReturnValue;
                    webServiceLog.ResultMessage = JIB.Framework.Utilities.Strings.LeftSafe(employeeInfo.ApplicationReturnMessage1, 100);

                    webServiceLog.EndDate = DateTime.Now;
                    // Don't add these for the Query transaction...
                    webServiceLog.Save();
                    persistenceWebServiceAudit.Dispose();

                    //					if (persistenceFoundationGallery != null)
                    //					{
                    //						persistenceFoundationGallery.Dispose();
                    //					}
                }
            }

            Logging.WriteLog(logFile, action + " - Complete");
        }

        #endregion

        #region Support methods

        internal static bool ValidTransactionDate(ref EmployeeInformation employeeInfo, string action, string logfile, string logFileExceptions)
        {
            bool returnCode = true;
            JIB.Framework.Persistence.OleDb persistenceWorkBrain = null;
            DateTime validDate = DateTime.Today;
            DateTime dateToTest = DateTime.Today;
            string dateToConvert = string.Empty;

            try
            {
                Employee employee = new Employee();
                validDate = validDate.AddDays(-30);
                switch (action.ToUpper())
                {
                    case LawsonActionBorrow:
                        dateToConvert = employeeInfo.BorrowStartDate;

                        dateToTest = employee.ConvertStringToDate(dateToConvert);
                        if (dateToTest < validDate)
                        {
                            returnCode = false;
                            employeeInfo.ApplicationReturnMessage1 = "The Borrow start date cannot be before " + validDate.ToShortDateString();
                        }
                        break;
                    case LawsonActionTransferIn:
                        dateToConvert = employeeInfo.TransferInDate;

                        dateToTest = employee.ConvertStringToDate(dateToConvert);
                        if (dateToTest < validDate)
                        {
                            returnCode = false;
                            employeeInfo.ApplicationReturnMessage1 = "The Transfer In date cannot be before " + validDate.ToShortDateString();
                        }
                        break;
                    case LawsonActionTransferOut:
                        dateToConvert = employeeInfo.TransferToDate;

                        dateToTest = employee.ConvertStringToDate(dateToConvert);
                        if (dateToTest < validDate)
                        {
                            returnCode = false;
                            employeeInfo.ApplicationReturnMessage1 = "The Transfer Out date cannot be before " + validDate.ToShortDateString();
                        }
                        break;
                    case LawsonActionLeaveOfAbsence:
                        dateToConvert = employeeInfo.LOABeginDate;

                        dateToTest = employee.ConvertStringToDate(dateToConvert);
                        if (dateToTest < validDate)
                        {
                            returnCode = false;
                            employeeInfo.ApplicationReturnMessage1 = "The Leave of Absence begin date cannot be before " + validDate.ToShortDateString();
                        }
                        break;
                    case LawsonActionLeaveOfAbsenceEnd:
                        dateToConvert = employeeInfo.LOAReturnedFromDate;

                        dateToTest = employee.ConvertStringToDate(dateToConvert);
                        if (dateToTest < validDate)
                        {
                            returnCode = false;
                            employeeInfo.ApplicationReturnMessage1 = "The Leave of Absence end date cannot be before " + validDate.ToShortDateString();
                        }
                        break;
                    case LawsonActionUpdate:
                        // Job code, prev, new
                        bool jobCodeInvalid = false;
                        if (employeeInfo.JobCode.Length > 0 & employeeInfo.JobCodePrevious.Length > 0)
                        {
                            if (string.Compare(employeeInfo.JobCode, employeeInfo.JobCodePrevious, true) != 0)
                            {
                                dateToConvert = employeeInfo.JobEffectiveDate;
                                dateToTest = employee.ConvertStringToDate(dateToConvert);
                                if (dateToTest < validDate)
                                {
                                    jobCodeInvalid = true;
                                    returnCode = false;
                                    employeeInfo.ApplicationReturnMessage1 = "The Job Code Effective Date cannot be before " + validDate.ToShortDateString();

                                }
                            }
                        }

                        // Pay rate, prev, new
                        if (employeeInfo.PayRate > 0 & employeeInfo.PayRatePrevious > 0)
                        {
                            if (employeeInfo.PayRate != employeeInfo.PayRatePrevious)
                            {
                                dateToConvert = employeeInfo.PayEffectiveDate;
                                dateToTest = employee.ConvertStringToDate(dateToConvert);
                                if (dateToTest < validDate)
                                {
                                    returnCode = false;
                                    if (jobCodeInvalid == true)
                                    {
                                        employeeInfo.ApplicationReturnMessage1 = "The Pay change Effective Date and the Job Code Effective Date cannot be before " + validDate.ToShortDateString();
                                    }
                                    else
                                    {
                                        employeeInfo.ApplicationReturnMessage1 = "The Pay Change Effective Date cannot be before " + validDate.ToShortDateString();
                                    }

                                }
                            }
                        }
                        break;
                    case LawsonActionTerminate:
                        dateToConvert = employeeInfo.TerminationDate;
                        dateToTest = employee.ConvertStringToDate(dateToConvert);

                        if (dateToTest > DateTime.Today)
                        {
                            returnCode = false;
                            employeeInfo.ApplicationReturnMessage1 = "The Termination Date cannot be in the future";
                        }
                        else
                        {
                            if (dateToTest < validDate)
                            {
                                returnCode = false;
                                employeeInfo.ApplicationReturnMessage1 = "The Termination date cannot be before " + validDate.ToShortDateString();
                            }
                            else
                            {
                                DateTime hireDate = employee.ConvertStringToDate(employeeInfo.HiredDate);
                                if (dateToTest < hireDate)
                                {
                                    returnCode = false;
                                    employeeInfo.ApplicationReturnMessage1 = "The Termination date cannot be before the Hire date (" + hireDate.ToShortDateString() + ").";
                                }
                                else
                                {
                                    bool checkWorkbrainForLastDayWorked = Convert.ToBoolean(ConfigurationManager.AppSettings["CheckWorkbrainForLastDayWorked"]);
                                    if (checkWorkbrainForLastDayWorked)
                                    {
                                        string oledbConnectString = System.Configuration.ConfigurationManager.AppSettings["OracleConnectStringWorkBrain"];

                                        persistenceWorkBrain = new JIB.Framework.Persistence.OleDb(oledbConnectString);
                                        persistenceWorkBrain.DataProvider = JIB.Framework.Persistence.OleDb.Provider.Oracle;

                                        string sqlStatement = string.Empty;
                                        string empLawsonID = string.Empty;
                                        empLawsonID = employeeInfo.Company.PadLeft(4, '0') + employeeInfo.EmployeeNumber.PadLeft(9, '0');
                                        sqlStatement = JIB.Workbrain.Data.BusinessLayer.Oracle.ViewWorkSummary.ViewWorkSummarySQLStatement(empLawsonID);

                                        JIB.Workbrain.Data.BusinessLayer.Oracle.ViewWorkSummary.ViewWorkSummarys viewWorkSummarys = new ViewWorkSummary.ViewWorkSummarys(persistenceWorkBrain, sqlStatement);
                                        if (viewWorkSummarys.Count > 0)
                                        {
                                            foreach (ViewWorkSummary wbViewWorkSummar in viewWorkSummarys)
                                            {

                                                if (wbViewWorkSummar.WRKS_WORK_DATE > dateToTest)
                                                {

                                                    DateTime lastDateWorked = wbViewWorkSummar.WRKS_WORK_DATE;
                                                    employeeInfo.ApplicationReturnMessage1 = "The termination date (" + dateToTest.ToShortDateString() + ") is less than the last day worked. (" + lastDateWorked.ToShortDateString() + ")";
                                                    returnCode = false;

                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        Logging.WriteLog(logfile, "CheckWorkbrainForLastDayWorked is disabled in web.config");
                                    }
                                }
                            }
                        }

                        break;

                }
            }
            catch (Exception e)
            {
                Logging.WriteLog(logfile, "Exception: " + e.Message);
                Logging.WriteLog(logFileExceptions, "Exception: " + e.Message);
                returnCode = false;
                employeeInfo.ApplicationReturnMessage1 = "An unexpected error occurred, please try again later.";
            }
            finally
            {
                if (persistenceWorkBrain != null)
                {
                    persistenceWorkBrain.Dispose();
                }
            }


            return returnCode;
        }

        internal static string GetElementValue(XPathNodeIterator iterator)
        {
            XPathNavigator nav;

            iterator.MoveNext();
            nav = iterator.Current;
            iterator = nav.SelectDescendants(XPathNodeType.Element, false);

            return iterator.Current.Value;
        }


        internal static string ReformMessage(string message, string[] replacementPhrases)
        {
            // Loop through the excluded phrases removing them...
            string reformMessage = message;

            if (reformMessage.Length > 0)
            {
                for (int i = 0; i < replacementPhrases.Length; i = i + 2)
                {
                    string searchPhrase = replacementPhrases[i];
                    string replacePhrase = replacementPhrases[i + 1];
                    reformMessage = reformMessage.Replace(searchPhrase, replacePhrase);
                }
            }

            return reformMessage;
        }

        private DateTime AdjustDate(DateTime date)
        {
            DateTime adjustDate = date;

            if (date == LawsonNullDate)
            {
                date = new DateTime();
            }

            return date;
        }

        private DateTime ConvertStringToDate(string dateTime)
        {
            DateTime convertStringToDate = new DateTime();

            try
            {
                if (dateTime != null)
                {
                    if (dateTime.Length > 0)
                    {
                        if (dateTime.Length == 8)
                        {
                            convertStringToDate = DateTime.ParseExact(dateTime, "yyyyMMdd", System.Globalization.CultureInfo.InstalledUICulture);
                        }
                        else
                        {
                            convertStringToDate = DateTime.ParseExact(dateTime, "yyyyMMddHHmmss", System.Globalization.CultureInfo.InstalledUICulture);
                        }
                    }
                }
            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }

            return convertStringToDate;
        }

        private string ConvertDateToString(DateTime dateTime)
        {
            string convertDateToString = string.Empty;

            try
            {
                if (dateTime != new DateTime())
                {
                    convertDateToString = dateTime.ToString("yyyyMMddhhmmss");
                }

            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }

            return convertDateToString;
        }

        private string ConvertPhoneNumber(string phoneNumber)
        {
            return ConvertPhoneNumber(phoneNumber, false);
        }
        private string ConvertPhoneNumber(string phoneNumber, bool forceConvertToPersonnel)
        {
            string convertPhoneNumber = phoneNumber;

            try
            {
                if (phoneNumber != null)
                {
                    if (phoneNumber.Length > 0)
                    {
                        // Remove any spaces in the string...
                        phoneNumber = phoneNumber.Replace(" ", "");
                        // Determine the current format...
                        bool lawsonFormat = false;
                        if (phoneNumber.Length == 12)
                        {
                            lawsonFormat = true;
                        }
                        // Override if forcing to Personnel...
                        if (forceConvertToPersonnel == true)
                        {
                            lawsonFormat = true;
                        }
                        // Remove all special characters...
                        phoneNumber = phoneNumber.Replace("-", "");
                        phoneNumber = phoneNumber.Replace("(", "");
                        phoneNumber = phoneNumber.Replace(")", "");
                        phoneNumber = phoneNumber.Replace("/", "");

                        // Convert to number...
                        ulong phone = 0;
                        if (JIB.Framework.Utilities.Strings.IsNumeric(phoneNumber) == true)
                        {
                            phone = Convert.ToUInt64(phoneNumber);
                            // Convert....
                            if (lawsonFormat == true)
                            {
                                // Convert to Personnel...
                                convertPhoneNumber = phone.ToString("(000)000-0000");
                            }
                            else
                            {
                                // Convert to Lawson...
                                convertPhoneNumber = phone.ToString("************");
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }

            return convertPhoneNumber;
        }

        private string RemoveSpecialCharacters(string original)
        {
            string removeSpecialCharacters = original;

            try
            {
                removeSpecialCharacters = removeSpecialCharacters.Replace(":", string.Empty);
                removeSpecialCharacters = removeSpecialCharacters.Replace("/", string.Empty);
                removeSpecialCharacters = removeSpecialCharacters.Replace("?", string.Empty);
                removeSpecialCharacters = removeSpecialCharacters.Replace("=", string.Empty);
                removeSpecialCharacters = removeSpecialCharacters.Replace("&", string.Empty);
                removeSpecialCharacters = removeSpecialCharacters.Replace("#", string.Empty);
            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }

            return removeSpecialCharacters;
        }

        private System.Text.StringBuilder ConvertSpecialToHex(System.Text.StringBuilder original)
        {
            System.Text.StringBuilder convertSpecialToHex = original;

            try
            {
                convertSpecialToHex = convertSpecialToHex.Replace(":", "%3A");
                convertSpecialToHex = convertSpecialToHex.Replace("/", "%2F");
                convertSpecialToHex = convertSpecialToHex.Replace("?", "%3F");
                convertSpecialToHex = convertSpecialToHex.Replace("=", "%3D");
                convertSpecialToHex = convertSpecialToHex.Replace("&", "%26");
                convertSpecialToHex = convertSpecialToHex.Replace("#", "%23");
            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }

            return convertSpecialToHex;
        }

        private static void SendEmailToStore(string storeNumber, EmployeeInformation[] employeeInfos)
        {
            try
            {
                // Get the list of job classes...
                string jobClassesFromConfig = System.Configuration.ConfigurationManager.AppSettings["SnapshotEmailJobClasses"];
                string[] jobClasses = jobClassesFromConfig.Split(",".ToCharArray());

                System.Text.StringBuilder body = new System.Text.StringBuilder(500);
                // Loop through the infos and create the email body...
                foreach (EmployeeInformation employeeInfo in employeeInfos)
                {
                    if (Array.IndexOf(jobClasses, employeeInfo.JobCode) >= 0)
                    {
                        body.Append(employeeInfo.LastName);
                        body.Append(", ");
                        body.Append(employeeInfo.FirstName);
                        body.Append("\t");
                        body.Append(employeeInfo.BadgeNumber);
                        body.Append("\r");
                    }
                }

                if (body.Length > 0)
                {
                    MailMessage mailMessage = new MailMessage();
                    mailMessage.Subject = System.Configuration.ConfigurationManager.AppSettings["SnapshotEmailSubject"];
                    mailMessage.Body = body.ToString();
                    mailMessage.IsBodyHtml = false;
                    mailMessage.From = new MailAddress(System.Configuration.ConfigurationManager.AppSettings["SnapshotEmailFromAddress"]);
                    mailMessage.To.Add("m" + storeNumber + System.Configuration.ConfigurationManager.AppSettings["SnapshotEmailStoreAddressIdentifier"]);
                    SmtpClient smtpClient = new SmtpClient(System.Configuration.ConfigurationManager.AppSettings["SnapshotEmailSmtpServer"]);

                    smtpClient.Send(mailMessage);
                }
            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }
        }

        internal static void Publish(string transactionType, EmployeeInformation employeeInfo, string logFile)
        {
            try
            {
                string publishEnabledFlag = System.Configuration.ConfigurationManager.AppSettings["PublishEnabled"];
                bool publishEnabled = true;
                if (publishEnabledFlag != null)
                {
                    if (publishEnabledFlag.Length > 0)
                    {
                        if (string.Compare(publishEnabledFlag, "false", true) == 0)
                        {
                            publishEnabled = false;
                        }
                    }
                }

                if (publishEnabled == true)
                {
                    string publishStoresConfig = System.Configuration.ConfigurationManager.AppSettings["PublishStores"];
                    System.Collections.ArrayList publishStores = null;
                    if (publishStoresConfig != null)
                    {
                        publishStores = new System.Collections.ArrayList(publishStoresConfig.Split(Convert.ToChar(",")));
                    }
                    // Only publish for the CBT stores...
                    // .. but if the publishStoresConfig is null, publish for everybody...
                    if (publishStores != null)
                    {
                        Logging.WriteLog(logFile, "publishStores.Count: " + publishStores.Count.ToString());
                        if (publishStores.Contains(employeeInfo.LocationCode) == false)
                        {
                            publishEnabled = false;
                        }
                    }
                    if (publishEnabled == true)
                    {
                        Logging.WriteLog(logFile, "Publish Start for: " + transactionType);

                        string messageQueueName = System.Configuration.ConfigurationManager.AppSettings["PublishMSMQName"];
                        if (messageQueueName != null)
                        {
                            Logging.WriteLog(logFile, "Message Queue: " + messageQueueName);

                            System.Messaging.MessageQueue queue = new System.Messaging.MessageQueue(messageQueueName);
                            if (queue != null)
                            {
                                Logging.WriteLog(logFile, "Got Queue");

                                System.Messaging.Message message = new System.Messaging.Message();
                                queue.Formatter = new System.Messaging.XmlMessageFormatter(new System.Type[] { typeof(EmployeeInformation) });
                                employeeInfo.SourceApplication = "WEBSERVICE";
                                message.Body = employeeInfo;

                                Logging.WriteLog(logFile, "Sending Message to Queue");
                                queue.Send(employeeInfo, transactionType);
                            }
                            else
                            {
                                Logging.WriteLog(logFile, "Queue not found");
                            }
                        }
                    }
                }
            }

            catch (Exception e)
            {
                Logging.WriteLog(logFile, "Publish Exception: " + e.StackTrace);
                throw new SystemException(e.Message, e);
            }

            finally
            {
                Logging.WriteLog(logFile, "Publish Complete");
            }
        }

        internal static string SerializeBytes(byte[] bytes)
        {
            string serializeTemplate = string.Empty;

            try
            {
                if (bytes.Length > 0)
                {
                    serializeTemplate = Encoding.Default.GetString(bytes);
                }
            }

            catch (Exception ex)
            {
                throw new SystemException(ex.Message, ex);
            }

            finally
            {
            }

            return serializeTemplate;
        }

        internal static byte[] DeSerializeBytes(string deserializedBytes)
        {
            byte[] deSerializeBytes = null;

            try
            {
                if (deserializedBytes.Length > 0)
                {
                    deSerializeBytes = Encoding.Default.GetBytes(deserializedBytes);
                }
            }

            catch (Exception ex)
            {
                throw new SystemException(ex.Message, ex);
            }

            finally
            {
            }

            return deSerializeBytes;
        }
        internal static string AssemblyVersion
        {
            get
            {
                System.Reflection.Assembly asmAssembly;
                System.Reflection.AssemblyName asmName;
                asmAssembly = System.Reflection.Assembly.GetExecutingAssembly();
                asmName = asmAssembly.GetName();
                return asmName.Version.ToString();
            }
        }

        internal void AddI9DataToAGSCall(EmployeeInformation employeeInfo, ref System.Text.StringBuilder agsCall)
        {
            try
            {
                if (ConvertStringToDate(employeeInfo.I9ExpirationDate) != new DateTime())
                {
                    agsCall.Append("&I9-EXP-DATE=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.I9ExpirationDate).ToString("yyyyMMdd"));
                }

                if (employeeInfo.Citizen != null)
                {
                    if (employeeInfo.Citizen.Length > 0)
                    {
                        agsCall.Append("&I9-CITIZEN=");
                        agsCall.Append(employeeInfo.Citizen);
                    }
                }

                if (employeeInfo.NonCitizen != null)
                {
                    if (employeeInfo.NonCitizen.Length > 0)
                    {
                        agsCall.Append("&I9-NON-CITIZEN=");
                        agsCall.Append(employeeInfo.NonCitizen);
                    }
                }

                if (employeeInfo.Permanent != null)
                {
                    if (employeeInfo.Permanent.Length > 0)
                    {
                        agsCall.Append("&I9-PERMANENT=");
                        agsCall.Append(employeeInfo.Permanent);
                    }
                }

                if (employeeInfo.Alien != null)
                {
                    if (employeeInfo.Alien.Length > 0)
                    {
                        agsCall.Append("&I9-ALIEN=");
                        agsCall.Append(employeeInfo.Alien);
                    }
                }

                if (employeeInfo.Admission != null)
                {
                    if (employeeInfo.Admission.Length > 0)
                    {
                        agsCall.Append("&I9-ADMISSION=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.Admission));
                    }
                }

                if (employeeInfo.Doc1Number != null)
                {
                    if (employeeInfo.Doc1Number.Length > 0)
                    {
                        agsCall.Append("&I9-DOC1-NBR=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.Doc1Number));
                    }
                }

                if (ConvertStringToDate(employeeInfo.ExpirationDate1) != new DateTime())
                {
                    agsCall.Append("&I9-DOC1-EXP-DATE=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.ExpirationDate1).ToString("yyyyMMdd"));
                }

                if (employeeInfo.Doc2Number != null)
                {
                    if (employeeInfo.Doc2Number.Length > 0)
                    {
                        agsCall.Append("&I9-DOC2-NBR=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.Doc2Number));
                    }
                }

                if (ConvertStringToDate(employeeInfo.ExpirationDate2) != new DateTime())
                {
                    agsCall.Append("&I9-DOC2-EXP-DATE=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.ExpirationDate2).ToString("yyyyMMdd"));
                }

                if (employeeInfo.AlienNumber != null)
                {
                    if (employeeInfo.AlienNumber.Length > 0)
                    {
                        agsCall.Append("&I9-ALIEN-NBR=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.AlienNumber));
                    }
                }

                if (ConvertStringToDate(employeeInfo.AlienExpirationDate) != new DateTime())
                {
                    agsCall.Append("&I9-ALIEN-EXP-DATE=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.AlienExpirationDate).ToString("yyyyMMdd"));
                }

                if (employeeInfo.IssueWho1 != null)
                {
                    if (employeeInfo.IssueWho1.Length > 0)
                    {
                        agsCall.Append("&I9-ISSUE-AUTH-1=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.IssueWho1));
                    }
                }

                if (employeeInfo.IssueWho2 != null)
                {
                    if (employeeInfo.IssueWho2.Length > 0)
                    {
                        agsCall.Append("&I9-ISSUE-AUTH-2=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.IssueWho2));
                    }
                }

                if (employeeInfo.I9ADescription != null)
                {
                    if (employeeInfo.I9ADescription.Length > 0)
                    {
                        agsCall.Append("&I9-I9A-DESC=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.I9ADescription));
                    }
                }

                if (employeeInfo.I9BDescription != null)
                {
                    if (employeeInfo.I9BDescription.Length > 0)
                    {
                        agsCall.Append("&I9-I9B-DESC=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.I9BDescription));
                    }
                }

                if (employeeInfo.I9CDescription != null)
                {
                    if (employeeInfo.I9CDescription.Length > 0)
                    {
                        agsCall.Append("&I9-I9C-DESC=");
                        agsCall.Append(RemoveSpecialCharacters(employeeInfo.I9CDescription));
                    }
                }

                if (employeeInfo.I9FormVersion != null)
                {
                    if (employeeInfo.I9FormVersion.Length > 0)
                    {
                        agsCall.Append("&I9-FORM-ID=");
                        agsCall.Append(employeeInfo.I9FormVersion);
                    }
                }

                if (ConvertStringToDate(employeeInfo.I9FormDate) != new DateTime())
                {
                    agsCall.Append("&I9-FORM-REV=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.I9FormDate).ToString("yyyyMMdd"));
                }

                if (ConvertStringToDate(employeeInfo.I9FormExpirationDate) != new DateTime())
                {
                    agsCall.Append("&I9-FORM-EXP=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.I9FormExpirationDate).ToString("yyyyMMdd"));
                }

                if (employeeInfo.ManagerBadgeNumber != null)
                {
                    agsCall.Append("&I9-MGR-BADGE-ID=");
                    agsCall.Append(employeeInfo.ManagerBadgeNumber);
                }

                if (ConvertStringToDate(employeeInfo.ManagerI9SignoffDateTime) != new DateTime())
                {
                    agsCall.Append("&I9-MGR-DATE=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.ManagerI9SignoffDateTime).ToString("yyyyMMdd"));
                }

                if (ConvertStringToDate(employeeInfo.ManagerI9SignoffDateTime) != new DateTime())
                {
                    agsCall.Append("&I9-MGR-TIME=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.ManagerI9SignoffDateTime).ToString("HH:mm:ss"));
                }

                if (employeeInfo.BadgeNumber != null)
                {
                    agsCall.Append("&I9-EMP-BADGE-ID=");
                    agsCall.Append(employeeInfo.BadgeNumber);
                }

                if (employeeInfo.Maiden != null)
                {
                    agsCall.Append("&I9-MAIDEN-NAME=");
                    agsCall.Append(RemoveSpecialCharacters(employeeInfo.Maiden));
                }

                if (ConvertStringToDate(employeeInfo.EmployeeI9SignoffDateTime) != new DateTime())
                {
                    agsCall.Append("&I9-EMP-DATE=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.EmployeeI9SignoffDateTime).ToString("yyyyMMdd"));
                }

                if (ConvertStringToDate(employeeInfo.EmployeeI9SignoffDateTime) != new DateTime())
                {
                    agsCall.Append("&I9-EMP-TIME=");
                    agsCall.Append(ConvertStringToDate(employeeInfo.EmployeeI9SignoffDateTime).ToString("HH:mm:ss"));
                }

                // I9 EVerify data END ...
            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }
        }

        internal static AuthenticationResults ValidateEncryptedPasswords(string logFile, string managerBadgeNumber, string managerPassword, string employeeBadgeNumber, string employeePassword)
        {
            AuthenticationResults validateEncryptedPasswords = AuthenticationResults.ManagerPasswordIncorrect;

            try
            {
                // Result tracker...
                int result = 0;
                string password = string.Empty;

                // Start with the manager...
                if (managerPassword == null)
                {
                    managerPassword = string.Empty;
                }
                if (managerPassword.Length > 0)
                {
                    Logging.WriteLog(logFile, "Decrypting Manager Password");
                    password = Encryption.Decrypt(managerPassword);

                    // Strip the leading zeroes from the badgeNumber...
                    managerBadgeNumber = JIB.Framework.Utilities.Strings.StripLeadingCharacters(managerBadgeNumber, Convert.ToChar("0"));

                    //					Logging.WriteLog(logFile, "Validating Manager BadgeID: " + managerBadgeNumber);
                    //					Logging.WriteLog(logFile, "Validating Manager Password: " + password);

                    if (ValidatePassword(managerBadgeNumber, password) == false)
                    {
                        Logging.WriteLog(logFile, "Manager Password Failed");
                        result = result + 1;
                    }
                }

                // Try the employee..
                if (employeePassword == null)
                {
                    employeePassword = string.Empty;
                }
                if (employeePassword.Length > 0)
                {
                    Logging.WriteLog(logFile, "Decrypting Employee Password");
                    password = Encryption.Decrypt(employeePassword);

                    // Strip the leading zeroes from the badgeNumber...
                    employeeBadgeNumber = JIB.Framework.Utilities.Strings.StripLeadingCharacters(employeeBadgeNumber, Convert.ToChar("0"));

                    Logging.WriteLog(logFile, "Validating Employee BadgeID: " + employeeBadgeNumber);
                    //					Logging.WriteLog(logFile, "Validating Employee Password: " + password);

                    if (ValidatePassword(employeeBadgeNumber, password) == false)
                    {
                        Logging.WriteLog(logFile, "Employee Password Failed");
                        result = result + 2;
                    }
                }

                switch (result)
                {
                    case 0:
                        validateEncryptedPasswords = AuthenticationResults.Authenticated;
                        break;

                    case 1:
                        validateEncryptedPasswords = AuthenticationResults.ManagerPasswordIncorrect;
                        break;

                    case 2:
                        validateEncryptedPasswords = AuthenticationResults.EmployeePasswordIncorrect;
                        break;

                    case 3:
                        validateEncryptedPasswords = AuthenticationResults.BothPasswordsIncorrect;
                        break;
                }
            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }

            return validateEncryptedPasswords;
        }

        internal static bool ValidatePassword(string badgeNumber, string password)
        {
            bool validatePassword = false;

            try
            {
                try
                {
                    System.DirectoryServices.DirectoryEntry entry = new System.DirectoryServices.DirectoryEntry("LDAP://ds.jitb.net", @"ds\" + badgeNumber, password);
                    object nativeObject = entry.NativeObject;
                    if (nativeObject != null)
                    {
                        System.DirectoryServices.DirectorySearcher searcher = new System.DirectoryServices.DirectorySearcher(entry);
                        searcher.Filter = "(SAMAccountName=" + badgeNumber + ")";
                        searcher.PropertiesToLoad.Add("cn");
                        System.DirectoryServices.SearchResult result = searcher.FindOne();
                        if (result != null)
                        {
                            validatePassword = true;
                        }
                    }
                }

                catch
                {
                    validatePassword = false;
                }

                finally
                {
                }
            }

            catch (Exception e)
            {
                throw new SystemException(e.Message, e);
            }

            finally
            {
            }

            return validatePassword;
        }

        private void AttemptSendToNServiceBus(string action, EmployeeInformation employeeInfo, string logFile, string logFileExceptions)
        {
            AttemptSendToNServiceBus(action, null, employeeInfo, logFile, logFileExceptions);
        }
        private void AttemptSendToNServiceBus(string action, LawsonActionUpdateFlags lawsonActionUpdateFlags, EmployeeInformation employeeInfo, string logFile, string logFileExceptions)
        {
            bool sendToNServiceBus = Convert.ToBoolean(ConfigurationManager.AppSettings["SendToNServiceBus"]);
            if (sendToNServiceBus == true)
            {
                Logging.WriteLog(logFile, string.Format(CommandSender.LogLineFormat_SendAttempt, action));
                try
                {
                    if (lawsonActionUpdateFlags != null)
                        CommandSender.DetermineNsbSender(action, lawsonActionUpdateFlags, employeeInfo);
                    else
                        CommandSender.DetermineNsbSender(action, employeeInfo);

                    Logging.WriteLog(logFile, string.Format(CommandSender.LogLineFormat_SendComplete));
                }
                catch (Exception ex)
                {
                    Logging.WriteLog(logFile, string.Format(CommandSender.LogLineFormat_SendException, ex.Message));
                    Logging.WriteLog(logFileExceptions, string.Format(CommandSender.LogLineFormat_SendException, ex.Message));
                }
            }
            else
            {
                Logging.WriteLog(logFile, "SendToNServiceBus is disabled in web.config");
            }
        }

        private bool AttemptToAuthenticateIP(string logFile)
        {
            bool authenticateIP = false;
            bool checkIPRejectionList = Convert.ToBoolean(ConfigurationManager.AppSettings["CheckIPRejectionList"]);
            if (checkIPRejectionList)
            {
                authenticateIP = storeHeaderFromClient.AuthenticateIP();
            }
            else
            {
                // Effectively disregards what IP address the web service call came from.
                authenticateIP = true;
                Logging.WriteLog(logFile, "CheckIPRejectionList is disabled in web.config");
            }
            return authenticateIP;
        }
        #endregion
    }

    //#region EmployeeInformation Structure

    //[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://JackInTheBox.com/webservices/", IsNullable = false)]
    //public struct EmployeeInformation
    //{
    //    // MSMQ data...
    //    public string SourceApplication;

    //    // Flag data...
    //    // 1=Send, 0=Don't Send...
    //    public int SnapshotSendEmail;

    //    // Hire Manager Flag...
    //    public int HireManagerFlag;

    //    // General data...
    //    public string RequestID;
    //    public string SnapShotRequestID;
    //    public byte[] FICANumber;
    //    public string LocationCode;
    //    public string LocationState;
    //    public string LocationCodeAlternate;
    //    public string HomeLocationCode;
    //    public string JobCode;
    //    public string JobCodePrevious;
    //    public string LastName;
    //    public string FirstName;
    //    public string MiddleName;
    //    public string HiredDate;
    //    public string HiredDateOriginal;
    //    public string BirthDate;
    //    public string StatusCode;
    //    public string RaceCode;
    //    public string GenderCode;
    //    public decimal PayRate;
    //    public decimal PayRatePrevious;
    //    public string EmployeeNumber;
    //    public string Company;
    //    public string PayCardNumber;
    //    public string PayCardNumberPrevious;
    //    public string PayCardNumEncrypted;
    //    public string PayCardNumPreviousEncrypted;
    //    public string JobEffectiveDate;
    //    public string PayEffectiveDate;
    //    public string Address1;
    //    public string Address2;
    //    public string City;
    //    public string State;
    //    public string ZIP;
    //    public string Phone;
    //    public string WorkExperienceDate;
    //    public string TeamLeaderMeritRating;
    //    public string SuperKey;
    //    public string BorrowedIndicator;
    //    public string ClockType;

    //    // Electronic Signature Authentication Data...
    //    public string BadgeNumber;
    //    public string Password;
    //    public string ManagerBadgeNumber;
    //    public string ManagerPassword;

    //    // Training Data
    //    public int EligibleForTrainingCode;
    //    public int SurveyFlag;

    //    // I9 Data
    //    public string I9ExpirationDate;
    //    public string Citizen;
    //    public string NonCitizen;
    //    public string Maiden;
    //    public string Permanent;
    //    public string Admission;
    //    public string I9ADescription;
    //    public string Doc1Number;
    //    public string ExpirationDate1;
    //    public string Alien;
    //    public string AlienNumber;
    //    public string AlienExpirationDate;
    //    public string IssueWho1;
    //    public string IssueWho2;
    //    public string Doc2Number;
    //    public string ExpirationDate2;
    //    public string I9BDescription;
    //    public string I9CDescription;
    //    public string I9FormVersion;
    //    public string I9FormDate;
    //    public string I9FormExpirationDate;
    //    public string ManagerI9SignoffDateTime;
    //    public string EmployeeI9SignoffDateTime;

    //    // W4 Data
    //    public string W4FormVersion;
    //    public string W4FormDate;
    //    public string ManagerW4SignoffDateTime;
    //    public string EmployeeW4SignoffDateTime;

    //    // General Manager Data
    //    public string EmployerID;
    //    public string GMHomeLocation;
    //    public string GMLocation01;
    //    public string GMLocation02;
    //    public string GMLocation03;
    //    public string GMLocation04;
    //    public string GMLocation05;
    //    public string GMLocation06;
    //    public string GMLocation07;
    //    public string GMLocation08;
    //    public string GMLocation09;
    //    public string GMLocation10;

    //    // Review Data
    //    public string Points;
    //    public string Score;
    //    public string ReviewDate;
    //    public string NoReview;

    //    public string Comment1;
    //    public string Comment2;
    //    public string Comment3;
    //    public string Comment4;


    //    // Termination data...
    //    public string TerminationDate;
    //    public string TerminationDatePrevious;
    //    public string LastDateWorked;
    //    public string TerminationAction;
    //    public string TerminationReason;
    //    public string EligibleForRehireFlag;

    //    // Borrow data...
    //    public string BorrowFromStoreNumber;
    //    public string BorrowStartDate;
    //    public string BorrowEndDate;
    //    public string BorrowForNewTraining;

    //    // Transfer In data...
    //    public string TransferInDate;
    //    public string TransferFromStoreNumber;

    //    // Transfer Out data...
    //    public string TransferToDate;
    //    public string TransferToStoreNumber;

    //    // Leave of Absence data...
    //    public string LOABeginDate;
    //    public string LOAEndDate;
    //    public string LOAType;
    //    public string LOAFlag;
    //    public string LOAWorkRelatedInjuryFlag;
    //    public string LOAInjuryDate;
    //    public string LOAInjuryLastWorkedDate;
    //    public string LOAInjuryFirstLightDutyDate;
    //    public string LOAInjuryFirstFullDutyDate;
    //    public string LOAReturnedFromDate;

    //    // Application return data...
    //    public int ApplicationReturnValue;
    //    public string ApplicationReturnMessage1;
    //    public string ApplicationReturnMessage2;
    //    public string ApplicationReturnMessage3;
    //    public string ApplicationReturnMessage4;
    //    public string ApplicationReturnMessage5;

    //    // Version stamping...
    //    public string PersonnelVersion;

    //    // Biometric Data for Timeclock...
    //    public byte[] Biometric01;
    //    public byte[] Biometric02;

    //    // Workbrain Clock return data...
    //    public string ClockEmployeeID;					// Workbrain Employee.EMP_ID
    //    public string ClockEmployeeNumber;				// Workbrain Employee.EMP_NAME
    //    public string ClockStatus;						// Workbrain Employee.EMP_STATUS
    //    public string ClockEnfSch;						// Workbrain Employee.Val4
    //    public string ClockClassification;				// Workbrain Employee.Val2
    //    public string ClockReaderGroupName;				// Workbrain Employee_Reader_Group.RDRGRP_ID
    //    public string ClockMemberType;					// "emp"
    //}

    //#endregion
}
