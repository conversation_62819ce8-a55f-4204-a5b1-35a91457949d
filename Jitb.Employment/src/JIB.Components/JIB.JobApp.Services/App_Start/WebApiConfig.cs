﻿using JIB.Framework.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace JIB.JobApp.Services
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            // Web API configuration and services

            // Web API routes
            config.MapHttpAttributeRoutes();

            config.Routes.MapHttpRoute(
                name: "Default<PERSON><PERSON>",
                routeTemplate: "api/{controller}/{id}",
                defaults: new { id = RouteParameter.Optional }
            );
        }

        public static async Task InvokeTestApi<T>(string _logFile, string _assemblyName, T item)
        {
            string typeName = item.GetType().Name;
            string logLinePrefix = string.Format("{0} - {1}: ", _assemblyName, typeName);

            bool sendDataToTestApi = Convert.ToBoolean(ConfigurationManager.AppSettings["SendDataToTestApi" + typeName]);
            if (sendDataToTestApi == true)
            {
                string url = ConfigurationManager.AppSettings["TestApiUrl" + typeName];

                try
                {
                    var client = new HttpClient();
                    Logging.WriteLog(_logFile, logLinePrefix + string.Format("Begin invoking URL {0}", url));

                    // Fire and forget.
                    await client.PostAsJsonAsync(url, item);

                    Logging.WriteLog(_logFile, logLinePrefix + string.Format("End invoking URL {0}", url));
                }
                catch (WebException ex)
                {
                    if (ex.Status != WebExceptionStatus.Timeout)
                    {
                        Logging.WriteLog(_logFile, logLinePrefix + string.Format("WebException invoking URL {0}: {1}", url, ex.Message));
                    }
                }
                catch (Exception ex)
                {
                    Logging.WriteLog(_logFile, logLinePrefix + string.Format("Exception invoking URL {0}: {1}", url, ex.Message));
                }
            }
            else
            {
                Logging.WriteLog(_logFile, logLinePrefix + string.Format("SendDataToTestApi{0} is disabled in web.config", typeName));
            }
        }
    }
}
