﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="MessageForwardingInCaseOfFaultConfig" type="NServiceBus.Config.MessageForwardingInCaseOfFaultConfig, NServiceBus.Core" />
    <section name="UnicastBusConfig" type="NServiceBus.Config.UnicastBusConfig, NServiceBus.Core" />
    <section name="AuditConfig" type="NServiceBus.Config.AuditConfig, NServiceBus.Core" />
    <section name="TransportConfig" type="NServiceBus.Config.TransportConfig, NServiceBus.Core" />
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
    <section name="MasterNodeConfig" type="NServiceBus.Config.MasterNodeConfig, NServiceBus.Core" />
  </configSections>
  <connectionStrings>
    <add name="NServiceBus/Persistence" connectionString="Data Source=cssqlt01v\vtest1;Initial Catalog=dbNServiceBus;User Id=***********;Password=***********;" />
    <add name="Default" connectionString="Data Source=cssqlt01v\vtest1;Initial Catalog=dbNServiceBus;User Id=***********;Password=***********;" />
  </connectionStrings>
  <appSettings>
    <add key="LogFile" value="C:\JIB Logs\JobAppTest\JIB.JobApp.Services_{0}.log" />
    <add key="LogFileExceptions" value="C:\JIB Logs\JobAppTest\JIB.JobApp.Services.Exceptions_{0}.log" />
    <add key="MessageException" value="An Unexpected Error was Returned" />
    <add key="MessageUnauthorized" value="Your Configuration Settings are Incorrect.  Please Contact your Support Help Desk." />
    <add key="MessageLawsonUnavailable" value="Lawson is Unavailable" />
    <add key="MessageLawsonUnexpectedResult" value="Lawson Returned an Unexpected Result" />
    <add key="LawsonBaseUrl" value="http://lawdev.corp.jitb.net:94/sso/SSOServlet" />
    <add key="LawsonTransactionUrl" value="http://lawdev.corp.jitb.net:94/servlet/Router/Transaction/Erp" />
    <add key="LawsonProxyID" value="webops" />
    <add key="LawsonProxyPassword" value="webops" />
    <add key="LawsonDataArea" value="LDV3" />
    <add key="LawsonEmployeeAddFormTag" value="FM4A.1" />
    <add key="LawsonEmployeeAddXMLNavigatorRoot" value="/XFM4A.1/FM4A.1" />
    <!-- Social check -->
    <add key="SocialFormatCheck" value="^(?!000)([0-8]\d{2}|7([0-6]\d|7[012]))([ -]?)(?!00)\d\d\3(?!0000)\d{4}$" />
    <!-- Money Network Prefix (account) number -->
    <add key="MoneyNetworkPrefix" value="472776" />
    <add key="StopTransmittingPlainTextPayCard" value="false" />
    <add key="CheckWorkbrainForLastDayWorked" value="false" />
    <add key="SendToNServiceBus" value="true" />
    <add key="CheckIPRejectionList" value="false" />
    <add key="SendDataToTestApiHireProcess" value="false" />
    <add key="TestApiUrlHireProcess" value="http://csccapatost0115/TEST/api/HireProcess" />
    <add key="SendDataToTestApiRehireEligibile" value="false" />
    <add key="TestApiUrlRehireEligibile" value="http://csccapatost0115/TEST/api/RehireEligible" />
  </appSettings>
  <system.web>
    <compilation debug="true" targetFramework="4.5.2" />
    <httpRuntime targetFramework="4.5" />
  </system.web>
  <system.webServer>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <modules runAllManagedModulesForAllRequests="true">
      <!-- Apparently needed for IIS7.0 and not IIS7.5 http://stackoverflow.com/questions/5677099/routing-http-error-404-0-0x80070002 -->
      <remove name="UrlRoutingModule" />
    </modules>
  </system.webServer>
  <TransportConfig MaximumConcurrencyLevel="5" MaxRetries="5" />
  <MessageForwardingInCaseOfFaultConfig ErrorQueue="error" />
  <UnicastBusConfig>
    <MessageEndpointMappings>
      <!-- Employment.Endpoint -->
      <add Namespace="Jitb.Employment.Contracts.Commands.Employment" Assembly="Jitb.Employment.Contracts" Endpoint="Jitb.Employment.Endpoint@CSNSBT01V" />
      <add Namespace="Jitb.Employment.Contracts.Events.Employment" Assembly="Jitb.Employment.Contracts" Endpoint="Jitb.Employment.Endpoint@CSNSBT01V" />
      <add Namespace="Jitb.Employment.Contracts.Messages.Employment" Assembly="Jitb.Employment.Contracts" Endpoint="Jitb.Employment.Endpoint@CSNSBT01V" />
    </MessageEndpointMappings>
  </UnicastBusConfig>
  <AuditConfig QueueName="audit" />
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>