﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using JIB.JobApp.Services.Models;
using JIB.Framework.Utilities;
using JIB.Lawson.Services.WebServices;

namespace JIB.JobApp.Services.Controllers
{
    public class RehireEligibleController : ApiController
    {
        public const string RESULT_YES = "Yes";
        public const string RESULT_NO = "No";
        public const string RESULT_ELIGIBLE = "Applicant is eligible for hire or re-hire.";
        public const string RESULT_NOT_ELIGIBLE = "Applicant is not eligible for hire or re-hire.";
        public const string ERROR_BAD_SSN = "Bad SSN";
        public const string ERROR_PERSONNEL = "Personnel System Error.";
        public const string LOG_DATE_FORMAT = "yyyyMMdd";

        string _logFile = string.Empty;
        string _logFileExceptions = string.Empty;
        string _assemblyName = string.Empty;
        
        // GET api/values
        public IEnumerable<string> Get()
        {
            return new string[] { };
        }

        // GET api/values/5
        public string Get(int id)
        {
            return string.Empty;
        }

        // POST api/values
        public HttpResponseMessage Post(RehireEligibile item)
        {
            InitializeLog();
            Logging.WriteLog(_logFile, "*********************************************************************************************************************");

            HttpResponseMessage response = null;
            byte[] key = { 00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32 };
            byte[] iv = { 55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32 };

            if (item != null)
            {
                Logging.WriteLog(_logFile, _assemblyName + " - RehireEligibile: " + item);

                // get a social security regular expresssion checker
                string regex = System.Configuration.ConfigurationManager.AppSettings["SocialFormatCheck"];
                System.Text.RegularExpressions.Regex pattern = new System.Text.RegularExpressions.Regex(regex);

                RehireEligibleResult result = new RehireEligibleResult();

                if (ModelState.IsValid)
                {
                    try
                    {
                        if (item == null || item.SourceSSN == null || item.SourceSSN.Length == 0 || !pattern.IsMatch(item.SourceSSN.Trim()))
                        {
                            result.Eligible = RESULT_NO;
                            result.Message = ERROR_BAD_SSN;
                        }
                        else if (pattern.IsMatch(item.SourceSSN.Trim()))
                        {
                            try
                            {
                                Employee employeeService = new Employee();
                                byte[] socialSecurityNumber = JIB.Framework.Security.Encryption.EncryptRijndaelManaged(item.SourceSSN.Trim(), key, iv);
                                string eligible = employeeService.JobAppRehireEligible(socialSecurityNumber);
                                if (eligible == "Y")
                                {
                                    result.Eligible = RESULT_YES;
                                    result.Message = RESULT_ELIGIBLE;
                                }
                                else
                                {
                                    result.Eligible = RESULT_NO;
                                    result.Message = RESULT_NOT_ELIGIBLE;
                                }

                                // Fire and forget a call to TEST API to enable real-time test data.
                                WebApiConfig.InvokeTestApi(_logFile, _assemblyName, item);
                            }
                            catch (Exception e)
                            {
                                Logging.WriteLog(_logFile, _assemblyName + " - ERROR calling Lawson: " + e.Message);
                                result.Eligible = RESULT_NO;
                                result.Message = ERROR_PERSONNEL;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Logging.WriteLog(_logFile, _assemblyName + " - ERROR invoking Lawson: " + ex.Message);
                        result.Eligible = RESULT_NO;
                        result.Message = ERROR_PERSONNEL;
                    }
                    finally
                    {
                        response = Request.CreateResponse<RehireEligibleResult>(HttpStatusCode.Created, result);
                    }
                }
                else
                {
                    response = Request.CreateErrorResponse(HttpStatusCode.BadRequest, ModelState);
                }

                Logging.WriteLog(_logFile, _assemblyName + " - RehireEligibileResult: " + result.ToString());
            }
            else
            {
                response = Request.CreateErrorResponse(HttpStatusCode.BadRequest, ModelState);
            }

            Logging.WriteLog(_logFile, "*********************************************************************************************************************");
            return response;
        }

        // PUT api/values/5
        public void Put(int id, [FromBody]string value)
        {
        }

        // DELETE api/values/5
        public void Delete(int id)
        {
        }

        private void InitializeLog()
        {
            _assemblyName = this.GetType().Assembly.GetName().FullName;
            _logFile = System.Configuration.ConfigurationManager.AppSettings["LogFile"];
            if (_logFile != null)
            {
                _logFile = string.Format(_logFile, DateTime.Now.ToString(LOG_DATE_FORMAT));
                if (_logFile.Length == 0)
                {
                    _logFile = null;
                }
            }
            _logFileExceptions = System.Configuration.ConfigurationManager.AppSettings["LogFileExceptions"];
            if (_logFileExceptions != null)
            {
                _logFileExceptions = string.Format(_logFileExceptions, DateTime.Now.ToString(LOG_DATE_FORMAT));
                if (_logFileExceptions.Length == 0)
                {
                    _logFileExceptions = null;
                }
            }
        }
    }
}
