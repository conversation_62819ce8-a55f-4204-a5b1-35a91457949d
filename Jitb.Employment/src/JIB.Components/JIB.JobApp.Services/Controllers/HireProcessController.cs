﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Xml;
using System.Net.Http;
using System.Web.Http;
using JIB.JobApp.Services.Models;
using JIB.Lawson.Services.WebServices;
using JIB.Framework.Utilities;
using System.Globalization;

namespace JIB.JobApp.Services.Controllers
{
    public class HireProcessController : ApiController
    {
        public const string RESULT_HIRE_COMPLETE = "HIRE COMPLETE";
        public const string RESULT_REJECT = "Reject";
        public const string RESULT_ACCEPT = "Accept";
        public const string ERROR_BAD_SSN = "Bad SSN";
        public const string ERROR_BAD_PAYCARD = "Bad Paycard Number";
        public const string ERROR_PERSONNEL = "Personnel System Error.";
        public const string LOG_DATE_FORMAT = "yyyyMMdd";
        public const int PAYCARD_LENGTH = 16;

        private string _logFile = string.Empty;
        private string _logFileExceptions = string.Empty;
        private string _assemblyName = string.Empty;

        // GET api/values
        public IEnumerable<string> Get()
        {
            return new string[] { };
        }

        // GET api/values/5
        public string Get(int id)
        {
            return string.Empty;
        }

        // POST api/values
        public HttpResponseMessage Post(HireProcess item)
        {
            InitializeLog();
            Logging.WriteLog(_logFile, "*********************************************************************************************************************");
            
            HttpResponseMessage response = null;
            byte[] key = { 00, 01, 02, 03, 04, 05, 06, 07, 42, 16, 93, 156, 78, 4, 128, 32 };
            byte[] iv = { 55, 103, 246, 79, 36, 99, 167, 3, 42, 16, 93, 156, 78, 4, 128, 32 };

            HireProcessResult result = new HireProcessResult { Badge = string.Empty, Company = string.Empty, Employee = string.Empty };
            result.Type = RESULT_ACCEPT;

            if (item != null)
            {
                Logging.WriteLog(_logFile, _assemblyName + " - HireProcess: " + item.ToString());

                if (ModelState.IsValid)
                {
                    try
                    {
                        var clonedItem = item.ShallowCopy();

                        try
                        {
                            TransformPayload(item); // perform all validation and business logic above the required fields enforced by Microsoft.
                            Logging.WriteLog(_logFile, _assemblyName + " - HireProcess Translated: " + item.ToString());
                        }
                        catch (Exception ex)
                        {
                            result.Type = RESULT_REJECT;
                            result.Message = ex.Message;
                        }

                        if (result.Type == RESULT_ACCEPT)
                        {

                            // Call Lawson to hire
                            Employee employeeService = new Employee();
                            EmployeeInformation employeeInfo = new EmployeeInformation
                            {
                                FirstName = item.FirstName,
                                MiddleName = item.MiddleName,
                                LastName = item.LastName,
                                Address1 = item.Address1,
                                Address2 = item.Address2,
                                City = item.City,
                                State = item.State,
                                ZIP = item.Zip,
                                Phone = item.Phone,
                                LocationCode = item.Location,
                                FICANumber = JIB.Framework.Security.Encryption.EncryptRijndaelManaged(item.SourceSSN, key, iv),
                                JobCode = item.Position,
                                RaceCode = item.EEORace,
                                GenderCode = item.EEOGender,
                                BirthDate = item.DateOfBirth,
                                HiredDate = item.StartDate,
                                PayRate = item.Salary,
                                SalaryClass = item.SalaryType,
                                FederalW4ExemptStatus = item.FederalW4ExemptStatus,
                                FederalW4Status = item.FederalW4Status,
                                FederalW4ExemptAmount = item.FederalW4ExemptAmount,
                                FederalW4AdditionalAmount = item.FederalW4AdditionalAmount,
                                StateW4ExemptStatus = item.StateW4ExemptStatus,
                                StateW4Status = item.StateW4Status,
                                StateW4ExemptAmount = item.StateW4ExemptAmount,
                                StateW4AdditionalAmount = item.StateW4AdditionalAmount,
                                //StateW4PercentArizona = item.StateW4PercentArizona,
                                DirectDepositAccount = item.DirectDepositAccount,
                                DirectDepositRouting = item.DirectDepositRouting,
                                PayCardNumber = item.PayCardNumber,
                                LocationCodeAlternate = item.TrainingLocation,
                                EmergencyContactFirstName = item.EmergencyContactFirstName,
                                EmergencyContactLastName = item.EmergencyContactLastName,
                                EmergencyContactHomePhone = item.EmergencyContactHomePhone,
                                EmergencyContactCellPhone = item.EmergencyContactCellPhone
                            };

                            try
                            {
                                employeeService.JobAppHireProcess(ref employeeInfo);

                                // Look at the results.
                                result.Badge = employeeInfo.BadgeNumber;
                                result.Company = employeeInfo.Company;
                                result.Employee = employeeInfo.EmployeeNumber;
                                result.Message = employeeInfo.ApplicationReturnMessage1;

                                if (!result.Message.ToUpper().StartsWith(RESULT_HIRE_COMPLETE))
                                {
                                    result.Type = RESULT_REJECT;
                                }
                                else
                                {
                                    // Fire and forget a call to TEST API to enable real-time test data.
                                    WebApiConfig.InvokeTestApi(_logFile, _assemblyName, clonedItem);
                                }
                            }
                            catch (Exception ex)
                            {
                                Logging.WriteLog(_logFile, _assemblyName + " - ERROR calling Lawson: " + ex.Message);
                                result.Type = RESULT_REJECT;
                                result.Message = ERROR_PERSONNEL;
                            }
                        }

                        response = Request.CreateResponse<HireProcessResult>(HttpStatusCode.Created, result);
                        Logging.WriteLog(_logFile, _assemblyName + " - HireProcessResult: " + result.ToString());
                    }
                    catch (Exception e)
                    {
                        Logging.WriteLog(_logFile, _assemblyName + " - ERROR invoking Lawson service: " + e.Message);
                        result.Type = RESULT_REJECT;
                        result.Message = ERROR_PERSONNEL;
                    }
                }
                else // a required field is missing.
                {
                    response = Request.CreateErrorResponse(HttpStatusCode.BadRequest, ModelState);
                    Logging.WriteLog(_logFile, _assemblyName + " - HireProcessResult Model ERROR: " + response.ReasonPhrase);
                    foreach (string modelkey in ModelState.Keys)
                    {
                        Logging.WriteLog(_logFile, modelkey);
                    }
                }
            }
            else
            {
                response = Request.CreateErrorResponse(HttpStatusCode.BadRequest, ModelState);
                Logging.WriteLog(_logFile, _assemblyName + " - HireProcessResult Model ERROR: NULL item. Check payload for missing braces, quotes, or content");
            }

            return response;
        }

        /// <summary>
        /// Apply validation and translation business logic to all received payload fields.
        /// </summary>
        /// <param name="item"></param>
        private void TransformPayload(HireProcess item)
        {
            item.FirstName = EliminateSpecialCharacters(ValidateRequiredField("FirstName", item.FirstName));
            item.MiddleName = EliminateSpecialCharacters(ValidateNonRequiredField("MiddleName", item.MiddleName));
            item.LastName = EliminateSpecialCharacters(ValidateRequiredField("LastName", item.LastName));
            item.Address1 = ValidateRequiredField("Address1", item.Address1);
            item.Address2 = ValidateNonRequiredField("Address2", item.Address2);
            item.City = ValidateRequiredField("City", item.City);
            item.State = ValidateRequiredField("State", item.State);
            item.Zip = ValidateRequiredField("Zip", item.Zip);
            item.Phone = FormatPhone(ValidateRequiredField("Phone", item.Phone));
            item.Location = ValidateRequiredField("Location", item.Location);
            item.SourceSSN = ValidateSSN(ValidateRequiredField("SourceSSN", item.SourceSSN));
            item.Position = ValidateRequiredField("Position", item.Position);
            item.EEORace = TranslateRace(ValidateRequiredField("EEORace", item.EEORace));
            item.EEOGender = TranslateGender(ValidateNonRequiredField("EEOGender", item.EEOGender));
            item.DateOfBirth = DateTime.Parse(ValidateRequiredField("DateOfBirth", item.DateOfBirth)).ToString(LOG_DATE_FORMAT);
            item.StartDate = DateTime.Parse(ValidateRequiredField("HiredDate", item.StartDate)).ToString(LOG_DATE_FORMAT);
            //item.Salary = item.Salary; // data type will not accept N/A so this must be a number.
            item.SalaryType = TranslateSalaryType(ValidateRequiredField("SalaryType", item.SalaryType));
            item.FederalW4ExemptStatus = ValidateRequiredField("FederalW4ExemptStatus", item.FederalW4ExemptStatus) == "0" ? "N" : "Y";
            item.FederalW4Status = TranslateW4Status(ValidateRequiredField("FederalW4Status", item.FederalW4Status));
            item.FederalW4ExemptAmount = ValidateNonRequiredField("FederalW4ExemptAmount", item.FederalW4ExemptAmount);
            item.FederalW4AdditionalAmount = ValidateNonRequiredField("FederalW4AdditionalAmount", item.FederalW4AdditionalAmount);

            string stateW4ExemptStatus = ValidateNonRequiredField("StateW4ExemptStatus", item.StateW4ExemptStatus);
            item.StateW4ExemptStatus = ((stateW4ExemptStatus == "0") || (stateW4ExemptStatus == string.Empty)) ? "N" : "Y";

            item.StateW4Status = TranslateW4Status(ValidateNonRequiredField("StateW4Status", item.StateW4Status));
            item.StateW4ExemptAmount = ValidateNonRequiredField("StateW4ExemptAmount", item.StateW4ExemptAmount);
            item.StateW4AdditionalAmount = ValidateNonRequiredField("StateW4AdditionalAmount", item.StateW4AdditionalAmount);
            item.DirectDepositAccount = ValidateNonRequiredField("DirectDepositAccount", item.DirectDepositAccount);
            item.DirectDepositRouting = ValidateNonRequiredField("DirectDepositRouting", item.DirectDepositRouting);
            item.PayCardNumber = ValidatePayCard(ValidateNonRequiredField("PayCardNumber", item.PayCardNumber));
            item.TrainingLocation = ValidateTrainingLocation(item.TrainingLocation);
            item.EmergencyContactFirstName = ValidateNonRequiredField("EmergencyContactFirstName", item.EmergencyContactFirstName);
            item.EmergencyContactLastName = ValidateNonRequiredField("EmergencyContactLastName", item.EmergencyContactLastName);
            item.EmergencyContactHomePhone = ValidateNonRequiredField("EmergencyContactHomePhone", item.EmergencyContactHomePhone);
            item.EmergencyContactCellPhone = ValidateNonRequiredField("EmergencyContactCellPhone", item.EmergencyContactCellPhone);
        }

        /// <summary>
        /// Pad location with zeroes to always supply 6 characters
        /// </summary>
        /// <param name="location"></param>
        /// <returns></returns>
        private string ValidateTrainingLocation(string location)
        {
            string trainingLocation = location;

            if (trainingLocation.ToUpper().Equals("N/A"))
            {
                trainingLocation = string.Empty;
            }
            else if (trainingLocation.Length > 0 && trainingLocation.Length < 6)
            {
                try
                {
                    trainingLocation = Convert.ToInt16(trainingLocation).ToString("000000");
                }
                catch (Exception)
                {
                    Logging.WriteLog(_logFile, _assemblyName + " - HireProcessResult: Training location format incorrect: " + trainingLocation);
                }
            }

            return trainingLocation;
        }

        /// <summary>
        /// Regex validation of SSN
        /// </summary>
        /// <param name="ssn"></param>
        /// <returns></returns>
        private string ValidateSSN(string ssn)
        {
            // get a social security regular expresssion checker
            string regex = System.Configuration.ConfigurationManager.AppSettings["SocialFormatCheck"];
            System.Text.RegularExpressions.Regex pattern = new System.Text.RegularExpressions.Regex(regex);

            if (!pattern.IsMatch(ssn.Trim()))
            {
                throw new Exception(ERROR_BAD_SSN);
            }

            return ssn.Trim();
        }

        /// <summary>
        /// Paycard translation to Lawson-friendly values with money market prefic prepended if not supplied.
        /// </summary>
        /// <param name="paycard"></param>
        /// <returns></returns>
        private string ValidatePayCard(string paycard)
        {
            // validate/fix the paycard number
            string paycardPrefix = System.Configuration.ConfigurationManager.AppSettings["MoneyNetworkPrefix"];

            if (paycard != null && paycard.Length > 0)
            {
                if (!paycard.StartsWith(paycardPrefix))
                {
                    paycard = paycardPrefix + paycard;
                }

                if (paycard.Length != PAYCARD_LENGTH)
                {
                    throw new Exception(ERROR_BAD_PAYCARD);
                }
            }

            return paycard;
        }

        /// <summary>
        /// Required fields cannot be N/A
        /// </summary>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        private string ValidateRequiredField(string field, string value)
        {
            if (value.ToUpper().Equals("N/A"))
            {
                throw new Exception("N/A not allowed in field " + field);
            }
            else
            {
                return value.ToUpper();
            }
        }

        /// <summary>
        /// Fields can be NA and are translated to blank for Lawson
        /// </summary>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        private string ValidateNonRequiredField(string field, string value)
        {
            if (value.ToUpper().Equals("N/A"))
            {
                return string.Empty;
            }
            else
            {
                return value.ToUpper();
            }
        }

        /// <summary>
        /// Lawson like upper case
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        private string ToUpper(string source)
        {
            return source != null ? source.ToUpper() : string.Empty;
        }

        /// <summary>
        /// Phone translation to Lawson-friendly values
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        private string FormatPhone(string source)
        {
            return source != null ? source.Replace(".", "-").Replace("(", string.Empty).Replace(")", string.Empty) : string.Empty;
        }

        /// <summary>
        /// Race translation to Lawson-friendly values
        /// </summary>
        /// <param name="race"></param>
        /// <returns></returns>
        private string TranslateRace(string race)
        {
            string raceTranslated = string.Empty;

            if (race != null)
            { 
                switch (race.ToUpper())
                {
                    case "AMERICAN INDIAN":
                    case "1":
                        raceTranslated = "AN";
                        break;

                    case "ASIAN":
                    case "2":
                        raceTranslated = "AS";
                        break;

                    case "BLACK":
                    case "3":
                        raceTranslated = "B";
                        break;

                    case "HAWAIIAN":
                    case "4":
                        raceTranslated = "PI";
                        break;

                    case "WHITE":
                    case "5":
                        raceTranslated = "WH";
                        break;

                    case "TWO OR MORE RACES":
                    case "6":
                        raceTranslated = "TR";
                        break;

                    case "HISPANIC":
                    case "7":
                        raceTranslated = "HI";
                        break;

                    default:
                        raceTranslated = "NS"; // Not specified ?
                        break;
                }
            }

            return raceTranslated;
        }

        /// <summary>
        /// Gender translation to Lawson-friendly values
        /// </summary>
        /// <param name="gender"></param>
        /// <returns></returns>
        private string TranslateGender(string gender)
        {
            string genderTranslated = string.Empty; // Not specified? is ok with Lawson?

            if (gender != null)
            {
                switch (gender.ToUpper())
                {
                    case "MALE":
                    case "1":
                        genderTranslated = "M";
                        break;

                    case "FEMALE":
                    case "2":
                        genderTranslated = "F";
                        break;

                    default:
                        break;
                }
            }

            return genderTranslated;
        }

        /// <summary>
        /// State and Federal Status translation to Lawson-friendly values
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private string TranslateW4Status(string status)
        {
            string statusTranslated = string.Empty;

            if (status != null)
            {
                switch (status.ToUpper())
                {
                    case "S":
                        statusTranslated = "1";
                        break;

                    case "M":
                        statusTranslated = "2";
                        break;

                    case "H":
                        statusTranslated = "6";
                        break;

                    case "N/A":
                        throw new Exception("Invalid W4 Status");

                    default:
                        statusTranslated = status.ToUpper();
                        break;
                }
            }

            return statusTranslated;
        }

        /// <summary>
        /// SalaryType translation to Lawson-friendly values
        /// </summary>
        /// <param name="salaryType"></param>
        /// <returns></returns>
        private string TranslateSalaryType(string salaryType)
        {
            string salaryTypeTranslated = string.Empty;

            if (salaryType != null)
            {
                switch (salaryType.ToUpper())
                {
                    case "PER HOUR":
                    case "HOURLY":
                        salaryTypeTranslated = "H";
                        break;

                    case "SALARY":
                        salaryTypeTranslated = "S";
                        break;

                    default:
                        salaryTypeTranslated = salaryType.ToUpper();
                        break;
                }
            }

            return salaryTypeTranslated;
        }

        /// <summary>
        /// Replace diacritics with non-accented characters
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private string EliminateSpecialCharacters(string name)
        {
            string formD = name.Normalize(NormalizationForm.FormD);

            var output = new StringBuilder();
            foreach (char c in formD)
            {
                UnicodeCategory uc = CharUnicodeInfo.GetUnicodeCategory(c);
                if (uc != UnicodeCategory.NonSpacingMark)
                    output.Append(c);
            }

            return (output.ToString().Normalize(NormalizationForm.FormC));
        }

        // PUT api/values/5
        public void Put(int id, [FromBody]string value)
        {
        }

        // DELETE api/values/5
        public void Delete(int id)
        {
        }

        private void InitializeLog()
        {
            _assemblyName = this.GetType().Assembly.GetName().FullName;
            _logFile = System.Configuration.ConfigurationManager.AppSettings["LogFile"];
            if (_logFile != null)
            {
                _logFile = string.Format(_logFile, DateTime.Now.ToString(LOG_DATE_FORMAT));
                if (_logFile.Length == 0)
                {
                    _logFile = null;
                }
            }
            _logFileExceptions = System.Configuration.ConfigurationManager.AppSettings["LogFileExceptions"];
            if (_logFileExceptions != null)
            {
                _logFileExceptions = string.Format(_logFileExceptions, DateTime.Now.ToString(LOG_DATE_FORMAT));
                if (_logFileExceptions.Length == 0)
                {
                    _logFileExceptions = null;
                }
            }
        }
    }
}
