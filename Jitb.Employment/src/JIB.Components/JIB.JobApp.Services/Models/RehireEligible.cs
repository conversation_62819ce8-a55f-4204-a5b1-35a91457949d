﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;

namespace JIB.JobApp.Services.Models
{
    public class RehireEligibile
    {
        [Required]
        public string SourceSSN { get; set; }
        public string Location { get; set; }

        public override string ToString()
        {
            if (SourceSSN.Length >= 4)
            {
                return string.Format("\"Social\":\"XXX-XX-{0}\", \"Location\":\"{1}\"", SourceSSN.Substring(SourceSSN.Length-4, 4), Location);
            }
            else
            {
                return SourceSSN;
            }
        }
    }
}