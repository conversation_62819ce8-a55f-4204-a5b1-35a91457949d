﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace JIB.JobApp.Services.Models
{
    public class HireProcess
    {
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string MiddleName { get; set; }
        [Required]
        public string LastName { get; set; }
        [Required]
        public string Address1 { get; set; }
        [Required]
        public string Address2 { get; set; }
        [Required]
        public string City { get; set; }
        [Required]
        public string State { get; set; }
        [Required]
        public string Zip { get; set; }
        [Required]
        public string Phone { get; set; }
        [Required]
        public string Location { get; set; }
        [Required]
        public string SourceSSN { get; set; }
        [Required]
        public string Position { get; set; }
        [Required]
        public string EEORace { get; set; }
        [Required]
        public string EEOGender { get; set; }
        [Required]
        public string DateOfBirth { get; set; }
        [Required]
        public string StartDate { get; set; }
        [Required]
        public decimal Salary { get; set; }
        [Required]
        public string SalaryType { get; set; }
        
        //Federal & State
        [Required]
        public string FederalW4Status { get; set; }
        [Required]
        public string FederalW4ExemptAmount { get; set; }
        [Required]
        public string FederalW4AdditionalAmount { get; set; }
        [Required]
        public string FederalW4ExemptStatus { get; set; }

        [Required]
        public string StateW4Status { get; set; }
        [Required]
        public string StateW4ExemptAmount { get; set; }
        [Required]
        public string StateW4AdditionalAmount { get; set; }
        [Required]
        public string StateW4ExemptStatus { get; set; }
        //public decimal StateW4PercentArizona { get; set; }
        
        //Direct Deposit
        [Required]
        public string DirectDepositRouting { get; set; }
        [Required]
        public string DirectDepositAccount { get; set; }
        [Required]
        public string PayCardNumber { get; set; }
        [Required]
        public string TrainingLocation { get; set; }

        //Emergency Contact
        [Required]
        public string EmergencyContactFirstName { get; set; }
        [Required]
        public string EmergencyContactLastName { get; set; }
        [Required]
        public string EmergencyContactHomePhone { get; set; }
        [Required]
        public string EmergencyContactCellPhone { get; set; }

        public override string ToString()
        {
            return string.Format("\"FirstName\":\"{0}\",\"MiddleName\":\"{1}\",\"LastName\":\"{2}\",\"Address1\":\"{3}\",\"Address2\":\"{4}\",\"City\":\"{5}\",\"State\":\"{6}\",\"Zip\":\"{7}\",\"Phone\":\"{8}\",\"Location\":\"{9}\",\"SourceSSN\":\"XXX-XX-{10}\",\"Position\":\"{11}\",\"EEORace\":\"{12}\",\"EEOGender\":\"{13}\",\"DateOfBirth\":\"{14}\",\"StartDate\":\"{15}\",\"Salary\":\"{16}\",\"SalaryType\":\"{17}\",\"FederalW4Status\":\"{18}\",\"FederalW4ExemptAmount\":\"{19}\",\"FederalW4AdditionalAmount\":\"{20}\",\"FederalW4ExemptStatus\":\"{21}\",\"StateW4Status\":\"{22}\",\"StateW4ExemptAmount\":\"{23}\",\"StateW4AdditionalAmount\":\"{24}\",\"StateW4ExemptStatus\":\"{25}\",\"DirectDepositRouting\":\"{26}\",\"DirectDepositAccount\":\"{27}\",\"PaycardNumber\":\"{28}\",\"TrainingLocation\":\"{29}\",\"EmergencyContactFirstName\":\"{30}\",\"EmergencyContactLastName\":\"{31}\",\"EmergencyContactHomePhone\":\"{32}\",\"EmergencyContactCellPhone\":\"{33}\"",
                                    FirstName,            MiddleName,            LastName,            Address1,            Address2,            City,            State,            Zip,            Phone,            Location,            SourceSSN.Length >= 4 ? SourceSSN.Substring(SourceSSN.Length - 4, 4) : SourceSSN, Position, EEORace, EEOGender, DateOfBirth, StartDate,         Salary,             SalaryType,             FederalW4Status,             FederalW4ExemptAmount,             FederalW4AdditionalAmount,             FederalW4ExemptStatus,             StateW4Status,             StateW4ExemptAmount,             StateW4AdditionalAmount,             StateW4ExemptStatus,             DirectDepositRouting,             DirectDepositAccount,             PayCardNumber,             TrainingLocation,             EmergencyContactFirstName,             EmergencyContactLastName,             EmergencyContactHomePhone,             EmergencyContactCellPhone);
        }

        public HireProcess ShallowCopy()
        {
            return (HireProcess)this.MemberwiseClone();
        }
    }
}