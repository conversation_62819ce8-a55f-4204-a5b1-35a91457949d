﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace JIB.JobApp.Services.Models
{
    public class HireProcessResult
    {
        public string Company { get; set; }
        public string Employee { get; set; }
        public string Badge { get; set; }
        public string Message { get; set; }
        public string Type { get; set; }

        public override string ToString()
        {
            return string.Format("Company: {0}, Employee: {1}, Badge: {2}, Type: {3}, Message: {4}", Company, Employee, Badge, Type, Message);
        }
    }
}