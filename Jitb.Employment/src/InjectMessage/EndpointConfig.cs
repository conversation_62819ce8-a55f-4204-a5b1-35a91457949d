﻿
using System;
using NServiceBus;

namespace InjectMessage
{
    using Jitb.Employment.Domain.Configuration;
    using Jitb.Employment.Domain.Nsb;

    /*
		This class configures this endpoint as a Server. More information about how to configure the NServiceBus host
		can be found here: http://particular.net/articles/the-nservicebus-host
	*/
    public class EndpointConfig : IConfigureThisEndpoint
    {
        public void Customize(EndpointConfiguration configuration)
        {
            configuration.DefaultJitbConfiguration();
            configuration.ConfigureMappings();

        }
    }
}