﻿namespace InjectMessage
{
    using System;
    using System.Linq;
    using Jitb.Employment.Contracts.Commands.MSMQListener;
    using Jitb.Employment.Domain.Nsb;
    using NLog;
    using NServiceBus;
    

    class Program
    {
        private static Logger _logger;
        static void Main(string[] args)
        {
            LogManager.ThrowExceptions = true;
            _logger = LogManager.GetLogger("InjectMessage");
            var command = args[0];
            var commandArgs = args.Skip(1).Take(args.Length - 1).ToArray();
            SendMessage(command, commandArgs);
        }


        private async static void SendMessage(string command, string[] args)
        { 
            var provider = new MessageMappingProvider();
            var endpointConfiguration = new EndpointConfiguration("InjectMessage.SelfHosted");
       //     endpointConfiguration.SendOnly();
            endpointConfiguration.DefaultJitbConfiguration();
            endpointConfiguration.ConfigureMappings(provider);
            var endpointInstance = Endpoint.Create(endpointConfiguration).Result.Start().Result;
            try
            {
                var injectMessage = new InjectMessage
                {
                    Message = command,
                    Args = args
                };

                await endpointInstance.Send(injectMessage);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
            if(System.Diagnostics.Debugger.IsAttached)
                Console.ReadLine();


            // Send a message to the queue.
            //var injectQueue = new MessageQueue(queueName);
            //                injectQueue.Formatter = new XmlMessageFormatter();
                          var messageBody = new InjectedMessage();
            ////                messageBody.command = "NewBadgeId";
            ////                messageBody.employeeId = long.Parse(message);
            ////                var queueMessage = new Message(messageBody);
            //
            //// var queueMessage = new Message("NewBadgeid,9148842");
            //_logger.Info($@"Sending message {message} to queue {queueName}");
            //injectQueue.Send(message);
            //_logger.Info($@"Message sent");
        }
    }
}
