﻿<connectionStrings>
  <add name="InstanceMapping" connectionString="data source=dbnsb.test-entsys.aws.jitb.net;initial catalog=dbNSB;integrated security=SSPI;"/>
  <add name="MessageRouteMapping" connectionString="data source=dbnsb.test-entsys.aws.jitb.net;initial catalog=dbNSB;integrated security=SSPI;"/>
  <add name="NServiceBus/Persistence" connectionString="data source=dbnservicebus.test-entsys.aws.jitb.net;initial catalog=dbnservicebus;integrated security=SSPI;"/>
  <add name="Default" connectionString="data source=dbNServiceBus.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;"/>
  <add name="ErestaurantIntegration" connectionString="data source=dz-nservicebus.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=true;"/>
  <add name="LocationDb" connectionString="data source=dblocation.test-entsys.aws.jitb.net;initial catalog=dbLocation;integrated security=SSPI;enlist=false;"/>
  <add name="Config" connectionString="data source=dbnservicebus-employment-config.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus_Employment_Config;integrated security=SSPI;"/>
  <add name="JibLocationEntityModel" connectionString="data source=dbNServiceBus.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  <add name="HireEligibility" connectionString="data source=dz-nservicebus.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI" />
  <!-- Note the CSNsbSqlAG-Lsnr was mondified in aws qa to use CSERESTT01V because aws does not have permissions for production. -->
  <add name="CSNsbSqlAG-Lsnr" connectionString="data source=dz-nservicebus.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;" />
  <add name="Model_FIMFMADEMP_WPS" connectionString="data source=dbNServiceBus.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  <add name="ModelEmployeeAction" connectionString="data source=dbNServiceBus.test-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  <!--
  <add name="Frantracker" connectionString="data source=dbNServiceBus.test-entsys.aws.jitb.net;initial catalog=dbTracker;integrated security=SSPI;enlist=false;" />
  -->
</connectionStrings>