﻿<connectionStrings>
  <add name="InstanceMapping" connectionString="data source=dbnsb.prod-entsys.aws.jitb.net;initial catalog=dbNSB;integrated security=SSPI;"/>
  <add name="MessageRouteMapping" connectionString="data source=dbnsb.prod-entsys.aws.jitb.net;initial catalog=dbNSB;integrated security=SSPI;"/>
  <add name="NServiceBus/Persistence" connectionString="data source=dbnservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNserviceBus;integrated security=SSPI;"/>
  <add name="Default" connectionString="data source=dbnservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNserviceBus;integrated security=SSPI;"/>
  <add name="ErestaurantIntegration" connectionString="data source=dz-nservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;"/>
  <add name="LocationDb" connectionString="data source=dblocation.prod-entsys.aws.jitb.net;initial catalog=dbLocation;integrated security=SSPI;enlist=false;"/>
  <add name="MessageRouteMapping" connectionString="data source=dbnsb.prod-entsys.aws.jitb.net;initial catalog=dbNSB;integrated security=SSPI;"/>

  <add name="Sitecenter" connectionString= "data source=SitecenterEDW.prod-entsys.aws.jitb.net;Initial Catalog=SitecenterEDW;Integrated Security=SSPI;enlist=false;"/>
  <add name="Config" connectionString="data source=dbnservicebus-employment-config.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus_Employment_Config;integrated security=SSPI;"/>
  <add name="CSNsbSqlAG-Lsnr" connectionString="data source=dz-nservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;" />
  <add name="JibLocationEntityModel" connectionString="data source=dz-nservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  <add name="HireEligibility" connectionString="data source=dz-nservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNserviceBus;integrated security=SSPI;" />
  <add name="Model_FIMFMADEMP_WPS" connectionString="data source=dbnservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  <add name="ModelEmployeeAction" connectionString="data source=dbnservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  <add name="Frantracker" connectionString="data source=dbTracker.prod-entsys.aws.jitb.net;initial catalog=dbTracker;integrated security=SSPI;enlist=false;"/>
  <add name="EmploymentLocation" connectionString="data source=dbnservicebus.prod-entsys.aws.jitb.net;initial catalog=dbNserviceBus;integrated security=SSPI;enlist=false;"/>
</connectionStrings>