﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jitb.Employment.ActiveDirectory.Endpoint
{
    using Internal.Contracts.Commands.ActiveDirectory;
    using NServiceBus;
    public class Startup : IWantToRunWhenEndpointStartsAndStops
    {
        public async Task Start(IMessageSession session)
        {
            await session.Send<StartCheckingActiveDirectory>(x =>
            {
                x.RequestId = "AD.CORP.JITB.NET";
            });
            await session.Send<StartCheckingActiveDirectory>(x =>
            {
<<<<<<< HEAD
                x.RequestId = "Jack.Franshise";
=======
                x.RequestId = "Jack.Franchise";
>>>>>>> ad4cccfaf5e96b7fa38126b3c521ae33902d8206
            });
            await session.Send<StartCheckingActiveDirectory>(x =>
            {
                x.RequestId = "AD.CORP.QDOBA";
            });
        }

        public async Task Stop(IMessageSession session)
        {
            await Task.CompletedTask;
        }
    }
}
