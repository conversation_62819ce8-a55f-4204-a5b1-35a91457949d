﻿using System.CodeDom;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel.PeerResolvers;
using System.Threading.Tasks;
using Amazon.Runtime.Internal;
using AutoMapper;
using Jitb.Employment.ActiveDirectory.Endpoint.Providers;
using Jitb.Employment.Contracts.Commands.Employment;
using Jitb.Employment.Domain.Constants;
using Jitb.Employment.Domain.EmployeeJobServiceRef;
using KellermanSoftware.CompareNetObjects;
using NHibernate.Criterion;

namespace Jitb.Employment.ActiveDirectory.Endpoint.Handlers
{
    using System;
    using Contracts.Commands.ActiveDirectory;
    using Contracts.Events.ActiveDirectory;
    using Domain.Concepts;
    using Domain.Repositories.Employment;
    using NServiceBus;
    using NLog;
    

    public  class CheckIfActiveDirectoryChangedHandler
        : IHandleMessages<CheckIfAdInfoChanged>
    {
        private readonly ICreateOrUpdatePersonaProvider _updatePersonaProvider;
        private readonly IPersonaRepository _personaRepository;

        private static readonly Logger _log = LogManager.GetCurrentClassLogger();


        public CheckIfActiveDirectoryChangedHandler(ICreateOrUpdatePersonaProvider updatePersonaProvider
            , IPersonaRepository personaRepository)
        {
            _updatePersonaProvider = updatePersonaProvider;
            _personaRepository = personaRepository;
        }

        public async Task Handle(CheckIfAdInfoChanged message, IMessageHandlerContext context)
        {
            var persona =
                _personaRepository.GetByGuid(message.AdGuid);

            if (persona == null)
                await _updatePersonaProvider.CreatePersona(message, context);
            else
                await _updatePersonaProvider.UpdatePersona(persona, message, context);


            await Task.FromResult(true);
        }
    }
}