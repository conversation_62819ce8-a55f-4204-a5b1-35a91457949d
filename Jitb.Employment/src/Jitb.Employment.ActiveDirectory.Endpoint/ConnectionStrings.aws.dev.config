﻿<connectionStrings>
	<add name="InstanceMapping" connectionString="data source=.;initial catalog=dbNSB;integrated security=SSPI;"/>
	<add name="MessageRouteMapping" connectionString="data source=.;initial catalog=dbNSB;integrated security=SSPI;"/>
	<add name="NServiceBus/Persistence" connectionString="data source=.;initial catalog=dbnservicebus;integrated security=SSPI;"/>
	<add name="Default" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;"/>
	<add name="ErestaurantIntegration" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=true;"/>
	<add name="LocationDb" connectionString="data source=.;initial catalog=dbLocation;integrated security=SSPI;enlist=false;"/>
	<add name="Config" connectionString="data source=.;initial catalog=dbNServiceBus-Employment-Config;integrated security=SSPI;"/>
	<add name="JibLocationEntityModel" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
	<add name="HireEligibility" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI" />
	<add name="CSNsbSqlAG-Lsnr" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;" />
	<add name="Model_FIMFMADEMP_WPS" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
	<add name="ModelEmployeeAction" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
	<!--
  <add name="Frantracker" connectionString="data source=.;initial catalog=dbTracker;integrated security=SSPI;enlist=false;" />
  -->
</connectionStrings>