﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<section name="nlog" type="NLog.Config.ConfigSection<PERSON>and<PERSON>, NLog" />
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSection<PERSON>and<PERSON>, log4net" />
		<section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
	</configSections>
	<connectionStrings>
		<add name="InstanceMapping" connectionString="Data Source=.;Initial Catalog=dbnservicebus;integrated security=SSPI;enlist=false;" />
		<add name="MessageRouteMapping" connectionString="Data Source=.;Initial Catalog=dbNServiceBus;integrated security=SSPI;" />
		<add name="NServiceBus/Persistence" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;enlist=false;" />
		<add name="Default" connectionString="data source=.;initial catalog=dbNServiceBus;integrated security=SSPI;enlist=false;" />
	</connectionStrings>
		<!--

  -->
	<!--<connectionStrings configSource="ConnectionStrings.config" />-->

	<appSettings>
		<add key="nhibernate-logger" value="Jitb.CommonLibrary.NLogFactory, Jitb.CommonLibrary" />
		<add key="env" value="" />
		<add key="ServiceControl/Queue" value="particular.servicecontrol" />
		<add key="MonitoringQueue" value="particular.monitoring" />
		<add key="EncryptionEnabled" value="false" />
		<add key="NServiceBus/License" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&lt;license UpgradeProtectionExpiration=&quot;2017-03-02&quot; Applications=&quot;All&quot; Edition=&quot;Enterprise&quot; Quantity=&quot;3&quot; MaxMessageThroughputPerSecond=&quot;Max&quot; AllowedNumberOfWorkerNodes=&quot;Max&quot; WorkerThreads=&quot;Max&quot; LicenseVersion=&quot;5.0&quot; type=&quot;Standard&quot; expiration=&quot;2116-04-19T18:03:27.1278394&quot; id=&quot;3116f539-3039-4d9b-b402-9dffcc8692dc&quot;&gt;&lt;name&gt;Jack in the Box Inc.&lt;/name&gt;&lt;Signature xmlns=&quot;http://www.w3.org/2000/09/xmldsig#&quot;&gt;&lt;SignedInfo&gt;&lt;CanonicalizationMethod Algorithm=&quot;http://www.w3.org/TR/2001/REC-xml-c14n-20010315&quot;/&gt;&lt;SignatureMethod Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#rsa-sha1&quot;/&gt;&lt;Reference URI=&quot;&quot;&gt;&lt;Transforms&gt;&lt;Transform Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#enveloped-signature&quot;/&gt;&lt;/Transforms&gt;&lt;DigestMethod Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#sha1&quot;/&gt;&lt;DigestValue&gt;Q4DLcl52+SRajlhxJqUk4HbVzEo=&lt;/DigestValue&gt;&lt;/Reference&gt;&lt;/SignedInfo&gt;&lt;SignatureValue&gt;Y6EcGxepSX2omKPtn1psTRPhvhOVmY2Ja9XgVSVtrMwSlBERip96YD/Y2Dvs0nByuRb4qodvLJiIou3js9U4WQxrgOujfWPbYBiXJsrBIXXJTSQsaL/SHPe1cYsNNKGzBVZwur+PTR7+aUddgHJmk9IQ41WZnpzlu2n4YpUjDEA=&lt;/SignatureValue&gt;&lt;/Signature&gt;&lt;/license&gt;" />
		<add key="DaysToLookBackOnNew" value="3" />
		<add key="ADCheckSagaMinutes" value="1" />
		<add key="ADSagaMaxRecordsPerBatch" value="25" />
		<add key="JackCorpPath" value="LDAP://AD.CORP.JITB.NET/DC=corp,DC=jitb,DC=net" />
		<add key="JackDSPath" value="LDAP://AD.CORP.JITB.NET/DC=ds,DC=jitb,DC=net" />
		<add key="RouteMapsFromDatabase" value="true" />
    <add key="RoutingBridge/Address" value="Jitb.Employment.RoutingBridge.Endpoint" />
    <add key="errorQueue" value="error" />
    <add key="auditQueue" value="audit" />
  </appSettings>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
	</startup>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="NServiceBus.Core" publicKeyToken="9fc386479f8a226c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="AutoMapper" publicKeyToken="be96cd2c38ef1005" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Renci.SshNet" publicKeyToken="1cee9f8bde3db106" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2016.1.0.0" newVersion="2016.1.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<publisherPolicy apply="no" />
				<assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
				<bindingRedirect oldVersion="4.121.0.0 - 4.65535.65535.65535" newVersion="*********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="NServiceBus.NHibernate" publicKeyToken="9fc386479f8a226c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="NServiceBus.NLog" publicKeyToken="9fc386479f8a226c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Iesi.Collections" publicKeyToken="aa95f207798dfdb4" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.0.4000" newVersion="4.0.0.4000" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.FileProviders.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.0.0.0" newVersion="1.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<!--<dependentAssembly>
				<assemblyIdentity name="RestSharp" publicKeyToken="598062e77f915f75" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*********" newVersion="*********" />
			</dependentAssembly>-->
		</assemblyBinding>
	</runtime>
	<system.data>
		<DbProviderFactories>
			<remove invariant="Oracle.ManagedDataAccess.Client" />
			<add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
		</DbProviderFactories>
	</system.data>
	<oracle.manageddataaccess.client>
		<version number="*">
			<dataSources>
				<dataSource alias="SampleDataSource" descriptor="(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL))) " />
			</dataSources>
		</version>
	</oracle.manageddataaccess.client>
</configuration>
