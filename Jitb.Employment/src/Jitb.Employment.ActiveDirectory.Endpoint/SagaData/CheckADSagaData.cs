﻿using System;


namespace Jitb.Employment.ActiveDirectory.Endpoint.SagaData
{
    using NServiceBus;
    public class CheckADSagaData: IContainSagaData
    {
        public virtual Guid Id { get; set; }
        public virtual string Originator { get; set; }
        public virtual string OriginalMessageId { get; set; }
        public virtual DateTime LastCheckedDate { get; set; }
        public virtual bool IsStarted { get; set; }
        public virtual string RequestId { get; set; }
        public virtual long usnChanged { get; set; }
        public virtual string whenChanged { get; set; }
    }
}
