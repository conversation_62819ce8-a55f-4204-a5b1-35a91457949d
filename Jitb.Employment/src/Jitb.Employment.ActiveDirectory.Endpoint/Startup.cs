﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Contracts.Models;

namespace Jitb.Employment.ActiveDirectory.Endpoint
{
    using Internal.Contracts.Commands.ActiveDirectory;
    using NServiceBus;
    public class Startup : IWantToRunWhenEndpointStartsAndStops
    {
        public async Task Start(IMessageSession session)
        {
            await session.Send<StartCheckingActiveDirectory>(x =>
            {
                x.RequestId = "AD.CORP.JITB.NET";
            });
            await session.Send<StartCheckingActiveDirectory>(x =>
            {
                x.RequestId = "AD.DS.JITB.NET";
            });
        }

        public async Task Stop(IMessageSession session)
        {
            await Task.CompletedTask;
        }
    }
}
