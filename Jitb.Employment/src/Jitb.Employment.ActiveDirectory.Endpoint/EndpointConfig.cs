﻿using NServiceBus;

namespace Jitb.Employment.ActiveDirectory.Endpoint
{
    using Domain.Nsb;
    using Jitb.NSB.Commons.Repository;
    using Jitb.NSB.Commons.RoutingBridge;

    public class EndpointConfig : IConfigureThisEndpoint
    {
        public void Customize(EndpointConfiguration endpointConfiguration)
        {
            var provider = new MessageMappingProvider();
            var repository = new TransportRouteMapRepository();

            endpointConfiguration.DefaultJitbConfiguration();

            var transportRouteMaps = repository.GetTransportRouteMaps(DomainName.Employment);

            ConfigurationMappingRoutingBridge.ConfigureMappingsForRouting(provider, transportRouteMaps);
            RoutingBridgeConfiguration.ConfigureMappingTransport(endpointConfiguration);

        }
    }
}