﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using StructureMap;

namespace Jitb.Employment.ActiveDirectory.Endpoint
{
    public class EndpointRegistry : Registry
    {
        public EndpointRegistry()
        {
            this.Scan(y =>
            {
                y.TheCallingAssembly();
                y.WithDefaultConventions();
            });
        }
    }
}
