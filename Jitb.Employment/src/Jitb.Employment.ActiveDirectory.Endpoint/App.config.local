<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<section name="nlog" type="NLog.Config.ConfigSectionHandler, NLog"/>
		<section name="MessageForwardingInCaseOfFaultConfig" type="NServiceBus.Config.MessageForwardingInCaseOfFaultConfig, NServiceBus.Core"/>
		<section name="RijndaelEncryptionServiceConfig" type="NServiceBus.Config.RijndaelEncryptionServiceConfig, NServiceBus.Core"/>
		<section name="AuditConfig" type="NServiceBus.Config.AuditConfig, NServiceBus.Core"/>
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
	</configSections>
	<connectionStrings configSource="ConnectionStrings.config"/>

	<appSettings>
		<add key="nhibernate-logger" value="Jitb.CommonLibrary.NLogFactory, Jitb.CommonLibrary"/>
		<add key="env" value=""/>
		<add key="ServiceControl/Queue" value="particular.servicecontrol"/>
		<add key="EncryptionEnabled" value="false"/>
		<add key="NServiceBus/License" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&lt;license UpgradeProtectionExpiration=&quot;2017-03-02&quot; Applications=&quot;All&quot; Edition=&quot;Enterprise&quot; Quantity=&quot;3&quot; MaxMessageThroughputPerSecond=&quot;Max&quot; AllowedNumberOfWorkerNodes=&quot;Max&quot; WorkerThreads=&quot;Max&quot; LicenseVersion=&quot;5.0&quot; type=&quot;Standard&quot; expiration=&quot;2116-04-19T18:03:27.1278394&quot; id=&quot;3116f539-3039-4d9b-b402-9dffcc8692dc&quot;&gt;&lt;name&gt;Jack in the Box Inc.&lt;/name&gt;&lt;Signature xmlns=&quot;http://www.w3.org/2000/09/xmldsig#&quot;&gt;&lt;SignedInfo&gt;&lt;CanonicalizationMethod Algorithm=&quot;http://www.w3.org/TR/2001/REC-xml-c14n-20010315&quot;/&gt;&lt;SignatureMethod Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#rsa-sha1&quot;/&gt;&lt;Reference URI=&quot;&quot;&gt;&lt;Transforms&gt;&lt;Transform Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#enveloped-signature&quot;/&gt;&lt;/Transforms&gt;&lt;DigestMethod Algorithm=&quot;http://www.w3.org/2000/09/xmldsig#sha1&quot;/&gt;&lt;DigestValue&gt;Q4DLcl52+SRajlhxJqUk4HbVzEo=&lt;/DigestValue&gt;&lt;/Reference&gt;&lt;/SignedInfo&gt;&lt;SignatureValue&gt;Y6EcGxepSX2omKPtn1psTRPhvhOVmY2Ja9XgVSVtrMwSlBERip96YD/Y2Dvs0nByuRb4qodvLJiIou3js9U4WQxrgOujfWPbYBiXJsrBIXXJTSQsaL/SHPe1cYsNNKGzBVZwur+PTR7+aUddgHJmk9IQ41WZnpzlu2n4YpUjDEA=&lt;/SignatureValue&gt;&lt;/Signature&gt;&lt;/license&gt;"/>
		<add key="DaysToLookBackOnNew" value="3"/>
		<add key="ADCheckSagaMinutes" value="1"/>
	</appSettings>
	<MessageForwardingInCaseOfFaultConfig ErrorQueue="error"/>
	<RijndaelEncryptionServiceConfig Key="tWIrVnPy3Smd2r/xcSb9r/1wcVY9/3KWbrzIaemx5yw=" KeyIdentifier="2015-10" KeyFormat="Base64">
	</RijndaelEncryptionServiceConfig>
	<AuditConfig QueueName="audit"/>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1"/>
	</startup>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="NServiceBus.Core" publicKeyToken="9fc386479f8a226c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>
