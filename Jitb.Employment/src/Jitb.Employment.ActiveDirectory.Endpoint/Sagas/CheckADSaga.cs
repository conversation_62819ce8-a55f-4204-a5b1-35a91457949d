﻿using System;
using System.Threading.Tasks;

namespace Jitb.Employment.ActiveDirectory.Endpoint.Sagas
{
    using Internal.Contracts.Commands.ActiveDirectory;
    using Internal.Contracts.Messages.ActiveDirectory;
    using Jitb.Employment.Domain.Providers;
    using NServiceBus;
    using NServiceBus.Logging;
    using SagaData;
    using System.Configuration;

    public class CheckADSaga :
        Saga<CheckADSagaData>
        , IAmStartedByMessages<StartCheckingActiveDirectory>
        , IHandleMessages<LookforAdChanges>
        , IHandleTimeouts<CheckActiveDirectory>
    {
        private readonly IActiveDirectoryProvider activeDirectoryProvider;
        private static ILog _log = LogManager.GetLogger<CheckADSaga>();

        public CheckADSaga(IActiveDirectoryProvider activeDirectoryProvider)
        {
            this.activeDirectoryProvider = activeDirectoryProvider;
        }
    
        public async Task Handle(StartCheckingActiveDirectory message, IMessageHandlerContext context)
        {
            if (!Data.IsStarted)
            {
                Data.RequestId = message.RequestId;
                Data.IsStarted = true;
                Data.LastCheckedDate = new DateTime(2017, 1, 1);

                await context.Send<LookforAdChanges>(x =>
                {
                    x.RequestId = message.RequestId;
                });
            }
        }


        private async Task LookForADChanges(IMessageHandlerContext context)
        {
            _log.Debug($"Method LookForADChanges started - {Data.RequestId}");

            var tupleResult = activeDirectoryProvider.SearchAndProcess(Data.RequestId, Data.usnChanged);

            Data.LastCheckedDate = tupleResult.lastCheckedDate;
            Data.usnChanged = tupleResult.usnChanged;
            Data.whenChanged = tupleResult.whenChanged;

            _log.Debug($"RequestId: {Data.RequestId}; count: {tupleResult.list.Count}");
            foreach (var result in tupleResult.list)
            {
           
                await context.Send(result);
            }

            var timeoutMinutes = int.Parse(ConfigurationManager.AppSettings["ADCheckSagaMinutes"].ToString());

            await base.RequestTimeout<CheckActiveDirectory>(context, DateTime.Now.AddMinutes(timeoutMinutes));
        }

        public async Task Timeout(CheckActiveDirectory state, IMessageHandlerContext context)
        {
            await LookForADChanges(context);
        }

        protected override void ConfigureHowToFindSaga(SagaPropertyMapper<CheckADSagaData> mapper)
        {
            mapper.ConfigureMapping<StartCheckingActiveDirectory>(x => x.RequestId).ToSaga(s => s.RequestId);
            mapper.ConfigureMapping<LookforAdChanges>(x => x.RequestId).ToSaga(s => s.RequestId);
        }

        public async Task Handle(LookforAdChanges message, IMessageHandlerContext context)
        {
            // This task only runs on saga startup - it should never run again unless we delete the saga data.
            var results = activeDirectoryProvider.SearchChanged(Data.RequestId);

            Data.usnChanged = Convert.ToInt64(results["usnChanged"]);
            Data.whenChanged = results["whenChanged"].ToString();

            _log.Info($@"RequestId: {Data.RequestId} usnChanged: {Data.usnChanged}");

            await LookForADChanges(context);
        }
    }
}