﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon.Runtime.Internal;
using AutoMapper;
using Jitb.Employment.Contracts.Commands.ActiveDirectory;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Repositories.Employment;
using KellermanSoftware.CompareNetObjects;
using NLog;
using NServiceBus;

namespace Jitb.Employment.ActiveDirectory.Endpoint.Providers
{
    public interface ICreateOrUpdatePersonaProvider
    {
        Task CreatePersona(CheckIfAdInfoChanged message, IMessageHandlerContext context);
        Task UpdatePersona(Persona persona, CheckIfAdInfoChanged message, IMessageHandlerContext context);
    }
    public class CreateOrUpdatePersonaProvider : ICreateOrUpdatePersonaProvider
    {
        private readonly IPersonaRepository _personaRepository;

        private static readonly Logger _log = LogManager.GetCurrentClassLogger();
        private readonly MapperConfiguration _mapperConfig;
        private readonly Mapper _mapper;


        public CreateOrUpdatePersonaProvider(IPersonaRepository personaRepository)
        {
            _personaRepository = personaRepository;

            _mapperConfig = new MapperConfiguration(cfg => cfg.CreateMap<CheckIfAdInfoChanged, Persona>());
            _mapper = new Mapper(_mapperConfig);
        }

        public async Task CreatePersona(CheckIfAdInfoChanged message, IMessageHandlerContext context)
        {
            _log.Info($"Creating Persona for {message.AdNetworkId}");
            var persona = new Persona
            {
                AdGuid = message.AdGuid,
                AdDomain = message.AdDomain,
                AdDistinguishedName = message.AdDistinguishedName,
                AdFirstName = message.AdFirstName,
                AdLastName = message.AdLastName,
                AdEmail = message.AdEmail,
                AdEmployeeId = message.AdEmployeeId,
                AdNetworkId = message.AdNetworkId,
                AdAccountExpires = message.AdAccountExpires,
                AdCompany = message.AdCompany,
                AdDepartment = message.AdDepartment,
                AdOffice = message.AdOffice,
                AdUserPrincipalName = message.AdUserPrincipalName,
                IsEnabled = message.IsEnabled,
                IsInActiveDirectory = true,
                AdWhenChanged = message.AdWhenChanged,
                AdUsnChanged = message.AdUsnChanged,
                AdSid = message.AdSid
            };

            // hack - should not be necessary
            var minDate = new DateTime(1900, 1, 1);
            if (persona.AdAccountExpires < minDate)
            {
                _log.Debug($"Insert persona: {persona.AdNetworkId} expiration date too low.  Was {persona.AdAccountExpires}");
                persona.AdAccountExpires = null;
            }

            var maxDate = new DateTime(3000, 1, 1);
            if (persona.AdAccountExpires > maxDate)
            {
                _log.Debug($"Insert persona: {persona.AdNetworkId} expiration date too high.  Was {persona.AdAccountExpires}");
                persona.AdAccountExpires = null;
            }
            _personaRepository.Add(persona);

        }

        public async Task UpdatePersona(Persona persona, CheckIfAdInfoChanged message, IMessageHandlerContext context)
        {
            _log.Info($"Validating Persona for {message.AdNetworkId}");


            var persona2 = MapMessageToPersona(message);


            var personaProperties = typeof(Persona).GetProperties();

            var compareResult = Compare(persona, persona2);

            if (!compareResult.AreEqual)
            {
                foreach (var d in compareResult.Differences)
                {
                    _log.Info(
                        $"AD Changes:  {message.AdNetworkId} - {d.PropertyName} changed from *{d.Object1Value}* to *{d.Object2Value}*");

                    var p = personaProperties.First(x => x.Name == d.PropertyName);

                    var propValue = p.GetValue(persona2);
                    p.SetValue(persona, propValue);

                }



                persona.AdWhenChanged = message.AdWhenChanged;
                persona.AdUsnChanged = message.AdUsnChanged;
                // hack - should not be necessary
                var minDate = new DateTime(1900, 1, 1);
                if (persona.AdAccountExpires < minDate)
                {
                    _log.Debug($"Update persona: {persona.AdNetworkId} expiration date too low.  Was {persona.AdAccountExpires}");
                    persona.AdAccountExpires = null;
                }

                var maxDate = new DateTime(3000, 1, 1);
                if (persona.AdAccountExpires > maxDate)
                {
                    _log.Debug($"Update persona: {persona.AdNetworkId} expiration date too high.  Was {persona.AdAccountExpires}");
                    persona.AdAccountExpires = null;
                }

                _personaRepository.Add(persona);
                _log.Info($"Persona updated for {message.AdNetworkId}");

            }
            else _log.Info($"Persona NOT updated for {message.AdNetworkId}");



        }

        private Persona MapMessageToPersona(CheckIfAdInfoChanged message)
        {
            var persona = _mapper.Map<CheckIfAdInfoChanged, Persona>(message);
            return persona;
        }

        private ComparisonResult Compare(Persona persona, Persona persona2)
        {
            var cl = new CompareLogic();
            cl.Config.MembersToIgnore = new AutoConstructedList<string>()
                {"PersonaId", "AdGuid", "AdEmployeeLinks", "AdWhenChanged", "AdUsnChanged"};
            cl.Config.MaxDifferences = 50;
            var compareResult = cl.Compare(persona, persona2);
            return compareResult;
        }
        
    }
}
