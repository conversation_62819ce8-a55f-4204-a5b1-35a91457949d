﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\packages\NServiceBus.Transport.Msmq.1.1.1\build\NServiceBus.Transport.Msmq.props" Condition="Exists('..\..\packages\NServiceBus.Transport.Msmq.1.1.1\build\NServiceBus.Transport.Msmq.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{83CB1E69-E1AB-4277-848C-D9E517960052}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Jitb.Employment.ActiveDirectory.Endpoint</RootNamespace>
    <AssemblyName>Jitb.Employment.ActiveDirectory.Endpoint</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\QA\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Azure|AnyCPU'">
    <OutputPath>bin\Azure\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'prod|AnyCPU'">
    <OutputPath>bin\prod\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'aws.prod|AnyCPU'">
    <OutputPath>bin\aws.prod\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'aws.qa|AnyCPU'">
    <OutputPath>bin\aws.qa\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Specflow2|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Specflow2\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Afterman.nRepo, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Afterman.nRepo.2021.9.15.2\lib\net461\Afterman.nRepo.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Antlr3.Runtime.3.5.1\lib\net40-client\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=10.0.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AutoMapper.10.1.1\lib\net461\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.Core, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AWSSDK.Core.3.3.107.40\lib\net45\AWSSDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.S3, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AWSSDK.S3.3.3.113.2\lib\net45\AWSSDK.S3.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.SQS, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AWSSDK.SQS.3.3.103.26\lib\net45\AWSSDK.SQS.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.OpenPgp, Version=1.8.1.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\..\packages\BouncyCastle.OpenPgp.1.8.1.1\lib\net20\BouncyCastle.OpenPgp.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core, Version=5.0.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Castle.Core.5.2.1\lib\netstandard2.0\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="FluentNHibernate, Version=2.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\FluentNHibernate.2.0.3.0\lib\net40\FluentNHibernate.dll</HintPath>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.Common.Contracts, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Jitb.Common.Contracts.2019.10.31.1\lib\net461\Jitb.Common.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.CommonLibrary, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Jitb.CommonLibrary.2022.11.22.4\lib\net461\Jitb.CommonLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.NSB.Commons, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Jitb.NSB.Commons.2022.11.10.1\lib\net461\Jitb.NSB.Commons.dll</HintPath>
    </Reference>
    <Reference Include="KellermanSoftware.Compare-NET-Objects, Version=4.74.0.0, Culture=neutral, PublicKeyToken=d970ace04cc85217, processorArchitecture=MSIL">
      <HintPath>..\..\packages\CompareNETObjects.4.74.0\lib\net46\KellermanSoftware.Compare-NET-Objects.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Bcl.AsyncInterfaces.1.1.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.Configuration.Abstractions.3.1.3\lib\netstandard2.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.3.1.3\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.FileProviders.Abstractions.3.1.3\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.Hosting.Abstractions.3.1.3\lib\netstandard2.0\Microsoft.Extensions.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.Logging.Abstractions.3.1.3\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.Primitives.3.1.3\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.12.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NHibernate.4.0.4.4000\lib\net40\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NLog.4.7.10\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.AmazonSQS, Version=4.0.0.0, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.AmazonSQS.4.4.1\lib\net452\NServiceBus.AmazonSQS.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Callbacks, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Callbacks.3.0.0\lib\net452\NServiceBus.Callbacks.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Core, Version=7.0.0.0, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.7.4.0\lib\net452\NServiceBus.Core.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.CustomChecks, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.CustomChecks.3.0.1\lib\net452\NServiceBus.CustomChecks.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Encryption.MessageProperty, Version=2.0.0.0, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Encryption.MessageProperty.2.0.1\lib\net452\NServiceBus.Encryption.MessageProperty.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Heartbeat, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Heartbeat.3.0.1\lib\net452\NServiceBus.Heartbeat.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Host, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Host.8.0.0\lib\net452\NServiceBus.Host.exe</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Log4Net, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Log4Net.3.0.0\lib\net452\NServiceBus.Log4Net.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Metrics, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Metrics.3.0.0\lib\net452\NServiceBus.Metrics.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Metrics.ServiceControl, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Metrics.ServiceControl.3.0.2\lib\net452\NServiceBus.Metrics.ServiceControl.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Metrics.ServiceControl.Msmq, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Metrics.ServiceControl.Msmq.3.0.1\lib\net452\NServiceBus.Metrics.ServiceControl.Msmq.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Newtonsoft.Json, Version=2.0.0.0, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Newtonsoft.Json.2.2.0\lib\net452\NServiceBus.Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.NHibernate, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.NHibernate.8.0.1\lib\net452\NServiceBus.NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.NLog, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.NLog.3.0.0\lib\net452\NServiceBus.NLog.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.ObjectBuilder.StructureMap, Version=7.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.StructureMap.7.0.0\lib\net452\NServiceBus.ObjectBuilder.StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Raw, Version=3.2.1.0, Culture=neutral, PublicKeyToken=215652b07edbd86c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Raw.3.2.1\lib\net452\NServiceBus.Raw.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Router, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Router.3.9.0\lib\netstandard2.0\NServiceBus.Router.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Router.Connector, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Router.Connector.3.9.1\lib\net461\NServiceBus.Router.Connector.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.SagaAudit, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.SagaAudit.3.0.1\lib\net452\NServiceBus.SagaAudit.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.Transport.Msmq, Version=1.0.0.0, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.Transport.Msmq.1.1.1\lib\net452\NServiceBus.Transport.Msmq.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.121.2.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Oracle.ManagedDataAccess.12.1.24160719\lib\net40\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Remotion.Linq.EagerFetching.2.2.0\lib\net45\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet, Version=2020.0.1.0, Culture=neutral, PublicKeyToken=1cee9f8bde3db106, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SSH.NET.2020.0.1\lib\net40\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap, Version=4.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\StructureMap.4.7.1\lib\net45\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SqlClient, Version=4.2.0.2, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Data.SqlClient.4.4.3\lib\net461\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.EventLog, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Diagnostics.EventLog.4.7.0\lib\net461\System.Diagnostics.EventLog.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Messaging" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Runtime.CompilerServices.Unsafe.4.7.1\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EndpointConfig.cs" />
    <Compile Include="EndpointRegistry.cs" />
    <Compile Include="Handlers\CheckIfActiveDirectoryChangedHandler.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Providers\CreateOrUpdatePersonaProvider.cs" />
    <Compile Include="SagaData\CheckADSagaData.cs" />
    <Compile Include="Sagas\CheckADSaga.cs" />
    <Compile Include="Startup.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.aws.dev.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>App.config</DependentUpon>
    </None>
    <None Include="App.aws.prod.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>App.config</DependentUpon>
    </None>
    <None Include="App.aws.qa.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>App.config</DependentUpon>
    </None>
    <None Include="App.aws.stage.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="App.config.local">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>App.config</DependentUpon>
    </None>
    <None Include="App.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="ConnectionStrings.aws.dev.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>ConnectionStrings.config</DependentUpon>
    </None>
    <None Include="ConnectionStrings.aws.prod.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>ConnectionStrings.config</DependentUpon>
    </None>
    <None Include="ConnectionStrings.aws.qa.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>ConnectionStrings.config</DependentUpon>
    </None>
    <None Include="ConnectionStrings.aws.stage.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="ConnectionStrings.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="NLog.aws.dev.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>NLog.config</DependentUpon>
    </None>
    <None Include="NLog.aws.prod.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>NLog.config</DependentUpon>
    </None>
    <None Include="NLog.aws.qa.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>NLog.config</DependentUpon>
    </None>
    <None Include="NLog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Jitb.Employment.Contracts\Jitb.Employment.Contracts.csproj">
      <Project>{cee04b09-0c80-488a-82d4-33b625ccd064}</Project>
      <Name>Jitb.Employment.Contracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\Jitb.Employment.Domain\Jitb.Employment.Domain.csproj">
      <Project>{3955DF0A-7228-4F87-9810-09659E9561B0}</Project>
      <Name>Jitb.Employment.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\Jitb.Employment.Internal.Contracts\Jitb.Employment.Internal.Contracts.csproj">
      <Project>{7f1194bc-b0ec-44c5-b85d-79cd3b664780}</Project>
      <Name>Jitb.Employment.Internal.Contracts</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\..\packages\AWSSDK.S3.3.3.113.2\analyzers\dotnet\cs\AWSSDK.S3.CodeAnalysis.dll" />
    <Analyzer Include="..\..\packages\AWSSDK.SQS.3.3.103.26\analyzers\dotnet\cs\AWSSDK.SQS.CodeAnalysis.dll" />
    <Analyzer Include="..\..\packages\NServiceBus.7.4.0\analyzers\dotnet\cs\NServiceBus.Core.Analyzer.dll" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <PropertyGroup>
    <StartAction>Program</StartAction>
    <StartProgram>$(ProjectDir)$(OutputPath)NServiceBus.Host.exe</StartProgram>
  </PropertyGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\OctoPack.3.6.5\build\OctoPack.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\OctoPack.3.6.5\build\OctoPack.targets'))" />
    <Error Condition="!Exists('..\..\packages\NServiceBus.Transport.Msmq.1.1.1\build\NServiceBus.Transport.Msmq.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\NServiceBus.Transport.Msmq.1.1.1\build\NServiceBus.Transport.Msmq.props'))" />
  </Target>
  <Import Project="..\..\packages\OctoPack.3.6.5\build\OctoPack.targets" Condition="Exists('..\..\packages\OctoPack.3.6.5\build\OctoPack.targets')" />
</Project>