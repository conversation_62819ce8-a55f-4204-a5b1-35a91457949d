﻿using System;
using NHibernate;

namespace Afterman.nRepo.NHibernate
{
    public class NhibernateUnitOfWork : IUnitOfWork
    {
        private ISessionBuilder _sessionBuilder;
        public NhibernateUnitOfWork(ISessionBuilder sessionBuilder)
        {
            this._sessionBuilder = sessionBuilder;
            this.UnitOfWorkId = Guid.NewGuid();
        }

        public ISession Session { get { return this._sessionBuilder.GetSession(); } }

        public Guid UnitOfWorkId { get; private set; }

        public void Start()
        {
            if (!this._sessionBuilder.GetSession().Transaction.IsActive)
                this._sessionBuilder.GetSession().BeginTransaction();
        }

        public void End()
        {
            if (this._sessionBuilder.GetSession().Transaction.IsActive)
                this._sessionBuilder.GetSession().Transaction.Commit();
            this._sessionBuilder.CloseSession();
        }

        public void Exception(Exception ex)
        {
            if (this._sessionBuilder.GetSession().Transaction.IsActive)
                this._sessionBuilder.GetSession().Transaction.Rollback();
        }



        public void Dispose()
        {
            this._sessionBuilder.CloseSession();
        }
    }
}
