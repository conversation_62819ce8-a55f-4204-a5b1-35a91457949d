﻿using System.Collections.Generic;
using System.Linq;
using Afterman.nRepo.Configuration;
using NHibernate;
using NHibernate.Linq;

namespace Afterman.nRepo.NHibernate
{
    using System.Data;

    public class NHibernateDataAccessor<T> : IDataAccessor<T>
    {
        private readonly ISession _session;
        private readonly bool _implicitTransactions;
        private IsolationLevel _isolationLevel;

        //private class Trans : IDisposable
        //{
        //    private NHibernateDataAccessor<T> _accessor;
        //    public Trans(NHibernateDataAccessor<T> accessor)
        //    {
        //        this._accessor = accessor;
        //        if (this._accessor._implicitTransactions)
        //        {
        //            this._accessor.BeginTransaction();
        //        }
        //    }

        //    public void Dispose()
        //    {
        //        if(this._accessor._implicitTransactions)
        //        {
        //            this._accessor.CommitTransaction();
        //        }
        //    }
        //}

        public void SetIsolationLevel(IsolationLevel level)
        {
            this._isolationLevel = level;
        }

        public NHibernateDataAccessor(ISessionBuilder sessionBuilder, bool implicitTransactions)
        {
            this._session = sessionBuilder.GetSession();
            this._implicitTransactions = implicitTransactions;
        }

        public ISession Session
        {
            get { return this._session; }
        }
        public virtual IQueryable<T> CreateQuery()
        {
            this.Session.SessionFactory.EvictQueries();
            return this.Session.Query<T>();
        }

        public virtual void Add(T entity)
        {
            this.BeginTransaction();
            this.Session.SaveOrUpdate(entity);
            this.CommitTransaction();
        }

        public virtual void Remove(IList<T> entities)
        {
            this.BeginTransaction();
            entities.ToList().ForEach(x => this.Session.Delete(x));
            this.CommitTransaction();
        }

        public virtual void Remove(T entity)
        {
            this.BeginTransaction();
            this.Session.Delete(entity);
            this.CommitTransaction();
        }

        public virtual IList<T> GetAll()
        {
            return (from item in this.CreateQuery() select item).ToList();
        }

        public virtual IList<T> GetAll(int pageSize, int pageNumber)
        {
            var query = this.CreateQuery().Skip(pageSize*pageNumber).Take(pageSize);
            return query.ToList();
        }

        public virtual T Get(object key)
        {
            return this.Session.Get<T>(key);
        }

        public virtual void Add(IList<T> entities)
        {
            this.BeginTransaction();
            foreach (T item in entities)
            {
                this.Session.SaveOrUpdate(item);
            }
            this.CommitTransaction();
            
               
        }

        public IList<T> ExecuteQuery(string query)
        {
            return this.Session.CreateQuery(query).List<T>();
        }

        public void Dispose()
        {
            if (null == this.Session)
                return;
            this.Session.Dispose();
        }

        public void BeginTransaction()
        {
            if (!this.Session.Transaction.IsActive)
                this.Session.BeginTransaction(this._isolationLevel);
        }

        public void CommitTransaction()
        {
            if (this.Session.Transaction.IsActive)
                this.Session.Transaction.Commit();
           
        }

        public void RollbackTransaction()
        {
            if (this.Session.Transaction.IsActive)
                this.Session.Transaction.Rollback();
        }


        
    }
}