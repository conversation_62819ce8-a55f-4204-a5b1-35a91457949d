﻿using Afterman.nRepo.Events;
using System.Collections.Generic;

namespace Afterman.nRepo
{
    public static class RepositoryEventRegistry
    {
        private static IList<IRepositoryEvent> _events = new List<IRepositoryEvent>();

        private static IList<IQueryInterceptor> _interceptors = new List<IQueryInterceptor>();

        public static void Register(IRepositoryEvent @event)
        {
            _events.Add(@event);
        }

        public static void Register(IQueryInterceptor interceptor)
        {
            _interceptors.Add(interceptor);
        }

        public static void RaiseEvent<T>(object entity)
            where T : class, IRepositoryEvent
        {
            foreach (var eventHandler in _events)
            {
                var handler = eventHandler as T;
                if(handler != null)
                {
                    handler.<PERSON><PERSON>(entity);
                }
            }
        }

        public static IEnumerable<IQueryInterceptor> GetQueryInterceptors<T>()
        {
            foreach (var eventHandler in _interceptors)
            {
                if (eventHandler.CanHandle<T>())
                    yield return eventHandler;
            }
            yield break;
        }
    }
}
