<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <variable name="endpointName" value="EmploymentAuditEndpoint"/>
  <targets async="true">
    <target name="console"
            xsi:type="ColoredConsole"
            layout="${date} ${level:uppercase=true} ${machinename} ${windows-identity} ${processname} ${processid} ${threadid} ${logger} ${callsite:className=true:includeNamespace=true:fileName=true:includeSourcePath=true:methodName=true:cleanNamesOfAnonymousDelegates=true:skipFrames=0} ${callsite-linenumber:skipFrames=0} ${message} ${stacktrace:topFrames=10} ${exception:format=toString,Data:maxInnerExceptionLevel=10}"/>
    <target name="file"
            xsi:type="File"
            fileName="C:\JIB Logs\Employment\${var:endpointName}_${shortdate}.txt"
            archiveFileName="C:\JIB Logs\Employment\${var:endpointName}_{#}.txt"
            archiveNumbering="DateAndSequence"
            archiveAboveSize="1000000"
            archiveDateFormat="yyyy-MM-dd"
            maxArchiveFiles="10">
      <layout xsi:type="CsvLayout" delimiter="Tab">
        <column name="Date" layout="${date}" />
        <column name="Level" layout="${level:uppercase=true}" />
        <column name="Machine" layout="${machinename}" />
        <column name="Identity" layout="${windows-identity}" />
        <column name="Process" layout="${processname}" />
        <column name="ProcessId" layout="${processid}" />
        <column name="ThreadId" layout="${threadid}" />
        <column name="Logger" layout="${logger}" />
        <column name="CallSite" layout="${callsite:className=true:includeNamespace=true:fileName=true:includeSourcePath=true:methodName=true:cleanNamesOfAnonymousDelegates=true:skipFrames=0}" />
        <column name="Line" layout="${callsite-linenumber:skipFrames=0}" />
        <column name="Message" layout="${message}" />
        <column name="StackTrace" layout="${stacktrace:topFrames=10}" />
        <column name="Exception" layout="${exception:format=toString,Data:maxInnerExceptionLevel=10}" />
      </layout>
    </target>
    <target name="database"
            xsi:type="Database"
            connectionString="Data Source=.;Initial catalog=dbNsbLogging;enlist=false;Integrated Security=SSPI;">
      <commandText>
        INSERT INTO [dbo].[${var:endpointName}] ([Date],[Level],[Machine],[Identity],[Process],[ProcessId],[ThreadId],[Logger],[CallSite],[Line],[Message],[StackTrace],[Exception])
        VALUES                                      (@Date, @Level, @Machine, @Identity, @Process, @ProcessId, @ThreadId, @Logger, @CallSite, @Line, @Message, @StackTrace, @Exception);
      </commandText>
      <parameter name="@Date" layout="${date}" />
      <parameter name="@Level" layout="${level:uppercase=true}" />
      <parameter name="@Machine" layout="${machinename}" />
      <parameter name="@Identity" layout="${windows-identity}" />
      <parameter name="@Process" layout="${processname}" />
      <parameter name="@ProcessId" layout="${processid}" />
      <parameter name="@ThreadId" layout="${threadid}" />
      <parameter name="@Logger" layout="${logger}" />
      <parameter name="@CallSite" layout="${callsite:className=true:includeNamespace=true:fileName=true:includeSourcePath=true:methodName=true:cleanNamesOfAnonymousDelegates=true:skipFrames=0}" />
      <parameter name="@Line" layout="${callsite-linenumber:skipFrames=0}" />
      <parameter name="@Message" layout="${message}" />
      <parameter name="@StackTrace" layout="${stacktrace:topFrames=10}" />
      <parameter name="@Exception" layout="${exception:format=toString,Data:maxInnerExceptionLevel=10}" />
      <install-command>
        <text>
          IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = N'${var:endpointName}')
          BEGIN
          CREATE TABLE [dbo].[${var:endpointName}](
          [Id] [int] IDENTITY(1,1) NOT NULL,
          [Date] [datetime] NOT NULL,
          [Level] [varchar](50) NOT NULL,
          [Machine] [varchar](255) NOT NULL,
          [Identity] [varchar](255) NOT NULL,
          [Process] [varchar](255) NOT NULL,
          [ProcessId] [varchar](50) NOT NULL,
          [ThreadId] [varchar](50) NOT NULL,
          [Logger] [varchar](255) NOT NULL,
          [CallSite] [varchar](1000) NOT NULL,
          [Line] [varchar](50) NOT NULL,
          [Message] [varchar](max) NOT NULL,
          [StackTrace] [varchar](max) NOT NULL,
          [Exception] [varchar](max) NULL
          ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
          END
        </text>
      </install-command>
    </target>
  </targets>
  <rules>
    <logger name="Jitb.*"
            minlevel="Debug"
            writeTo="console" />
    <logger name="Jitb.*"
            minlevel="Debug"
            writeTo="file" />
    <logger name="Jitb.*"
            minlevel="Debug"
            writeTo="database" />

    <logger name="NServiceBus*"
            minlevel="Info"
            writeTo="console" />
    <logger name="NServiceBus*"
            minlevel="Info"
            writeTo="file" />
    <logger name="NServiceBus*"
            minlevel="Info"
            writeTo="database" />

    <logger name="NHibernate.cfg*"
            minlevel="Off"
            writeTo="console" />
    <logger name="NHibernate.cfg*"
            minlevel="Off"
            writeTo="file" />
    <logger name="NHibernate.cfg*"
            minlevel="Off"
            writeTo="database" />

    <logger name="NHibernate.Dialect*"
            minlevel="Off"
            writeTo="console" />
    <logger name="NHibernate.Dialect*"
            minlevel="Off"
            writeTo="file" />
    <logger name="NHibernate.Dialect*"
            minlevel="Off"
            writeTo="database" />
    
    <logger name="NHibernate.SQL"
            minlevel="Debug"
            writeTo="console" />
    <logger name="NHibernate.SQL"
            minlevel="Debug"
            writeTo="file" />
    <logger name="NHibernate.SQL"
            minlevel="Debug"
            writeTo="database" />
  </rules>
</nlog>
