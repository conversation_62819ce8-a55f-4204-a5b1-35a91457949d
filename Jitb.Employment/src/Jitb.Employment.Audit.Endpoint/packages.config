﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Afterman.nRepo" version="2021.9.15.2" targetFramework="net461" />
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net461" />
  <package id="AutoMapper" version="8.1.1" targetFramework="net461" />
  <package id="BouncyCastle.OpenPgp" version="1.8.1.1" targetFramework="net461" />
  <package id="Castle.Core" version="5.2.1" targetFramework="net461" />
  <package id="FluentNHibernate" version="2.0.3.0" targetFramework="net461" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net461" />
  <package id="Jitb.Common.Contracts" version="2019.10.31.1" targetFramework="net461" />
  <package id="Jitb.CommonLibrary" version="2022.11.22.4" targetFramework="net461" />
  <package id="Jitb.NSB.Commons" version="2022.11.10.1" targetFramework="net461" />
  <package id="log4net" version="2.0.12" targetFramework="net461" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="3.1.3" targetFramework="net461" />
  <package id="Microsoft.Extensions.Primitives" version="3.1.3" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="12.0.1" targetFramework="net461" />
  <package id="NHibernate" version="4.0.4.4000" targetFramework="net461" />
  <package id="NLog" version="4.7.10" targetFramework="net461" />
  <package id="NServiceBus" version="7.4.0" targetFramework="net461" />
  <package id="NServiceBus.AmazonSQS" version="4.4.1" targetFramework="net461" />
  <package id="NServiceBus.Callbacks" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.CustomChecks" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.Encryption.MessageProperty" version="2.0.1" targetFramework="net461" />
  <package id="NServiceBus.Heartbeat" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.Host" version="8.0.0" targetFramework="net461" />
  <package id="NServiceBus.Log4Net" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.Metrics" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.Metrics.ServiceControl" version="3.0.2" targetFramework="net461" />
  <package id="NServiceBus.Metrics.ServiceControl.Msmq" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.Newtonsoft.Json" version="2.2.0" targetFramework="net461" />
  <package id="NServiceBus.NHibernate" version="8.0.1" targetFramework="net461" />
  <package id="NServiceBus.NLog" version="3.0.0" targetFramework="net461" />
  <package id="NServiceBus.Raw" version="3.2.1" targetFramework="net461" />
  <package id="NServiceBus.Router" version="3.9.0" targetFramework="net461" />
  <package id="NServiceBus.Router.Connector" version="3.9.1" targetFramework="net461" />
  <package id="NServiceBus.SagaAudit" version="3.0.1" targetFramework="net461" />
  <package id="NServiceBus.StructureMap" version="7.0.0" targetFramework="net461" />
  <package id="NServiceBus.Transport.Msmq" version="1.1.1" targetFramework="net461" />
  <package id="OctoPack" version="3.6.5" targetFramework="net461" developmentDependency="true" />
  <package id="Oracle.ManagedDataAccess" version="12.1.24160719" targetFramework="net461" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net461" />
  <package id="Remotion.Linq.EagerFetching" version="2.2.0" targetFramework="net461" />
  <package id="SSH.NET" version="2020.0.1" targetFramework="net461" />
  <package id="StructureMap" version="4.7.1" targetFramework="net461" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net461" />
  <package id="System.Data.SqlClient" version="4.4.3" targetFramework="net461" />
  <package id="System.Diagnostics.EventLog" version="4.7.0" targetFramework="net461" />
  <package id="System.Memory" version="4.5.4" targetFramework="net461" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net461" />
  <package id="System.Reflection.Emit" version="4.7.0" targetFramework="net461" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.1" targetFramework="net461" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net461" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net461" />
</packages>