﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <connectionStrings configSource="ConnectionStrings.aws.prod.config" xdt:Transform="Replace"/>

  <appSettings>
    <add key="env" value="prod"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EncryptionEnabled" value="true"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="ServiceControl/Queue" value="particular.servicecontrol@AW2NSBSCP01"
			 xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="auditQueue" value="audit@AW2NSBSCP01"
      xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="errorQueue" value="error@AW2NSBSCP01"
      xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	  <add key="MonitoringQueue" value="particular.monitoring@AW2NSBSCP01"
	  xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>
</configuration>