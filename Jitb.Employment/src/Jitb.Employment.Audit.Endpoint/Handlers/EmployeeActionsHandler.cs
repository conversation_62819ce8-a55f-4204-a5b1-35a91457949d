﻿using System;
using System.Threading.Tasks;

namespace Jitb.Employment.Audit.Endpoint.Handlers
{
    using Contracts.Events.Audit;
    using Contracts.Events.Employment;
    using Contracts.Models;
    using Domain.Integrations.Components.Ultipro.Interrogation;
    using Domain.Repositories;
    using Domain.Repositories.Employment;
    using NSB.Commons;
    using NServiceBus;
    using NServiceBus.Logging;

    //REINSTATE, TERMINATE, NEW NETWORK ID, NETWORK ID CHANGE, BEGIN LOA, PERINFOCHG
    public class EmployeeActionsHandler :
        IHandleMessages<IHiredAnEmployee>
        , IHandleMessages<IMadeAGenericUpdateToAnEmployee>
        , IHandleMessages<IBorrowedAnEmployeeEnded>
        , IHandleMessages<IBorrowedAnEmployee>
        , IHandleMessages<ITerminatedAnEmployee>
        , IHandleMessages<IStartedALeaveOfAbsence>
        , IHandleMessages<IEndedALeaveOfAbsence>
        , IHandleMessages<IChangedPersonnelInformation>
        , IHandleMessages<IAssignedABadgeId>
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(EmployeeActionsHandler));
        private readonly IEmployeeActionRepository _actionRepository;
        private readonly IEmployeeRepository _employeeRepository;
        private readonly ILocationRepository _locationRepository;

        public EmployeeActionsHandler(IEmployeeActionRepository actionRepository,
            IEmployeeRepository employeeRepository, ILocationRepository locationRepository)
        {
            this._actionRepository = actionRepository;
            this._employeeRepository = employeeRepository;
            this._locationRepository = locationRepository;
        }

        public async Task Handle(IHiredAnEmployee message, IMessageHandlerContext context)
        {
            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(employee.HomeLocation?.LocationNumber) &&
                employee.IsRestaurantWorker)
            {
                /*
                 * Send the message that creates the Active Directory transaction.
                 * Currently only process JITB restaurant employees, both corp and franchisee. 
                 * Check is performed here so it can easily be changed if we start processing Qdoba Also.
                 */

                var action = employee.GetActionForActionsHandler(sourceSystem
                    , string.Empty //description
                    , "100" //status
                    , employee.HomeLocation?.LocationNumber
                    , "ADD"); //action code

                _actionRepository.Add(action);
                await context.Publish<ICreatedANewEmployeeAction>(x => { x.ActionId = action.Id; });
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }

        public async Task Handle(IAssignedABadgeId message, IMessageHandlerContext context)
        {
            if (!message.IsNewFromLawson) return;

            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(employee.HomeLocation?.LocationNumber) &&
                employee.IsRestaurantWorker)
            {
                var action = employee.GetActionForActionsHandler(sourceSystem
                    , String.Empty //description
                    , "100" //status
                    , employee.HomeLocation?.LocationNumber
                    , "ADD"); //action code

                _actionRepository.Add(action);
                await context.Publish<ICreatedANewEmployeeAction>(x => { x.ActionId = action.Id; });
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }

        public async Task Handle(IMadeAGenericUpdateToAnEmployee message, IMessageHandlerContext context)
        {
            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(employee.HomeLocation?.LocationNumber) &&
                employee.IsRestaurantWorker)
            {
                if (message.ForcePersonInfoChange ||
                    message.OriginatingCommand.HasChangesOn(AvailableFieldNames.EmailAddress) ||
                    message.OriginatingCommand.HasChangesOn(AvailableFieldNames.FirstName) ||
                    message.OriginatingCommand.HasChangesOn(AvailableFieldNames.LastName) ||
                    message.OriginatingCommand.HasChangesOn(AvailableFieldNames.NickName) ||
                    message.OriginatingCommand.HasChangesOn(AvailableFieldNames.MiddleName) ||
                    message.OriginatingCommand.HasChangesOn(AvailableFieldNames.JobCode) ||
                    message.OriginatingCommand.HasChangesOn(AvailableFieldNames.SupervisorId)
                )
                {
                    /*
                     * Send the message that creates the Active Directory transaction.
                     * Currently only process JITB restaurant employees, both corp and franchisee. 
                     * Check is performed  here so it can easily be changed if we start processing Qdoba Also.
                     */

                    var updAction = employee.GetActionForActionsHandler(sourceSystem
                        , "PERINFOCHG" //description
                        , "100" //status
                        , employee.HomeLocation?.LocationNumber
                        , "ADD"); //action code
                    _actionRepository.Add(updAction);
                    await context.Publish<ICreatedANewEmployeeAction>(x => { x.ActionId = updAction.Id; });
                }
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }

        public async Task Handle(IBorrowedAnEmployeeEnded message, IMessageHandlerContext context)
        {
            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(employee.HomeLocation?.LocationNumber) &&
                employee.IsRestaurantWorker)
            {
                /*
                 * Send the message that creates the Active Directory transaction.
                 * Currently only process JITB restaurant employees, both corp and franchisee. 
                 * Check is performed here so it can easily be changed if we start processing Qdoba Also.
                 */

                var action = employee.GetActionForActionsHandler(sourceSystem
                    , String.Empty //description
                    , "100" //status
                    , message.BorrowingLocation
                    , "BORROWEND"); //action code

                _actionRepository.Add(action);
                await context.Publish<ICreatedANewEmployeeAction>(x => { x.ActionId = action.Id; });
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }

        public async Task Handle(IBorrowedAnEmployee message, IMessageHandlerContext context)
        {
            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(employee.HomeLocation?.LocationNumber) &&
                employee.IsRestaurantWorker)
            {
/*
                 * Send the message that creates the Active Directory transaction.
                 * Currently only process JITB restaurant employees, both corp and franchisee. 
                 * Check is performed here so it can easily be changed if we start processing Qdoba Also.
                 */

                var action = employee.GetActionForActionsHandler(sourceSystem
                    , string.Empty //description
                    , "100" //status
                    , message.BorrowingLocation
                    , "BORROWSTART"); //action code

                _actionRepository.Add(action);
                await context.Publish<ICreatedANewEmployeeAction>(x => { x.ActionId = action.Id; });
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }

        public async Task Handle(ITerminatedAnEmployee message, IMessageHandlerContext context)
        {
            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(message.Location) &&
                employee.IsRestaurantWorker)
            {
/*
                 * Send the message that creates the Active Directory transaction.
                 * Currently only process JITB restaurant employees, both corp and franchisee. 
                 * Check is performed here so it can easily be changed if we start processing Qdoba Also.
                 */

                var action = employee.GetActionForActionsHandler(sourceSystem
                    , "TERMINATE" //description
                    , "100" //status
                    , message.Location
                    , "DELETE"); //action code

                _actionRepository.Add(action);
                await context.Publish<ICreatedANewEmployeeAction>(x => { x.ActionId = action.Id; });
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }


        public async Task Handle(IStartedALeaveOfAbsence message, IMessageHandlerContext context)
        {
            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(employee.HomeLocation?.LocationNumber) &&
                employee.IsRestaurantWorker)
            {
                /*
                 * Send the message that creates the Active Directory transaction.
                 * Currently only process JITB restaurant employees, both corp and franchisee. 
                 * Done here so it can easily be changed if we start processing Qdoba Also.
                 */

                var action = employee.GetActionForActionsHandler(sourceSystem
                    , "BEGIN LOA"
                    , "100"
                    , employee.HomeLocation?.LocationNumber
                    , "DISABLE");

                _actionRepository.Add(action);
                await context.Publish<ICreatedANewEmployeeAction>(x => { x.ActionId = action.Id; });
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }


        public async Task Handle(IEndedALeaveOfAbsence message, IMessageHandlerContext context)
        {
            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(employee.HomeLocation?.LocationNumber) &&
                employee.IsRestaurantWorker)
            {
                /*
                 * Send the message that creates the Active Directory transaction.
                 * Currently only process JITB restaurant employees, both corp and franchisee. 
                 * Check is performed  here so it can easily be changed if we start processing Qdoba Also.
                 */

                var action = employee.GetActionForActionsHandler(sourceSystem
                    , "REINSTATE"
                    , "100"
                    , employee.HomeLocation?.LocationNumber
                    , "ENABLE");

                _actionRepository.Add(action);
                await context.Publish<ICreatedANewEmployeeAction>(x => { x.ActionId = action.Id; });
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }


        public async Task Handle(IChangedPersonnelInformation message, IMessageHandlerContext context)
        {
            var sourceSystem = context.SafeGetMessageHeader(CustomHeaders.SourceSystem);
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (_locationRepository.IsJackIntheBoxRestaurant(employee.HomeLocation?.LocationNumber) &&
                employee.IsRestaurantWorker)
            {
                /*
                 * Send the message that creates the Active Directory transaction.
                 * Currently only process JITB restaurant employees, both corp and franchisee. 
                 * Check is performed here so it can easily be changed if we start processing Qdoba Also.
                 */
                var UpdAction = employee.GetActionForActionsHandler(sourceSystem //source system
                    , "PERINFOCHG" //description
                    , "100" //status
                    , employee.HomeLocation?.LocationNumber //location number
                    , "ADD"); //action code
                _actionRepository.Add(UpdAction);
            }
            else
            {
                Log.Warn($"employee {message.EmployeeId} was deemed not to be an instore employee");
            }
            await Task.CompletedTask;
        }
    }
}