using NServiceBus;

namespace Jitb.Employment.Audit.Endpoint
{
    using Domain.Nsb;
    using Jitb.NSB.Commons.Extensions;
    using Jitb.NSB.Commons.Repository;
    using Jitb.NSB.Commons.RoutingBridge;

    /*
		This class configures this endpoint as a Server. More information about how to configure the NServiceBus host
		can be found here: http://particular.net/articles/the-nservicebus-host
	*/
    public class EndpointConfig : IConfigureThisEndpoint
    {
        public void Customize(EndpointConfiguration endpointConfiguration)
        {
            var provider = new MessageMappingProvider();
            var repository = new TransportRouteMapRepository();

            endpointConfiguration.DefaultJitbConfiguration();

            var transportRouteMaps = repository.GetTransportRouteMaps(DomainName.Employment);

            ConfigurationMappingRoutingBridge.ConfigureMappingsForRouting(provider, transportRouteMaps);
            RoutingBridgeConfiguration.ConfigureMappingTransport(endpointConfiguration);
        }
    }
}
