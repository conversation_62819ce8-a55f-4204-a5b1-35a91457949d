﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
	<connectionStrings configSource="ConnectionStrings.aws.dev.config" xdt:Transform="Replace"/>

	<appSettings>
		<add key="env" value="dev"
		     xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="ServiceControl/Queue" value="particular.servicecontrol"
		     xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="EncryptionEnabled" value ="true"
		     xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="auditQueue" value="audit"
		     xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="errorQueue" value="error"
		     xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="MonitoringQueue" value="particular.monitoring"
		     xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	</appSettings>
</configuration>