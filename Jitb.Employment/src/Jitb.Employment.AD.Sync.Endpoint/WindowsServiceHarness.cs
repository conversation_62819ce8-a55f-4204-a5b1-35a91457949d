namespace Jitb.Employment.AD.Sync.Endpoint
{
    using System;
    using System.ServiceProcess;
    using Framework;

    // A generic Windows Service that can handle any assembly that 
    // implements IWindowsService (including AbstractWindowsService)

    public sealed partial class WindowsServiceHarness : ServiceBase
    {
        // Get the class implementing the windows service
        public IWindowsService ServiceImplementation { get; private set; }

        // Constructor a generic windows service from the given class
        public WindowsServiceHarness(IWindowsService serviceImplementation)
        {
            // make sure service passed in is valid
            if (serviceImplementation == null)
            {
                throw new ArgumentNullException("serviceImplementation", "IWindowsService cannot be null in call to GenericWindowsService");
            }
            // set instance and backward instance
            this.ServiceImplementation = serviceImplementation;

            // configure our service
            this.ConfigureServiceFromAttributes(serviceImplementation);
        }

        // Pass disposal off to service disposal method
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                this.ServiceImplementation.Dispose();

                if (this._components != null)
                {
                    this._components.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        // Override service control on continue   
        protected override void OnContinue()
        {
            // perform class specific behavior
            this.ServiceImplementation.OnContinue();
        }

        // Called when service is paused
        protected override void OnPause()
        {
            // perform class specific behavior
            this.ServiceImplementation.OnPause();
        }

        // Called when a custom command is requested
        protected override void OnCustomCommand(int command)
        {
            // perform class specific behavior
            this.ServiceImplementation.OnCustomCommand(command);
        }

        // Called when the Operating System is shutting down
        protected override void OnShutdown()
        {
            // perform class specific behavior
            this.ServiceImplementation.OnShutdown();
        }

        // Called when service is requested to start
        protected override void OnStart(string[] args)
        {
            this.ServiceImplementation.OnStart(args);
        }

        // Called when service is requested to stop
        protected override void OnStop()
        {
            this.ServiceImplementation.OnStop();
        }

        // Set configuration data   
        private void ConfigureServiceFromAttributes(IWindowsService serviceImplementation)
        {
            var attribute = serviceImplementation.GetType().GetAttribute<WindowsServiceAttribute>();
            if (attribute != null)
            {
                this.EventLog.Source = string.IsNullOrEmpty(attribute.EventLogSource)
                        ? "WindowsServiceHarness"
                        : attribute.EventLogSource;

                this.CanStop = attribute.CanStop;
                this.CanPauseAndContinue = attribute.CanPauseAndContinue;
                this.CanShutdown = attribute.CanShutdown;

                // we don't handle: laptop power change event
                this.CanHandlePowerEvent = false;

                // we don't handle: Term Services session event
                this.CanHandleSessionChangeEvent = false;

                // always auto-event-log
                this.AutoLog = true;
            }
            else
            {
                throw new InvalidOperationException(string.Format("IWindowsService implementer {0} must have a WindowsServiceAttribute.",
                            serviceImplementation.GetType().FullName));
            }
        }
    }
}