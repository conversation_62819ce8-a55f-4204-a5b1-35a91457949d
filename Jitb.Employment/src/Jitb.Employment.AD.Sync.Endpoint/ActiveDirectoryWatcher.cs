﻿

namespace Jitb.Employment.AD.Sync.Endpoint
{
    using System;
    using System.DirectoryServices.Protocols;
    using System.Threading;
    using Contracts.Commands.ActiveDirectory;
    using NServiceBus.Logging;


    class ActiveDirectoryWatcher

    {
        private static ILog _log = LogManager.GetLogger(typeof(ActiveDirectoryWatcher));
        //       public static TextWriter writer = File.CreateText("C:\\temp\\ADTest3.txt");

        public void WatchForChanges_Jack()
        {
            using (LdapConnection connect = new LdapConnection("CORP.JITB.NET"))
            {
                using (var notifier = new ChangeNotifier(connect))
                {
                    //register some objects for notifications (limit 5)
                    //  notifier.Register("dc=dunnry,dc=net", SearchScope.OneLevel);

                    notifier.Register("OU=Users,OU=Managed Objects,DC=corp,DC=jitb,DC=net", SearchScope.Subtree);
                    notifier.ObjectChanged += new EventHandler<ObjectChangedEventArgs>(notifier_ObjectChanged);

                    _log.Info("Waiting for Jack changes...");
                    //                        writer.WriteLine("Waiting for Jack changes...");
                    //                       writer.Flush();
                    Thread.Sleep(Timeout.Infinite);
                    //   Console.WriteLine();
                    //   Console.ReadLine();
                }
            }
        }

        public void WatchForChanges_Qdoba()
        {
            using (LdapConnection connect = new LdapConnection("CORP.QDOBA"))
            {
                using (var notifier = new ChangeNotifier(connect))
                {
                    //register some objects for notifications (limit 5)
                    //  notifier.Register("dc=dunnry,dc=net", SearchScope.OneLevel);

                    notifier.Register("ou=_QDOBA,DC=corp,DC=qdoba", SearchScope.Subtree);
                    notifier.ObjectChanged += new EventHandler<ObjectChangedEventArgs>(notifier_ObjectChanged);

                    _log.Info("Waiting for Qdoba changes...");
                    //                   writer.WriteLine("Waiting for Qdoba changes...");
                    //                   writer.Flush();
                    Thread.Sleep(Timeout.Infinite);
                    //   Console.WriteLine();
                    //   Console.ReadLine();
                }
            }
        }



        public SearchResultEntry Result { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        static void notifier_ObjectChanged(object sender, ObjectChangedEventArgs e)
        {
            //long usnChanged = 0;
            string company = string.Empty;
            string eid = string.Empty;
            string email = string.Empty;
            string networkid = string.Empty;




            //  writer.WriteLine(e.Result.DistinguishedName);
            //   writer.Flush();
            /*
                            try
                            {
                                usnChanged = BitConverter.ToInt64((byte[])e.Result.Attributes["usnChanged"].GetValues(typeof(string)),1);
                            }
                            catch
                            {
                                usnChanged = 0;
                            }
                  */
            // if employee id is not available, throw an exception
            eid = (string) e.Result.Attributes["employeeid"].GetValues(typeof(string))[0];
            if (int.Parse(eid) < 0)
                throw new NullReferenceException();
            try
            {
                company = (string) e.Result.Attributes["company"].GetValues(typeof(string))[0];
            }
            catch
            {
                company = "0";
            }

            try
            {
                networkid = (string) e.Result.Attributes["samaccountname"].GetValues(typeof(string))[0];
            }
            catch
            {
                networkid = "";
            }

            try
            {
                email = (string) e.Result.Attributes["mail"].GetValues(typeof(string))[0];
            }
            catch
            {
                email = "";
            }

            _log.Info($@"Sending: company: {company}, employeeid: {eid}, networkid: {networkid}, email: {email}");
            //writer.WriteLine($@"company: {company}, employeeid: {eid}, networkid: {networkid}, email: {email}");
            //writer.Flush();
            try
            {
            //    ServiceBus._bus.Send<CheckIfADInfoChanged>(x =>
            //    {
            //        x.uSnChanged = 0;
            //        x.companyId = int.Parse(company);
            //        x.employeeNumber = long.Parse(eid);
            //        x.networkId = networkid;
            //        x.emailAddress = email;

              //  });
                _log.Info($@"Sent: company: {company}, employeeid: {eid}, networkid: {networkid}, email: {email}");
            }
            catch (Exception ex)
            {
                _log.Error($@"Error: company: {company}, employeeid: {eid}, networkid: {networkid}, email: {email}", ex);
            }


        }

    }
}


