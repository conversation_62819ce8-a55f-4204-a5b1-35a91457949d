namespace Jitb.Employment.AD.Sync.Endpoint.Framework
{
    using System;
    using System.Collections;
    using System.ComponentModel;
    using System.Configuration.Install;
    using System.Linq;
    using System.Reflection;
    using System.ServiceProcess;
    using Endpoint;

    // A generic windows service installer
    [RunInstaller(true)]
    public partial class WindowsServiceInstaller : Installer
    {
        // Gets or sets the type of the windows service to install.
        public WindowsServiceAttribute Configuration { get; set; }

        // Creates a blank windows service installer with configuration in ServiceImplementation
        public WindowsServiceInstaller()
            : this(typeof(ServiceImplementation))
        { }

        // Creates a windows service installer using the type specified.
        public WindowsServiceInstaller(Type windowsServiceType)
        {
            if (!windowsServiceType.GetInterfaces().Contains(typeof(IWindowsService)))
            {
                throw new ArgumentException("Type to install must implement IWindowsService.",
                    "windowsServiceType");
            }

            var attribute = windowsServiceType.GetAttribute<WindowsServiceAttribute>();
            if (attribute == null)
            {
                throw new ArgumentException("Type to install must be marked with a WindowsServiceAttribute.", "windowsServiceType");
            }
            this.Configuration = attribute;
        }

        // Performs a transacted installation at run-time of the AutoCounterInstaller and any other listed installers. 
        public static void RuntimeInstall<T>()
            where T : IWindowsService
        {
            string path = "/assemblypath=" + Assembly.GetEntryAssembly().Location;
            using (var ti = new TransactedInstaller())
            {
                ti.Installers.Add(new WindowsServiceInstaller(typeof(T)));
                ti.Context = new InstallContext(null, new[] { path });
                ti.Install(new Hashtable());
            }
        }

        // Performs a transacted un-installation at run-time of the AutoCounterInstaller and any other listed installers.
        public static void RuntimeUnInstall<T>(params Installer[] otherInstallers)
           where T : IWindowsService
        {
            string path = "/assemblypath=" + Assembly.GetEntryAssembly().Location;
            using (var ti = new TransactedInstaller())
            {
                ti.Installers.Add(new WindowsServiceInstaller(typeof(T)));
                ti.Context = new InstallContext(null, new[] { path });
                ti.Uninstall(null);
            }
        }

        // Installer class, to use run InstallUtil against this .exe
        public override void Install(System.Collections.IDictionary savedState)
        {
            ConsoleHarness.WriteToConsole(ConsoleColor.White, "Installing service {0}.", this.Configuration.Name);
            // install the service
            this.ConfigureInstallers();
            base.Install(savedState);
        }

        // Removes the counters, then calls the base uninstall.
        public override void Uninstall(System.Collections.IDictionary savedState)
        {
            ConsoleHarness.WriteToConsole(ConsoleColor.White, "Un-Installing service {0}.", this.Configuration.Name);

            // load the assembly file name and the config
            this.ConfigureInstallers();
            base.Uninstall(savedState);
        }

        // Method to configure the installers
        private void ConfigureInstallers()
        {
            // load the assembly file name and the config
            Installers.Add(this.ConfigureProcessInstaller());
            Installers.Add(this.ConfigureServiceInstaller());
        }

        // Helper method to configure a process installer for this windows service 
        private ServiceProcessInstaller ConfigureProcessInstaller()
        {
            var result = new ServiceProcessInstaller();

            // if a user name is not provided, will run under local service acct 
            if (string.IsNullOrEmpty(this.Configuration.UserName))
            {
                result.Account = ServiceAccount.LocalService;
                result.Username = null;
                result.Password = null;
            }
            else
            {
                // otherwise, runs under the specified user authority
                result.Account = ServiceAccount.User;
                result.Username = this.Configuration.UserName;
                result.Password = this.Configuration.Password;
            }

            return result;
        }

        // Helper method to configure a service installer for this windows service
        private ServiceInstaller ConfigureServiceInstaller()
        {
            // create and config a service installer
            var result = new ServiceInstaller
            {
                ServiceName = this.Configuration.Name,
                DisplayName = this.Configuration.DisplayName,
                Description = this.Configuration.Description,
                StartType = this.Configuration.StartMode,
            };
            return result;
        }
    }
}
