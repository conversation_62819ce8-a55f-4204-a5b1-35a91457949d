﻿using System;

namespace Jitb.Employment.AD.Sync.Endpoint
{
    using Domain;
    using Domain.Nsb;
    using NServiceBus;
    using NServiceBus.Features;

    public class ServiceBus : IServiceBus
    {
        public static ServiceBus _bus = new ServiceBus();

        private IEndpointInstance _endpointInstance;

        public ServiceBus()
        {
            this.Init();
        }

        public void Send<T>(Action<T> command)
        {
            //var options = new SendOptions();
            //options.SetDestination(ConfigurationManager.AppSettings["CoreEndpointAddress"]);

            this._endpointInstance.Send(command);//, options);
        }

        private void Init()
        {
            var configuration =
                new EndpointConfiguration(ConfigurationContext.SendonlyEndpointAddress);
            //configuration.SendFailedMessagesTo(ConfigurationManager.AppSettings["SendonlyEndpointErrorQueue"]);
            configuration.SendOnly();
            configuration.DisableFeature<MessageDrivenSubscriptions>();

            //configuration.Conventions()
            //    .DefiningCommandsAs(x => x.Namespace != null && x.Namespace.Contains(".Contracts.Commands"));
            //configuration.Conventions()
            //    .DefiningEventsAs(x => x.Namespace != null && x.Namespace.Contains(".Contracts.Events"));
            //configuration.Conventions()
            //    .DefiningMessagesAs(x => x.Namespace != null && x.Namespace.Contains(".Contracts.Messages"));
            configuration.DefaultJitbConfiguration();
            configuration.ConfigureMappings();
            //var transport = configuration.UseTransport<MsmqTransport>();
            //var routing = transport.Routing();
            //var instanceMappingFile = routing.InstanceMappingFile();
            //var environment = String.IsNullOrEmpty(ConfigurationManager.AppSettings["env"])
            //    ? String.Empty
            //    : $"-{ConfigurationManager.AppSettings["env"]}";
            //instanceMappingFile.FilePath($"instance-mapping{environment}.xml");
            //ConfigureRouteForEntireNamespace<CheckIfADInfoChanged>(routing,
            //    ConfigurationManager.AppSettings["SendonlyEndpointAddress"]);
            this._endpointInstance = Endpoint.Create(configuration).Result.Start().Result;
        }

        //private static void ConfigureRouteForEntireNamespace<T>(RoutingSettings<MsmqTransport> routing,
        //    string endpointName)
        //{
        //    routing.RouteToEndpoint(typeof(T).Assembly, typeof(T).Namespace, endpointName);
        //    var ns = typeof(T).Namespace;
        //    if (null == ns) return;
        //    if (ns.Contains(".Contracts.Events"))
        //        routing.RegisterPublisher(typeof(T).Assembly, ns, endpointName);
        //}
    }

}
