﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <connectionStrings>
    <add name="NServiceBus/Persistence" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNServiceBus;integrated security=false;user id=***********;password=***********;"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    <add name="Default" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNServiceBus;integrated security=false;user id=***********;password=***********;"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
  </connectionStrings>
  <appSettings>
    <add key="env" value="qa" 
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SendonlyEndpointAddress" value="Jitb.Employment.Ad.Sync.Endpoint" 
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="ServiceControl/Queue" value="particular.servicecontrol@CSNSBT01V"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EncryptionEnabled" value ="true"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>
  <RijndaelEncryptionServiceConfig Key="tWIrVnPy3Smd2r/xcSb9r/1wcVY9/3KWbrzIaemx5yw="
                                 KeyIdentifier="2015-10"
                                 KeyFormat="Base64">
  </RijndaelEncryptionServiceConfig>
  <MessageForwardingInCaseOfFaultConfig ErrorQueue="error@CSNSBT01V"
               xdt:Transform="SetAttributes" />
  <AuditConfig QueueName="audit@CSNSBT01V" 
               xdt:Transform="SetAttributes"/>
</configuration>