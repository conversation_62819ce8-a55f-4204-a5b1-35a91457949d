namespace Jitb.Employment.AD.Sync.Endpoint
{
    using System.ServiceProcess;
    using System.Threading;
    using Framework;

    [WindowsService("Jitb.Employment.AD.Endpoint",
    DisplayName = "Jitb.Employment.AD.Endpoint",
    Description = "Watch for Active Directory Changes",
    EventLogSource = "Jitb.Employment.AD.Endpoint",
    StartMode = ServiceStartMode.Automatic)]

    public class ServiceImplementation : IWindowsService
    {
     
        // This method is called when the service gets a request to start.    
        public void OnStart(string[] args) 
        {
            
            ActiveDirectoryWatcher watcherJack = new ActiveDirectoryWatcher();
            Thread corpWatcherThreadJack = new Thread(new ThreadStart(watcherJack.WatchForChanges_Jack));
            corpWatcherThreadJack.Start();

            ActiveDirectoryWatcher watcherQdoba = new ActiveDirectoryWatcher();
            Thread corpWatcherThreadQdoba = new Thread(new ThreadStart(watcherQdoba.WatchForChanges_Qdoba));
            corpWatcherThreadQdoba.Start();

   //         Thread.Sleep(Timeout.Infinite);

        }

        // This method is called when the service gets a request to stop.   
        public void OnStop() { }

        // This method is called when a service gets a request to pause,    
        // but not stop completely.   
        public void OnPause() { }

        // This method is called when a service gets a request to resume
        public void OnContinue() { }

        // This method is called when the machine the service is running on   
        public void OnShutdown() { }

        // dispose any resources 
        public void Dispose() { }

        public void OnCustomCommand(int cmd) { }

 

        public static void TraceService(string content)
        {
            /*
            //set up a filestream
            FileStream fs = new FileStream(@"c:\TempService\ScheduledService.txt", FileMode.OpenOrCreate, FileAccess.Write);

            //set up a streamwriter for adding text
            StreamWriter sw = new StreamWriter(fs);

            //find the end of the underlying filestream
            sw.BaseStream.Seek(0, SeekOrigin.End);

            //add the text
            sw.WriteLine(content);

            //add the text to the underlying filestream
            sw.Flush();

            //close the writer
            sw.Close();
            */
        }

            
    }
}
