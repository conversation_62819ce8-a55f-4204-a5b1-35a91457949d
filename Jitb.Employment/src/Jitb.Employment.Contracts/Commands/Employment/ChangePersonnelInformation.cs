﻿namespace Jitb.Employment.Contracts.Commands.Employment
{
    using System;

    public class ChangePersonnelInformation
    {
        public long EmployeeId { get; set; }
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public DateTime? AdjustedHireDate { get; set; }
        public DateTime? BirthDate { get; set; }
        public string Ssn { get; set; }
        public DateTime? HireDate { get; set; }
        public string CostCenter { get; set; }
        public string AdditionalData { get; set; }

    }
}
