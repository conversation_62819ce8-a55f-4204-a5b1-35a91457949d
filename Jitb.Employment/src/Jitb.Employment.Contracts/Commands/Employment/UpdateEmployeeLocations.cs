﻿using System.Collections.Generic;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    using Models;

    public class UpdateEmployeeLocations
    {
        public UpdateEmployeeLocations()
        {
            this.LocationUpdates = new List<LocationUpdate>();
        }
        public List<LocationUpdate> LocationUpdates { get; set; }

        public long EmployeeNumber { get; set; }

        public long EmployeeId { get; set; }
    }
}
