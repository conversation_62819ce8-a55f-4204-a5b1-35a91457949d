﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    public class CreateNewHire
    {
        public CreateNewHire()
        {
            RequestId = Guid.NewGuid().ToString();
        }

        public long? AuditKey { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public DateTime? AdjustedHireDate { get; set; }
        public bool SkipGenerateBadgeId { get; set; }
        public int BadgeId { get; set; }
        public DateTime BirthDate { get; set; }
        public string CellPhoneNumber { get; set; }
        public string City { get; set; }
        public string ContactEmailAddress { get; set; }
        public string AlternateEmailAddress { get; set; }
        public string ContactPhoneNumber { get; set; }
        public string CostCenter { get; set; }
        public string DomainName { get; set; }
        public long EmployeeId { get; set; }
        public string EmployeeType { get; set; }
        public bool SkipStatusReset { get; set; }
        public string EmploymentStatusCode { get; set; }
        public string eRestaurantEmployeeId { get; set; }
        public string eRestaurantSupervisorId { get; set; }
        public string eRestaurantPayrollId { get; set; }
        public string ExemptStatus { get; set; }
        public string FimEmailPreferenceCode { get; set; }
        public string FirstName { get; set; }
        public string Gender { get; set; }
        public DateTime HireDate { get; set; }
        public bool IsPendingHire { get; set; }
        public bool IsRehire { get; set; }
        public bool IsTraining { get; set; }
        public string JobClass { get; set; }
        public string JobCode { get; set; }
        public DateTime? JobCodeEffectiveDate { get; set; }
        public string JobCodeDescription { get; set; }
        public string LastName { get; set; }
        public int? LawsonCompanyId { get; set; }
        public string LawsonCostCenter { get; set; }
        public string LawsonDepartmentCode { get; set; }
        public int? LawsonEmployeeId { get; set; }
        public string LawsonEmployeeStatus { get; set; }
        public string LawsonProcessLevelCode { get; set; }
        public long? LawsonSuperKeyId { get; set; }
        public string LawsonSupervisorCode { get; set; }
        public int? LawsonSupervisorId { get; set; }
        public int LocationNumber { get; set; }
        public int? AltLocationNumber { get; set; }
        public string MiddleName { get; set; }
        public string Nickname { get; set; }
        public string NetworkId { get; set; }
        public string PayGrade { get; set; }
        public decimal PayRate { get; set; }
        public string RequestId { get; set; }
        public string SalaryClass { get; set; }
        public bool SendReplyWhenComplete { get; set; }
        public string Ssn { get; set; }
        public string State { get; set; }
        public string UltiproCompanyBrand { get; set; }
        public string UltiproCompanyId { get; set; }
        public string UltiproDepartment { get; set; }
        public string UltiproDepartmentCode { get; set; }
        public string UltiproEmployeeIdentityId { get; set; }
        public long? UltiproEmployeeNumber { get; set; }
        public long? UltiproSupervisorId { get; set; }
        public string UpdateSource { get; set; }
        public string WorkPhoneNumber { get; set; }
        public string Zip { get; set; }
        public bool IsEligibleForRehire { get; set; }

        public bool ErrorIfRehiringActiveEmployee { get; set; }
        public DateTime? LocationStartDate { get; set; }
        public bool SendReply { get; set; }
        public Guid? HarriTenantId { get; set; }
    }
}