﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    public class FinishOnboarding
    {
        public string AdditionalData { get; set; }
        public DateTime? AdjustedHireDate { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public DateTime BirthDate { get; set; }
        public string CellPhoneNumber { get; set; }
        public string City { get; set; }
        public string ContactEmailAddress { get; set; }
        public string ContactPhoneNumber { get; set; }
        public string CostCenter { get; set; }
        public string DomainName { get; set; }
        public string ExemptStatus { get; set; }
        public string EmploymentStatusCode { get; set; }
        public string FimEmailPreferenceCode { get; set; }
        public string Gender { get; set; }
        public bool IsTraining { get; set; }
        public string JobClass { get; set; }
        public string JobCode { get; set; }
        public string JobCodeDescription { get; set; }
        public int? LawsonCompanyId { get; set; }
        public string LawsonCostCenter { get; set; }
        public string LawsonDepartmentCode { get; set; }
        public int? LawsonEmployeeId { get; set; }
        public string LawsonProcessLevelCode { get; set; }
        public long? LawsonSuperKeyId { get; set; }
        public string LawsonSupervisorCode { get; set; }
        public int? LawsonSupervisorId { get; set; }
        public int LocationNumber { get; set; }
        public string PayGrade { get; set; }
        public decimal PayRate { get; set; }
        public string SalaryClass { get; set; }
        public string Ssn { get; set; }
        public string State { get; set; }
        public string UltiproDepartmentCode { get; set; }
        public string UpdateSource { get; set; }
        public string WorkPhoneNumber { get; set; }
        public string Zip { get; set; }

        public bool SendReply { get; set; }
    }
}
