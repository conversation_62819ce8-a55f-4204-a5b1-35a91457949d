﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    public class StartLeaveOfAbsence
    {
        public long EmployeeId { get; set; }

        public DateTime LeaveOfAbsenceBeginDate { get; set; }
        public string LeaveOfAbsenceComment { get; set; }
        public DateTime? LeaveOfAbsenceEndDate { get; set; }
        public DateTime? LeaveOfAbsenceInjuryDate { get; set; }
        public DateTime LeaveOfAbsenceLastWorkedDate { get; set; }
        public DateTime? LeaveOfAbsenceReturnedToWorkDate { get; set; }
        public string AdditionalData { get; set; }

        public bool SendReply { get; set; }
    }
}