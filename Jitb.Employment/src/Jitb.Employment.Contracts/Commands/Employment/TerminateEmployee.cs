﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    public class TerminateEmployee
    {
        public long EmployeeId { get; set; }

        public string TerminationAction { get; set; }
        public string TerminationComment { get; set; }
        public DateTime TerminationDate { get; set; }
        public virtual bool? TerminationEligibleForRehire { get; set; }
        public virtual DateTime TerminationLastWorkedDate { get; set; }
        public string TerminationReason { get; set; }
        public string AdditionalData { get; set; }

        public bool? SendToErestaurant { get; set; }
        public bool SendReply { get; set; }
    }
}