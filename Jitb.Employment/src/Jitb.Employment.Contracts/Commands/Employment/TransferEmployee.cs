﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    using Messages;

    public class TransferEmployee : IAmBoundByExpectations
    {
        public long EmployeeId { get; set; }
        public int TransferFromStoreNumber { get; set; }
        public DateTime TransferInDate { get; set; }
        public int TransferToStoreNumber { get; set; }
        public string AdditionalData { get; set; }
        public string FimEmailPreferenceCode { get; set; }
        public long? ExpectationId { get; set; }

        public bool LeaveActiveInFromStore { get; set; }
        public bool SendReply { get; set; }
    }
}