﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    using Messages;
    public class EndBorrowEmployee : IAmBoundByExpectations
    {
        public long EmployeeId { get; set; }
        public DateTime EndDate { get; set; }
        public int BorrowToStoreNumber { get; set; }
        public string AdditionalData { get; set; }

        public long? ExpectationId { get; set; }
    }
}
