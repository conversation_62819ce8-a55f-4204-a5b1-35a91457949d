﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment.Reply
{
    public class StartLeaveOfAbsenceReply
    {
        public long? EntityId { get; set; }
        public long? EmployeeNumber { get; set; }
        public string SuperKeyId { get; set; }
        //public string LocationNumber { get; set; }
        public string EmploymentStatusCode { get; set; }
        public string Ssn { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Phone { get; set; }
        public string JobCode { get; set; }
        public string BorrowedCode { get; set; }
        public decimal PayRate { get; set; }
        public DateTime? PayEffectiveDate { get; set; }
        public DateTime? BirthDate { get; set; }
        public DateTime? HireDate { get; set; }
        public DateTime? AdjustedHireDate { get; set; }
        //public string Points { get; set; }
        //public string Score { get; set; }
        public DateTime? JobCodeEffectiveDate { get; set; }
        public int BadgeId { get; set; }
        public string Sex { get; set; }
        //public string PayCardNumber { get; set; }
        public string RaceCode { get; set; }
        //public DateTime TransferInDate { get; set; }
        //public DateTime TerminationDate { get; set; }
        //public DateTime TerminationDatePervious { get; set; }
        //public DateTime WorkExperienceDate { get; set; }
        //public DateTime I9ExpirationDate { get; set; }
        //public string MaidenName { get; set; }
        //public string Citizen { get; set; }
        //public string Permanent { get; set; }
        //public string Alien { get; set; }
        //public string Admission { get; set; }
        //public string Doc1Number { get; set; }
        //public DateTime Doc1ExpirationDate { get; set; }
        //public string Doc2Number { get; set; }
        //public DateTime Doc2ExpirationDate { get; set; }
        //public string AlienNumber { get; set; }
        //public DateTime AlienExpirationDate { get; set; }
        //public string IssueAuth1 { get; set; }
        //public string IssueAuth2 { get; set; }
        //public string I9ADescription { get; set; }
        //public string I9BDescription { get; set; }
        //public string I9CDescription { get; set; }
        //public string LOAType { get; set; }
        public int EligibleForTrainingCode { get; set; }
        public DateTime? LOABeginDate { get; set; }
        public DateTime? LOAEndDate { get; set; }
    }
}