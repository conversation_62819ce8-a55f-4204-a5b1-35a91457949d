﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment.Reply
{
    public class CreateNewHireReply
    {
        //* EMP-COMPANY = (Company=LawsonCompanyId) > 2353
        public long? EntityId { get; set; }
        //* EMP-EMPLOYEE = (EmployeeNumber) > LawsonEmployeeID = 3876
        public long? EmployeeNumber { get; set; }
        public string SuperKeyId { get; set; }
        //* EMP-FICA-NBR = &EMP-FICA-NBR(FICANumber) > Ssn = ***********
        public string Ssn { get; set; }
        //* PEM-SECURITY-NBR = (BadgeNumber) - Is this created by <PERSON>? > 0000817570 *** Does the Lawson webservice create the badgeId?***
        public int BadgeId { get; set; }
        //* EMP-EMP-STATUS = (StatusCode) > 06
        public string EmploymentStatusCode { get; set; }
        //* RETURN - CODE(ApplicationReturnValue) Success or Failure > 1
        //* EMP-DATE-HIRED = &EMP - DATE - HIRED(HiredDate) > 20180606
        public DateTime? HireDate { get; set; }
        //* EMP - ADJ - HIRE - DATE = (HiredDateOriginal) > 20180606
        public DateTime? AdjustedHireDate { get; set; }
        //* EMP - TERM - DATE = (TerminationDate) > empty
        public DateTime? TerminationDate { get; set; }
    }
}