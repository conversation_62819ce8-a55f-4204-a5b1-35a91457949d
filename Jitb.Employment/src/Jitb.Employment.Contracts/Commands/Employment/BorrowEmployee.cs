﻿using System;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    using Messages;
    public class BorrowEmployee : IAmBoundByExpectations
    {
        public long EmployeeId { get; set; }
        public DateTime BeginDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int BorrowToStoreNumber { get; set; }

        public string AdditionalData { get; set; }

        public long? ExpectationId { get; set; }
        public bool SendReply { get; set; }
    }
}
