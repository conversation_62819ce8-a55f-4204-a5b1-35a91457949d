﻿using Jitb.Common.Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    public class ChangeJobCodeDefinition : IAmNotAudited
    {
        public string JobFamily { get; set; }

        public string JobClass { get; set; }

        public string JobCode { get; set; }

        public string JobTitle { get; set; }

        public string StatusCode { get; set; }

        public DateTime? StatusAsOfDate { get; set; }
    }
}
