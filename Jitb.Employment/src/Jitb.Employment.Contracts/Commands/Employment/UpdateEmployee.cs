﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    public class UpdateEmployee
    {
        public UpdateEmployee()
        {
            Changes = new List<EmployeeChange>();
        }

        public bool IsFromErestaurant => String.IsNullOrEmpty(eRestaurantEmployeeId) == false;

        public bool IsFromUltiPro => String.IsNullOrEmpty(UltiProEmployeeId) == false;
        public bool IsFromLawson => String.IsNullOrEmpty(LawsonEmployeeId) == false;

        public string UltiProEmployeeId { get; set; }

        public string eRestaurantEmployeeId { get; set; }

        public string LawsonEmployeeId { get; set; }

        public long EmployeeId { get; set; }

        public List<EmployeeChange> Changes { get; set; }

        public DateTime Date { get; set; }

        public bool HasChangesOn(string fieldName)
        {
            foreach (var change in Changes)
            {
                if(String.Equals(change.FieldName, fieldName, StringComparison.InvariantCultureIgnoreCase))
                    return true;
            }
            return false;
        }

        public bool HasChangesOn(List<string> fieldNames)
        {
            return
            (from fieldName in fieldNames
                from change in this.Changes
                where fieldName == change.FieldName
                select fieldName).Any();
        }

        public EmployeeChange GetChangeOn(string fieldName)
        {
            var changes = this.Changes.FirstOrDefault(x => String.Equals(x.FieldName, fieldName, StringComparison.CurrentCultureIgnoreCase));

            return changes ?? new EmployeeChange();
        }

        public bool HasChangesOnWithValue(string fieldName, string aStringValue)
        {
            return this.Changes.Any(x => String.Equals(x.FieldName, fieldName, StringComparison.CurrentCultureIgnoreCase) && String.Equals(x.Value, aStringValue, StringComparison.CurrentCultureIgnoreCase));
        }

        public string AdditionalData { get; set; }
        public bool AccompaniesFirstClassAction { get; set; }
        public string AccomanyingActions { get; set; }
    }

    public class EmployeeChange
    {
        public string FieldName { get; set; }

        public string Value { get; set; }
        public string OldValue { get; set; }
        public DateTime Date { get; set; }
        public long? AuditKey { get; set; }

        protected bool IsInvalid;

        public EmployeeChange Was(object value)
        {
            var passedCheck = Object.Equals(value, OldValue);
            this.IsInvalid = this.IsInvalid || !passedCheck;
            return this;
        }

        public EmployeeChange Is(object value)
        {
            var passedCheck = Object.Equals(value, Value);
            this.IsInvalid = this.IsInvalid || !passedCheck;
            return this;
        }

        public bool IsValid()
        {
            var isValid = !this.IsInvalid;
            this.Reset();
            return isValid;
        }

        public void Reset()
        {
            this.IsInvalid = false;
        }

        public EmployeeChange WasNotSet()
        {
            this.IsInvalid = this.IsInvalid && (OldValue == null || OldValue.ToString().Trim() == String.Empty);
            return this;
        }

        public EmployeeChange IsSet()
        {
            this.IsInvalid = this.IsInvalid || this.Value == null || this.Value.ToString().Trim().Length == 0;
            return this;
        }

        public EmployeeChange IsNotSet()
        {
            this.IsInvalid = this.IsInvalid && (Value == null || Value.ToString().Trim() == String.Empty);
            return this;
        }

        public T ValueAs<T>()
        {
            return Value.ConvertValue<T>();
        }

        public T OldValueAs<T>()
        {
            if (null == OldValue) return default(T);
            var type = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
            return (T) Convert.ChangeType(OldValue, type);
        }

        

        
    }
}
