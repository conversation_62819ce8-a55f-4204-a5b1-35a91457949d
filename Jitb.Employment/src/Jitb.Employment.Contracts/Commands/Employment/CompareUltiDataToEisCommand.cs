﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Jitb.Common.Contracts;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    public class CompareUltiDataToEisCommand : IAmNotAudited
    {
        public long RunId { get; set; }

        public long HrEmployeeNumber { get; set; }

        public string EmployeeFullName { get; set; }

        public int CompensationAuditKey { get; set; }

        public int AuditKey { get; set; }

        public string UltiProDepartmentCode { get; set; }

        public string UltiProEmployeeId { get; set; }

        public string CompanyBrand { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Nickname { get; set; }

        public string WorkPhone { get; set; }

        public string HomePhone { get; set; }

        public bool IsHomePhonePrivate { get; set; }

        public string UltiProSupervisorId { get; set; }

        public string JobCode { get; set; }

        public string Gender { get; set; }

        public string Ssn { get; set; }

        public string SalaryClass { get; set; }

        public DateTime? LastHireDate { get; set; }

        public DateTime OriginalHireDate { get; set; }

        public string EmployeeStatus { get; set; }

        public decimal PayRate { get; set; }

        public string FimEmailPreferenceCode { get; set; }

        public bool IsOkToRehire { get; set; }

        public DateTime? TerminationDate { get; set; }

        public long EmployeeId { get; set; }

        public long UltiProToEisDataComparisonCheckId { get; set; }

        public string EmailAddress { get; set; }

        public string AlternateEmailAddress { get; set; }
    }
}
