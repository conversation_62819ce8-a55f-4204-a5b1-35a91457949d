﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jitb.Employment.Contracts.Commands.Employment
{
    public class RehireEmployee
    {
        public long EmployeeId { get; set; }
        public DateTime RehireDate { get; set; }
        public int LocationNumber { get; set; }
        public string JobCode { get; set; }
        public decimal? PayRate { get; set; }
        public bool? IsSalary { get; set; }
        public int? EntityId { get; set; }
    }
}
