﻿namespace Jitb.Employment.Contracts.Commands.CoreLifecycle
{

    public class PassThruHire : IStartAConversation
    {
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string StateCode { get; set; }
        public string ZipCode { get; set; }
        public string HomePhoneNumber { get; set; }
        public string LocationCode { get; set; }
        public string Ssn { get; set; }
        public string JobCode { get; set; }
        public string RaceCode { get; set; }
        public string GenderCode { get; set; }
        public string BirthDate { get; set; }
        public string HiredDate { get; set; }
        public decimal PayRate { get; set; }
        public string PayRateType { get; set; }
        public string SalaryClass { get; set; }
        public string FederalW4ExemptStatus { get; set; }
        public string FederalW4Status { get; set; }
        public string FederalW4ExemptAmount { get; set; }
        public string FederalW4AdditionalAmount { get; set; }
        public string StateW4ExemptStatus { get; set; }
        public string StateW4Status { get; set; }
        public string StateW4ExemptAmount { get; set; }
        public string StateW4AdditionalAmount { get; set; }
        public string DirectDepositAccount { get; set; }
        public string DirectDepositRouting { get; set; }
        public string PayCardNumber { get; set; }
        public string LocationCodeAlternate { get; set; }
        public string EmergencyContactFirstName { get; set; }
        public string EmergencyContactLastName { get; set; }
        public string EmergencyContactHomePhone { get; set; }
        public string EmergencyContactCellPhone { get; set; }
        public string FimEmailPreference { get; set; }
        public string PersonalEmail { get; set; }
    }
}
