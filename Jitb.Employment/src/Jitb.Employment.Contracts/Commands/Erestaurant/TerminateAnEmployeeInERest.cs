﻿using Jitb.Employment.Contracts.Events.Employment;
using System;
using System.Collections.Generic;

namespace Jitb.Employment.Contracts.Commands.Erestaurant
{
    public class TerminateAnEmployeeInERest : ITerminatedAnEmployee
    {
        public long EmployeeId { get; set; }
        public DateTime EffectiveDate { get; set; }
        public int Location { get; set; }
        public bool? SendToERestaurant { get; set; }
        public DateTime DateSent { get; set; }
        public int? EntityId { get; set; }
        public string Concept { get; set; }
        public string JobCode { get; set; }
        public string Reason { get; set; }
        public List<int> Locations { get; set; }
    }
}
