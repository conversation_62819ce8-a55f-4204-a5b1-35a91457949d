﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Jitb.Common.Contracts;


namespace Jitb.Employment.Contracts.Commands.ActiveDirectory
{
    public class CheckIfAdInfoChanged : IAmNotAudited
    {

        // note:  these properties should match the name of the corresponding property in 
        // Persona.  There is a routing that compares the properties by property name.
        public virtual long PersonaId { get; set; }
        public virtual Guid AdGuid { get; set; }
        public virtual String AdDomain { get; set; }
        public virtual string AdDistinguishedName { get; set; }
        public virtual string AdFirstName { get; set; }
        public virtual string AdLastName { get; set; }
        public virtual string AdEmail { get; set; }
        public virtual string AdEmployeeId { get; set; }
        public virtual string AdNetworkId { get; set; }
        public virtual DateTime? AdAccountExpires { get; set; }
        public virtual string AdCompany { get; set; }
        public virtual string AdDepartment { get; set; }
        public virtual string AdOffice { get; set; }
        public virtual string AdUserPrincipalName { get; set; }
        public virtual bool? IsEnabled { get; set; }
        public virtual DateTime? AdWhenChanged { get; set; }
        public virtual long? AdUsnChanged { get; set; }
        public virtual bool? IsInActiveDirectory { get; set; }
        public virtual string AdSid { get; set; }
    }
}
