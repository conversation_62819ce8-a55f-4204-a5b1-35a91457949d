﻿<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <targets async="true">
    <target name="database"
            xsi:type="Database"
            connectionString="data source=dbnsblogging.prod-entsys.aws.jitb.net;initial catalog=dbNsbLogging;enlist=false;integrated security=SSPI;"
            xdt:Transform="SetAttributes" xdt:Locator="Match(name)">
    </target>
  </targets>
  <rules>
    <logger name="Jitb.*" minlevel="Info" writeTo="console"
            xdt:Transform="SetAttributes" xdt:Locator="Match(name,writeTo)"/>
    <logger name="Jitb.*" minlevel="Info" writeTo="file"
            xdt:Transform="SetAttributes" xdt:Locator="Match(name,writeTo)"/>
    <logger name="Jitb.*" minlevel="Info" writeTo="database"
            xdt:Transform="SetAttributes" xdt:Locator="Match(name,writeTo)"/>

    <logger name="NHibernate.SQL" minlevel="Off" writeTo="console"
            xdt:Transform="SetAttributes" xdt:Locator="Match(name,writeTo)"/>
    <logger name="NHibernate.SQL" minlevel="Off" writeTo="file"
            xdt:Transform="SetAttributes" xdt:Locator="Match(name,writeTo)"/>
    <logger name="NHibernate.SQL" minlevel="Off" writeTo="database"
            xdt:Transform="SetAttributes" xdt:Locator="Match(name,writeTo)"/>
  </rules>
</nlog>