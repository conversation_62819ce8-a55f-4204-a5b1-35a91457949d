﻿<?xml version="1.0" encoding="utf-8" ?>
<!-- For more information on using transformations 
     see the web.config examples at http://go.microsoft.com/fwlink/?LinkId=214134. -->
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
	<connectionStrings configSource="ConnectionStrings.qa.config" xdt:Transform="Replace" />

	<appSettings>
    <add key="ServiceControl/Queue" value="particular.servicecontrol@csnsbsct01v"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="env" value="qa"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="auditQueue" value="audit@csnsbsct01v"
        xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="errorQueue" value="error@csnsbsct01v"
        xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>
</configuration>