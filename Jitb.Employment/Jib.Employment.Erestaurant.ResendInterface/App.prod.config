﻿<?xml version="1.0" encoding="utf-8" ?>
<!-- For more information on using transformations 
     see the web.config examples at http://go.microsoft.com/fwlink/?LinkId=214134. -->
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
	<connectionStrings configSource="ConnectionStrings.prod.config" xdt:Transform="Replace"/>

	<appSettings>
    <add key="ServiceControl/Queue" value="particular.servicecontrol@csnsbscp01v"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="env" value="prod"
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="auditQueue" value="audit@csnsbscp01v"
        xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="errorQueue" value="error@csnsbscp01v"
        xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>
</configuration>