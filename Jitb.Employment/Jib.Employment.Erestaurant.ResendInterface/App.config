﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<configuration>
  <configSections>
    <section name="nlog" type="NLog.Config.ConfigSection<PERSON>and<PERSON>, NLog" />
    <section name="Logging" type="NServiceBus.Config.Logging,NServiceBus.Core" />
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <system.transactions>
    <defaultSettings timeout="00:10:00" />
  </system.transactions>
  <connectionStrings configSource="ConnectionStrings.config" />
  <!--
  -->
  <!--
  <connectionStrings>
    <add name="NServiceBus/Persistence" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNSB;integrated security=false;user id=NServiceBus;password=xxxxxxxx;enlist=false;"/>
    <add name="Default" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNServiceBus;integrated security=false;user id=NServiceBus;password=xxxxxxxx;enlist=false;"/>
    <add name="ErestaurantIntegration" connectionString="Data Source=.;Initial Catalog=Jitb_Employment;Integrated Security=SSPI;enlist=false;" />
    <add name="LocationDb" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbLocation;integrated security=false;user id=NServiceBus;password=xxxxxxxx;enlist=false;"/>
    <add name="Sitecenter" connectionString="Data Source=.;Initial Catalog=Jitb_Employment;Integrated Security=SSPI;enlist=false;" />
    <add name="Config" connectionString="data source=CSSQLT01V\VTEST1;initial catalog=dbNServiceBus_Employment_Config;integrated security=false;user id=NServiceBus;password=xxxxxxxx;enlist=false;"/>
    <add name="CSNsbSqlAG-Lsnr" connectionString="data source=.;initial catalog=Jitb_Employment;integrated security=SSPI;enlist=false;" />
    <add name="JibLocationEntityModel" connectionString="Data source=.;initial catalog=jitb_employment;integrated security=true;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="HireEligibility" connectionString="Data Source=.;Initial Catalog=Jitb_Employment;Integrated Security=SSPI;enlist=false;" />
    <add name="Model_FIMFMADEMP_WPS" connectionString="data source=CSSQLT01V\vtest1;initial catalog=dbNServiceBus;integrated security=false;user id=NServiceBus;password=xxxxxxxx;enlist=false;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="ModelEmployeeAction" connectionString="data source=CSSQLT01V\vtest1;initial catalog=dbNServiceBus;integrated security=false;user id=NServiceBus;password=xxxxxxxx;enlist=false;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="Frantracker" connectionString="data source=cssqlt01v\vtest1;initial catalog=dbTracker;integrated security=false;user id=NServiceBus;password=xxxxxxxx;enlist=false;" />
  </connectionStrings>
  -->
  <Logging Threshold="Debug" />
  <appSettings>
    <add key="SendonlyEndpointAddress" value="ResendInterfacesToErest" />
    <add key="nhibernate-logger" value="Jitb.CommonLibrary.NLogFactory, Jitb.CommonLibrary" />
    <add key="env" value="" />
    <add key="ServiceControl/Queue" value="particular.servicecontrol@CSNSBSCT01V" />
    <add key="RoutingBridge/Address" value="Jitb.Employment.RoutingBridge.Endpoint" />
    <add key="RouteMapsFromDatabase" value="true" />
    <add key="errorQueue" value="error" />
    <add key="auditQueue" value="audit" />
  </appSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="NServiceBus.Core" publicKeyToken="9fc386479f8a226c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Renci.SshNet" publicKeyToken="1cee9f8bde3db106" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2016.1.0.0" newVersion="2016.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Iesi.Collections" publicKeyToken="aa95f207798dfdb4" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.4000" newVersion="4.0.0.4000" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="AutoMapper" publicKeyToken="be96cd2c38ef1005" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.1.1.0" newVersion="8.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.FileProviders.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.3.0" newVersion="3.1.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.0.0.0" newVersion="1.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
  </startup>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>
