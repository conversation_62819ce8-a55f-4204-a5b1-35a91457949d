﻿namespace Jitb.Employment.Erestaurant.ResendInterface
{
    using System;
    using Jitb.Employment.Domain.Integrations.Components.Erestaurant.Outgoing.Providers;
    using NServiceBus;
    using StructureMap;

    class Program
    {
        private static readonly IContainer _container;
        private static readonly IEndpointInstance _bus;

        static Program()
        {

            _container = new BusStartup().Start();
            _bus = _container.GetInstance<IEndpointInstance>();
        }

        static void Main(string[] args)
        {
            var interfaces = args[0].Split(',');

            var resendInterfaceProvider = _container.GetInstance<IResendInterfacesProvider>();
            resendInterfaceProvider.Bus = _bus;

            foreach (var i in interfaces)
            {
                var g = new Guid(i);
                resendInterfaceProvider.ResendToErestaurant(g);
            }
        }
    }
}