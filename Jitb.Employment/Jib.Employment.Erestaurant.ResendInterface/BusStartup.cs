﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jitb.Employment.Erestaurant.ResendInterface
{
    using System.ComponentModel;
    using Jitb.Employment.Domain.Nsb;
    using Jitb.NSB.Commons;
    using Jitb.NSB.Commons.Extensions;
    using Jitb.NSB.Commons.Features;
    using Jitb.NSB.Commons.Repository;
    using Jitb.NSB.Commons.RoutingBridge;
    using NLog.Config;
    using NServiceBus;
    using NServiceBus.Features;
    using NServiceBus.Logging;
    using StructureMap;

    public class BusStartup
    {
        public StructureMap.IContainer Start()
        {
            var provider = new MessageMappingProvider();
            var repository = new TransportRouteMapRepository();
            LogManager.Use<NLogFactory>();
            NLog.LogManager.Configuration.Install(new InstallationContext());
            var configuration = new EndpointConfiguration("Jitb.UserManagement.Erestaurant.ResendInterface");
            var bootstrapper = new Bootstrapper();
            bootstrapper.BootstrapStructureMap();
            configuration.UseContainer<StructureMapBuilder>(x => { x.ExistingContainer(bootstrapper.Container); });

            configuration.Conventions()
                .DefiningCommandsAs(x => x.Namespace != null && x.Namespace.Contains(".Contracts.Commands"));
            configuration.Conventions()
                .DefiningEventsAs(x => x.Namespace != null && x.Namespace.Contains(".Contracts.Events"));
            configuration.Conventions()
                .DefiningMessagesAs(x => x.Namespace != null && x.Namespace.Contains(".Contracts.Messages"));
            //configuration.Conventions().DefiningEncryptedPropertiesAs(x =>
            //    x.Name.EndsWith("Secret") || x.Name.ToLower() == "ssn" || x.Name.ToLower() == "socialsecuritynumber" ||
            //    x.Name.ToLower() == "ein");
            //configuration.RijndaelEncryptionService();
            configuration.WithDefaultEncryptionConfiguration();

            var transportRouteMaps = repository.GetTransportRouteMaps(DomainName.Employment);

            ConfigurationMappingRoutingBridge.ConfigureMappingsForRouting(provider, transportRouteMaps);
            RoutingBridgeConfiguration.ConfigureMappingTransport(configuration);

            //endpointConfiguration.ConfigureMappings();

            configuration.DisableFeature<MessageDrivenSubscriptions>();
            configuration.DisableFeature<FeatureInstanceMapping>();
            configuration.EnableFeature<FeatureInstanceMappingView>();
            //configuration.DisableFeature<ServiceControl.Features.SagaAudit>();
            //configuration.DisableFeature<ServiceControl.Features.CustomChecks>();
            //configuration.DisableFeature<ServiceControl.Features.Heartbeats>();
            configuration.UsePersistence<NHibernatePersistence>();
            configuration.SendOnly();
            var endpointInstance = Endpoint.Start(configuration).Result;
            bootstrapper.Container.Configure(x =>
            {
                x.For<IEndpointInstance>()
                    .Use(endpointInstance);
            });
            return bootstrapper.Container;
        }
    }
}
