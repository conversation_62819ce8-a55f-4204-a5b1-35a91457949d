﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Afterman.nRepo" version="2021.6.8.1" targetFramework="net461" />
  <package id="AutoMapper" version="8.1.1" targetFramework="net461" />
  <package id="BouncyCastle.OpenPgp" version="1.8.1.1" targetFramework="net461" />
  <package id="Castle.Core" version="4.4.0" targetFramework="net461" />
  <package id="FluentNHibernate" version="2.0.3.0" targetFramework="net461" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net461" />
  <package id="Jitb.Common.Contracts" version="2019.10.31.1" targetFramework="net461" />
  <package id="Jitb.CommonLibrary" version="2021.6.8.1" targetFramework="net461" />
  <package id="Jitb.UserManagement.Internal.Contracts" version="2021.2.10.4" targetFramework="net461" />
  <package id="log4net" version="2.0.8" targetFramework="net461" />
  <package id="Moq" version="4.16.1" targetFramework="net461" />
  <package id="MSTest.TestAdapter" version="2.2.4" targetFramework="net461" />
  <package id="MSTest.TestFramework" version="2.2.4" targetFramework="net461" />
  <package id="NHibernate" version="4.0.4.4000" targetFramework="net461" />
  <package id="NLog" version="4.7.10" targetFramework="net461" />
  <package id="SSH.NET" version="2016.1.0" targetFramework="net461" />
  <package id="StructureMap" version="4.7.1" targetFramework="net461" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net461" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net461" />
</packages>