﻿using Jitb.CommonLibrary.Providers;

namespace Jitb.Employment.Erestaurant.SoldLocationsiterface.Tests
{
    using CommonLibrary.Xml;
    using Domain.Concepts.LocationDb;
    using Domain.Repositories.Config;
    using Domain.Repositories.Employment;
    using Jitb.Employment.Domain.Repositories.LocationDb;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Moq;
    using System;
    using System.Diagnostics.CodeAnalysis;

    [TestClass]
    [ExcludeFromCodeCoverage]
    public class CloseSoldLocationsTests
    {
        [TestMethod]
        public void given_a_valid_payload_id_then_the_handler_should_send_send_to_erestaurant_command() 
        {
            var locationsSold  =
                new LocationSiteList
                {
                    new LocationSite
                    {
                        Id = 3369,
                        LocationType = "JA",
                        ManagementType = "F",
                        OpenDate = new DateTime(),
                        CloseDate = null,
                        ProjectedOpenDate = null,
                        Entity = 91190,
                        OperationalName = "Jack In The Box Franchise",
                        Address1 = "24620 Madison Ave",
                        Address2 = null,
                        City = "Murrieta",
                        State = "CA",
                        Zip = "92562",
                        Category = "SQ",
                        PollingActiveDate = new DateTime()
                    },
                    new LocationSite
                    {
                        Id = 3012,
                        LocationType = "JA",
                        ManagementType = "F",
                        OpenDate = new DateTime(),
                        CloseDate = null,
                        ProjectedOpenDate = null,
                        Entity = 92365,
                        OperationalName = "Jack In The Box Franchise",
                        Address1 = "3801 Murphy Canyon Rd",
                        Address2 = null,
                        City = "San Diego",
                        State = "CA",
                        Zip = "92123",
                        Category = "CC",
                        PollingActiveDate = new DateTime()
                    }
                };
            string oldInterface =
                "<Poll concept=\"jib.test\" location=\"000000\" search=\"ALIGNMENT_STORES\" date=\"20180904\" created=\"2018-09-04T18:17:26\" dateScope=\"EndOfDay\">\r\n  <AH0>\r\n    <AHS ds=\"003369\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"24620 Madison Ave\" c=\"Murrieta\" st=\"CA\" z=\"92562\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"003012\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"3801 Murphy Canyon Rd\" c=\"San Diego\" st=\"CA\" z=\"92123\" sow=\"MON\" oid=\"2365\" />\r\n    <AHS ds=\"003019\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"1471 N Santa Fe Ave\" c=\"Vista\" st=\"CA\" z=\"92084\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"003033\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"14039 Highway 8 Business\" c=\"El Cajon\" st=\"CA\" z=\"92021\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"003040\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"1050 W El Norte Pkwy\" c=\"Escondido\" st=\"CA\" z=\"92026\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"003046\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"2404 Market St\" c=\"San Diego\" st=\"CA\" z=\"92102\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"003050\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"350 E Chase Ave\" c=\"El Cajon\" st=\"CA\" z=\"92020\" sow=\"MON\" oid=\"3000\" />\r\n    <AHS ds=\"003051\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"TestRoad3051\" c=\"Escondido\" st=\"CA\" z=\"92025\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"003200\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"4715 Green River Rd\" c=\"Corona\" st=\"CA\" z=\"92880\" sow=\"MON\" oid=\"2098\" />\r\n    <AHS ds=\"003239\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"3111 W Florida Ave\" c=\"Hemet\" st=\"CA\" z=\"92545\" sow=\"MON\" oid=\"1191\" />\r\n    <AHS ds=\"003276\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"2296 Griffin Way\" c=\"Corona\" st=\"CA\" z=\"92879\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"000450\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"TestRoad99\" c=\"Salinas\" st=\"CA\" z=\"93901\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"000013\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"3850 Clairemont Mesa Blvd\" c=\"San Diego\" st=\"CA\" z=\"92117\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"000020\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"9337 Mission Gorge Rd\" c=\"Santee\" st=\"CA\" z=\"92071\" sow=\"MON\" oid=\"2364\" />\r\n    <AHS ds=\"000023\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"2959 Upas St\" c=\"San Diego\" st=\"CA\" z=\"92104\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"000029\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"5155 College Ave\" c=\"San Diego\" st=\"CA\" z=\"92115\" sow=\"MON\" oid=\"2364\" />\r\n    <AHS ds=\"000031\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"1110 C St\" c=\"San Diego\" st=\"CA\" z=\"92101\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"000032\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"495 N 2nd St\" c=\"El Cajon\" st=\"CA\" z=\"92021\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"000042\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"1967 San Elijo Ave\" c=\"Cardiff By The Sea\" st=\"CA\" z=\"92007\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"000054\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"2733 Navajo Rd\" c=\"El Cajon\" st=\"CA\" z=\"92020\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"000073\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"1047 Sweetwater Rd\" c=\"Spring Valley\" st=\"CA\" z=\"91977\" sow=\"MON\" oid=\"1155\" />\r\n    <AHS ds=\"000074\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"1880 Coronado Ave\" c=\"San Diego\" st=\"CA\" z=\"92154\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"000075\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"550 W Mission Ave\" c=\"Escondido\" st=\"CA\" z=\"92025\" sow=\"MON\" oid=\"2014\" />\r\n    <AHS ds=\"000076\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"402 W San Ysidro Blvd\" c=\"San Ysidro\" st=\"CA\" z=\"92173\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"000077\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"1056 Main St\" c=\"Ramona\" st=\"CA\" z=\"92065\" sow=\"MON\" oid=\"1184\" />\r\n    <AHS ds=\"000078\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"140 Broadway\" c=\"El Cajon\" st=\"CA\" z=\"92021\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"000080\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"3138 Plaza Blvd\" c=\"National City\" st=\"CA\" z=\"91950\" sow=\"MON\" oid=\"1184\" />\r\n    <AHS ds=\"000090\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"1619 Pacific Hwy\" c=\"San Diego\" st=\"CA\" z=\"92101\" sow=\"MON\" oid=\"2364\" />\r\n    <AHS ds=\"000097\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"1439 Encinitas Blvd\" c=\"Encinitas\" st=\"CA\" z=\"92024\" sow=\"MON\" oid=\"2380\" />\r\n    <AHS ds=\"000168\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"1595 E Florida Ave\" c=\"Hemet\" st=\"CA\" z=\"92544\" sow=\"MON\" oid=\"1191\" />\r\n    <AHS ds=\"000178\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"14864 Pipeline Ave\" c=\"Chino Hills\" st=\"CA\" z=\"91709\" sow=\"MON\" oid=\"2098\" />\r\n    <AHS ds=\"005407\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"33080 Antelope Rd\" c=\"Murrieta\" st=\"CA\" z=\"92563\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"003069\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"TESTCOREJ1\" c=\"San Diego\" st=\"CA\" z=\"92105\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"003077\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"2890 National Ave\" c=\"San Diego\" st=\"CA\" z=\"92113\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"003081\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"3238 Guadalcanal Rd\" c=\"Coronado\" st=\"CA\" z=\"92155\" sow=\"MON\" oid=\"1190\" />\r\n    <AHS ds=\"000550\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"TESTSTREET1\" c=\"Salinas\" st=\"CA\" z=\"93901\" sow=\"MON\" oid=\"1229\" />\r\n    <AHS ds=\"000576\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"TESTREJ1\" c=\"Salinas\" st=\"CA\" z=\"93901\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"000544\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"3111 W Florida Ave\" c=\"Hemet\" st=\"CA\" z=\"92545\" sow=\"MON\" oid=\"1191\" />\r\n    <AHS ds=\"000555\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"TESTSTREET1\" c=\"Salinas\" st=\"CA\" z=\"93901\" sow=\"MON\" oid=\"1229\" />\r\n    <AHS ds=\"000553\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"TESTSTREET1\" c=\"Salinas\" st=\"CA\" z=\"93901\" sow=\"MON\" oid=\"1229\" />\r\n    <AHS ds=\"000573\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"1471 N Santa Fe Ave\" c=\"Vista\" st=\"CA\" z=\"92084\" sow=\"MON\" oid=\"2001\" />\r\n    <AHS ds=\"000567\" name=\"Jack In The Box\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" a=\"1471 N Santa Fe Ave\" c=\"Vista\" st=\"CA\" z=\"92084\" sow=\"MON\" oid=\"1\" />\r\n    <AHS ds=\"008885\" name=\"Jack In The Box Franchise\" b=\"Jack in the Box\" t=\"FRAN\" ss=\"OPEN\" a=\"TestRoad99\" c=\"Salinas\" st=\"CA\" z=\"93901\" sow=\"MON\" oid=\"1229\" />\r\n    <AHS ds=\"999999\" name=\"Altametrics Test Store\" b=\"Jack in the Box\" t=\"CORP\" ss=\"OPEN\" sow=\"MON\" oid=\"1\" />\r\n  </AH0>\r\n</Poll>";
            var _locationTempCloseRepositoryMock       = new Mock<ILocationTempCloseRepository>();
            var _xmlPayloadWriterMock                  = new Mock<IXmlPayloadWriter>();
            var _locationInterfaceSentRepository       = new Mock<ILocationInterfaceSentRepository>();
            var _eRestaurantInterfaceJobRepository     = new Mock<IERestaurantInterfaceJobRepository>();
            var _locationSoftwareErestRepositoryMock   = new Mock<ILocationSoftwareErestRepository>();
            var _warehouseRepositoryMock = new Mock<IWarehouseRepository>();
            var _configSettingProviderMock = new Mock<IConfigSettingProvider>();


//SLB fix (uncomment) if implementing
            // var _createLocationsInterface = new CreateLocationInterfaceBuilder(_locationTempCloseRepositoryMock.Object
            // , _xmlPayloadWriterMock.Object
            // , _locationInterfaceSentRepository.Object 
            // , _eRestaurantInterfaceJobRepository.Object
            // , _locationSoftwareErestRepositoryMock.Object
            // , _warehouseRepositoryMock.Object
            // , _configSettingProviderMock.Object);
            //
            // var result = _createLocationsInterface.CreateLocationsInterfaceSold(locationsSold, oldInterface);
        }
    }
}
