﻿using AutoFixture.Xunit2;
using CreateTestData;
using FluentAssertions;
using Jitb.Employment.DatabaseMonitor.Endpoint.Handlers;
using Jitb.Employment.Domain.Concepts;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.Internal.Contracts.Events.DatabaseMonitor;
using Moq;
using NServiceBus.Testing;
using Xunit;

namespace Jitb.Employment.DatabaseMonitor.Xunit.Tests
{
    public class UpdateEmployeeDisabledAccountTests
    {
        [Theory]
        [AutoMoqData]
        [Trait("Category", "Persona")]
        [Trait("Category", "2025-03")]
        //    [InlineAutoData("When employee has both enabled and disabled personas, only enabled persona updates employee")]
        public void Handle_UpdateEmployeeRecord_WithEnabledAndDisabledPersonas_OnlyUsesEnabledPersona(
            string testDescription,
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] IPersonaAdLinkRepository personaAdLinkRepository,
            [Frozen] IFoundAnUpdatedEmployeeR<PERSON>ord message,
            [Frozen] Employee employee,
            UpdateEmployeeRecordHandler sut)
        {
            // Arrange
            employee.EmailAddress = "<EMAIL>";
            employee.NetworkId = "original-network";

            var enabledPersona = new Persona
            {
                IsEnabled = true,
                AdEmail = "<EMAIL>",
                AdNetworkId = "enabled-network",
                AdDomain = "ad.corp.jitb.net"
            };

            var disabledPersona = new Persona
            {
                IsEnabled = false,
                AdEmail = "<EMAIL>",
                AdNetworkId = "disabled-network",
                AdDomain = "ad.corp.jitb.net"
            };

            var enabledPersonaAdLink = new PersonaAdLink
            {
                Persona = enabledPersona,
                InternalId = employee.EmployeeId,
                InternalTable = 2
            };

            var disabledPersonaAdLink = new PersonaAdLink
            {
                Persona = disabledPersona,
                InternalId = employee.EmployeeId,
                InternalTable = 2
            };

            Mock.Get(personaAdLinkRepository)
                .Setup(x => x.GetByEisIdAndTable(It.IsAny<int>(), It.IsAny<long>()))
                .Returns(enabledPersonaAdLink);

            Mock.Get(employeeRepository)
                .Setup(x => x.Get(It.IsAny<long>()))
                .Returns(employee);

            // Capture updated employee
            Employee updatedEmployee = null;
            Mock.Get(employeeRepository)
                .Setup(x => x.Add(It.IsAny<Employee>()))
                .Callback<Employee>(e => updatedEmployee = e);

            // Act
            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            // Assert
            Mock.Get(employeeRepository).Verify(x => x.Add(It.IsAny<Employee>()), Times.Once);
            updatedEmployee.Should().NotBeNull();
            updatedEmployee.EmailAddress.Should().Be(enabledPersona.AdEmail);
            updatedEmployee.NetworkId.Should().Be(enabledPersona.AdNetworkId);

            context.PublishedMessages.Length.Should().Be(1);
        }
        [Theory]
        [AutoMoqData]
        [Trait("Category", "Persona")]
        [Trait("Category", "2025-03")]
        public void Handle_UpdateEmployeeRecord_WithOnlyDisabledPersona_DoesNotUpdateEmployee(
            string testDescription,
            [Frozen] IEmployeeRepository employeeRepository,
            [Frozen] IPersonaAdLinkRepository personaAdLinkRepository,
            [Frozen] IFoundAnUpdatedEmployeeRecord message,
            [Frozen] Employee employee,
            UpdateEmployeeRecordHandler sut)
        {
            // Arrange
            employee.EmailAddress = "<EMAIL>";
            employee.NetworkId = "original-network";

            var disabledPersona = new Persona
            {
                IsEnabled = false,
                AdEmail = "<EMAIL>",
                AdNetworkId = "disabled-network",
                AdDomain = "ad.corp.jitb.net"
            };

            var disabledPersonaAdLink = new PersonaAdLink
            {
                Persona = disabledPersona,
                InternalId = employee.EmployeeId,
                InternalTable = 2
            };

            Mock.Get(personaAdLinkRepository)
                .Setup(x => x.GetByEisIdAndTable(It.IsAny<int>(), It.IsAny<long>()))
                .Returns(disabledPersonaAdLink);

            Mock.Get(employeeRepository)
                .Setup(x => x.Get(It.IsAny<long>()))
                .Returns(employee);

            // Capture updated employee
            Employee updatedEmployee = null;
            Mock.Get(employeeRepository)
                .Setup(x => x.Add(It.IsAny<Employee>()))
                .Callback<Employee>(e => updatedEmployee = e);

            // Act
            var context = new TestableMessageHandlerContext();
            sut.Handle(message, context).GetAwaiter().GetResult();

            // Assert
            Mock.Get(employeeRepository).Verify(x => x.Add(It.IsAny<Employee>()), Times.Never);
            updatedEmployee.Should().BeNull();

            context.PublishedMessages.Length.Should().Be(0);
        }
    }
}
