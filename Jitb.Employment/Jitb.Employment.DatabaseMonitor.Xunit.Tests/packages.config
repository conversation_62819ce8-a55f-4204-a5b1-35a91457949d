﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Afterman.nRepo" version="2024.10.17.2" targetFramework="net48" />
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net48" />
  <package id="AutoFixture" version="4.18.1" targetFramework="net48" />
  <package id="AutoFixture.AutoMoq" version="4.18.1" targetFramework="net48" />
  <package id="AutoFixture.Xunit2" version="4.18.1" targetFramework="net48" />
  <package id="Castle.Core" version="5.2.1" targetFramework="net48" />
  <package id="Fare" version="2.1.1" targetFramework="net48" />
  <package id="FluentAssertions" version="7.0.0" targetFramework="net48" />
  <package id="FluentNHibernate" version="2.0.3.0" targetFramework="net48" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net48" />
  <package id="Microsoft.TestPlatform.ObjectModel" version="17.12.0" targetFramework="net48" />
  <package id="Moq" version="4.20.72" targetFramework="net48" />
  <package id="NHibernate" version="5.3.3" targetFramework="net48" />
  <package id="NLog" version="4.5.11" targetFramework="net48" />
  <package id="NServiceBus" version="7.0.1" targetFramework="net48" />
  <package id="NServiceBus.Testing" version="7.0.0" targetFramework="net48" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net48" />
  <package id="Remotion.Linq.EagerFetching" version="2.2.0" targetFramework="net48" />
  <package id="StructureMap" version="4.7.1" targetFramework="net48" />
  <package id="System.Collections.Immutable" version="1.5.0" targetFramework="net48" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net48" />
  <package id="System.Reflection.Metadata" version="1.6.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="xunit" version="2.9.3" targetFramework="net48" />
  <package id="xunit.abstractions" version="2.0.3" targetFramework="net48" />
  <package id="xunit.analyzers" version="1.18.0" targetFramework="net48" developmentDependency="true" />
  <package id="xunit.assert" version="2.9.3" targetFramework="net48" />
  <package id="xunit.core" version="2.9.3" targetFramework="net48" />
  <package id="xunit.extensibility.core" version="2.9.3" targetFramework="net48" />
  <package id="xunit.extensibility.execution" version="2.9.3" targetFramework="net48" />
  <package id="xunit.runner.visualstudio" version="3.0.2" targetFramework="net48" developmentDependency="true" />
</packages>