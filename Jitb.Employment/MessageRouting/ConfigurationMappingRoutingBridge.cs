﻿using Jitb.Employment.Contracts.Commands.ActiveDirectory;
using Jitb.Employment.Contracts.Commands.CoreLifecycle;
using Jitb.Employment.Contracts.Commands.Employment;
using Jitb.Employment.Contracts.Commands.Employment.Reply;
using Jitb.Employment.Contracts.Commands.Erestaurant;
using Jitb.Employment.Contracts.Commands.ErestaurantSync;
using Jitb.Employment.Contracts.Commands.Fim;
using Jitb.Employment.Contracts.Commands.Franchise;
using Jitb.Employment.Contracts.Commands.Lawson;
using Jitb.Employment.Contracts.Commands.MSMQListener;
using Jitb.Employment.Contracts.Commands.Scheduler;
using Jitb.Employment.Contracts.Commands.Timekeeping;
using Jitb.Employment.Contracts.Commands.Ultipro;
using Jitb.Employment.Contracts.Events.Audit;
using Jitb.Employment.Contracts.Events.CoreLifecycle;
using Jitb.Employment.Contracts.Events.DatabaseMonitor;
using Jitb.Employment.Contracts.Events.Employment;
using Jitb.Employment.Contracts.Events.Erestaurant;
using Jitb.Employment.Contracts.Events.FranchiseUserSync;
using Jitb.Employment.Contracts.Events.RoleManagement;
using Jitb.Employment.Contracts.Events.Ultipro;
using Jitb.Employment.Contracts.Messages.Employment;
using Jitb.Employment.Domain.Configuration;
using Jitb.Employment.Internal.Contracts.Commands.ActiveDirectory;
using Jitb.Employment.Internal.Contracts.Commands.CoreLifecycle;
using Jitb.Employment.Internal.Contracts.Commands.DatabaseMonitor;
using Jitb.Employment.Internal.Contracts.Commands.Erestaurant;
using Jitb.Employment.Internal.Contracts.Commands.Lawson;
using Jitb.Employment.Internal.Contracts.Commands.LawsonSync;
using Jitb.Employment.Internal.Contracts.Commands.Ultipro;
using Jitb.Employment.Internal.Contracts.Commands.UltiProSync;
using Jitb.Employment.Internal.Contracts.Events.DatabaseMonitor;
using Jitb.Employment.Internal.Contracts.Events.LawsonSync;
using Jitb.Employment.Internal.Contracts.Messages.CoreLifecycle;
using Jitb.Employment.Internal.Contracts.Messages.Fim;
using Jitb.Employment.Internal.Contracts.Messages.LawsonSync;
using Jitb.Employment.Internal.Contracts.Messages.UltiProSync;
using Jitb.NSB.Commons.Extensions;
using Jitb.NSB.Commons.Repository.Model;
using Jitb.NSB.Commons.RoutingBridge;
using Jitb.Payroll.Contracts.Commands;
using Jitb.SharedServices.Contracts.Commands.Notifications;
using Jitb.SharedServices.Contracts.Commands.ServiceControl;
using Jitb.SharedServices.Contracts.Events;
using System;
using System.Collections.Generic;

namespace Jitb.Employment.Domain.Nsb
{
    public static class DomainName
    {
        public const string Employment = "Employment";
    }

    public static class ConfigurationMappingRoutingBridge
    {
        public static void ConfigureMappingsForRouting(MessageMappingProvider provider, List<TransportRouteMap> transportRouteMaps)
        {
            var routeMapsFromDatabase = ConfigurationContext.RouteMapsFromDatabase;

            if (routeMapsFromDatabase == true && (transportRouteMaps != null && transportRouteMaps?.Count > 0))
            {
                var registerBridgeMappingMethod = typeof(MessageMappingProvider).GetMethod("RegisterBridgeMapping", new[]
                {
                    typeof(string), typeof(ConfigurationRoutingTransports)
                });
                var addSubscriptionRouteMethod = typeof(MessageMappingProvider).GetMethod("AddSubscriptionRoute", new[]
                {
                    typeof(string), typeof(ConfigurationRoutingTransports)
                });

                foreach (var transportRouteMap in transportRouteMaps)
                {
                    if (transportRouteMap.IsEnabled == true)
                    {
                        var type = Type.GetType($"{transportRouteMap.MessageType}, {transportRouteMap.MessageAssembly}");

                        if (transportRouteMap.RegistrationType == MessageTypes.Command)
                        {
                            var registerTypeMethod = registerBridgeMappingMethod.MakeGenericMethod(type);

                            var parameters = new object[] { transportRouteMap.HandlerNamespace, transportRouteMap.TransportType };
                            registerTypeMethod.Invoke(provider, parameters);
                        }
                        if (transportRouteMap.RegistrationType == MessageTypes.Subscription)
                        {
                            var subscriptionTypeMethod = addSubscriptionRouteMethod.MakeGenericMethod(type);

                            var parameters = new object[] { transportRouteMap.HandlerNamespace, transportRouteMap.TransportType };
                            subscriptionTypeMethod.Invoke(provider, parameters);
                        }
                    }
                }
            }
            else
            {
                provider.RegisterBridgeMapping<SharedServices.Contracts.Commands.ErestaurantSync.SendToErestaurant>("Jitb.SharedServices.Erestaurant.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<CheckForBirthdaysOnDate>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<UpdateLocationHierarchy>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<BeginStretchAssignment>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartPollingForLocationUpdates>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<PassThruHire>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<ProcessIndividualGenericUpdate>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<CheckIfAdInfoChanged>("Jitb.Employment.ActiveDirectory.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<SendJobCodeOrRateChangeUpdate>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<RequestSendToErestaurant>("Jitb.Employment.Erestaurant.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartSchedule>("Jitb.Employment.Scheduler.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartCheckingActiveDirectory>("Jitb.Employment.ActiveDirectory.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<SyncronizeLocations>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<ChangeJobCodeDefinition>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<AnnounceJtkEmployee>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartFimEmployeeUpdate>("Jitb.Employment.Fim.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<RunEmployeeCurrentInfoReportCommand>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<HireAnEmployeeInTimekeeping>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<TerminateAnEmployeeInTimekeeping>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<CompareUltiDataToEisCommand>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<SyncLocationsToBing>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<ProcessHeartbeat>("Jitb.SharedServices.ServiceControl.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<RePersistPayload>("Jitb.Payroll.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.AddSubscriptionRoute<IBorrowedAnEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<ICreatedANewEmployeeAction>("Jitb.Employment.Audit.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IChangedAnEmailAddress>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.AddSubscriptionRoute<IFinishedSyncingAnUltiProReportSet>("Jitb.Employment.UltiPro.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<ISentAnEmployeeToErestaurant>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IPerformedARoleChange>("Jitb.Employment.RoleManagement.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IAddedAFranchiseAboveStoreUser>("Jitb.UserManagement.Franchise.AboveStoreSync", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IHadAnEmployeeBirthday>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<CreateNewHireResponse>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartCheckingActiveDirectory>("Jitb.Employment.ActiveDirectory.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartPollingForBirthdays>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<SyncDataFromLawson>("Jitb.Employment.Lawson.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartPollingLawson>("Jitb.Employment.Lawson.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartSyncingUltiProReportSet>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartPollingUltiPro>("Jitb.Employment.UltiPro.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartUltiProEmployeeDeleteSaga>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartPayRateSync>("Jitb.Employment.UltiPro.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<SendInterfacesToERest>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<PreProcessLawsonData>("Jitb.Employment.Lawson.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<ProcessIndividualLawsonSyncRecord>("Jitb.Employment.Lawson.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<SyncFranTracker>("Jitb.Employment.Franchise.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartDatabaseMonitorSaga>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<IFoundANewPersonaRecord>("Jitb.UserManagement.AdUpdate.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<CheckForOutstandingUpdates>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.AddSubscriptionRoute<ICompletedLawsonSync>("Jitb.Employment.Lawson.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFinishedPreProcessingLawsonData>("Jitb.Employment.Lawson.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<CheckForBirthdays>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<TimeToCheckLawson>("Jitb.Employment.Lawson.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<TimeToCheckUltiPro>("Jitb.Employment.UltiPro.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<InjectMessage>("Jitb.Employment.MSMQListener.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<TimeToRunFimSync>("Jitb.Employment.Fim.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<CheckForProcessingEmployeeBorrows>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<SyncNextUltiProReportInSet>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<CreateNewHire>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<TerminateEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<BorrowEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<EndLeaveOfAbsence>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<FinishOnboarding>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartLeaveOfAbsence>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Contracts.Commands.Employment.TransferEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<CreateNewHireReply>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<TerminateEmployeeReply>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<BorrowEmployeeReply>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<EndLeaveOfAbsenceReply>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<FinishOnboardingReply>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<StartLeaveOfAbsenceReply>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<TransferEmployeeReply>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<UpdateEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<ChangePayRate>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<EndBorrowEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<ChangeAnEmployeeJobCodeInTimekeeping>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<ChangeEmployeeJobCode>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                provider.RegisterBridgeMapping<ProcessPayroll>("Jitb.Payroll.Core.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<SendEmail>("Jitb.SharedServices.Notifications.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<UploadFtpFile>("Jitb.ItOperations.FileIO.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.SharedServices.AcknowledgeSentPayload>("Jitb.SharedServices.Erestaurant.PayloadRouting.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.SharedServices.DecryptErestaurantPayload>("Jitb.SharedServices.Erestaurant.PayloadRouting.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IGotAPayloadAcknowledgement>("Jitb.SharedServices.Erestaurant.PayloadRouting.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.SharedServices.ReplayErestaurantPayloads>("Jitb.SharedServices.Erestaurant.PayloadRouting.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.SharedServices.ReplayErestaurantPayload>("Jitb.SharedServices.Erestaurant.PayloadRouting.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.ServiceControl.ProcessCheckpoint>("Jitb.SharedServices.ServiceControl.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.ServiceControl.RequestWebServiceHeartbeat>("Jitb.SharedServices.ServiceControl.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.ServiceControl.WebServiceHeartbeat>("Jitb.SharedServices.ServiceControl.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.Notifications.SetRegenRequestStatus>("Jitb.SharedServices.Notifications.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.Notifications.NotifySubscription>("Jitb.SharedServices.Notifications.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.Notifications.SendSlackNotification>("Jitb.SharedServices.Notifications.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.Notifications.SubscribeToNotifications>("Jitb.SharedServices.Notifications.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.ErestaurantSync.RequestSendToErestaurant>("Jitb.SharedServices.Erestaurant.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.ErestaurantSync.StartSendToErestaurantSaga>("Jitb.SharedServices.Erestaurant.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.ErestaurantSync.StartReceiveFromErestaurantSaga>("Jitb.SharedServices.Erestaurant.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.SharedServices.Contracts.Commands.DynamicallyRouted.ProcessIncomingErestaurantPayload>("Jitb.SharedServices.RoutingBridge.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Timekeeping.SyncronizeLocationsTimeout>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Timekeeping.CheckForJTKEmployee>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.BorrowAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.EndBorrowAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.EndLeaveOfAbsenceAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.GenericUpdateAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.IPartialAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.JobCodeOrPayRateChangeAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.LeaveOfAbsenceAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.NewHireAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.RehireAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.TerminationAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Erestaurant.TransferAnnouncement>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.Employment.StartPollingForBirthdays>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.DatabaseMonitor.EndPersonaUpdateSaga>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.DatabaseMonitor.CheckIfDatabaseChanged>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.DatabaseMonitor.CheckIfDatabaseChangedReply>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.DatabaseMonitor.LookForDatabaseChanges>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.CoreLifecycle.StartProcessingEmployeeBorrows>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.CoreLifecycle.StartPollingForNewUsers>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Internal.Contracts.Commands.ActiveDirectory.LookforAdChanges>("Jitb.Employment.ActiveDirectory.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Ultipro.ProcessIndividualBorrow>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Ultipro.ProcessIndividualEndBorrow>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Ultipro.ProcessIndividualLocationUpdates>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Ultipro.ProcessIndividualPayRateChange>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Ultipro.ProcessIndividualTransfer>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Timekeeping.BorrowAnEmployee>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Timekeeping.EndBorrowAnEmployee>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Timekeeping.InactivateJtkEmployee>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Timekeeping.InactivateJtkUser>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Timekeeping.ReactivateJtkEmployee>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Timekeeping.ReactivateJtkUser>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Timekeeping.TransferEmployee>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Timekeeping.UpdateJtkUserSecurityLevel>("Jitb.Employment.Timekeeping.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Fim.CreateNewUserStubAccount>("Jitb.Employment.Fim.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Erestaurant.PersistOutboundErestaurantPayload>("Jitb.Employment.Erestaurant.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.AnnounceLocation>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.ProcessEmployeeBorrows>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.RehireEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.AnnounceEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.BulkAnnounceEmployees>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.ChangePersonnelInformation>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.CreateBadgeId>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.EndStretchAssignment>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.CheckForRehireEligibility>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.UpdateBadgeId>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.EmployeeChange>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.UpdateEmployeeLocations>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.UpdateEntityDepartmentCode>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.UpdateEntityJobCode>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.Reply.AnnounceLocationReply>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.Employment.UpdateEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.ItOperations.Contracts.Commands.FileParser.ParseRawData>("Jitb.ItOperations.FileParser.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.ItOperations.Contracts.Commands.FileIO.CheckForFtpFile>("Jitb.ItOperations.FileMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.ItOperations.Contracts.Commands.FileIO.DeleteFtpFile>("Jitb.ItOperations.FileIO.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.ItOperations.Contracts.Commands.FileIO.DownloadFtpFile>("Jitb.ItOperations.FileIO.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.ItOperations.Contracts.Commands.FileIO.ScanFtpFiles>("Jitb.ItOperations.FileIO.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.ItOperations.Contracts.Commands.FileIO.SaveToDisk>("Jitb.ItOperations.FileIO.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                //HarriInbound
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.HarriInbound.GetAllEmployees>("Jitb.Employment.HarriInbound.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.HarriInbound.NewHarriEmployeeFound>("Jitb.Employment.HarriInbound.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.RegisterBridgeMapping<Jitb.Employment.Contracts.Commands.HarriInbound.StartHarriInboundSyncSaga>("Jitb.Employment.HarriInbound.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);


                provider.AddSubscriptionRoute<Jitb.SharedServices.Contracts.Events.IFailedAnErestaurantPayloadAfterAllRetries>("Jitb.SharedServices.Erestaurant.Sync.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.SharedServices.Contracts.Events.IGotAFailedErestaurantPayload>("Jitb.SharedServices.Erestaurant.PayloadRouting.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.SharedServices.Contracts.Events.IReceivedAPayload>("Jitb.SharedServices.Erestaurant.PayloadRouting.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Internal.Contracts.Events.Employment.IUpdatedAnEmployeeJobcode>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Ultipro.IAmCheckingForARehire>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Ultipro.IGotUltiProReportResults>("Jitb.Employment.UltiPro.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.FranchiseUserSync.IModifiedFranchiseEntity>("Jitb.UserManagement.Franchise.AboveStoreSync", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IAssignedABadgeId>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IAnnouncedAnEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IBorrowedAnEmployeeEnded>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IChangedAJobCodeDefinition>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IChangedAnEmployeeDomain>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFinishedOnboarding>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFinishedSyncingLocationsAndHierarchies>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFoundACreatedLocation>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFoundANewUserChange>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFoundAnOpenedLocation>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFoundALocationEntityUpdate>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFoundALocationHierarchyUpdate>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFoundALocationStateUpdate>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IChangedAnEmployeeJobCode>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IChangedAnEmployeeJobCodeWithClass>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IChangedAnEmployeePayRate>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IChangedPersonnelInformation>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IFoundADeletedLocation>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IEndedALeaveOfAbsence>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IEndedAStretchAssignment>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IHiredAnEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IHiredAnEmployeeWithClass>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IMadeAGenericUpdateToAnEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IProcessEmployeeBorrows>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.ISyncronizedAnEmployeesRoles>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IPassedThruAHire>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IStartedALeaveOfAbsence>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IStartedAStretchAssignment>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.ITerminatedAnEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.ITransferredAnEmployee>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.Employment.IUpdatedASupervisorId>("Jitb.Employment.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.CoreLifecycle.ISyncedLocationsToBing>("Jitb.Employment.CoreLifecycle.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Contracts.Events.ActiveDirectory.IChangedANetworkId>("Jitb.Employment.ActiveDirectory.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Internal.Contracts.Events.DatabaseMonitor.IFoundAPersonaRecordChange>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Internal.Contracts.Events.DatabaseMonitor.IFoundANewPersonaRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Internal.Contracts.Events.DatabaseMonitor.IFoundAnUpdatedPersonaRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<Jitb.Employment.Internal.Contracts.Events.DatabaseMonitor.IFoundANewPersonaAdLinkRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundAnEmployeeRecordChange>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundANewEmployeeRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundAnUpdatedEmployeeRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundAContractorRecordChange>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundANewContractorRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundAnUpdatedContractorRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundAUserRecordChange>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundANewUserRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);
                provider.AddSubscriptionRoute<IFoundAnUpdatedUserRecord>("Jitb.Employment.DatabaseMonitor.Endpoint", ConfigurationRoutingTransports.ConfigureTransportForMSMQ);

                //HarriInbound

            }
        }
    }
}