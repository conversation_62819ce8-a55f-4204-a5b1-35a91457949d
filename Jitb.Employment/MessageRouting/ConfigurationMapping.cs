﻿using Jitb.Employment.Contracts.Commands.HarriInbound;
using Jitb.Employment.Contracts.Events.DatabaseMonitor;
using Jitb.Employment.Internal.Contracts.Commands.DatabaseMonitor;
using Jitb.Employment.Internal.Contracts.Events.DatabaseMonitor;
using NServiceBus;

namespace Jitb.Employment.Domain.Nsb
{
    using Contracts.Commands.ActiveDirectory;
    using Contracts.Commands.CoreLifecycle;
    using Contracts.Commands.Employment;
    using Contracts.Commands.Erestaurant;
    using Contracts.Commands.Fim;
    using Contracts.Commands.Franchise;
    using Contracts.Commands.Lawson;
    using Contracts.Commands.MSMQListener;
    //using Contracts.Commands.ErestaurantSync;
    using Contracts.Commands.Scheduler;
    using Contracts.Commands.Timekeeping;
    using Contracts.Commands.Ultipro;
    using Contracts.Events.ActiveDirectory;
    using Contracts.Events.Audit;
    using Contracts.Events.Employment;
    using Contracts.Events.Erestaurant;
    using Contracts.Events.FranchiseUserSync;
    using Contracts.Events.Ultipro;
    using Contracts.Messages.Employment;
    using Internal.Contracts.Commands.ActiveDirectory;
    using Internal.Contracts.Commands.CoreLifecycle;
    using Internal.Contracts.Commands.Erestaurant;
    using Internal.Contracts.Commands.Lawson;
    using Internal.Contracts.Commands.LawsonSync;
    using Internal.Contracts.Commands.Ultipro;
    using Internal.Contracts.Commands.UltiProSync;
    using Internal.Contracts.Events.LawsonSync;
    using Internal.Contracts.Messages.CoreLifecycle;
    using Internal.Contracts.Messages.Fim;
    using Internal.Contracts.Messages.LawsonSync;
    using Internal.Contracts.Messages.UltiProSync;
    //using Jitb.Employment.Contracts.Commands.Employment;
    using Jitb.Employment.Contracts.Commands.Employment.Reply;
    using Jitb.Employment.Contracts.Commands.ErestaurantSync;
    using Jitb.Employment.Contracts.Events.CoreLifecycle;
    using Jitb.Employment.Contracts.Events.RoleManagement;
    using Jitb.ItOperations.Contracts.Commands.FileIO;
    using Jitb.SharedServices.Contracts.Commands.ServiceControl;
    using Jitb.SharedServices.Contracts.Events;
    using Payroll.Contracts.Commands;
    using SharedServices.Contracts.Commands.Notifications;

    public static class ConfigurationMapping
    {
        public static RoutingSettings<MsmqTransport> ConfigureMappings(this EndpointConfiguration configuration, IMessageMappingProvider provider)
        {
            var transport = configuration.UseTransport<MsmqTransport>();
            var routing = transport.Routing();

            // uncomment to switch back to using the instance-mapping files (disable all entries in InstanceMapping db, first)
            //routing.InstanceMappingFile().ConfigureInstanceMappingFile();

            //public commands
            //
            // Jitb.SharedServices.Contracts.Commands.ErestaurantSync.SendToErestaurant
            provider.RegisterLegacyMapping<SharedServices.Contracts.Commands.ErestaurantSync.SendToErestaurant>(routing, "Jitb.SharedServices.Erestaurant.Sync.Endpoint");
            provider.RegisterLegacyMapping<CheckForBirthdaysOnDate>(routing, "Jitb.Employment.CoreLifecycle.Endpoint");
            provider.RegisterLegacyMapping<UpdateLocationHierarchy>(routing, "Jitb.Employment.CoreLifecycle.Endpoint");
            provider.RegisterLegacyMapping<BeginStretchAssignment>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<PassThruHire>(routing, "Jitb.Employment.CoreLifecycle.Endpoint");
            provider.RegisterLegacyMapping<ProcessIndividualGenericUpdate>(routing, "Jitb.Employment.UltiPro.Endpoint");
            provider.RegisterLegacyMapping<CheckIfAdInfoChanged>(routing, "Jitb.Employment.ActiveDirectory.Endpoint");
            provider.RegisterLegacyMapping<SendJobCodeOrRateChangeUpdate>(routing, "Jitb.Employment.Erestaurant.Endpoint");
            provider.RegisterLegacyMapping<RequestSendToErestaurant>(routing, "Jitb.Employment.Erestaurant.Sync.Endpoint");
            provider.RegisterLegacyMapping<StartSchedule>(routing, "Jitb.Employment.Scheduler.Endpoint");
            provider.RegisterLegacyMapping<StartCheckingActiveDirectory>(routing, "Jitb.Employment.ActiveDirectory.Endpoint");
            provider.RegisterLegacyMapping<InjectMessage>(routing, "Jitb.Employment.MSMQListener.Endpoint");
            provider.RegisterLegacyMapping<SyncronizeLocations>(routing, "Jitb.Employment.Timekeeping.Endpoint");
            provider.RegisterLegacyMapping<AnnounceJtkEmployee>(routing, "Jitb.Employment.Timekeeping.Endpoint");
            provider.RegisterLegacyMapping<StartFimEmployeeUpdate>(routing, "Jitb.Employment.Fim.Endpoint");
            provider.RegisterLegacyMapping<RunEmployeeCurrentInfoReportCommand>(routing, "Jitb.Employment.UltiPro.Endpoint");
            provider.RegisterLegacyMapping<CompareUltiDataToEisCommand>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<SyncLocationsToBing>(routing, "Jitb.Employment.CoreLifecycle.Endpoint");
            provider.RegisterLegacyMapping<ProcessHeartbeat>(routing, "Jitb.SharedServices.ServiceControl.Endpoint");
            provider.RegisterLegacyMapping<RePersistPayload>(routing, "Jitb.Payroll.Endpoint");

            //public events
            provider.RegisterLegacyMapping<IBorrowedAnEmployee>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<ICreatedANewEmployeeAction>(routing, "Jitb.Employment.Audit.Endpoint");
            provider.RegisterLegacyMapping<IChangedAnEmailAddress>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundAPersonaRecordChange>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundANewPersonaRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundAnUpdatedPersonaRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundANewPersonaAdLinkRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundAnEmployeeRecordChange>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundANewEmployeeRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundAnUpdatedEmployeeRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundAContractorRecordChange>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundANewContractorRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundAnUpdatedContractorRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundAUserRecordChange>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundANewUserRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFoundAnUpdatedUserRecord>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");
            provider.RegisterLegacyMapping<IFinishedSyncingAnUltiProReportSet>(routing, "Jitb.Employment.UltiPro.Sync.Endpoint");
            provider.RegisterLegacyMapping<ISentAnEmployeeToErestaurant>(routing, "Jitb.Employment.Erestaurant.Endpoint");
            provider.RegisterLegacyMapping<AnnounceJtkEmployee>(routing, "Jitb.Employment.Timekeeping.Endpoint");
            provider.RegisterLegacyMapping<IPerformedARoleChange>(routing, "Jitb.Employment.RoleManagement.Endpoint");
            provider.RegisterLegacyMapping<IAddedAFranchiseAboveStoreUser>(routing, "Jitb.UserManagement.Franchise.AboveStoreSync");
            provider.RegisterLegacyMapping<IHadAnEmployeeBirthday>(routing, "Jitb.Employment.CoreLifecycle.Endpoint");
            provider.RegisterLegacyMapping<IChangedANetworkId>(routing, "Jitb.Employment.Endpoint");
            //public messages
            provider.RegisterLegacyMapping<CreateNewHireResponse>(routing, "Jitb.Employment.Endpoint");

            //internal commands
            provider.RegisterLegacyMapping<StartCheckingActiveDirectory>(routing, "Jitb.Employment.ActiveDirectory.Endpoint");
            provider.RegisterLegacyMapping<StartPollingForBirthdays>(routing, "Jitb.Employment.CoreLifecycle.Endpoint");
            provider.RegisterLegacyMapping<SyncDataFromLawson>(routing, "Jitb.Employment.Lawson.Endpoint");
            provider.RegisterLegacyMapping<StartPollingLawson>(routing, "Jitb.Employment.Lawson.Sync.Endpoint");
            provider.RegisterLegacyMapping<StartSyncingUltiProReportSet>(routing, "Jitb.Employment.UltiPro.Endpoint");
            provider.RegisterLegacyMapping<StartPollingUltiPro>(routing, "Jitb.Employment.UltiPro.Sync.Endpoint");
            provider.RegisterLegacyMapping<SendInterfacesToERest>(routing, "Jitb.Employment.Erestaurant.Endpoint");
            provider.RegisterLegacyMapping<PreProcessLawsonData>(routing, "Jitb.Employment.Lawson.Endpoint");
            provider.RegisterLegacyMapping<ProcessIndividualLawsonSyncRecord>(routing, "Jitb.Employment.Lawson.Endpoint");
            provider.RegisterLegacyMapping<SyncFranTracker>(routing, "Jitb.Employment.Franchise.Sync.Endpoint");
            provider.RegisterLegacyMapping<StartDatabaseMonitorSaga>(routing, "Jitb.Employment.DatabaseMonitor.Endpoint");

            //internal events
            provider.RegisterLegacyMapping<ICompletedLawsonSync>(routing, "Jitb.Employment.Lawson.Sync.Endpoint");
            provider.RegisterLegacyMapping<IFinishedPreProcessingLawsonData>(routing, "Jitb.Employment.Lawson.Sync.Endpoint");

            //internal messages
            provider.RegisterLegacyMapping<CheckForBirthdays>(routing, "Jitb.Employment.CoreLifecycle.Endpoint");
            provider.RegisterLegacyMapping<TimeToCheckLawson>(routing, "Jitb.Employment.Lawson.Sync.Endpoint");
            provider.RegisterLegacyMapping<TimeToCheckUltiPro>(routing, "Jitb.Employment.UltiPro.Sync.Endpoint"); provider.RegisterLegacyMapping<InjectMessage>(routing, "Jitb.Employment.MSMQListener.Endpoint");
            provider.RegisterLegacyMapping<TimeToRunFimSync>(routing, "Jitb.Employment.Fim.Endpoint");
            provider.RegisterLegacyMapping<CheckForProcessingEmployeeBorrows>(routing, "Jitb.Employment.CoreLifecycle.Endpoint");

            provider.RegisterLegacyMapping<CreateNewHire>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<TerminateEmployee>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<BorrowEmployee>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<EndLeaveOfAbsence>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<FinishOnboarding>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<StartLeaveOfAbsence>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<Contracts.Commands.Employment.TransferEmployee>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<CreateNewHireReply>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<TerminateEmployeeReply>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<BorrowEmployeeReply>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<EndLeaveOfAbsenceReply>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<FinishOnboardingReply>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<StartLeaveOfAbsenceReply>(routing, "Jitb.Employment.Endpoint");
            provider.RegisterLegacyMapping<TransferEmployeeReply>(routing, "Jitb.Employment.Endpoint");

            provider.RegisterLegacyMapping<ProcessPayroll>(routing, "Jitb.Payroll.Core.Endpoint");
            provider.RegisterLegacyMapping<SendEmail>(routing, "Jitb.SharedServices.Notifications.Endpoint");
            provider.RegisterLegacyMapping<UploadFtpFile>(routing, "Jitb.ItOperations.FileIO.Endpoint");


            provider.AddLegacySubscriptionRoute<IGotAPayloadAcknowledgement>(routing, "Jitb.SharedServices.Erestaurant.PayloadRouting.Endpoint");
            provider.AddLegacySubscriptionRoute<IFailedAnErestaurantPayloadAfterAllRetries>(routing, "Jitb.SharedServices.Erestaurant.Sync.Endpoint");

            //HarriInbound
            provider.AddLegacySubscriptionRoute<GetAllEmployees>(routing, "Jitb.Employment.HarriInbound.Endpoint");
            provider.AddLegacySubscriptionRoute<NewHarriEmployeeFound>(routing, "Jitb.Employment.HarriInbound.Endpoint");
            provider.AddLegacySubscriptionRoute<StartHarriInboundSyncSaga>(routing, "Jitb.Employment.HarriInbound.Endpoint");

            return routing;
        }
    }
}