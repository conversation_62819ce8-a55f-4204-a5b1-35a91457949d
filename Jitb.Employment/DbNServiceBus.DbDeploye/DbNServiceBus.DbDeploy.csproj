﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2CC7D907-A708-49A4-AD33-EB7A96CEF21C}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>DbNServiceBus.DbDeploy</RootNamespace>
    <AssemblyName>DbNServiceBus.DbDeploy</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Specflow2|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Specflow2\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=8.1.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.8.1.1\lib\net461\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.OpenPgp, Version=1.8.1.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.OpenPGP.1.8.1\lib\net20\BouncyCastle.OpenPgp.dll</HintPath>
    </Reference>
    <Reference Include="dbup-core, Version=4.4.0.0, Culture=neutral, PublicKeyToken=4b419c53bdfd4cbf, processorArchitecture=MSIL">
      <HintPath>..\packages\dbup-core.4.4.0\lib\net45\dbup-core.dll</HintPath>
    </Reference>
    <Reference Include="dbup-sqlserver, Version=4.4.0.0, Culture=neutral, PublicKeyToken=4b419c53bdfd4cbf, processorArchitecture=MSIL">
      <HintPath>..\packages\dbup-sqlserver.4.4.0\lib\net46\dbup-sqlserver.dll</HintPath>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.CommonLibrary, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Jitb.CommonLibrary.2022.11.22.4\lib\net461\Jitb.CommonLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Services.AppAuthentication, Version=1.3.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Services.AppAuthentication.1.3.1\lib\net472\Microsoft.Azure.Services.AppAuthentication.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Clients.ActiveDirectory, Version=4.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Clients.ActiveDirectory.4.3.0\lib\net45\Microsoft.IdentityModel.Clients.ActiveDirectory.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.4.0.4.4000\lib\net40\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.5.11\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet, Version=2020.0.1.0, Culture=neutral, PublicKeyToken=1cee9f8bde3db106, processorArchitecture=MSIL">
      <HintPath>..\packages\SSH.NET.2020.0.1\lib\net40\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap, Version=4.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\StructureMap.4.7.1\lib\net45\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Scripts\00100.0020 - Load ItOpsAction Data.sql" />
    <EmbeddedResource Include="Scripts\00100.0030 - ItOpsTable Create.sql" />
    <EmbeddedResource Include="Scripts\00200.0030 - Create Trigger tr_Persona_Update.sql" />
    <EmbeddedResource Include="Scripts\00200.0020 - Create Trigger tr_Persona_Insert.sql" />
    <EmbeddedResource Include="Scripts\00100.0070 - PersonaAudit Create.sql" />
    <EmbeddedResource Include="Scripts\00200.0010 - Create Trigger PersonaAuditTrigger.sql" />
    <EmbeddedResource Include="Scripts\00100.0080 - Persona Add Columns.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
    <EmbeddedResource Include="Scripts\00100.0005 - Franchisee Create.sql" />
    <EmbeddedResource Include="Scripts\00100.0005 - EncryptionProfile Create.sql" />
    <Content Include="Scripts\00100.0010 - Location Create.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00900.0020 - vuSailpointEmployee_AboveStore.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0060 - Persona Create.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00900.0010 - vuSailpointContractor.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00900.0030 - vuSailpointAboveStoreCombined.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0100 - PersonaAdLink Create.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0090 - Create PersonaAdLink.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0010 - ItOpsAction Create.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0010 - ItOpsOutstandingUpdate Create.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0040  - Load ItOpsTable Data.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0050 - Foreign Keys for ItOpsOutstandingUpdate.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0010 - Employee Create.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0010 - Jobcodes Create.sql" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Scripts\00100.0010 - EmployeeLocation Create.sql" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\OctoPack.3.6.5\build\OctoPack.targets" Condition="Exists('..\packages\OctoPack.3.6.5\build\OctoPack.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\OctoPack.3.6.5\build\OctoPack.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\OctoPack.3.6.5\build\OctoPack.targets'))" />
  </Target>
</Project>