﻿SET IDENTITY_INSERT [dbo].[ItopsAction] ON
if NOT EXISTS (Select * from dbo.[ItopsAction] where [ActionName] = 'Insert')
INSERT [dbo].[ItopsAction] ([ActionId], [ActionName], [CreateDate],  [ModifiedDate], CreatedUser) VALUES(1, N'Insert', GetDate(), GetDate(), 'Load')
GO

if NOT EXISTS (Select * from dbo.[ItopsAction] where [ActionName] = 'Delete')
INSERT[dbo].[ItopsAction] ([ActionId], [ActionName], [CreateDate],  [ModifiedDate], CreatedUser) VALUES(2, N'Delete', GetDate(), GetDate(),'Load')
GO

if NOT EXISTS (Select * from dbo.[ItopsAction] where [ActionName] = 'Update')
INSERT[dbo].[ItopsAction] ([ActionId], [ActionName], [CreateDate], [ModifiedDate], CreatedUser) VALUES(3, N'Update', GetDate(), GetDate(), 'Load')
GO
SET IDENTITY_INSERT [dbo].[ItopsAction] OFF
