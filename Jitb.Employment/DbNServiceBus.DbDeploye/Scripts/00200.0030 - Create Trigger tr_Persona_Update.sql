﻿CREATE OR ALTER TRIGGER [dbo].[tr_Persona_Update]
ON  [dbo].[Persona]
AFTER Update
AS 
BEGIN
set nocount on
update Persona 
set ModifyDate = getdate()
from inserted i
where persona.personaid = i.personaid
--	and persona.AdDistinguishedName + persona.AdEmployeeId + persona.AdFirstName + persona.AdLastName + persona.AdEmail + persona.AdUserPrincipalName + persona.AdNetworkId + cast(persona.AdAccountExpires as nvarchar) + Persona.AdCompany + persona.adDepartment + persona.AdOffice + cast (Persona.IsInActiveDirectory as char)
--	= i.AdDistinguishedName + i.AdEmployeeId + i.AdFirstName + i.AdLastName + i.AdEmail + i.AdUserPrincipalName + i.AdNetworkId + cast(i.AdAccountExpires as nvarchar) + i.AdCompany + i.adDepartment + i.AdOffice + cast (i.IsInActiveDirectory as char)
END
GO

ALTER TABLE [dbo].[Persona] ENABLE TRIGGER [tr_Persona_Update]
GO