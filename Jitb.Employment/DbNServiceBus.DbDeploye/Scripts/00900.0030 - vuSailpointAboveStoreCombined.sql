﻿CREATE OR ALTER VIEW [vuSailpointAboveStoreCombined]
AS
SELECT [personaId]
,[eisId]
,[hremployeeid]
,[NetworkId]
,[EmailAddress]
,[jobcode]
,[firstname]
,[lastname]
,[NickName]
,[CalculatedNameNickName]
,[CurrentStatus]
,[ManagerPersonaId]
,[LocationNumber]
,[Address1]
,[HRDepartmentCode]
,[CostCenterCode]
,[HireDate]
,[PostalCode]
,[Country]
,[City]
,[State]
,[TerminationDate]
,[FimEmailPreferenceCode]
,[CellPhoneNumber]
,[WorkPhoneNumber]
,[division]
,[AdAccountExpires]
,[AdCompany]
,[AdDepartment]
,[AdOffice]
,[IsInActiveDirectory]
,[IsEnabled]
FROM [vuSailpointEmployee_AboveStore]
union
SELECT [personaid]
,[CtrId]
,[HREmployeeId]
,AdNetworkId
,AdEmail
,[Jobcode]
,AdFirstName
,AdLastName
,[NickName]
,[CalculatedNameNickName]
,[Currentstatus]
--     ,[HrSupervisorId]
,[ManagerId]
,[LocationNumber]
,[Address1]
,[HrDepartmentCode]
,[CostCenterCode]
,[HireDate]
,[PostalCode]
,[Country]
,[City]
,[State]
,[TerminationDate]
,[FimEmailPreferenceCode]
,[CellPhoneNumber]
,[WorkPhoneNumber]
,[Division]
,[AdAccountExpires]
,[AdCompany]
,[AdDepartment]
,[AdOffice]
,[IsInActiveDirectory]
,[IsEnabled]
FROM [vuSailpointContractor]
  
GO