﻿SET IDENTITY_INSERT [dbo].[ItopsTable] ON 
GO
if NOT EXISTS (Select * from dbo.ItopsTable where tablename = 'tblContractor')
INSERT [dbo].[ItopsTable] ([TableId], [TableName], [CreateDate], [CreatedUser], [ModifiedDate]) VALUES (1, N'tblContractor', GetDate(), 'Load', GetDate())
GO
if NOT EXISTS (Select * from dbo.ItopsTable where tablename = 'Employee')
INSERT [dbo].[ItopsTable] ([TableId], [TableName], [CreateDate], [CreatedUser], [ModifiedDate]) VALUES (2, N'Employee', GetDate(), 'Load', GetDate())
GO
SET IDENTITY_INSERT [dbo].[ItopsTable] OFF
GO