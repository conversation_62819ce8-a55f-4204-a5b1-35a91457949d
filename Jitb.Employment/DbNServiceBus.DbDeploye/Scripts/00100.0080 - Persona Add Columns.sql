﻿
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'AdFirstName')
ALTER TABLE[Persona]
ADD [AdFirstName] [nvarchar](255) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'AdLastName')
ALTER TABLE[Persona]
ADD  [AdLastName] [nvarchar](255) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'AdEmail')
ALTER TABLE[Persona]
ADD [AdEmail] [nvarchar](255) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'AdUserPrincipalName')
ALTER TABLE[Persona]
ADD [AdUserPrincipalName] [nvarchar](255) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'AdAccountExpires')
ALTER TABLE[Persona]
ADD [AdAccountExpires] [datetime] NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'AdCompany')
ALTER TABLE[Persona]
ADD [AdCompany] [nvarchar](30) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'AdDepartment')
ALTER TABLE[Persona]
ADD [AdDepartment] [nvarchar](30) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'AdOffice')
ALTER TABLE[Persona]
ADD [AdOffice] [nvarchar](30) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'IsInActiveDirectory')
ALTER TABLE[Persona]
ADD [IsInActiveDirectory] [bit] NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'IsEnabled')
ALTER TABLE[Persona]
ADD [IsEnabled] [bit] NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'CreateDate')
ALTER TABLE[Persona]
ADD [CreateDate] [datetime] NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Persona' and COLUMN_NAME = 'ModifyDate')
ALTER TABLE[Persona]
ADD [ModifyDate] [datetime] NULL
GO