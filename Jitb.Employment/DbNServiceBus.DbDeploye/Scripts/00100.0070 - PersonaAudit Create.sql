﻿
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'PersonaAudit')

CREATE TABLE[dbo].[PersonaAudit](

[AuditID][bigint] IDENTITY(1, 1) NOT NULL,

[MessageID] [nvarchar](255) NULL,
[AuditActionCode] [varchar](30) NULL,
[AuditSource] [varchar](30) NULL,
[AuditDateTimeStamp] [datetime] NULL,
[PersonaId] [bigint] NOT NULL,
[AdDistinguishedName] [nvarchar](255) NULL,
[AdEmployeeId] [nvarchar](255) NULL,
[AdFirstName] [nvarchar](255) NULL,
[AdLastName] [nvarchar](255) NULL,
[AdEmail] [nvarchar](255) NULL,
[AdUserPrincipalName] [nvarchar](255) NULL,
[AdNetworkId] [nvarchar](255) NULL,
[AdAccountExpires] [datetime] NULL,
[AdCompany] [nvarchar](30) NULL,
[AdDepartment] [nvarchar](30) NULL,
[AdOffice] [nvarchar](30) NULL,
[IsInActiveDirectory] [bit] NULL,
[IsEnabled] [bit] NULL,
CONSTRAINT[PK_PersonaAudit] PRIMARY KEY CLUSTERED 
(
[AuditID] ASC
)WITH(PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON[PRIMARY]
) ON[PRIMARY]
GO
