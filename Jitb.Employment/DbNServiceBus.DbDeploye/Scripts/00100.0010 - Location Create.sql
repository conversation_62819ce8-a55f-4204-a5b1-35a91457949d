﻿IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Location')

CREATE TABLE [dbo].[Location](
	[LocationNumber] [int] NULL,
	[LocationName] [varchar](30) NULL,
	[Address1] [varchar](30) NULL,
	[Address2] [varchar](30) NULL,
	[City] [varchar](30) NULL,
	[StateCode] [varchar](2) NULL,
	[ZipCode] [varchar](10) NULL,
	[BrandConceptCode] [varchar](1) NULL,
	[CountyName] [varchar](50) NULL,
	[DivisionCode] [varchar](8000) NULL,
	[RegionCode] [varchar](8000) NULL,
	[AreaCode] [varchar](8000) NULL,
	[PhoneNumber] [varchar](20) NULL,
	[ManagementTypeCode] [varchar](1) NULL,
	[OperationsOpenDate] [smalldatetime] NULL,
	[OperationsCloseDate] [smalldatetime] NULL,
	[TimeZone] [varchar](2) NULL,
	[StoreOpen] [varchar](1) NOT NULL,
	[EntityNumber] [int] NULL,
	[EntityName] [varchar](50) NULL,
	[DateTimeStamp] [datetime] NULL,
	[DaylightSave] [varchar](1) NULL,
	[OperatorName] [varchar](50) NULL,
	[EncryptionProfile_id] [bigint] NULL,
	[Franchisee_id] [bigint] NULL,
	[OrgCode] [int] NULL
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Location]  WITH CHECK ADD  CONSTRAINT [FK2FE121BE3B86E54F] FOREIGN KEY([Franchisee_id])
REFERENCES [dbo].[Franchisee] ([Id])
GO

ALTER TABLE [dbo].[Location] CHECK CONSTRAINT [FK2FE121BE3B86E54F]
GO

ALTER TABLE [dbo].[Location]  WITH CHECK ADD  CONSTRAINT [FK2FE121BE724F61F9] FOREIGN KEY([EncryptionProfile_id])
REFERENCES [dbo].[EncryptionProfile] ([Id])
GO

ALTER TABLE [dbo].[Location] CHECK CONSTRAINT [FK2FE121BE724F61F9]
GO


