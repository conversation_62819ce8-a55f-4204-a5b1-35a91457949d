﻿IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Jobcodes')

CREATE TABLE [dbo].[JobCodes](
	[JobCode] [varchar](10) NOT NULL,
	[JobCodeDescription] [varchar](50) NOT NULL,
	[JobClass] [varchar](10) NULL,
	[ActiveFlag] [char](1) NULL,
	[InStoreFlag] [char](1) NULL,
	[PosJobCode] [varchar](2) NULL,
	[ManagementType] [varchar](2) NULL,
	[ConceptType] [char](1) NULL,
	[JobRole] [varchar](10) NULL,
	[SourceApplication] [char](1) NULL,
	[LawsonJobClass] [varchar](10) NULL,
	[JobFamily] [varchar](50) NULL,
	[IsSalary] [bit] NOT NULL,
 CONSTRAINT [PK_JobCodes] PRIMARY KEY NONCLUSTERED 
(
	[JobCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[JobCodes] ADD  CONSTRAINT [DF_JobCodes_IsSalary]  DEFAULT ((0)) FOR [IsSalary]
GO


