﻿IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Employee')


CREATE TABLE [dbo].[Employee](
	[EmployeeID] [bigint] IDENTITY(1,1) NOT NULL,
	[BadgeID] [int] NULL,
	[Address1] [varchar](50) NULL,
	[Address2] [varchar](50) NULL,
	[City] [varchar](50) NULL,
	[StateCode] [char](2) NULL,
	[ZipCode] [char](10) NULL,
	[WorkPhoneNumber] [varchar](15) NULL,
	[HomePhoneNumber] [varchar](15) NULL,
	[CellPhoneNumber] [varchar](15) NULL,
	[NetworkID] [varchar](50) NULL,
	[FimEmailPreferenceCode] [varchar](10) NULL,
	[LawsonCompanyID] [int] NULL,
	[LawsonEmployeeID] [int] NULL,
	[LawsonSupervisorCode] [char](10) NULL,
	[LawsonSupervisorID] [int] NULL,
	[LawsonSuperKeyID] [bigint] NULL,
	[LawsonEmpStatus] [varchar](5) NULL,
	[LawsonDepartmentCode] [varchar](10) NULL,
	[LawsonProcessLevelCode] [varchar](5) NULL,
	[LawsonCostCenter] [varchar](10) NULL,
	[eRestaurantEmployeeID] [varchar](30) NULL,
	[eRestaurantSupervisorID] [varchar](30) NULL,
	[HREmployeeIdentityID] [varchar](15) NULL,
	[HREmployeeID] [numeric](9, 0) NULL,
	[HRSupervisorID] [numeric](9, 0) NULL,
	[HRDepartmentCode] [varchar](50) NULL,
	[HRDepartment] [varchar](50) NULL,
	[HRCompany] [varchar](30) NULL,
	[HRCompanyBrand] [varchar](30) NULL,
	[EligibleForRehire] [char](1) NULL,
	[SupervisorEmployeeID] [bigint] NULL,
	[HireDate] [date] NULL,
	[PreviousHireDate] [date] NULL,
	[TerminationDate] [date] NULL,
	[AdjustedHireDate] [date] NULL,
	[EmailAddress] [varchar](50) NULL,
	[ExemptStatus] [char](1) NULL,
	[PayGrade] [char](3) NULL,
	[SalaryClass] [char](1) NULL,
	[BirthDate] [date] NULL,
	[SSN] [varchar](11) NULL,
	[Sex] [char](1) NULL,
	[CurrentStatus] [char](2) NULL,
	[EmployeeType] [char](1) NULL,
	[MiddleInitial] [char](1) NULL,
	[FirstName] [varchar](50) NULL,
	[MiddleName] [varchar](50) NULL,
	[LastName] [varchar](50) NULL,
	[NickName] [varchar](50) NULL,
	[TrainingFlag] [nchar](10) NULL,
	[DomainName] [char](10) NULL,
	[JobCode] [varchar](10) NULL,
	[JobClass] [varchar](10) NULL,
	[CostCenterCode] [varchar](10) NULL,
	[DateTimeStamp] [datetime] NULL,
	[VersionNumber] [bigint] NULL,
	[JobCodeEffectiveDate] [date] NULL,
	[LeaveOfAbsenceDate] [date] NULL,
	[ExpectedLeaveOfAbsenceReturnDate] [date] NULL,
	[AlternateEmailAddress] [varchar](50) NULL,
	[CompanyId] [int] NULL,
	[HomeLocationNumber] [int] NULL,
	[CreatedDate] [datetime] NULL,
	[SSNSha256Digest] [varbinary](32) NULL,
	[LastNameSha256Digest] [varbinary](32) NULL,
	[BirthDateSha256Digest] [varbinary](32) NULL,
	[eRestaurantPayrollID] [varchar](30) NULL,
	[LastReceivedFromUltiPro] [datetime] NULL,
 CONSTRAINT [PK_Employee] PRIMARY KEY CLUSTERED 
(
	[EmployeeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'The previous hire date for someone who is re-hired.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Employee', @level2type=N'COLUMN',@level2name=N'PreviousHireDate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Termination date if aplicable. Not last day worked.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Employee', @level2type=N'COLUMN',@level2name=N'TerminationDate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'FLSA Status. Exempt or Non-exempt.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Employee', @level2type=N'COLUMN',@level2name=N'ExemptStatus'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Pay type hourly or salaried.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Employee', @level2type=N'COLUMN',@level2name=N'SalaryClass'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Active (Full or Part Time), LOA, temp, pending, terminated, etc...' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Employee', @level2type=N'COLUMN',@level2name=N'CurrentStatus'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'M,S,F,N...' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Employee', @level2type=N'COLUMN',@level2name=N'EmployeeType'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Flag to indicate if employee is in training.  Y/N' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Employee', @level2type=N'COLUMN',@level2name=N'TrainingFlag'
GO


