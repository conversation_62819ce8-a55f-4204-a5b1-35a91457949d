﻿CREATE OR ALTER VIEW [dbo].[vuSailpointContractor]
AS
SELECT ad.personaid personaid
, <PERSON>tr<PERSON><PERSON>
, CtrAdEmpId AS HREmployeeId
, p.AdNetworkId
, p.AdEmail
, cast (NULL AS nvarchar) Jobcode
, 'Contractor' as JobCodeDescription
, p.AdFirst<PERSON>ame
, p.<PERSON>ame
, cast (NULL AS nvarchar) NickName, p.AdFirstName as CalculatedNameNickname
, case when CtrEnabled = 1 and CtrDeleted = 0 then '01' else '11' end Currentstatus
, CtrManager AS HrSupervisorId
, p2.personaid as ManagerId
, NULL AS LocationNumber
, cast (NULL AS nvarchar) Address1
, NULL AS HrDepartmentCode
, cast (NULL AS nvarchar) CostCenterCode
, cast(NULL AS date) HireDate
, NULL AS PostalCode
, NULL AS Country
, NULL AS City
, NULL AS State
, cast (NULL AS Date) TerminationDate
, 'CORPJ' AS FimEmailPreferenceCode
, cast (NULL as nvarchar)AS CellPhoneNumber
, cast (NULL AS nvarchar) WorkPhoneNumber
, NULL AS Division
, p.AdAccountExpires
, p.AdCompany
, p.AdDepartment
, p.AdOffice
, p.IsInActiveDirectory 
, p.IsEnabled

FROM tblContractor c
left join personaadLink ad on c.CtrId = ad.internalid
left join persona p on p.PersonaId = ad.personaid
left join persona p2 on c.CtrManager = p2.AdDistinguishedName
left join ItopsTable it on it.TableId = ad.InternalTable
where ad.personaid is not null
and TableName = 'tblContractor'

GO