﻿IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'EncryptionProfile')

CREATE TABLE [dbo].[EncryptionProfile](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[PgpPassphrase] [varchar](max) NULL,
	[PrivatePgpKey] [varchar](max) NULL,
	[PublicPgpKey] [varchar](max) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO