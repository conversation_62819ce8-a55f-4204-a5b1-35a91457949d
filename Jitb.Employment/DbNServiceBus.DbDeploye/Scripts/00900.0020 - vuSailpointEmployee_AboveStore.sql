﻿CREATE OR ALTER  VIEW [dbo].[vuSailpointEmployee_AboveStore]
AS
with employees as
(select ad.personaid
, e.employeeid
, e.hremployeeid
, p.AdNetworkId Networkid
, p.AdEmail EmailAddress
, e.jobcode
, j.JobCodeDescription
, p.AdFirstName Firstname
, p.AdLastName Lastname
, e.<PERSON>
, e.CurrentStatus
, e.H<PERSON>
, e2.employeeId supervisor
, ad2.personaId ManagerPersonaId
, ROW_NUMBER() OVER (PARTITION BY el.employeeid order by el.activeflag desc, el.locationnumber ASC) row
, el.LocationNumber
, e.HRDepartmentCode
, e.CostCenterCode
, e.HireDate
, null PostalCode
, NULL Country
, NULL City
, NULL [State]
, e.TerminationDate
, e.FimEmailPreferenceCode
, e.CellPhoneNumber
, e.WorkPhoneNumber
, NULL division
, AdAccountExpires
, AdCompany
, AdDepartment
, AdOffice
, IsInActiveDirectory 
, IsEnabled
from employee e
join jobcodes j on e.JobCode = j.<PERSON>
join employeelocation el on e.employeeid = el.employeeid
join PersonaAdLink ad on e.EmployeeID = ad.internalId
left join persona p on p.PersonaId = ad.PersonaId
join ItopsTable t on ad.InternalTable = t.TableId
left join employee e2 on e2.hremployeeid = e.HRSupervisorID
left join PersonaAdLink ad2 on e2.EmployeeID = ad2.internalId and ad2.internalTable = 2
left join ItopsTable t2 on ad2.InternalTable = t2.TableId
where InStoreFlag = 'N'
and e.CompanyId = 1
and t.Tablename = 'employee'
and coalesce(t2.tablename,'employee') = 'employee'
and coalesce (e.hrEmployeeid,0) > 0
and ((e.currentstatus in ('01', '03')
or e.currentstatus = '11' and e.TerminationDate >= getdate() - 120))
)

select
personaId
,employeeid eisId
,hremployeeid
, NetworkId
, EmailAddress 
,jobcode
,JobCodeDescription
, firstname
, lastname
,NickName
,coalesce (NickName, Firstname) CalculatedNameNickname
,CurrentStatus
,ManagerPersonaId
,employees.LocationNumber
,Address1
,HRDepartmentCode
,CostCenterCode
,HireDate
,PostalCode
,Country
,employees.City
,[State]
,TerminationDate
,FimEmailPreferenceCode
,CellPhoneNumber
,WorkPhoneNumber
,division
, AdAccountExpires
, AdCompany
, AdDepartment
, AdOffice
,IsInActiveDirectory 
, IsEnabled
from employees
join Location l on l.LocationNumber = employees.Locationnumber
where row= 1



GO
