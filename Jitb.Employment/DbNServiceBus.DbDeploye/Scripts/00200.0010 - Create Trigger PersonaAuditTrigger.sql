﻿CREATE OR ALTER TRIGGER [dbo].[PersonaAuditTrigger]
ON [dbo].[Persona]
After Update, Insert, Delete
AS
BEGIN
	SET NOCOUNT ON
	DECLARE @Action Varchar(15);
	SET @Action = 'Insert'
	IF EXISTS (SELECT * FROM DELETED)
	BEGIN
		 SET @Action = 
            CASE
                WHEN EXISTS(SELECT * FROM INSERTED) THEN 'Update' -- Set Action to Updated.
                ELSE 'Deleted' -- Set Action to Deleted.       
            END
	END
	IF @Action = 'Deleted' 
	BEGIN
		INSERT INTO [dbo].[PersonaAudit]

	([AuditActionCode] 
	,[AuditDateTimeStamp]
	,[PersonaId] 
	,[AdDistinguishedName]
	,[AdEmployeeId]
	,[AdFirstName]
	,[AdLastName]
	,[AdEmail]
	,[AdUserPrincipalName]
	,[AdNetworkId]
	,[AdAccountExpires]
	,[AdCompany] 
	,[AdDepartment] 
	,[AdOffice]
	,[IsInActiveDirectory]
	,[IsEnabled]
	)
		 
	SELECT
           @Action
		   ,GETDATE()
	,[PersonaId] 
	,[AdDistinguishedName]
	,[AdEmployeeId]
	,[AdFirstName]
	,[AdLastName]
	,[AdEmail]
	,[AdUserPrincipalName]
	,[AdNetworkId]
	,[AdAccountExpires]
	,[AdCompany] 
	,[AdDepartment] 
	,[AdOffice]
	,[IsInActiveDirectory]
	,[IsEnabled]
		 FROM DELETED
	END
	ELSE
		IF NOT EXISTS(SELECT * FROM INSERTED) RETURN; -- Nothing updated or inserted.
		INSERT INTO [dbo].PersonaAudit
			([AuditActionCode]
           ,[AuditDateTimeStamp]
	,[PersonaId] 
	,[AdDistinguishedName]
	,[AdEmployeeId]
	,[AdFirstName]
	,[AdLastName]
	,[AdEmail]
	,[AdUserPrincipalName]
	,[AdNetworkId]
	,[AdAccountExpires]
	,[AdCompany] 
	,[AdDepartment] 
	,[AdOffice]
	,[IsInActiveDirectory]
	,[IsEnabled]
	)
	SELECT
           @Action
		   ,GETDATE()
		  	,[PersonaId] 
	,[AdDistinguishedName]
	,[AdEmployeeId]
	,[AdFirstName]
	,[AdLastName]
	,[AdEmail]
	,[AdUserPrincipalName]
	,[AdNetworkId]
	,[AdAccountExpires]
	,[AdCompany] 
	,[AdDepartment] 
	,[AdOffice]
	,[IsInActiveDirectory]
	,[IsEnabled]
	FROM INSERTED
	END





GO

ALTER TABLE [dbo].[Persona] ENABLE TRIGGER [PersonaAuditTrigger]
GO
