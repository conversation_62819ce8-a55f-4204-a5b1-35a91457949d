﻿IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Franchisee')

CREATE TABLE [dbo].[Franchisee](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[FranchiseeName] [nvarchar](255) NULL,
	[EncryptionProfile_id] [bigint] NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Franchisee]  WITH CHECK ADD  CONSTRAINT [FKAA9F7990724F61F9] FOREIGN KEY([EncryptionProfile_id])
REFERENCES [dbo].[EncryptionProfile] ([Id])
GO

ALTER TABLE [dbo].[Franchisee] CHECK CONSTRAINT [FKAA9F7990724F61F9]
GO