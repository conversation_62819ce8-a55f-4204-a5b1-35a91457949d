﻿IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME = 'FK281487DA5E804B63')
BEGIN
ALTER TABLE [dbo].[ItopsOutstandingUpdate]  WITH CHECK ADD  CONSTRAINT [FK281487DA5E804B63] FOREIGN KEY([ActionId])
REFERENCES [dbo].[ItopsAction] ([ActionId])

ALTER TABLE [dbo].[ItopsOutstandingUpdate] CHECK CONSTRAINT [FK281487DA5E804B63]
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME = 'FK281487DA83E72C97')
BEGIN
ALTER TABLE [dbo].[ItopsOutstandingUpdate]  WITH CHECK ADD  CONSTRAINT [FK281487DA83E72C97] FOREI<PERSON><PERSON> KEY([TableId])
REFERENCES [dbo].[ItopsTable] ([TableId])

ALTER TABLE [dbo].[ItopsOutstandingUpdate] CHECK CONSTRAINT [FK281487DA83E72C97]
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME = 'FK6B9261803E616E40')
BEGIN
ALTER TABLE [dbo].[ItopsOutstandingUpdate]  WITH CHECK ADD  CONSTRAINT [FK6B9261803E616E40] FOREIGN KEY([ActionId])
REFERENCES [dbo].[ItopsAction] ([ActionId])

ALTER TABLE [dbo].[ItopsOutstandingUpdate] CHECK CONSTRAINT [FK6B9261803E616E40]
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME = 'FK6B926180A5E4B3CF')
BEGIN
ALTER TABLE [dbo].[ItopsOutstandingUpdate]  WITH CHECK ADD  CONSTRAINT [FK6B926180A5E4B3CF] FOREIGN KEY([TableId])
REFERENCES [dbo].[ItopsTable] ([TableId])

ALTER TABLE [dbo].[ItopsOutstandingUpdate] CHECK CONSTRAINT [FK6B926180A5E4B3CF]
END
GO
