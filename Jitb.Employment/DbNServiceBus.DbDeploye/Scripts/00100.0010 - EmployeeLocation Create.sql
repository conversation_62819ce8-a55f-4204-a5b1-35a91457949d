﻿IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'EmployeeLocation')

CREATE TABLE [dbo].[EmployeeLocation](
	[EmployeeLocationID] [bigint] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [bigint] NOT NULL,
	[LocationNumber] [int] NOT NULL,
	[PrimaryFlag] [char](1) NOT NULL,
	[ActiveFlag] [char](1) NULL,
	[BeginDate] [date] NULL,
	[EndDate] [date] NULL,
	[DateTimeStamp] [datetime] NULL,
 CONSTRAINT [PK_EmployeeLocation] PRIMARY KEY CLUSTERED 
(
	[EmployeeLocationID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[EmployeeLocation]  WITH CHECK ADD  CONSTRAINT [FK81EAE1C37E4C7197] FOREIGN KEY([EmployeeID])
REFERENCES [dbo].[Employee] ([EmployeeID])
GO

ALTER TABLE [dbo].[EmployeeLocation] CHECK CONSTRAINT [FK81EAE1C37E4C7197]
GO

ALTER TABLE [dbo].[EmployeeLocation]  WITH CHECK ADD  CONSTRAINT [FK81EAE1C3ABF9EB38] FOREIGN KEY([EmployeeID])
REFERENCES [dbo].[Employee] ([EmployeeID])
GO

ALTER TABLE [dbo].[EmployeeLocation] CHECK CONSTRAINT [FK81EAE1C3ABF9EB38]
GO

ALTER TABLE [dbo].[EmployeeLocation]  WITH CHECK ADD  CONSTRAINT [FK81EAE1C3B9C6394A] FOREIGN KEY([EmployeeID])
REFERENCES [dbo].[Employee] ([EmployeeID])
GO

ALTER TABLE [dbo].[EmployeeLocation] CHECK CONSTRAINT [FK81EAE1C3B9C6394A]
GO

ALTER TABLE [dbo].[EmployeeLocation]  WITH CHECK ADD  CONSTRAINT [FKFB95C992C4B75962] FOREIGN KEY([EmployeeID])
REFERENCES [dbo].[Employee] ([EmployeeID])
GO

ALTER TABLE [dbo].[EmployeeLocation] CHECK CONSTRAINT [FKFB95C992C4B75962]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Where employee is working with this job code.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmployeeLocation', @level2type=N'COLUMN',@level2name=N'LocationNumber'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'What type of location, home, borrrow, streatch.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmployeeLocation', @level2type=N'COLUMN',@level2name=N'PrimaryFlag'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Active or not active. For ease of use in query.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmployeeLocation', @level2type=N'COLUMN',@level2name=N'ActiveFlag'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Effective period start date.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmployeeLocation', @level2type=N'COLUMN',@level2name=N'BeginDate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'End of effective period.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmployeeLocation', @level2type=N'COLUMN',@level2name=N'EndDate'
GO


