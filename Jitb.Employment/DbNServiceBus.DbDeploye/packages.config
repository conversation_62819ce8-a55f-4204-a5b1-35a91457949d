﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="8.1.1" targetFramework="net472" />
  <package id="BouncyCastle.OpenPGP" version="1.8.1" targetFramework="net472" />
  <package id="dbup" version="4.4.0" targetFramework="net472" />
  <package id="dbup-core" version="4.4.0" targetFramework="net472" />
  <package id="dbup-sqlserver" version="4.4.0" targetFramework="net472" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net472" />
  <package id="Jitb.CommonLibrary" version="2022.11.22.4" targetFramework="net472" />
  <package id="Microsoft.Azure.Services.AppAuthentication" version="1.3.1" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Clients.ActiveDirectory" version="4.3.0" targetFramework="net472" />
  <package id="NHibernate" version="4.0.4.4000" targetFramework="net472" />
  <package id="NLog" version="4.5.11" targetFramework="net472" />
  <package id="OctoPack" version="3.6.5" targetFramework="net472" developmentDependency="true" />
  <package id="SSH.NET" version="2020.0.1" targetFramework="net472" />
  <package id="StructureMap" version="4.7.1" targetFramework="net472" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net472" />
</packages>