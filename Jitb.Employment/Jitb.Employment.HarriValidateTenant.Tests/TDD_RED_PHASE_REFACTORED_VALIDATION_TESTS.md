# TDD Red Phase: Refactored HarriValidateTenant System Tests

## Overview

This document outlines the comprehensive failing unit tests created for the refactored HarriValidateTenant system that follows the new pseudo code structure. These tests are written in **TDD Red phase** and **WILL FAIL** until the implementation is created.

## Test Strategy

### Core Design Principles Tested

1. **ValidationRoutine()** - Orchestrates overall validation process
2. **ValueTuple Return Types** - All methods return structured results with (result text, additional details)
3. **Tenant-Focused Processing** - Groups locations by tenant, handles null tenants appropriately
4. **Skip Logic Implementation** - Built-in logic for skipping manager loading based on existing employee counts
5. **Environment Independence** - All tests use mocked dependencies, no external API calls

## Created Test Files

### 1. RefactoredTenantValidationServiceTests.cs

**Primary test class covering core functionality:**

#### ValidationRoutine Tests
- ✅ `ValidationRoutine_WhenCalledWithMixedTenants_ProcessesAllTenantsAndGeneratesReport`
- ✅ `ValidationRoutine_WhenAllTenantsAreNull_AddsAllLocationsToFailReport`
- ✅ `ValidationRoutine_WhenNoTenantsFound_CompletesSuccessfully`

#### ValidateTenantsAsync Tests
- ✅ `ValidateTenantsAsync_WhenSkipConditionsMet_SkipsLoadManagerAndReturns`
- ✅ `ValidateTenantsAsync_WhenSkipConditionsNotMet_ExecutesLoadManager`
- ✅ `ValidateTenantsAsync_WhenMultipleLocations_ProcessesEachLocation`

#### GetTenantsForAllLocationsAsync Tests
- ✅ `GetTenantsForAllLocationsAsync_WhenCalledWithValidData_ReturnsExpectedValueTuples`
- ✅ `GetTenantsForAllLocationsAsync_WhenNoLocationsConfigured_ReturnsEmptyArray`

#### GetEmployeesAsync Tests
- ✅ `GetEmployeesAsync_WhenEmployeesExist_ReturnsCountAndFirstEmployee`
- ✅ `GetEmployeesAsync_WhenNoEmployeesFound_ReturnsZeroCountAndNull`
- ✅ `GetEmployeesAsync_WhenApiThrowsException_PropagatesException`

#### LoadManagerAsync Tests
- ✅ `LoadManagerAsync_WhenSkipConditionsMet_ReturnsSkippedResult`
- ✅ `LoadManagerAsync_WhenSkipConditionsNotMet_LoadsManagerAndReturnsResult`
- ✅ `LoadManagerAsync_WhenManagerLoadFails_ReturnsFailResult`

#### ReadEmployeeAsync Tests
- ✅ `ReadEmployeeAsync_WhenWasSkipped_ReadsFirstEmployeeFromGetEmployees`
- ✅ `ReadEmployeeAsync_WhenWasNotSkipped_ReadsLoadedManagerEmployee`
- ✅ `ReadEmployeeAsync_WhenReadFails_ReturnsFailResult`

#### ValidatePositionsAsync Tests
- ✅ `ValidatePositionsAsync_WhenPositionsValid_ReturnsPassResult`
- ✅ `ValidatePositionsAsync_WhenPositionsInvalid_ReturnsFailResult`
- ✅ `ValidatePositionsAsync_WhenValidationThrowsException_ReturnsFailResult`

#### Integration Tests
- ✅ `FullValidationWorkflow_WhenMixedScenarios_ProcessesCorrectly`

### 2. RefactoredTenantValidationServiceEdgeCaseTests.cs

**Comprehensive edge case and boundary condition testing:**

#### Boundary Condition Tests
- ✅ `GetEmployeesAsync_WhenExactly50Employees_ReturnsCorrectValueTuple`
- ✅ `LoadManagerAsync_WhenExactlyAtMinimumCountToSkip_SkipsCorrectly`
- ✅ `LoadManagerAsync_WhenOneBelowMinimumCountToSkip_DoesNotSkip`

#### Null and Empty Data Tests
- ✅ `ValidateTenantsAsync_WhenEmptyLocationsList_CompletesWithoutError`
- ✅ `ReadEmployeeAsync_WhenEmployeeToReadIsNull_HandlesGracefully`
- ✅ `GetTenantsForAllLocationsAsync_WhenTenantNameLookupReturnsNull_UsesTenantId`

#### Configuration Edge Cases
- ✅ `LoadManagerAsync_WhenSkipConfigurationDisabled_AlwaysLoadsManager`
- ✅ `LoadManagerAsync_WhenMinimumCountToSkipIsZero_SkipsIfAnyEmployeesExist`

#### Concurrent Processing Tests
- ✅ `ValidateTenantsAsync_WhenProcessingConcurrently_MaintainsDataIntegrity`

#### Error Propagation Tests
- ✅ `ValidationRoutine_WhenGetTenantsThrowsException_PropagatesException`
- ✅ `ValidatePositionsAsync_WhenPartialFailure_ReturnsWarningResult`

#### ValueTuple Structure Validation
- ✅ `AllMethods_WhenCalled_ReturnCorrectValueTupleStructures`

### 3. IRefactoredTenantValidationService.cs

**Interface definition with complete method signatures:**

```csharp
public interface IRefactoredTenantValidationService
{
    Task ValidationRoutine();
    Task ValidateTenantsAsync(string tenantId, string tenantName, List<int> locations);
    Task<(string tenantId, string tenantName, List<int> locations)[]> GetTenantsForAllLocationsAsync();
    Task<(int count, object firstEmployee)> GetEmployeesAsync(string tenantId, int locationId);
    Task<(string result, string details)> LoadManagerAsync(string tenantId, int locationId, 
        int existingCount, bool skipWhenPreexistingFound, int minimumCountToSkip);
    Task<(string result, string details)> ReadEmployeeAsync(string tenantId, int locationId, 
        bool wasSkipped, object employeeToRead);
    Task<(string result, string details)> ValidatePositionsAsync(string tenantId, int locationId);
}
```

## Expected Test Failures

### These tests WILL FAIL because:

1. **IRefactoredTenantValidationService interface** - Does not exist yet
2. **RefactoredTenantValidationService class** - Does not exist yet
3. **Method implementations** - None of the new methods exist
4. **Constructor signatures** - New service constructor not implemented
5. **ValueTuple return types** - Current implementation doesn't return structured results

## TDD Red Phase Validation

### To verify TDD approach is working:

1. **Build the test project** - Should compile successfully
2. **Run the tests** - ALL new tests should fail with "Method not found" or similar errors
3. **Implement interface and skeleton methods** - Tests should start failing with assertion errors
4. **Implement full logic** - Tests should begin passing one by one

### Key Validation Points:

#### Method Signatures Match Requirements:
- ✅ `ValidationRoutine()` - Main orchestration method
- ✅ `ValidateTenantsAsync(string, string, List<int>)` - Core tenant validation
- ✅ `GetTenantsForAllLocationsAsync()` - Returns tenant ValueTuples
- ✅ `GetEmployeesAsync(string, int)` - Returns (count, firstEmployee) ValueTuple
- ✅ `LoadManagerAsync(...)` - Skip logic with (result, details) ValueTuple
- ✅ `ReadEmployeeAsync(...)` - Employee reading with (result, details) ValueTuple
- ✅ `ValidatePositionsAsync(string, int)` - Position validation with (result, details) ValueTuple

#### Pseudo Code Structure Implementation:
- ✅ **Tenant list as ValueTuples** - (tenantId, tenantName, List<int> locations)
- ✅ **Null tenant handling** - Add to report with 'Fail' result
- ✅ **Skip logic** - `skipWhenPreexistingFound && existingEmployeeCount >= minimumCountToSkip`
- ✅ **Employee reading logic** - First employee if skipped, loaded manager if not
- ✅ **ValueTuple results** - All methods return structured (result, details) format

#### Mock Dependencies Verification:
- ✅ `IHarriTenantByLocationRepository` - Tenant-location mapping
- ✅ `IHarriTenantRepository` - Tenant details and names
- ✅ `ILocationValidationService` - Existing validation logic
- ✅ `IConfigurationService` - Skip settings and location lists
- ✅ `ITenantValidationResultCapture` - Report building
- ✅ `ICallHarriWebServiceProvider` - Harri API calls

## Next Steps (Green Phase)

1. **Create `RefactoredTenantValidationService` class**
2. **Implement interface methods with skeleton logic**
3. **Run tests to see assertion failures**
4. **Implement each method to pass tests**
5. **Refactor for clean code organization**

## Test Coverage Summary

- **Total Test Methods**: 35+ comprehensive test cases
- **Core Functionality**: ValidationRoutine, ValidateTenantsAsync flow
- **Boundary Conditions**: Exact threshold values, empty collections
- **Error Handling**: API failures, null data, configuration issues
- **Concurrency**: Multi-tenant processing validation
- **ValueTuple Validation**: Correct return type structures
- **Integration Scenarios**: End-to-end workflow testing

This comprehensive test suite ensures that the refactored HarriValidateTenant system will be implemented according to the exact pseudo code specifications with proper error handling, skip logic, and ValueTuple-based result structures.