# TDD Red Phase: Tenant-Focused Processing Tests

## Overview

This document summarizes the comprehensive failing unit tests created for single-location-per-tenant processing logic following TDD Red phase principles. These tests **WILL FAIL** until the corresponding implementation is created, which is the expected behavior in TDD.

## Test Files Created

### 1. `/Services/TenantValidationServiceTests.cs`
**Purpose**: Tests for the new `ITenantValidationService` interface and `TenantValidationService` class that will handle tenant-specific validation.

**Key Tests**:
- `ValidateLocationForTenantAsync_WhenCalledWithValidTenant_PerformsAllValidationSteps`
- `ValidateLocationForTenantAsync_WhenTenantIsNull_HandlesProperly` 
- `ValidateLocationForTenantAsync_WhenLocationNotFoundForTenant_LogsWarning`
- `ValidateLocationForTenantAsync_WhenLocationValidationFails_PropagatesException`
- `ValidateLocationForTenantAsync_WhenCalledWithNullParameters_ThrowsArgumentException`
- `ValidateLocationForTenantAsync_WhenCalledConcurrently_HandlesMultipleTenants`
- `ValidateLocationForTenantAsync_WhenConfigurationDisablesValidation_SkipsValidation`

**Expected Dependencies** (to be created):
```csharp
public interface ITenantValidationService
{
    Task ValidateLocationForTenantAsync(string tenantId, int locationId);
}

public class TenantValidationService : ITenantValidationService
{
    // Implementation needed
}
```

### 2. `/Workflows/TenantProcessingWorkflowTests.cs`
**Purpose**: Tests for the complete tenant processing workflow that orchestrates tenant extraction and validation.

**Key Tests**:
- `ProcessTenants_WhenGivenTenantFirstLocationMappings_ProcessesOnlyFirstLocationPerTenant`
- `ProcessTenants_WhenProcessingTenants_TracksUntestedLocations`
- `ProcessTenants_WhenTenantValidationFails_ContinuesWithOtherTenants`
- `IntegratedTenantProcessing_WhenGivenLocationList_ProcessesTenantsCorrectly`
- `ProcessTenants_WhenNoTenants_ReturnsEmptyResult`
- `ProcessTenants_WhenWorkflowDisabled_SkipsProcessing`
- `ProcessTenants_WhenProcessingConcurrently_HandlesMultipleTenantsSimultaneously`

**Expected Dependencies** (to be created):
```csharp
public interface ITenantProcessingWorkflowService
{
    Task<TenantProcessingResult> ProcessTenantsAsync(Dictionary<string, int> tenantFirstLocations);
    Task<TenantProcessingResult> ProcessFullWorkflowAsync(IEnumerable<int> allLocations);
}

public class TenantProcessingResult
{
    public Dictionary<string, bool> ProcessedTenants { get; set; }
    public Dictionary<string, bool> SuccessfulTenants { get; set; }
    public Dictionary<string, Exception> FailedTenants { get; set; }
    public IEnumerable<int> UntestedLocations { get; set; }
    // ... other properties
}
```

### 3. `/Integration/ProgramWorkflowIntegrationTests.cs`
**Purpose**: Integration tests that verify the expected changes to `Program.cs` for tenant-focused workflow.

**Key Tests**:
- `ProgramMain_WhenUsingNewTenantWorkflow_ProcessesTenantsInsteadOfLocations`
- `ProgramDIContainer_WhenConfigured_RegistersNewTenantServices`
- `ProgramMain_WhenTenantWorkflowDisabled_FallsBackToLocationProcessing`
- `ProgramMain_WhenTenantProcessingFails_ContinuesWithRemainingTenants`

## Current vs Target Architecture

### Current Architecture (Location-Focused)
```csharp
// Program.cs - Current implementation
foreach (var location in locations)
{
    await validationService.ValidateLocationAsync(location);
}
```

### Target Architecture (Tenant-Focused)
```csharp
// Program.cs - Target implementation
var tenantLocationMappings = tenantExtractionService.ExtractTenantsFromLocations(locations);
var tenantFirstLocations = tenantExtractionService.SelectFirstLocationPerTenant(tenantLocationMappings);

foreach (var tenant in tenantFirstLocations)
{
    await tenantValidationService.ValidateLocationForTenantAsync(tenant.Key, tenant.Value);
}
```

## Expected Test Scenarios

### Example Data Processing
**Input Locations**: [1234, 1235, 2345, 2346, 2347, 3456, 3457, 3458, 3459]

**Tenant Mappings**:
- `"(null)"`: [1234, 1235] → Process only 1234
- `"TenantA"`: [2345, 2346, 2347] → Process only 2345  
- `"TenantB"`: [3456, 3457, 3458, 3459] → Process only 3456

**Expected Results**:
- **Tested Locations**: [1234, 2345, 3456]
- **Untested Locations**: [1235, 2346, 2347, 3457, 3458, 3459]
- **Processing**: "Additional Locations: Not Tested" logged for each tenant

## Why These Tests Will Fail

### Compilation Failures Expected:
1. **`ITenantValidationService` interface doesn't exist**
2. **`TenantValidationService` class doesn't exist**  
3. **`ITenantProcessingWorkflowService` interface doesn't exist**
4. **`TenantProcessingWorkflowService` class doesn't exist**
5. **`TenantProcessingResult` class doesn't exist**
6. **Configuration extension methods don't exist**:
   - `IsTenantValidationEnabled()`
   - `IsTenantWorkflowEnabled()`
   - `GetMaxConcurrentTenants()`

### Behavioral Failures Expected:
1. **Program.cs still uses location-focused workflow**
2. **DI container doesn't register tenant services**
3. **No tenant-level exception handling**
4. **No tracking of untested locations**

## Implementation Targets

To make these tests pass, the following must be implemented:

### 1. Service Interfaces and Classes
- `ITenantValidationService` / `TenantValidationService`
- `ITenantProcessingWorkflowService` / `TenantProcessingWorkflowService`  
- `TenantProcessingResult` class

### 2. Configuration Extensions
- Add tenant-specific configuration methods to `IConfigurationService`
- Configuration settings for enabling/disabling tenant processing

### 3. Program.cs Updates
- Update DI container registration
- Replace location-focused loop with tenant-focused workflow
- Add tenant-level exception handling and logging
- Implement concurrency control at tenant level

### 4. Integration Logic
- Integrate with existing `ITenantExtractionService` 
- Delegate to existing `ILocationValidationService` for core validation
- Track and report untested locations per tenant

## Test Categories

All tests are marked with appropriate categories:
- `[TestCategory("TenantValidation")]`
- `[TestCategory("TenantWorkflow")]` 
- `[TestCategory("Integration")]`
- `[TestCategory("TDD_Red_Phase")]`

## Next Steps (TDD Green Phase)

1. **Implement service interfaces and classes** to resolve compilation errors
2. **Update Program.cs** to use tenant-focused workflow
3. **Add configuration methods** for tenant processing control
4. **Run tests** to verify they now pass
5. **Refactor implementation** as needed (TDD Refactor phase)

## Key Validation Points

These tests ensure:
- ✅ Each tenant processes exactly one location (the first one)
- ✅ Additional locations per tenant are tracked but not processed  
- ✅ All existing validation logic is preserved through delegation
- ✅ Proper error handling and logging at tenant level
- ✅ Tenant isolation (one tenant failure doesn't affect others)
- ✅ Concurrency control at tenant level instead of location level
- ✅ Configuration-driven workflow enabling/disabling

---

**Note**: This is the TDD Red phase where tests are intentionally written to fail. These tests define the expected behavior and API contracts before any implementation exists, ensuring that the implementation will be driven by the test requirements.