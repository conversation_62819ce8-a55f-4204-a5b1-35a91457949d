<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="MSTest.TestFramework" version="2.1.2" targetFramework="net48" />
  <package id="MSTest.TestAdapter" version="2.1.2" targetFramework="net48" />
  <package id="AutoFixture" version="4.18.0" targetFramework="net48" />
  <package id="Fare" version="2.1.1" targetFramework="net48" />
  <package id="Moq" version="4.16.1" targetFramework="net48" />
  <package id="Castle.Core" version="4.4.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="RestSharp" version="106.15.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
</packages>