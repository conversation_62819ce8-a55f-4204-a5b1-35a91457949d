using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using Jitb.Employment.HarriValidateTenant.Models;

namespace Jitb.Employment.HarriValidateTenant.Tests.Models
{
    /// <summary>
    /// Tests for TenantValidationResult model enhancements.
    /// These tests are written in TDD Red phase and WILL FAIL initially until implementation is updated.
    /// 
    /// User requirements:
    /// 1. TenantValidationResult should have TenantName property
    /// 2. Display should show tenant name instead of tenant ID when available
    /// 3. TenantName should be used for sorting and display purposes
    /// </summary>
    [TestClass]
    public class TenantValidationResultTests
    {
        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_ShouldHaveTenantNameProperty()
        {
            // Arrange & Act
            var result = new TenantValidationResult();

            // Assert
            // THIS WILL FAIL - TenantName property doesn't exist yet
            Assert.IsTrue(result.GetType().GetProperty("TenantName") != null,
                "TenantValidationResult should have a TenantName property");
            
            // Property should be settable
            var testTenantName = "Jack in the Box Corporate";
            result.TenantName = testTenantName;
            
            Assert.AreEqual(testTenantName, result.TenantName,
                "TenantName property should be settable and gettable");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_TenantNameProperty_ShouldAllowNullValue()
        {
            // Arrange
            var result = new TenantValidationResult();

            // Act
            // THIS WILL FAIL - TenantName property doesn't exist yet
            result.TenantName = null;

            // Assert
            Assert.IsNull(result.TenantName,
                "TenantName property should allow null values for locations without tenant mappings");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_TenantNameProperty_ShouldAllowEmptyString()
        {
            // Arrange
            var result = new TenantValidationResult();

            // Act
            // THIS WILL FAIL - TenantName property doesn't exist yet
            result.TenantName = string.Empty;

            // Assert
            Assert.AreEqual(string.Empty, result.TenantName,
                "TenantName property should allow empty strings");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_TenantNameProperty_ShouldHandleLongNames()
        {
            // Arrange
            var result = new TenantValidationResult();
            var longTenantName = "Very Long Tenant Name That Exceeds Normal Display Length - Corporate Headquarters Division";

            // Act
            // THIS WILL FAIL - TenantName property doesn't exist yet
            result.TenantName = longTenantName;

            // Assert
            Assert.AreEqual(longTenantName, result.TenantName,
                "TenantName property should handle long tenant names without truncation");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_ShouldPreferTenantNameOverTenantIdForDisplay()
        {
            // Arrange
            var result = new TenantValidationResult
            {
                TenantId = "6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e",
                // THIS WILL FAIL - TenantName property doesn't exist yet
                TenantName = "Jack in the Box Corporate"
            };

            // Act & Assert
            // Test that when both TenantId and TenantName are available, TenantName is preferred for display
            Assert.IsNotNull(result.TenantName,
                "TenantName should be available when provided");

            Assert.AreNotEqual(result.TenantId, result.TenantName,
                "TenantName should be different from TenantId (human readable vs GUID)");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_ShouldFallbackToTenantIdWhenTenantNameIsNull()
        {
            // Arrange
            var tenantId = "6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e";
            var result = new TenantValidationResult
            {
                TenantId = tenantId,
                // THIS WILL FAIL - TenantName property doesn't exist yet  
                TenantName = null
            };

            // Act
            // THIS WILL FAIL - GetDisplayName method doesn't exist yet
            var displayName = result.GetDisplayName();

            // Assert
            Assert.AreEqual(tenantId, displayName,
                "Should fallback to TenantId for display when TenantName is null");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_ShouldFallbackToTenantIdWhenTenantNameIsEmpty()
        {
            // Arrange
            var tenantId = "6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e";
            var result = new TenantValidationResult
            {
                TenantId = tenantId,
                // THIS WILL FAIL - TenantName property doesn't exist yet
                TenantName = string.Empty
            };

            // Act
            // THIS WILL FAIL - GetDisplayName method doesn't exist yet
            var displayName = result.GetDisplayName();

            // Assert
            Assert.AreEqual(tenantId, displayName,
                "Should fallback to TenantId for display when TenantName is empty");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_ShouldFallbackToTenantIdWhenTenantNameIsWhitespace()
        {
            // Arrange
            var tenantId = "6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e";
            var result = new TenantValidationResult
            {
                TenantId = tenantId,
                // THIS WILL FAIL - TenantName property doesn't exist yet
                TenantName = "   "
            };

            // Act
            // THIS WILL FAIL - GetDisplayName method doesn't exist yet
            var displayName = result.GetDisplayName();

            // Assert
            Assert.AreEqual(tenantId, displayName,
                "Should fallback to TenantId for display when TenantName is whitespace only");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_ShouldUseTenantNameWhenAvailable()
        {
            // Arrange
            var tenantId = "6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e";
            var tenantName = "Jack in the Box Corporate";
            var result = new TenantValidationResult
            {
                TenantId = tenantId,
                // THIS WILL FAIL - TenantName property doesn't exist yet
                TenantName = tenantName
            };

            // Act
            // THIS WILL FAIL - GetDisplayName method doesn't exist yet
            var displayName = result.GetDisplayName();

            // Assert
            Assert.AreEqual(tenantName, displayName,
                "Should use TenantName for display when available");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_Constructor_ShouldInitializeTenantNameAsNull()
        {
            // Act
            var result = new TenantValidationResult();

            // Assert
            // THIS WILL FAIL - TenantName property doesn't exist yet
            Assert.IsNull(result.TenantName,
                "TenantName should be initialized as null by default constructor");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_GetDisplayName_ShouldHandleBothTenantIdAndTenantNameNull()
        {
            // Arrange
            var result = new TenantValidationResult
            {
                TenantId = null,
                // THIS WILL FAIL - TenantName property doesn't exist yet
                TenantName = null
            };

            // Act
            // THIS WILL FAIL - GetDisplayName method doesn't exist yet
            var displayName = result.GetDisplayName();

            // Assert
            Assert.AreEqual("(Unknown)", displayName,
                "Should return '(Unknown)' when both TenantId and TenantName are null");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("Models")]
        [TestCategory("TDD_Red_Phase")]
        public void TenantValidationResult_GetDisplayName_ShouldTrimTenantName()
        {
            // Arrange
            var tenantName = "  Jack in the Box Corporate  ";
            var result = new TenantValidationResult
            {
                TenantId = "some-id",
                // THIS WILL FAIL - TenantName property doesn't exist yet
                TenantName = tenantName
            };

            // Act
            // THIS WILL FAIL - GetDisplayName method doesn't exist yet
            var displayName = result.GetDisplayName();

            // Assert
            Assert.AreEqual("Jack in the Box Corporate", displayName,
                "Should trim whitespace from TenantName when using for display");
        }
    }
}