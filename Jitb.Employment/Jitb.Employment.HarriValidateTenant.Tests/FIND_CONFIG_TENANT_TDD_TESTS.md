# FindConfigTenantForLocationAsync - TDD Red Phase Tests

## Overview
This document describes the comprehensive failing unit tests created for the new `FindConfigTenantForLocationAsync` method in the RefactoredTenantValidationService. These tests follow strict TDD (Test-Driven Development) methodology and are currently in the **RED PHASE** - they will fail until the method is implemented.

## Method Signature
```csharp
private async Task<Configuration.ConfigTenant> FindConfigTenantForLocationAsync(int locationId, List<Configuration.ConfigTenant> configuredTenants)
```

## Business Requirement
**ClaudeInstructions line 237**: "You need to get all the locations from the 'fallback' tenants and find the tenant with the location, if any. You cannot just default to the first one."

The current implementation in `GetTenantsForAllLocationsAsync()` defaults to the first configured tenant when no database tenant is found. This new method replaces that logic by actually checking each config tenant's locations via the Harri API.

## Test Scenarios Covered

### 1. Happy Path Scenarios
- **Location exists in first config tenant** - Should return the first tenant immediately without checking others
- **Location exists in second config tenant** - Should check first tenant, fail to find location, then check second tenant and return it

### 2. Not Found Scenarios  
- **Location doesn't exist in any config tenant** - Should check all tenants and return null
- **No config tenants provided** - Should return null without making API calls
- **Null config tenants list** - Should handle null input gracefully

### 3. Error Handling Scenarios
- **API call fails for a tenant** - Should handle HTTP failures gracefully and continue checking other tenants
- **API call throws exception for a tenant** - Should catch exceptions and continue checking other tenants  
- **API returns invalid JSON** - Should handle JSON deserialization errors and continue checking other tenants
- **Tenant has invalid ClientId** - Should skip tenants with malformed GUIDs and continue

## Test Framework Details
- **Testing Framework**: MSTest 2.1.2
- **Mocking Framework**: Moq 4.16.1
- **Test Data**: Manual setup (no AutoFixture used for these specific tests to maintain explicit control)
- **JSON Serialization**: Newtonsoft.Json 13.0.3
- **HTTP Client**: RestSharp 106.15.0

## Test Categories
All tests are marked with:
- `[TestCategory("FindConfigTenantForLocationAsync")]` 
- `[TestCategory("TDD_Red_Phase")]`

## Implementation Strategy
The method should:

1. **Null/Empty Check**: Return null immediately if configuredTenants is null or empty
2. **Sequential Search**: Iterate through each config tenant in order
3. **API Call**: For each tenant, call Harri locations API using tenant's ClientId as GUID
4. **Location Matching**: Deserialize response to HarriLocations2List and check if targetLocationId exists
5. **Early Return**: Return the tenant immediately when location is found
6. **Error Handling**: Catch and log exceptions, continue to next tenant
7. **Not Found**: Return null if no tenant contains the location

## Expected API Integration
```csharp
var tenantGuid = Guid.Parse(configTenant.ClientId);
var response = await _callHarriWebServiceProvider.Call(tenantGuid, Method.Get, "locations", null, false, "v1");
var locations = JsonConvert.DeserializeObject<HarriLocations2List>(response.Content);
var locationExists = locations.Any(loc => loc.Id == locationId);
```

## Files Modified
1. `/Services/IRefactoredTenantValidationService.cs` - Added method signature to interface
2. `/Tests/Services/RefactoredTenantValidationServiceTests.cs` - Added comprehensive test suite (9 test methods)

## Test Execution
These tests will **FAIL** until the `FindConfigTenantForLocationAsync` method is implemented in `RefactoredTenantValidationService.cs`. This is the expected behavior for TDD Red Phase.

To run tests:
```bash
dotnet test --filter "TestCategory=FindConfigTenantForLocationAsync"
```

## Next Steps (Green Phase)
1. Implement `FindConfigTenantForLocationAsync` method in `RefactoredTenantValidationService`
2. Make all tests pass
3. Refactor implementation if needed while keeping tests passing
4. Update `GetTenantsForAllLocationsAsync` to use the new method instead of defaulting to first tenant