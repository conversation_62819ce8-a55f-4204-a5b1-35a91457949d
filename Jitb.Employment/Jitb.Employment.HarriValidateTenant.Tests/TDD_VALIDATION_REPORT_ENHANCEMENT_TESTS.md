# TDD Failing Tests for HarriValidateTenant Validation Report Enhancement

## Overview
This document describes the comprehensive failing unit tests created for the HarriValidateTenant validation report functionality enhancements. These tests follow Test-Driven Development (TDD) principles and **WILL FAIL** initially until the corresponding implementation is added.

## User Requirements Summary
Based on user feedback, the current validation report needs three key enhancements:

1. **Show tenant name instead of tenant ID** - Currently shows "6ab0de5a-8aae-" but should show the actual tenant name
2. **Add "Read Employee" component** - Should appear after "Load Employee" in the component list
3. **Fix Load Employee status** - Should show "Skipped" when employees were skipped due to preexisting employees, not "Pass"

## Test Files Created

### 1. Models/TenantValidationResultTests.cs
**Purpose**: Tests for TenantValidationResult model enhancements  
**Key Failing Tests**:
- `TenantValidationResult_ShouldHaveTenantNameProperty()` - TenantName property doesn't exist yet
- `TenantValidationResult_ShouldUseTenantNameWhenAvailable()` - GetDisplayName() method doesn't exist yet
- `TenantValidationResult_GetDisplayName_ShouldHandleBothTenantIdAndTenantNameNull()` - Method missing

**Implementation Needed**:
- Add `string TenantName { get; set; }` property to TenantValidationResult
- Add `string GetDisplayName()` method that returns TenantName if available, otherwise TenantId
- Handle null/empty/whitespace fallbacks

### 2. Services/SummaryReportServiceTests.cs
**Purpose**: Tests for SummaryReportService to display tenant names and include Read Employee component  
**Key Failing Tests**:
- `GenerateSummaryReport_ShouldShowTenantNameInsteadOfTenantId_WhenTenantNameIsAvailable()` - Report uses TenantId instead of TenantName
- `GenerateSummaryReport_ShouldIncludeReadEmployeeComponent_InCorrectOrder()` - Read Employee component missing from report
- `GenerateSummaryReport_ShouldSortByTenantName_WhenTenantNameIsAvailable()` - Sorting logic doesn't consider TenantName

**Implementation Needed**:
- Update SummaryReportService.AppendTenantResult() to use TenantValidationResult.GetDisplayName()
- Update GetComponentDisplayOrder() to include "Read Employee" component with order value between Load Employee and Position Mapping
- Update sorting logic to sort by TenantName when available

### 3. Services/TenantValidationServiceSkippedStatusTests.cs  
**Purpose**: Tests for TenantValidationService to handle "Skipped" status for Load Employee component  
**Key Failing Tests**:
- `ValidateLocationForTenantAsync_ShouldRecordSkippedForLoadEmployee_WhenEmployeesWereSkippedDueToPreexistingEmployees()` - Detailed status tracking missing
- `ValidateLocationForTenantAsync_ShouldRecordComponentsInCorrectOrder_IncludingReadEmployee()` - Read Employee component not recorded

**Implementation Needed**:
- Create LocationValidationResult class to return detailed validation results
- Update ILocationValidationService.ValidateLocationAsync() to return LocationValidationResult
- Update TenantValidationService to handle detailed results and record appropriate statuses
- Add Read Employee component recording

### 4. Services/TenantValidationResultCaptureTests.cs
**Purpose**: Tests for TenantValidationResultCapture to support tenant names  
**Key Failing Tests**:
- `StartTenantValidation_ShouldAcceptTenantName_WhenTenantNameProvided()` - StartTenantValidation overload with tenantName missing
- `ITenantValidationResultCapture_Interface_ShouldIncludeTenantNameOverload()` - Interface missing new method signature

**Implementation Needed**:
- Add `void StartTenantValidation(string tenantId, string tenantName, int location)` overload to ITenantValidationResultCapture
- Implement the overload in TenantValidationResultCapture
- Update result creation to set TenantName property

### 5. Services/ComponentDisplayOrderTests.cs
**Purpose**: Tests for component display order with Read Employee component  
**Key Failing Tests**:
- `GetComponentDisplayOrder_ShouldReturnCorrectOrderForReadEmployee()` - Read Employee not in component ordering logic
- `GetComponentDisplayOrder_ShouldReturn_CorrectNumericOrderForReadEmployee()` - Numeric order value missing

**Implementation Needed**:
- Update SummaryReportService.GetComponentDisplayOrder() method to include "Read Employee" case
- Assign order value (suggested: 3.5 or 4) between Load Employee (3) and Position Mapping (4/5)

## Expected Component Order (After Implementation)
1. Tenant Lookup (1)
2. GetLocations (2)  
3. Load Employee (3)
4. **Read Employee (4)** ← NEW COMPONENT
5. Position Mapping (5)
6. Event Received (6)
7. Final Result (7)

## Key Implementation Steps

### Phase 1: Model Enhancement
1. Add `TenantName` property to TenantValidationResult
2. Add `GetDisplayName()` method to TenantValidationResult
3. Update TenantValidationResult constructor to initialize TenantName as null

### Phase 2: Interface Updates  
1. Add StartTenantValidation overload to ITenantValidationResultCapture
2. Consider creating LocationValidationResult class for detailed status reporting

### Phase 3: Service Implementation
1. Implement TenantValidationResultCapture.StartTenantValidation(tenantId, tenantName, location)
2. Update SummaryReportService to use GetDisplayName() for display
3. Update SummaryReportService.GetComponentDisplayOrder() to include Read Employee
4. Update TenantValidationService to record Read Employee component
5. Enhance TenantValidationService to handle detailed Load Employee status (Pass/Skipped/Fail)

### Phase 4: Integration
1. Update calling code to pass tenant names when available  
2. Update LocationValidationService to provide detailed results (if needed)
3. Test integration with existing workflow

## Test Categories
All tests are tagged with appropriate categories:
- `[TestCategory("TDD_Red_Phase")]` - Indicates these are failing tests by design
- `[TestCategory("TenantValidation")]` - General tenant validation functionality
- `[TestCategory("Models")]` - Model-related tests
- `[TestCategory("SkippedStatus")]` - Load Employee skipped status functionality
- `[TestCategory("ComponentOrder")]` - Component ordering functionality  
- `[TestCategory("ResultCapture")]` - Result capture functionality

## Verification of TDD Red Phase

These tests **WILL FAIL** when initially run because:

1. **Missing Properties**: TenantValidationResult.TenantName property doesn't exist
2. **Missing Methods**: TenantValidationResult.GetDisplayName() method doesn't exist
3. **Missing Overloads**: ITenantValidationResultCapture.StartTenantValidation(tenantId, tenantName, location) doesn't exist  
4. **Missing Component**: "Read Employee" not included in component ordering logic
5. **Missing Class**: LocationValidationResult class doesn't exist
6. **Limited Status Tracking**: Current implementation doesn't distinguish between Pass/Skipped for Load Employee

## Success Criteria

Tests will pass when:
- ✅ Tenant names are displayed instead of tenant IDs when available
- ✅ "Read Employee" component appears in correct order in reports
- ✅ Load Employee shows "Skipped" status when appropriate  
- ✅ All existing functionality continues to work
- ✅ Report formatting and column alignment are maintained
- ✅ Thread safety and error handling are preserved

## Future Considerations

After implementing these changes, consider:
- Adding more granular status reporting for other components
- Implementing tenant name caching for performance
- Adding configuration options for component visibility
- Enhancing error details and logging for better troubleshooting

---

**Generated**: 2025-01-07  
**TDD Methodology**: Red-Green-Refactor  
**Test Framework**: MSTest with Moq  
**Status**: 🔴 RED PHASE - Tests created and will fail until implementation is added