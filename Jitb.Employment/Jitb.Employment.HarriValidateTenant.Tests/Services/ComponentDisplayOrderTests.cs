using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using System.Linq;
using Jitb.Employment.HarriValidateTenant.Models;
using Jitb.Employment.HarriValidateTenant.Services;

namespace Jitb.Employment.HarriValidateTenant.Tests.Services
{
    /// <summary>
    /// Tests for component display order with the new "Read Employee" component.
    /// These tests are written in TDD Red phase and WILL FAIL initially until implementation is updated.
    /// 
    /// User requirements:
    /// 1. "Read Employee" component should appear after "Load Employee" component
    /// 2. "Read Employee" component should appear before "Position Mapping" component  
    /// 3. Component ordering should be: Tenant Lookup, GetLocations, Load Employee, Read Employee, Position Mapping, Event Received, Final Result
    /// 4. GetComponentDisplayOrder method should return correct order for Read Employee component
    /// </summary>
    [TestClass]
    public class ComponentDisplayOrderTests
    {
        private ISummaryReportService _summaryReportService;

        [TestInitialize]
        public void Setup()
        {
            _summaryReportService = new SummaryReportService();
        }

        [TestMethod]
        [TestCategory("ComponentOrder")]
        [TestCategory("ReadEmployee")]
        [TestCategory("TDD_Red_Phase")]
        public void GetComponentDisplayOrder_ShouldReturnCorrectOrderForReadEmployee()
        {
            // Arrange
            var service = new SummaryReportService();
            var readEmployeeComponent = new ValidationComponentResult("Read Employee", "Pass", "");

            // Act
            // THIS WILL FAIL - GetComponentDisplayOrder method is private, need to test indirectly
            // We'll test by creating a report with components in random order and verifying the sorted output
            var components = new List<ValidationComponentResult>
            {
                new ValidationComponentResult("Position Mapping", "Pass", ""),  // Should be order 5
                new ValidationComponentResult("Read Employee", "Pass", ""),     // Should be order 4 (new)
                new ValidationComponentResult("Load Employee", "Pass", ""),     // Should be order 3
                new ValidationComponentResult("GetLocations", "Pass", ""),      // Should be order 2
                new ValidationComponentResult("Tenant Lookup", "Pass", ""),     // Should be order 1
                new ValidationComponentResult("Event Received", "True", "")     // Should be order 6
            };

            var result = new TenantValidationResult
            {
                TenantId = "test-tenant",
                ProcessedLocation = 3365,
                ComponentResults = components,
                FinalResult = "Pass"
            };

            var report = _summaryReportService.GenerateSummaryReport(new List<TenantValidationResult> { result });

            // Assert
            // Parse the report to verify component order
            var reportLines = report.Split('\n')
                .Where(line => !string.IsNullOrWhiteSpace(line) && 
                              !line.Contains("---") && 
                              !line.Contains("Tenant") && 
                              !line.Contains("Location") && 
                              !line.Contains("Test Component"))
                .ToList();

            var componentOrder = new List<string>();
            var expectedComponents = new[]
            {
                "Tenant Lookup",
                "GetLocations", 
                "Load Employee",
                "Read Employee",     // THIS IS THE KEY TEST - Read Employee should be here
                "Position Mapping",
                "Event Received"
            };

            foreach (var line in reportLines)
            {
                foreach (var component in expectedComponents)
                {
                    if (line.Contains(component) && !componentOrder.Contains(component))
                    {
                        componentOrder.Add(component);
                        break;
                    }
                }
            }

            // Verify the exact expected order
            CollectionAssert.AreEqual(expectedComponents, componentOrder,
                "Components should appear in correct order with Read Employee between Load Employee and Position Mapping");
        }

        [TestMethod]
        [TestCategory("ComponentOrder")]
        [TestCategory("ReadEmployee")]
        [TestCategory("TDD_Red_Phase")]
        public void SummaryReportService_ShouldHandleReadEmployeeComponentOrdering_WithAllPossibleStatuses()
        {
            // Arrange
            var testCases = new[]
            {
                new { Status = "Pass", Details = "Employee data read successfully" },
                new { Status = "Fail", Details = "Failed to read employee data" },
                new { Status = "Skipped", Details = "Employee data read was skipped" },
                new { Status = "Partial", Details = "Some employee data read successfully" }
            };

            foreach (var testCase in testCases)
            {
                var components = new List<ValidationComponentResult>
                {
                    new ValidationComponentResult("Event Received", "True", ""),
                    new ValidationComponentResult("Position Mapping", "Pass", ""),
                    new ValidationComponentResult("Read Employee", testCase.Status, testCase.Details),
                    new ValidationComponentResult("Load Employee", "Pass", ""),
                    new ValidationComponentResult("GetLocations", "Pass", ""),
                    new ValidationComponentResult("Tenant Lookup", "Pass", "")
                };

                var result = new TenantValidationResult
                {
                    TenantId = $"test-tenant-{testCase.Status.ToLower()}",
                    ProcessedLocation = 3365,
                    ComponentResults = components,
                    FinalResult = "Pass"
                };

                // Act
                var report = _summaryReportService.GenerateSummaryReport(new List<TenantValidationResult> { result });

                // Assert
                var reportLines = report.Split('\n').Where(line => !string.IsNullOrWhiteSpace(line)).ToList();
                
                int loadEmployeeIndex = -1;
                int readEmployeeIndex = -1; 
                int positionMappingIndex = -1;

                for (int i = 0; i < reportLines.Count; i++)
                {
                    if (reportLines[i].Contains("Load Employee"))
                        loadEmployeeIndex = i;
                    else if (reportLines[i].Contains("Read Employee"))
                        readEmployeeIndex = i;
                    else if (reportLines[i].Contains("Position Mapping"))
                        positionMappingIndex = i;
                }

                Assert.IsTrue(loadEmployeeIndex >= 0, $"Load Employee should be present for status {testCase.Status}");
                Assert.IsTrue(readEmployeeIndex >= 0, $"Read Employee should be present for status {testCase.Status}");
                Assert.IsTrue(positionMappingIndex >= 0, $"Position Mapping should be present for status {testCase.Status}");

                Assert.IsTrue(loadEmployeeIndex < readEmployeeIndex,
                    $"Load Employee should come before Read Employee for status {testCase.Status}");
                Assert.IsTrue(readEmployeeIndex < positionMappingIndex,
                    $"Read Employee should come before Position Mapping for status {testCase.Status}");

                // Verify the status and details are displayed correctly
                var readEmployeeLine = reportLines[readEmployeeIndex];
                Assert.IsTrue(readEmployeeLine.Contains(testCase.Status),
                    $"Read Employee line should contain status {testCase.Status}");
                if (!string.IsNullOrEmpty(testCase.Details))
                {
                    Assert.IsTrue(readEmployeeLine.Contains(testCase.Details) || 
                                  readEmployeeLine.Contains(testCase.Details.Substring(0, 20)), // Handle truncation
                        $"Read Employee line should contain details or truncated details for status {testCase.Status}");
                }
            }
        }

        [TestMethod]
        [TestCategory("ComponentOrder")]
        [TestCategory("ReadEmployee")]
        [TestCategory("TDD_Red_Phase")]
        public void GetComponentDisplayOrder_ShouldReturnUniqueOrderValues_ForAllComponents()
        {
            // Arrange
            var allComponents = new[]
            {
                "Tenant Lookup",      // Expected order: 1
                "GetLocations",       // Expected order: 2
                "Load Employee",      // Expected order: 3
                "Read Employee",      // Expected order: 4 (NEW - this will fail)
                "Position Mapping",   // Expected order: 5
                "Event Received",     // Expected order: 6
                "Final Result"        // Expected order: 7
            };

            var components = allComponents.Select(name => 
                new ValidationComponentResult(name, "Pass", "")).ToList();

            var result = new TenantValidationResult
            {
                TenantId = "test-tenant",
                ProcessedLocation = 3365,
                ComponentResults = components,
                FinalResult = "Pass"
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(new List<TenantValidationResult> { result });

            // Assert
            // Extract the order in which components appear in the report
            var reportLines = report.Split('\n')
                .Where(line => !string.IsNullOrWhiteSpace(line) && 
                              !line.Contains("---") &&
                              !line.StartsWith("Tenant") &&
                              !line.StartsWith("Location"))
                .ToList();

            var actualOrder = new List<string>();
            
            foreach (var line in reportLines)
            {
                foreach (var component in allComponents)
                {
                    if (line.Contains(component) && !actualOrder.Contains(component))
                    {
                        actualOrder.Add(component);
                        break;
                    }
                }
            }

            // Verify all components are present and in correct order
            CollectionAssert.AreEqual(allComponents, actualOrder,
                "All components should be present in the correct order including Read Employee");

            // Verify Read Employee specifically is positioned correctly
            var readEmployeeIndex = actualOrder.IndexOf("Read Employee");
            var loadEmployeeIndex = actualOrder.IndexOf("Load Employee");
            var positionMappingIndex = actualOrder.IndexOf("Position Mapping");

            Assert.IsTrue(readEmployeeIndex > loadEmployeeIndex,
                "Read Employee should have higher order than Load Employee");
            Assert.IsTrue(readEmployeeIndex < positionMappingIndex,
                "Read Employee should have lower order than Position Mapping");
        }

        [TestMethod]
        [TestCategory("ComponentOrder")]
        [TestCategory("ReadEmployee")]
        [TestCategory("TDD_Red_Phase")]
        public void GetComponentDisplayOrder_ShouldHandle_UnknownComponentsCorrectly()
        {
            // Arrange
            var components = new List<ValidationComponentResult>
            {
                new ValidationComponentResult("Unknown Component", "Pass", ""),
                new ValidationComponentResult("Read Employee", "Pass", ""),
                new ValidationComponentResult("Load Employee", "Pass", ""),
                new ValidationComponentResult("Another Unknown", "Pass", "")
            };

            var result = new TenantValidationResult
            {
                TenantId = "test-tenant",
                ProcessedLocation = 3365,
                ComponentResults = components,
                FinalResult = "Pass"
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(new List<TenantValidationResult> { result });

            // Assert
            // Unknown components should appear after known components (with default order 999)
            var reportLines = report.Split('\n').Where(line => !string.IsNullOrWhiteSpace(line)).ToList();
            
            int loadEmployeeIndex = -1;
            int readEmployeeIndex = -1;
            int unknownComponent1Index = -1;
            int unknownComponent2Index = -1;

            for (int i = 0; i < reportLines.Count; i++)
            {
                var line = reportLines[i];
                if (line.Contains("Load Employee"))
                    loadEmployeeIndex = i;
                else if (line.Contains("Read Employee"))
                    readEmployeeIndex = i;
                else if (line.Contains("Unknown Component"))
                    unknownComponent1Index = i;
                else if (line.Contains("Another Unknown"))
                    unknownComponent2Index = i;
            }

            Assert.IsTrue(loadEmployeeIndex >= 0, "Load Employee should be present");
            Assert.IsTrue(readEmployeeIndex >= 0, "Read Employee should be present");
            Assert.IsTrue(loadEmployeeIndex < readEmployeeIndex,
                "Load Employee should come before Read Employee");

            // Unknown components should come after known components
            if (unknownComponent1Index >= 0)
            {
                Assert.IsTrue(unknownComponent1Index > readEmployeeIndex,
                    "Unknown components should come after known components");
            }
        }

        [TestMethod]
        [TestCategory("ComponentOrder")]
        [TestCategory("ReadEmployee")]
        [TestCategory("TDD_Red_Phase")]
        public void GetComponentDisplayOrder_ShouldReturn_CorrectNumericOrderForReadEmployee()
        {
            // This test verifies the actual numeric values returned by GetComponentDisplayOrder
            // Since the method is private, we test it indirectly through sorting behavior

            // Arrange
            var components = new List<ValidationComponentResult>
            {
                // Add components in reverse order to test sorting
                new ValidationComponentResult("Event Received", "True", ""),     // Order: 6
                new ValidationComponentResult("Position Mapping", "Pass", ""),   // Order: 5  
                new ValidationComponentResult("Read Employee", "Pass", ""),      // Order: 4 (NEW)
                new ValidationComponentResult("Load Employee", "Pass", ""),      // Order: 3
                new ValidationComponentResult("GetLocations", "Pass", ""),       // Order: 2
                new ValidationComponentResult("Tenant Lookup", "Pass", "")       // Order: 1
            };

            var result = new TenantValidationResult
            {
                TenantId = "test-tenant",
                ProcessedLocation = 3365,
                ComponentResults = components,
                FinalResult = "Pass"
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(new List<TenantValidationResult> { result });

            // Assert
            // The report should sort components correctly regardless of input order
            var reportLines = report.Split('\n')
                .Where(line => !string.IsNullOrWhiteSpace(line) && 
                              (line.Contains("Tenant Lookup") || 
                               line.Contains("GetLocations") || 
                               line.Contains("Load Employee") || 
                               line.Contains("Read Employee") || 
                               line.Contains("Position Mapping") || 
                               line.Contains("Event Received")))
                .ToList();

            // Extract component names in order of appearance
            var sortedComponents = new List<string>();
            foreach (var line in reportLines)
            {
                if (line.Contains("Tenant Lookup") && !sortedComponents.Contains("Tenant Lookup"))
                    sortedComponents.Add("Tenant Lookup");
                else if (line.Contains("GetLocations") && !sortedComponents.Contains("GetLocations"))
                    sortedComponents.Add("GetLocations");
                else if (line.Contains("Load Employee") && !sortedComponents.Contains("Load Employee"))
                    sortedComponents.Add("Load Employee");
                else if (line.Contains("Read Employee") && !sortedComponents.Contains("Read Employee"))
                    sortedComponents.Add("Read Employee");
                else if (line.Contains("Position Mapping") && !sortedComponents.Contains("Position Mapping"))
                    sortedComponents.Add("Position Mapping");
                else if (line.Contains("Event Received") && !sortedComponents.Contains("Event Received"))
                    sortedComponents.Add("Event Received");
            }

            // Verify exact ordering
            var expectedOrder = new[]
            {
                "Tenant Lookup",    // Order: 1
                "GetLocations",     // Order: 2
                "Load Employee",    // Order: 3
                "Read Employee",    // Order: 4 (THIS WILL FAIL - component doesn't have assigned order yet)
                "Position Mapping", // Order: 5
                "Event Received"    // Order: 6
            };

            CollectionAssert.AreEqual(expectedOrder, sortedComponents,
                "Components should be sorted in correct numeric order with Read Employee at position 4");

            // Specifically verify Read Employee position
            var readEmployeePosition = sortedComponents.IndexOf("Read Employee");
            Assert.AreEqual(3, readEmployeePosition, // 0-based index, so position 4 = index 3
                "Read Employee should be at position 4 in the sort order (index 3)");
        }

        [TestMethod]
        [TestCategory("ComponentOrder")]
        [TestCategory("ReadEmployee")]
        [TestCategory("TDD_Red_Phase")]
        public void SummaryReportService_ShouldMaintainReadEmployeeOrder_WithMultipleTenants()
        {
            // Arrange
            var results = new List<TenantValidationResult>
            {
                // First tenant with all components
                new TenantValidationResult
                {
                    TenantId = "tenant-1",
                    ProcessedLocation = 1001,
                    ComponentResults = new List<ValidationComponentResult>
                    {
                        new ValidationComponentResult("Position Mapping", "Pass", ""),
                        new ValidationComponentResult("Read Employee", "Pass", ""),
                        new ValidationComponentResult("Load Employee", "Skipped", ""),
                        new ValidationComponentResult("Tenant Lookup", "Pass", "")
                    },
                    FinalResult = "Pass"
                },
                // Second tenant with different component order
                new TenantValidationResult
                {
                    TenantId = "tenant-2",
                    ProcessedLocation = 2002,
                    ComponentResults = new List<ValidationComponentResult>
                    {
                        new ValidationComponentResult("Event Received", "True", ""),
                        new ValidationComponentResult("Read Employee", "Fail", "Connection timeout"),
                        new ValidationComponentResult("GetLocations", "Pass", ""),
                        new ValidationComponentResult("Load Employee", "Pass", "")
                    },
                    FinalResult = "Fail"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            var reportLines = report.Split('\n').Where(line => !string.IsNullOrWhiteSpace(line)).ToList();
            
            // Find all Read Employee occurrences
            var readEmployeeLines = new List<int>();
            var loadEmployeeLines = new List<int>();
            var positionMappingLines = new List<int>();

            for (int i = 0; i < reportLines.Count; i++)
            {
                var line = reportLines[i];
                if (line.Contains("Read Employee"))
                    readEmployeeLines.Add(i);
                else if (line.Contains("Load Employee"))
                    loadEmployeeLines.Add(i);
                else if (line.Contains("Position Mapping"))
                    positionMappingLines.Add(i);
            }

            // Verify ordering for each tenant's section
            Assert.IsTrue(readEmployeeLines.Count >= 2, "Should have Read Employee entries for both tenants");
            Assert.IsTrue(loadEmployeeLines.Count >= 2, "Should have Load Employee entries for both tenants");

            // For each Read Employee occurrence, verify it comes after Load Employee and before Position Mapping in the same section
            foreach (var readEmployeeLineIndex in readEmployeeLines)
            {
                // Find the closest Load Employee before this Read Employee
                var precedingLoadEmployee = loadEmployeeLines
                    .Where(idx => idx < readEmployeeLineIndex)
                    .OrderByDescending(idx => idx)
                    .FirstOrDefault();

                // Find the closest Position Mapping after this Read Employee  
                var followingPositionMapping = positionMappingLines
                    .Where(idx => idx > readEmployeeLineIndex)
                    .OrderBy(idx => idx)
                    .FirstOrDefault();

                if (precedingLoadEmployee > 0)
                {
                    Assert.IsTrue(precedingLoadEmployee < readEmployeeLineIndex,
                        $"Load Employee should precede Read Employee at line {readEmployeeLineIndex}");
                }

                if (followingPositionMapping > 0)
                {
                    Assert.IsTrue(readEmployeeLineIndex < followingPositionMapping,
                        $"Read Employee should precede Position Mapping after line {readEmployeeLineIndex}");
                }
            }
        }
    }
}