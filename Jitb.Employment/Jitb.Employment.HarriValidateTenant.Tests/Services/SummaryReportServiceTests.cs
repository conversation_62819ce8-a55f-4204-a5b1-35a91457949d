using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System.Collections.Generic;
using System.Linq;
using Jitb.Employment.HarriValidateTenant.Models;
using Jitb.Employment.HarriValidateTenant.Services;

namespace Jitb.Employment.HarriValidateTenant.Tests.Services
{
    /// <summary>
    /// Tests for SummaryReportService enhancements.
    /// These tests are written in TDD Red phase and WILL FAIL initially until implementation is updated.
    /// 
    /// User requirements:
    /// 1. Show tenant name instead of tenant ID when tenant name is available
    /// 2. Add "Read Employee" component in the correct order (after Load Employee)
    /// 3. Maintain existing functionality while enhancing report display
    /// </summary>
    [TestClass]
    public class SummaryReportServiceTests
    {
        private ISummaryReportService _summaryReportService;

        [TestInitialize]
        public void Setup()
        {
            _summaryReportService = new SummaryReportService();
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("TenantDisplay")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldShowTenantNameInsteadOfTenantId_WhenTenantNameIsAvailable()
        {
            // Arrange
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = "6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e",
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = "Jack in the Box Corporate",
                    ProcessedLocation = 3365,
                    ComponentResults = new List<ValidationComponentResult>
                    {
                        new ValidationComponentResult("Tenant Lookup", "Pass", "")
                    },
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            // THIS WILL FAIL - Report should show tenant name instead of GUID
            Assert.IsTrue(report.Contains("Jack in the Box Corporate"),
                "Report should display the tenant name when available");

            Assert.IsFalse(report.Contains("6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e"),
                "Report should not display the tenant ID GUID when tenant name is available");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("TenantDisplay")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldFallbackToTenantId_WhenTenantNameIsNull()
        {
            // Arrange
            var tenantId = "6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e";
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = tenantId,
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = null,
                    ProcessedLocation = 3365,
                    ComponentResults = new List<ValidationComponentResult>
                    {
                        new ValidationComponentResult("Tenant Lookup", "Pass", "")
                    },
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            Assert.IsTrue(report.Contains(tenantId),
                "Report should display the tenant ID when tenant name is not available");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("TenantDisplay")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldFallbackToTenantId_WhenTenantNameIsEmpty()
        {
            // Arrange
            var tenantId = "6ab0de5a-8aae-4b5c-9d7e-1f2a3b4c5d6e";
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = tenantId,
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = string.Empty,
                    ProcessedLocation = 3365,
                    ComponentResults = new List<ValidationComponentResult>
                    {
                        new ValidationComponentResult("Tenant Lookup", "Pass", "")
                    },
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            Assert.IsTrue(report.Contains(tenantId),
                "Report should display the tenant ID when tenant name is empty");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("TenantDisplay")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldHandleMultipleTenantsWithMixedNameAvailability()
        {
            // Arrange
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = "tenant-with-name-id",
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = "Corporate Tenant",
                    ProcessedLocation = 1001,
                    ComponentResults = new List<ValidationComponentResult>(),
                    FinalResult = "Pass"
                },
                new TenantValidationResult
                {
                    TenantId = "tenant-without-name-id",
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = null,
                    ProcessedLocation = 2002,
                    ComponentResults = new List<ValidationComponentResult>(),
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            Assert.IsTrue(report.Contains("Corporate Tenant"),
                "Report should display tenant name for tenant that has a name");

            Assert.IsTrue(report.Contains("tenant-without-name-id"),
                "Report should display tenant ID for tenant that doesn't have a name");

            Assert.IsFalse(report.Contains("tenant-with-name-id"),
                "Report should not display tenant ID when tenant name is available");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("ComponentOrder")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldIncludeReadEmployeeComponent_InCorrectOrder()
        {
            // Arrange
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = "test-tenant",
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = "Test Tenant",
                    ProcessedLocation = 3365,
                    ComponentResults = new List<ValidationComponentResult>
                    {
                        new ValidationComponentResult("Tenant Lookup", "Pass", ""),
                        new ValidationComponentResult("GetLocations", "Pass", ""),
                        new ValidationComponentResult("Load Employee", "Pass", ""),
                        // THIS WILL FAIL - Read Employee component should be included
                        new ValidationComponentResult("Read Employee", "Pass", ""),
                        new ValidationComponentResult("Position Mapping", "Pass", ""),
                        new ValidationComponentResult("Event Received", "True", "")
                    },
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            Assert.IsTrue(report.Contains("Read Employee"),
                "Report should include the Read Employee component");

            // Verify component ordering by checking that Read Employee appears after Load Employee
            var reportLines = report.Split('\n').Where(line => !string.IsNullOrWhiteSpace(line)).ToList();
            
            int loadEmployeeIndex = -1;
            int readEmployeeIndex = -1;
            
            for (int i = 0; i < reportLines.Count; i++)
            {
                if (reportLines[i].Contains("Load Employee"))
                    loadEmployeeIndex = i;
                if (reportLines[i].Contains("Read Employee"))
                    readEmployeeIndex = i;
            }

            Assert.IsTrue(loadEmployeeIndex >= 0, "Load Employee component should be present in report");
            Assert.IsTrue(readEmployeeIndex >= 0, "Read Employee component should be present in report");
            Assert.IsTrue(readEmployeeIndex > loadEmployeeIndex,
                "Read Employee component should appear after Load Employee component");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("ComponentOrder")]
        [TestCategory("TDD_Red_Phase")]
        public void GetComponentDisplayOrder_ShouldReturnCorrectOrderForReadEmployee()
        {
            // Arrange
            var service = new SummaryReportService();
            var readEmployeeComponent = new ValidationComponentResult("Read Employee", "Pass", "");
            var loadEmployeeComponent = new ValidationComponentResult("Load Employee", "Pass", "");
            var positionMappingComponent = new ValidationComponentResult("Position Mapping", "Pass", "");

            // Act
            // THIS WILL FAIL - GetComponentDisplayOrder method is private, need to test through public interface
            // This test verifies the ordering logic works correctly
            var components = new List<ValidationComponentResult>
            {
                positionMappingComponent,      // Should be order 4
                readEmployeeComponent,         // Should be order 3.5 (between Load Employee and Position Mapping)  
                loadEmployeeComponent          // Should be order 3
            };

            var result = new TenantValidationResult
            {
                TenantId = "test-tenant",
                ProcessedLocation = 3365,
                ComponentResults = components,
                FinalResult = "Pass"
            };

            var report = _summaryReportService.GenerateSummaryReport(new List<TenantValidationResult> { result });
            var reportLines = report.Split('\n').Where(line => !string.IsNullOrWhiteSpace(line) && !line.Contains("---")).ToList();

            // Assert
            // Find the indices where each component appears
            int loadEmployeeLineIndex = -1;
            int readEmployeeLineIndex = -1; 
            int positionMappingLineIndex = -1;

            for (int i = 0; i < reportLines.Count; i++)
            {
                if (reportLines[i].Contains("Load Employee"))
                    loadEmployeeLineIndex = i;
                else if (reportLines[i].Contains("Read Employee"))
                    readEmployeeLineIndex = i;
                else if (reportLines[i].Contains("Position Mapping"))
                    positionMappingLineIndex = i;
            }

            Assert.IsTrue(loadEmployeeLineIndex < readEmployeeLineIndex,
                "Load Employee should appear before Read Employee in the report");
            Assert.IsTrue(readEmployeeLineIndex < positionMappingLineIndex,
                "Read Employee should appear before Position Mapping in the report");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("ComponentOrder")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldMaintainCompleteComponentOrder_WithReadEmployee()
        {
            // Arrange
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = "test-tenant",
                    ProcessedLocation = 3365,
                    ComponentResults = new List<ValidationComponentResult>
                    {
                        // Add components in random order to test sorting
                        new ValidationComponentResult("Event Received", "True", ""),
                        new ValidationComponentResult("Position Mapping", "Pass", ""),
                        new ValidationComponentResult("Read Employee", "Pass", ""),
                        new ValidationComponentResult("Load Employee", "Pass", ""),
                        new ValidationComponentResult("GetLocations", "Pass", ""),
                        new ValidationComponentResult("Tenant Lookup", "Pass", "")
                    },
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            var reportLines = report.Split('\n')
                .Where(line => !string.IsNullOrWhiteSpace(line) && 
                              !line.Contains("---") && 
                              !line.Contains("Tenant") && 
                              !line.Contains("Location") && 
                              !line.Contains("Test Component"))
                .ToList();

            // Expected order: Tenant Lookup, GetLocations, Load Employee, Read Employee, Position Mapping, Event Received
            var expectedComponentOrder = new[]
            {
                "Tenant Lookup",
                "GetLocations", 
                "Load Employee",
                "Read Employee",
                "Position Mapping",
                "Event Received",
                "Final Result"
            };

            var actualComponentOrder = new List<string>();
            foreach (var line in reportLines)
            {
                foreach (var component in expectedComponentOrder)
                {
                    if (line.Contains(component) && !actualComponentOrder.Contains(component))
                    {
                        actualComponentOrder.Add(component);
                        break;
                    }
                }
            }

            CollectionAssert.AreEqual(expectedComponentOrder, actualComponentOrder,
                "Components should appear in the correct order with Read Employee between Load Employee and Position Mapping");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("TenantDisplay")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldTrimTenantNameForDisplay()
        {
            // Arrange
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = "test-tenant-id",
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = "  Corporate Tenant Name  ",
                    ProcessedLocation = 3365,
                    ComponentResults = new List<ValidationComponentResult>(),
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            Assert.IsTrue(report.Contains("Corporate Tenant Name"),
                "Report should contain the trimmed tenant name");
            Assert.IsFalse(report.Contains("  Corporate Tenant Name  "),
                "Report should not contain untrimmed tenant name with leading/trailing spaces");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("TenantDisplay")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldHandleLongTenantNames()
        {
            // Arrange
            var longTenantName = "Very Long Corporate Tenant Name That Might Exceed Column Width";
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = "test-tenant-id",
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = longTenantName,
                    ProcessedLocation = 3365,
                    ComponentResults = new List<ValidationComponentResult>(),
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            // The report should handle long tenant names appropriately (either truncate or expand column)
            Assert.IsTrue(report.Contains(longTenantName.Substring(0, 14)), // At least first 14 chars should be visible
                "Report should display at least the beginning of long tenant names");
        }

        [TestMethod]
        [TestCategory("SummaryReport")]
        [TestCategory("TenantDisplay")]
        [TestCategory("TDD_Red_Phase")]
        public void GenerateSummaryReport_ShouldSortByTenantName_WhenTenantNameIsAvailable()
        {
            // Arrange
            var results = new List<TenantValidationResult>
            {
                new TenantValidationResult
                {
                    TenantId = "zzz-tenant-id",
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = "Alpha Tenant",
                    ProcessedLocation = 1001,
                    ComponentResults = new List<ValidationComponentResult>(),
                    FinalResult = "Pass"
                },
                new TenantValidationResult
                {
                    TenantId = "aaa-tenant-id",
                    // THIS WILL FAIL - TenantName property doesn't exist yet
                    TenantName = "Zulu Tenant",
                    ProcessedLocation = 2002,
                    ComponentResults = new List<ValidationComponentResult>(),
                    FinalResult = "Pass"
                }
            };

            // Act
            var report = _summaryReportService.GenerateSummaryReport(results);

            // Assert
            var reportLines = report.Split('\n').ToList();
            
            int alphaTenantLineIndex = -1;
            int zuluTenantLineIndex = -1;
            
            for (int i = 0; i < reportLines.Count; i++)
            {
                if (reportLines[i].Contains("Alpha Tenant"))
                    alphaTenantLineIndex = i;
                else if (reportLines[i].Contains("Zulu Tenant"))
                    zuluTenantLineIndex = i;
            }

            Assert.IsTrue(alphaTenantLineIndex >= 0, "Alpha Tenant should be present in report");
            Assert.IsTrue(zuluTenantLineIndex >= 0, "Zulu Tenant should be present in report");
            Assert.IsTrue(alphaTenantLineIndex < zuluTenantLineIndex,
                "Tenants should be sorted alphabetically by tenant name when available");
        }
    }
}