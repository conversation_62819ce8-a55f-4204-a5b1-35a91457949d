using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Configuration;
using System.Collections.Specialized;
using Jitb.Employment.HarriValidateTenant.Services;
using System;

namespace Jitb.Employment.HarriValidateTenant.Tests.Services
{
    [TestClass]
    public class ConfigurationServiceTests
    {
        private ConfigurationService _configurationService;
        private NameValueCollection _originalAppSettings;

        [TestInitialize]
        public void Setup()
        {
            _configurationService = new ConfigurationService();
            
            // Store original app settings to restore later
            _originalAppSettings = new NameValueCollection();
            foreach (string key in ConfigurationManager.AppSettings.AllKeys)
            {
                _originalAppSettings[key] = ConfigurationManager.AppSettings[key];
            }
        }

        [TestCleanup]
        public void Cleanup()
        {
            // This is a limitation in testing with ConfigurationManager - 
            // we can't easily reset it, so tests should be independent
        }

        [TestMethod]
        [TestCategory("Configuration")]
        public void SkipEmployeeLoadWhenPreexistingFound_WhenSettingIsTrue_ReturnsTrue()
        {
            // Arrange
            SetAppSetting("SkipEmployeeLoadWhenPreexistingFound", "true");

            // Act
            var result = _configurationService.SkipEmployeeLoadWhenPreexistingFound();

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        [TestCategory("Configuration")]
        public void SkipEmployeeLoadWhenPreexistingFound_WhenSettingIsFalse_ReturnsFalse()
        {
            // Arrange
            SetAppSetting("SkipEmployeeLoadWhenPreexistingFound", "false");

            // Act
            var result = _configurationService.SkipEmployeeLoadWhenPreexistingFound();

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        [TestCategory("Configuration")]
        public void SkipEmployeeLoadWhenPreexistingFound_WhenSettingIsMissing_ReturnsTrue()
        {
            // Arrange
            RemoveAppSetting("SkipEmployeeLoadWhenPreexistingFound");

            // Act
            var result = _configurationService.SkipEmployeeLoadWhenPreexistingFound();

            // Assert
            Assert.IsTrue(result, "Should default to true for backward compatibility");
        }

        [TestMethod]
        [TestCategory("Configuration")]
        [ExpectedException(typeof(ConfigurationErrorsException))]
        public void SkipEmployeeLoadWhenPreexistingFound_WhenSettingIsInvalid_ThrowsConfigurationException()
        {
            // Arrange
            SetAppSetting("SkipEmployeeLoadWhenPreexistingFound", "invalid");

            // Act
            _configurationService.SkipEmployeeLoadWhenPreexistingFound();

            // Assert - Exception expected
        }

        [TestMethod]
        [TestCategory("Configuration")]
        public void MinimumExistingEmployeeCountToSkip_WhenSettingIsValid_ReturnsCorrectValue()
        {
            // Arrange
            SetAppSetting("MinimumExistingEmployeeCountToSkip", "15");

            // Act
            var result = _configurationService.MinimumExistingEmployeeCountToSkip();

            // Assert
            Assert.AreEqual(15, result);
        }

        [TestMethod]
        [TestCategory("Configuration")]
        public void MinimumExistingEmployeeCountToSkip_WhenSettingIsZero_ReturnsZero()
        {
            // Arrange
            SetAppSetting("MinimumExistingEmployeeCountToSkip", "0");

            // Act
            var result = _configurationService.MinimumExistingEmployeeCountToSkip();

            // Assert
            Assert.AreEqual(0, result);
        }

        [TestMethod]
        [TestCategory("Configuration")]
        public void MinimumExistingEmployeeCountToSkip_WhenSettingIsMissing_ReturnsDefaultValue()
        {
            // Arrange
            RemoveAppSetting("MinimumExistingEmployeeCountToSkip");

            // Act
            var result = _configurationService.MinimumExistingEmployeeCountToSkip();

            // Assert
            Assert.AreEqual(10, result, "Should default to 10 for backward compatibility");
        }

        [TestMethod]
        [TestCategory("Configuration")]
        [ExpectedException(typeof(ConfigurationErrorsException))]
        public void MinimumExistingEmployeeCountToSkip_WhenSettingIsNegative_ThrowsConfigurationException()
        {
            // Arrange
            SetAppSetting("MinimumExistingEmployeeCountToSkip", "-1");

            // Act
            _configurationService.MinimumExistingEmployeeCountToSkip();

            // Assert - Exception expected
        }

        [TestMethod]
        [TestCategory("Configuration")]
        [ExpectedException(typeof(ConfigurationErrorsException))]
        public void MinimumExistingEmployeeCountToSkip_WhenSettingIsNotNumeric_ThrowsConfigurationException()
        {
            // Arrange
            SetAppSetting("MinimumExistingEmployeeCountToSkip", "invalid");

            // Act
            _configurationService.MinimumExistingEmployeeCountToSkip();

            // Assert - Exception expected
        }

        [TestMethod]
        [TestCategory("Configuration")]
        public void GetProcessLocations_WithValidConfiguration_ReturnsCorrectLocations()
        {
            // Arrange
            SetAppSetting("ProcessLocations", "1,2,3,4");

            // Act
            var result = _configurationService.GetProcessLocations();

            // Assert
            CollectionAssert.AreEqual(new[] { 1, 2, 3, 4 }, new List<int>(result));
        }

        // Helper methods for configuration testing
        private void SetAppSetting(string key, string value)
        {
            // Note: In a real test environment, you might use a configuration wrapper
            // or dependency injection to make this more testable
            // For now, this is a demonstration of the test structure
            var field = typeof(ConfigurationManager).GetField("s_appSettings", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            
            if (field?.GetValue(null) is NameValueCollection appSettings)
            {
                appSettings[key] = value;
            }
        }

        private void RemoveAppSetting(string key)
        {
            var field = typeof(ConfigurationManager).GetField("s_appSettings", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            
            if (field?.GetValue(null) is NameValueCollection appSettings)
            {
                appSettings.Remove(key);
            }
        }
    }
}