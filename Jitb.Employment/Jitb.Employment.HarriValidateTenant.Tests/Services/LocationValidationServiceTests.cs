using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Threading.Tasks;
using Jitb.Employment.HarriValidateTenant.Services;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.Domain.Concepts.Config;
using System.Collections.Generic;
using System.Linq;
using RestSharp;
using Newtonsoft.Json;

namespace Jitb.Employment.HarriValidateTenant.Tests.Services
{
    [TestClass]
    public class LocationValidationServiceTests
    {
        private Mock<IHarriTenantByLocationRepository> _mockHarriTenantByLocationRepository;
        private Mock<IEmployeeRepository> _mockEmployeeRepository;
        private Mock<ICallHarriWebServiceProvider> _mockCallHarriWebServiceProvider;
        private Mock<IGenderDictionary> _mockGenderDictionary;
        private Mock<IHarriTransformsProvider> _mockHarriTransformsProvider;
        private Mock<IConfigurationService> _mockConfigurationService;
        private LocationValidationService _locationValidationService;

        [TestInitialize]
        public void Setup()
        {
            _mockHarriTenantByLocationRepository = new Mock<IHarriTenantByLocationRepository>();
            _mockEmployeeRepository = new Mock<IEmployeeRepository>();
            _mockCallHarriWebServiceProvider = new Mock<ICallHarriWebServiceProvider>();
            _mockGenderDictionary = new Mock<IGenderDictionary>();
            _mockHarriTransformsProvider = new Mock<IHarriTransformsProvider>();
            _mockConfigurationService = new Mock<IConfigurationService>();

            _locationValidationService = new LocationValidationService(
                _mockHarriTenantByLocationRepository.Object,
                _mockEmployeeRepository.Object,
                _mockCallHarriWebServiceProvider.Object,
                _mockGenderDictionary.Object,
                _mockHarriTransformsProvider.Object,
                _mockConfigurationService.Object);
        }

        [TestMethod]
        [TestCategory("LocationValidation")]
        public async Task ValidateLocationAsync_WhenSkipConfiguredAndEnoughEmployees_ShouldSkipEmployeeLoad()
        {
            // Arrange
            const int locationId = 123;
            const int existingEmployeeCount = 15;
            const int minimumCountToSkip = 10;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid() 
            };

            // Setup configuration to enable skipping
            _mockConfigurationService.Setup(x => x.SkipEmployeeLoadWhenPreexistingFound())
                .Returns(true);
            _mockConfigurationService.Setup(x => x.MinimumExistingEmployeeCountToSkip())
                .Returns(minimumCountToSkip);

            // Setup repository to return a tenant
            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // Setup location validation to return success
            SetupSuccessfulLocationValidation(harriTenant.TenantId, locationId);

            // Setup employee count to meet skip criteria
            SetupEmployeeCountResponse(harriTenant.TenantId, locationId, existingEmployeeCount);

            // Act
            await _locationValidationService.ValidateLocationAsync(locationId);

            // Assert
            // Verify that employee repository was NOT called (meaning we skipped employee loading)
            _mockEmployeeRepository.Verify(x => x.GetListByLocationId(It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<bool>()), 
                Times.Never, "Should not attempt to get employees when skipping is enabled and threshold is met");
        }

        [TestMethod]
        [TestCategory("LocationValidation")]
        public async Task ValidateLocationAsync_WhenSkipDisabledButEnoughEmployees_ShouldNotSkipEmployeeLoad()
        {
            // Arrange
            const int locationId = 123;
            const int existingEmployeeCount = 15;
            const int minimumCountToSkip = 10;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid() 
            };

            // Setup configuration to disable skipping
            _mockConfigurationService.Setup(x => x.SkipEmployeeLoadWhenPreexistingFound())
                .Returns(false);
            _mockConfigurationService.Setup(x => x.MinimumExistingEmployeeCountToSkip())
                .Returns(minimumCountToSkip);

            // Setup repository to return a tenant
            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // Setup location validation to return success
            SetupSuccessfulLocationValidation(harriTenant.TenantId, locationId);

            // Setup employee count to meet skip criteria (but skipping is disabled)
            SetupEmployeeCountResponse(harriTenant.TenantId, locationId, existingEmployeeCount);

            // Setup empty employee list to avoid null reference
            _mockEmployeeRepository.Setup(x => x.GetListByLocationId(locationId, false, false))
                .Returns(new List<Jitb.Employment.Domain.Concepts.Employee>().AsQueryable());

            // Act
            await _locationValidationService.ValidateLocationAsync(locationId);

            // Assert
            // Verify that employee repository WAS called (meaning we did not skip employee loading)
            _mockEmployeeRepository.Verify(x => x.GetListByLocationId(locationId, false, false), 
                Times.Once, "Should attempt to get employees when skipping is disabled");
        }

        [TestMethod]
        [TestCategory("LocationValidation")]
        public async Task ValidateLocationAsync_WhenSkipEnabledButNotEnoughEmployees_ShouldNotSkipEmployeeLoad()
        {
            // Arrange
            const int locationId = 123;
            const int existingEmployeeCount = 5;
            const int minimumCountToSkip = 10;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid() 
            };

            // Setup configuration to enable skipping
            _mockConfigurationService.Setup(x => x.SkipEmployeeLoadWhenPreexistingFound())
                .Returns(true);
            _mockConfigurationService.Setup(x => x.MinimumExistingEmployeeCountToSkip())
                .Returns(minimumCountToSkip);

            // Setup repository to return a tenant
            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // Setup location validation to return success
            SetupSuccessfulLocationValidation(harriTenant.TenantId, locationId);

            // Setup employee count below skip threshold
            SetupEmployeeCountResponse(harriTenant.TenantId, locationId, existingEmployeeCount);

            // Setup empty employee list to avoid null reference
            _mockEmployeeRepository.Setup(x => x.GetListByLocationId(locationId, false, false))
                .Returns(new List<Jitb.Employment.Domain.Concepts.Employee>().AsQueryable());

            // Act
            await _locationValidationService.ValidateLocationAsync(locationId);

            // Assert
            // Verify that employee repository WAS called (meaning we did not skip employee loading)
            _mockEmployeeRepository.Verify(x => x.GetListByLocationId(locationId, false, false), 
                Times.Once, "Should attempt to get employees when threshold is not met");
        }

        [TestMethod]
        [TestCategory("LocationValidation")]
        public async Task ValidateLocationAsync_WhenNoTenantFound_ShouldReturnEarly()
        {
            // Arrange
            const int locationId = 123;

            // Setup repository to return null (no tenant found)
            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns((HarriTenantByLocation)null);

            // Act
            await _locationValidationService.ValidateLocationAsync(locationId);

            // Assert
            // Verify that no further processing occurred
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(It.IsAny<Guid>(), It.IsAny<Method>(), 
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<string>()), 
                Times.Never, "Should not make Harri calls when no tenant is found");
        }

        // Helper methods to setup common mock scenarios
        private void SetupSuccessfulLocationValidation(Guid tenantId, int locationId)
        {
            // Mock successful location validation response
            var locationResponse = new RestResponse
            {
                IsSuccessful = true,
                Content = $"[{{\"Id\": {locationId}, \"Name\": \"J{locationId:D6}\", \"Type\": \"Store\"}}]"
            };

            _mockCallHarriWebServiceProvider.Setup(x => x.Call(
                tenantId, 
                Method.Get, 
                It.Is<string>(url => url.Contains("locations")), 
                null, 
                false, 
                "V1"))
                .ReturnsAsync(locationResponse);
        }

        private void SetupEmployeeCountResponse(Guid tenantId, int locationId, int employeeCount)
        {
            // Create mock employee list
            var employees = new List<object>();
            for (int i = 0; i < employeeCount; i++)
            {
                employees.Add(new { Id = i + 1, Name = $"Employee{i + 1}" });
            }

            var employeeResponse = new RestResponse
            {
                IsSuccessful = true,
                Content = $"{{\"Employees\": {JsonConvert.SerializeObject(employees)}}}"
            };

            _mockCallHarriWebServiceProvider.Setup(x => x.Call(
                tenantId, 
                Method.Get, 
                It.Is<string>(url => url.Contains("employees") && url.Contains($"location_id={locationId}")), 
                null, 
                false, 
                "V2"))
                .ReturnsAsync(employeeResponse);
        }
    }
}