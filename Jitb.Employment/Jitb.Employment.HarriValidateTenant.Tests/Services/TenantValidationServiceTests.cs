using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Threading.Tasks;
using Jitb.Employment.HarriValidateTenant.Services;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;

namespace Jitb.Employment.HarriValidateTenant.Tests.Services
{
    /// <summary>
    /// Tests for ITenantValidationService - New tenant-focused validation service.
    /// These tests are written in TDD Red phase and WILL FAIL until implementation is created.
    /// 
    /// The ITenantValidationService interface and TenantValidationService class DO NOT EXIST YET.
    /// This is intentional - we're defining expected behavior before implementation (TDD Red phase).
    /// </summary>
    [TestClass]
    public class TenantValidationServiceTests
    {
        private Mock<IHarriTenantByLocationRepository> _mockHarriTenantByLocationRepository;
        private Mock<IHarriTenantRepository> _mockHarriTenantRepository;
        private Mock<ILocationValidationService> _mockLocationValidationService;
        private Mock<IConfigurationService> _mockConfigurationService;
        private Mock<ITenantValidationResultCapture> _mockResultCapture;
        private Mock<ICallHarriWebServiceProvider> _mockCallHarriWebServiceProvider;
        private ITenantValidationService _tenantValidationService;

        [TestInitialize]
        public void Setup()
        {
            _mockHarriTenantByLocationRepository = new Mock<IHarriTenantByLocationRepository>();
            _mockHarriTenantRepository = new Mock<IHarriTenantRepository>();
            _mockLocationValidationService = new Mock<ILocationValidationService>();
            _mockConfigurationService = new Mock<IConfigurationService>();
            _mockResultCapture = new Mock<ITenantValidationResultCapture>();
            _mockCallHarriWebServiceProvider = new Mock<ICallHarriWebServiceProvider>();

            _tenantValidationService = new TenantValidationService(
                _mockHarriTenantByLocationRepository.Object,
                _mockHarriTenantRepository.Object,
                _mockLocationValidationService.Object,
                _mockConfigurationService.Object,
                _mockResultCapture.Object,
                _mockCallHarriWebServiceProvider.Object);
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_WhenCalledWithValidTenant_PerformsAllValidationSteps()
        {
            // Arrange
            const string tenantId = "TenantA";
            const int locationId = 2345;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // Act & Assert
            // THIS WILL FAIL - ValidateLocationForTenantAsync method doesn't exist yet
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Verify that the underlying location validation service was called
            // This ensures tenant validation delegates to existing location validation logic
            _mockLocationValidationService.Verify(x => x.ValidateLocationAsync(locationId), 
                Times.Once, 
                "Should delegate to location validation service to perform core validation logic");

            // Verify tenant lookup was performed
            _mockHarriTenantByLocationRepository.Verify(x => x.GetTenantByLocation(locationId), 
                Times.Once, 
                "Should lookup tenant information for the location");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_WhenTenantIsNull_HandlesProperly()
        {
            // Arrange
            const string nullTenantId = "(null)";
            const int locationId = 1234;
            
            // Setup for null tenant scenario
            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns((HarriTenantByLocation)null);

            // Act
            // THIS WILL FAIL - ValidateLocationForTenantAsync method doesn't exist yet
            await _tenantValidationService.ValidateLocationForTenantAsync(nullTenantId, locationId);

            // Assert
            // Should still validate the location even with null tenant
            _mockLocationValidationService.Verify(x => x.ValidateLocationAsync(locationId), 
                Times.Once, 
                "Should still validate location even when tenant is null");

            // Verify tenant lookup was attempted
            _mockHarriTenantByLocationRepository.Verify(x => x.GetTenantByLocation(locationId), 
                Times.Once, 
                "Should attempt to lookup tenant even for null tenant scenarios");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_WhenLocationNotFoundForTenant_LogsWarning()
        {
            // Arrange
            const string tenantId = "TenantB";
            const int locationId = 3456;
            
            // Setup scenario where location exists but has different tenant
            var differentTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = "DifferentTenant"
            };
            
            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(differentTenant);

            // Act
            // THIS WILL FAIL - ValidateLocationForTenantAsync method doesn't exist yet
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            // Should still proceed with validation despite tenant mismatch
            _mockLocationValidationService.Verify(x => x.ValidateLocationAsync(locationId), 
                Times.Once, 
                "Should proceed with validation even when tenant doesn't match expected");

            // Note: In real implementation, this should log a warning about tenant mismatch
            // Actual logging verification would require capturing log output
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_WhenLocationValidationFails_PropagatesException()
        {
            // Arrange
            const string tenantId = "TenantC";
            const int locationId = 4567;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // Setup location validation to throw an exception
            var expectedException = new InvalidOperationException("Location validation failed");
            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .ThrowsAsync(expectedException);

            // Act & Assert
            // THIS WILL FAIL - ValidateLocationForTenantAsync method doesn't exist yet
            var actualException = await Assert.ThrowsExceptionAsync<InvalidOperationException>(
                () => _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId));

            Assert.AreEqual(expectedException.Message, actualException.Message,
                "Should propagate the exact exception from location validation");

            // Verify tenant lookup was performed before the exception
            _mockHarriTenantByLocationRepository.Verify(x => x.GetTenantByLocation(locationId), 
                Times.Once, 
                "Should perform tenant lookup before attempting validation");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_WhenCalledWithNullParameters_ThrowsArgumentException()
        {
            // Act & Assert - Test null tenant ID
            // THIS WILL FAIL - ValidateLocationForTenantAsync method doesn't exist yet
            await Assert.ThrowsExceptionAsync<ArgumentNullException>(
                () => _tenantValidationService.ValidateLocationForTenantAsync(null, 1234));

            // Act & Assert - Test invalid location ID
            // THIS WILL FAIL - ValidateLocationForTenantAsync method doesn't exist yet
            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => _tenantValidationService.ValidateLocationForTenantAsync("ValidTenant", 0));

            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => _tenantValidationService.ValidateLocationForTenantAsync("ValidTenant", -1));
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_WhenCalledConcurrently_HandlesMultipleTenants()
        {
            // Arrange - Multiple tenant/location pairs
            var tenantLocationPairs = new[]
            {
                ("TenantA", 1001),
                ("TenantB", 2002),
                ("TenantC", 3003),
                ("(null)", 4004)
            };

            foreach (var (tenant, location) in tenantLocationPairs)
            {
                var harriTenant = tenant == "(null)" ? null : new HarriTenantByLocation 
                { 
                    LocationId = location, 
                    TenantId = Guid.NewGuid(),
                    TenantCode = tenant
                };

                _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(location))
                    .Returns(harriTenant);
            }

            // Act - Process all tenant validations concurrently
            // THIS WILL FAIL - ValidateLocationForTenantAsync method doesn't exist yet
            var validationTasks = tenantLocationPairs.Select(pair => 
                _tenantValidationService.ValidateLocationForTenantAsync(pair.Item1, pair.Item2));

            await Task.WhenAll(validationTasks);

            // Assert - Verify all locations were validated
            foreach (var (tenant, location) in tenantLocationPairs)
            {
                _mockLocationValidationService.Verify(x => x.ValidateLocationAsync(location), 
                    Times.Once, 
                    $"Should validate location {location} for tenant {tenant}");

                _mockHarriTenantByLocationRepository.Verify(x => x.GetTenantByLocation(location), 
                    Times.Once, 
                    $"Should lookup tenant for location {location}");
            }
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_WhenConfigurationDisablesValidation_SkipsValidation()
        {
            // Arrange
            const string tenantId = "TenantD";
            const int locationId = 5678;
            
            // Setup configuration to disable tenant validation
            _mockConfigurationService.Setup(x => x.IsTenantValidationEnabled())
                .Returns(false);

            // Act
            // THIS WILL FAIL - ValidateLocationForTenantAsync method doesn't exist yet
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            // Should skip validation when disabled by configuration
            _mockLocationValidationService.Verify(x => x.ValidateLocationAsync(It.IsAny<int>()), 
                Times.Never, 
                "Should not perform validation when tenant validation is disabled");

            // Should still check configuration
            _mockConfigurationService.Verify(x => x.IsTenantValidationEnabled(), 
                Times.Once, 
                "Should check if tenant validation is enabled");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TenantNameLookup")]
        public async Task ValidateLocationForTenantAsync_WhenTenantFound_UsesActualTenantNameForDisplay()
        {
            // Arrange
            const string tenantId = "TenantE";
            const int locationId = 6789;
            var tenantGuid = Guid.NewGuid();
            const string expectedTenantName = "Corporate HQ";
            
            var harriTenantByLocation = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = tenantGuid,
                TenantCode = tenantId
            };

            var harriTenant = new HarriTenant
            {
                TenantId = tenantGuid,
                Name = expectedTenantName,
                IsActive = true
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenantByLocation);
                
            _mockHarriTenantRepository.Setup(x => x.FirstOrDefault(It.IsAny<Func<HarriTenant, bool>>()))
                .Returns(harriTenant);

            _mockConfigurationService.Setup(x => x.IsTenantValidationEnabled())
                .Returns(true);

            // Act
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            // Verify that the result capture was called with the actual tenant name, not the GUID
            _mockResultCapture.Verify(x => x.StartTenantValidation(tenantId, expectedTenantName, locationId), 
                Times.Once, 
                "Should use actual tenant name from HarriTenant table for display");

            // Verify tenant lookup was performed
            _mockHarriTenantByLocationRepository.Verify(x => x.GetTenantByLocation(locationId), 
                Times.Once, 
                "Should lookup tenant-location mapping");
                
            _mockHarriTenantRepository.Verify(x => x.FirstOrDefault(It.IsAny<Func<HarriTenant, bool>>()), 
                Times.Once, 
                "Should lookup tenant details for name");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("TenantNameLookup")]
        public async Task ValidateLocationForTenantAsync_WhenTenantNotFoundInTable_FallsBackToGuid()
        {
            // Arrange
            const string tenantId = "TenantF";
            const int locationId = 7890;
            var tenantGuid = Guid.NewGuid();
            
            var harriTenantByLocation = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = tenantGuid,
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenantByLocation);
                
            _mockHarriTenantRepository.Setup(x => x.FirstOrDefault(It.IsAny<Func<HarriTenant, bool>>()))
                .Returns((HarriTenant)null);

            _mockConfigurationService.Setup(x => x.IsTenantValidationEnabled())
                .Returns(true);

            // Act
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            // Should fall back to using GUID when tenant name not found
            _mockResultCapture.Verify(x => x.StartTenantValidation(tenantId, tenantGuid.ToString(), locationId), 
                Times.Once, 
                "Should fall back to GUID when tenant name not found in HarriTenant table");
        }
    }
}