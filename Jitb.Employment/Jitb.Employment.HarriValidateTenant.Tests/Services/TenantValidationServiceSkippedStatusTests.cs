using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Threading.Tasks;
using Jitb.Employment.HarriValidateTenant.Services;
using Jitb.Employment.Domain.Repositories.Config;

namespace Jitb.Employment.HarriValidateTenant.Tests.Services
{
    /// <summary>
    /// Tests for TenantValidationService to handle "Skipped" status for Load Employee component.
    /// These tests are written in TDD Red phase and WILL FAIL initially until implementation is updated.
    /// 
    /// User requirements:
    /// 1. Load Employee should show "Skipped" when employees were skipped due to preexisting employees, not "Pass"
    /// 2. TenantValidationService should distinguish between actual success vs skipped scenarios
    /// 3. Read Employee component should be added after Load Employee
    /// </summary>
    [TestClass]
    public class TenantValidationServiceSkippedStatusTests
    {
        private Mock<IHarriTenantByLocationRepository> _mockHarriTenantByLocationRepository;
        private Mock<ILocationValidationService> _mockLocationValidationService;
        private Mock<IConfigurationService> _mockConfigurationService;
        private Mock<ITenantValidationResultCapture> _mockResultCapture;
        private TenantValidationService _tenantValidationService;

        [TestInitialize]
        public void Setup()
        {
            _mockHarriTenantByLocationRepository = new Mock<IHarriTenantByLocationRepository>();
            _mockLocationValidationService = new Mock<ILocationValidationService>();
            _mockConfigurationService = new Mock<IConfigurationService>();
            _mockResultCapture = new Mock<ITenantValidationResultCapture>();

            // Setup default configuration to enable validation
            _mockConfigurationService.Setup(x => x.IsTenantValidationEnabled()).Returns(true);

            _tenantValidationService = new TenantValidationService(
                _mockHarriTenantByLocationRepository.Object,
                _mockLocationValidationService.Object,
                _mockConfigurationService.Object,
                _mockResultCapture.Object);
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("SkippedStatus")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_ShouldRecordSkippedForLoadEmployee_WhenEmployeesWereSkippedDueToPreexistingEmployees()
        {
            // Arrange
            const string tenantId = "test-tenant";
            const int locationId = 3365;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // THIS WILL FAIL - Need to enhance LocationValidationService to return detailed results
            // Mock location validation to indicate employees were skipped
            var locationValidationResult = new LocationValidationResult
            {
                Success = true,
                LoadEmployeeStatus = "Skipped",
                LoadEmployeeDetails = "Employees skipped due to preexisting employees",
                EmployeesProcessed = 0,
                EmployeesSkipped = 5
            };

            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .ReturnsAsync(locationValidationResult);

            // Act
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            // THIS WILL FAIL - Need to update TenantValidationService to handle detailed results from LocationValidationService
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Load Employee", 
                "Skipped", 
                "Employees skipped due to preexisting employees"), 
                Times.Once,
                "Should record 'Skipped' status when employees were skipped due to preexisting employees");

            // Should still record Read Employee as Pass (if employees were read successfully)
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Read Employee", 
                "Pass", 
                It.IsAny<string>()), 
                Times.Once,
                "Should record Read Employee as Pass when employee data was read successfully");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("SkippedStatus")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_ShouldRecordPassForLoadEmployee_WhenEmployeesWereActuallyLoaded()
        {
            // Arrange
            const string tenantId = "test-tenant";
            const int locationId = 3365;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // THIS WILL FAIL - Need to enhance LocationValidationService to return detailed results
            var locationValidationResult = new LocationValidationResult
            {
                Success = true,
                LoadEmployeeStatus = "Pass",
                LoadEmployeeDetails = "3 employees loaded successfully",
                EmployeesProcessed = 3,
                EmployeesSkipped = 0
            };

            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .ReturnsAsync(locationValidationResult);

            // Act
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Load Employee", 
                "Pass", 
                "3 employees loaded successfully"), 
                Times.Once,
                "Should record 'Pass' status when employees were actually loaded");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("SkippedStatus")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_ShouldRecordSkippedForReadEmployee_WhenEmployeeDataCouldNotBeRead()
        {
            // Arrange
            const string tenantId = "test-tenant";
            const int locationId = 3365;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // THIS WILL FAIL - Need to enhance LocationValidationService to return detailed results
            var locationValidationResult = new LocationValidationResult
            {
                Success = true,
                LoadEmployeeStatus = "Pass",
                LoadEmployeeDetails = "2 employees loaded successfully",
                ReadEmployeeStatus = "Skipped",
                ReadEmployeeDetails = "Employee data read operation was skipped",
                EmployeesProcessed = 2,
                EmployeesSkipped = 0
            };

            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .ReturnsAsync(locationValidationResult);

            // Act
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Read Employee", 
                "Skipped", 
                "Employee data read operation was skipped"), 
                Times.Once,
                "Should record 'Skipped' status for Read Employee when read operation was skipped");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("SkippedStatus")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_ShouldRecordFailForLoadEmployee_WhenLoadEmployeeFails()
        {
            // Arrange
            const string tenantId = "test-tenant";
            const int locationId = 3365;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // THIS WILL FAIL - Need to enhance LocationValidationService to return detailed results
            var locationValidationResult = new LocationValidationResult
            {
                Success = false,
                LoadEmployeeStatus = "Fail",
                LoadEmployeeDetails = "Database connection failed during employee load",
                EmployeesProcessed = 0,
                EmployeesSkipped = 0
            };

            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .ReturnsAsync(locationValidationResult);

            // Act & Assert
            var exception = await Assert.ThrowsExceptionAsync<Exception>(() =>
                _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId));

            // Verify failure was recorded
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Load Employee", 
                "Fail", 
                "Database connection failed during employee load"), 
                Times.Once,
                "Should record 'Fail' status when Load Employee operation fails");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("SkippedStatus")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_ShouldProvideDetailedStatusForMixedResults()
        {
            // Arrange
            const string tenantId = "test-tenant";
            const int locationId = 3365;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // THIS WILL FAIL - Need to enhance LocationValidationService to return detailed results
            var locationValidationResult = new LocationValidationResult
            {
                Success = true,
                LoadEmployeeStatus = "Partial",
                LoadEmployeeDetails = "2 employees loaded, 3 skipped (already exist)",
                ReadEmployeeStatus = "Pass",
                ReadEmployeeDetails = "Employee data read successfully",
                EmployeesProcessed = 2,
                EmployeesSkipped = 3
            };

            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .ReturnsAsync(locationValidationResult);

            // Act
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Load Employee", 
                "Partial", 
                "2 employees loaded, 3 skipped (already exist)"), 
                Times.Once,
                "Should record detailed status for partial success scenarios");

            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Read Employee", 
                "Pass", 
                "Employee data read successfully"), 
                Times.Once,
                "Should record Pass status for Read Employee when successful");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("SkippedStatus")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_ShouldUseDefaultBehavior_WhenLocationValidationServiceDoesNotProvideDetailedResults()
        {
            // Arrange
            const string tenantId = "test-tenant";
            const int locationId = 3365;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            // Mock the old behavior - just success/failure without detailed results
            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .Returns(Task.CompletedTask); // Old method signature - returns void

            // Act
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            // Should fall back to default "Pass" behavior when detailed results are not available
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Load Employee", 
                "Pass", 
                It.IsAny<string>()), 
                Times.Once,
                "Should default to 'Pass' when detailed results are not available");

            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, 
                "Read Employee", 
                "Pass", 
                It.IsAny<string>()), 
                Times.Once,
                "Should default to 'Pass' for Read Employee when detailed results are not available");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("SkippedStatus")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_ShouldRecordComponentsInCorrectOrder_IncludingReadEmployee()
        {
            // Arrange
            const string tenantId = "test-tenant";
            const int locationId = 3365;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            var locationValidationResult = new LocationValidationResult
            {
                Success = true,
                LoadEmployeeStatus = "Skipped",
                ReadEmployeeStatus = "Pass"
            };

            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .ReturnsAsync(locationValidationResult);

            // Track the order of component recordings
            var componentOrder = new List<string>();
            _mockResultCapture.Setup(x => x.RecordComponentResult(
                It.IsAny<string>(), 
                It.IsAny<string>(), 
                It.IsAny<string>(), 
                It.IsAny<string>()))
                .Callback<string, string, string, string>((tenant, component, status, details) =>
                {
                    componentOrder.Add(component);
                });

            // Act
            await _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId);

            // Assert
            var expectedOrder = new[]
            {
                "Tenant Lookup",
                "GetLocations", 
                "Load Employee",
                "Read Employee",
                "Position Mapping",
                "Event Received"
            };

            // Check that the components are recorded in the correct order
            foreach (var expectedComponent in expectedOrder)
            {
                Assert.IsTrue(componentOrder.Contains(expectedComponent),
                    $"Component '{expectedComponent}' should be recorded");
            }

            var loadEmployeeIndex = componentOrder.IndexOf("Load Employee");
            var readEmployeeIndex = componentOrder.IndexOf("Read Employee");
            var positionMappingIndex = componentOrder.IndexOf("Position Mapping");

            Assert.IsTrue(loadEmployeeIndex >= 0, "Load Employee should be recorded");
            Assert.IsTrue(readEmployeeIndex >= 0, "Read Employee should be recorded");
            Assert.IsTrue(positionMappingIndex >= 0, "Position Mapping should be recorded");

            Assert.IsTrue(loadEmployeeIndex < readEmployeeIndex,
                "Load Employee should be recorded before Read Employee");
            Assert.IsTrue(readEmployeeIndex < positionMappingIndex,
                "Read Employee should be recorded before Position Mapping");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("SkippedStatus")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateLocationForTenantAsync_ShouldHandleException_WhenLocationValidationServiceReturnsDetailedResults()
        {
            // Arrange
            const string tenantId = "test-tenant";
            const int locationId = 3365;
            
            var harriTenant = new HarriTenantByLocation 
            { 
                LocationId = locationId, 
                TenantId = Guid.NewGuid(),
                TenantCode = tenantId
            };

            _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(locationId))
                .Returns(harriTenant);

            var expectedException = new InvalidOperationException("Location validation failed");
            _mockLocationValidationService.Setup(x => x.ValidateLocationAsync(locationId))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var actualException = await Assert.ThrowsExceptionAsync<InvalidOperationException>(() =>
                _tenantValidationService.ValidateLocationForTenantAsync(tenantId, locationId));

            Assert.AreEqual(expectedException.Message, actualException.Message);

            // Should record failure for all components when validation fails
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, "GetLocations", "Fail", It.IsAny<string>()), Times.Once);
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, "Load Employee", "Fail", It.IsAny<string>()), Times.Once);
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, "Read Employee", "Fail", It.IsAny<string>()), Times.Once);
            _mockResultCapture.Verify(x => x.RecordComponentResult(
                tenantId, "Position Mapping", "Fail", It.IsAny<string>()), Times.Once);
        }
    }

    /// <summary>
    /// THIS WILL FAIL - LocationValidationResult class doesn't exist yet
    /// This is the expected return type for enhanced LocationValidationService
    /// </summary>
    public class LocationValidationResult
    {
        public bool Success { get; set; }
        public string LoadEmployeeStatus { get; set; }
        public string LoadEmployeeDetails { get; set; }
        public string ReadEmployeeStatus { get; set; }
        public string ReadEmployeeDetails { get; set; }
        public int EmployeesProcessed { get; set; }
        public int EmployeesSkipped { get; set; }
    }
}