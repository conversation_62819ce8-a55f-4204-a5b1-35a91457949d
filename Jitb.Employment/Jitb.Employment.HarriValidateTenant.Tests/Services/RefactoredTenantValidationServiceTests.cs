using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Jitb.Employment.HarriValidateTenant.Services;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.Domain.Repositories.Employment;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.HarriOutbound.Endpoint.Providers;
using Jitb.Employment.HarriCaller.Domain.Concepts;
using Newtonsoft.Json;
using RestSharp;

namespace Jitb.Employment.HarriValidateTenant.Tests.Services
{
    /// <summary>
    /// Comprehensive failing unit tests for the refactored HarriValidateTenant system.
    /// These tests follow TDD Red phase and WILL FAIL until the new interface methods are implemented.
    /// 
    /// CRITICAL: These tests define the new pseudo code structure:
    /// - ValidationRoutine() orchestrates the overall validation process
    /// - ValidateTenantsAsync() handles individual tenant validation with skip logic
    /// - All methods return ValueTuples with result text and additional information
    /// - Tenant list contains ValueTuples with tenantId, tenantName, and List&lt;int&gt; locations
    /// </summary>
    [TestClass]
    public class RefactoredTenantValidationServiceTests
    {
        private Mock<IHarriTenantByLocationRepository> _mockHarriTenantByLocationRepository;
        private Mock<IHarriTenantRepository> _mockHarriTenantRepository;
        private Mock<ILocationValidationService> _mockLocationValidationService;
        private Mock<IConfigurationService> _mockConfigurationService;
        private Mock<ITenantValidationResultCapture> _mockResultCapture;
        private Mock<ICallHarriWebServiceProvider> _mockCallHarriWebServiceProvider;
        private Mock<IEmployeeRepository> _mockEmployeeRepository;
        private Mock<IHireEmployeeOutboundProvider> _mockHireEmployeeOutboundProvider;
        private Mock<IGenderDictionary> _mockGenderDictionary;
        private Mock<IHarriTransformsProvider> _mockHarriTransformsProvider;
        private Mock<IRefactoredTenantValidationService> _mockRefactoredService;
        private RefactoredTenantValidationService _refactoredTenantValidationService;

        [TestInitialize]
        public void Setup()
        {
            _mockHarriTenantByLocationRepository = new Mock<IHarriTenantByLocationRepository>();
            _mockHarriTenantRepository = new Mock<IHarriTenantRepository>();
            _mockLocationValidationService = new Mock<ILocationValidationService>();
            _mockConfigurationService = new Mock<IConfigurationService>();
            _mockResultCapture = new Mock<ITenantValidationResultCapture>();
            _mockCallHarriWebServiceProvider = new Mock<ICallHarriWebServiceProvider>();
            _mockEmployeeRepository = new Mock<IEmployeeRepository>();
            _mockHireEmployeeOutboundProvider = new Mock<IHireEmployeeOutboundProvider>();
            _mockGenderDictionary = new Mock<IGenderDictionary>();
            _mockHarriTransformsProvider = new Mock<IHarriTransformsProvider>();
            _mockRefactoredService = new Mock<IRefactoredTenantValidationService>();

            // Create the RefactoredTenantValidationService with all mocked dependencies
            _refactoredTenantValidationService = new RefactoredTenantValidationService(
                _mockHarriTenantByLocationRepository.Object,
                _mockHarriTenantRepository.Object,
                _mockLocationValidationService.Object,
                _mockConfigurationService.Object,
                _mockResultCapture.Object,
                _mockCallHarriWebServiceProvider.Object,
                _mockEmployeeRepository.Object,
                _mockHireEmployeeOutboundProvider.Object,
                _mockGenderDictionary.Object,
                _mockHarriTransformsProvider.Object);
        }

        #region ValidationRoutine Tests

        [TestMethod]
        [TestCategory("ValidationRoutine")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidationRoutine_WhenCalledWithMixedTenants_ProcessesAllTenantsAndGeneratesReport()
        {
            // Arrange
            var tenantList = new[]
            {
                ("TenantA", "Corporate HQ", new List<int> { 1001, 1002 }),
                ("TenantB", "Franchise Alpha", new List<int> { 2001 }),
                (null, null, new List<int> { 3001 }) // Null tenant scenario
            };

            // Set up mocks for GetTenantsForAllLocationsAsync to return our test data
            _mockRefactoredService.Setup(x => x.GetTenantsForAllLocationsAsync())
                .ReturnsAsync(tenantList);

            // Act - Call ValidationRoutine which now exists
            await _mockRefactoredService.Object.ValidationRoutine();

            // Assert
            // Verify ValidateTenantsAsync was called for valid tenants
            _mockRefactoredService.Verify(x => x.ValidateTenantsAsync("TenantA", "Corporate HQ", 
                It.Is<List<int>>(list => list.SequenceEqual(new[] { 1001, 1002 }))), 
                Times.Once, "Should validate TenantA with its locations");

            _mockRefactoredService.Verify(x => x.ValidateTenantsAsync("TenantB", "Franchise Alpha", 
                It.Is<List<int>>(list => list.SequenceEqual(new[] { 2001 }))), 
                Times.Once, "Should validate TenantB with its location");

            // Verify null tenant was NOT passed to ValidateTenantsAsync (handled differently)
            _mockRefactoredService.Verify(x => x.ValidateTenantsAsync(null, null, It.IsAny<List<int>>()), 
                Times.Never, "Null tenants should be handled separately, not passed to ValidateTenantsAsync");
        }

        [TestMethod]
        [TestCategory("ValidationRoutine")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidationRoutine_WhenAllTenantsAreNull_AddsAllLocationsToFailReport()
        {
            // Arrange - All tenants are null
            var allNullTenantList = new[]
            {
                (null, null, new List<int> { 1001, 1002, 1003 }),
                (null, null, new List<int> { 2001, 2002 })
            };

            // THIS WILL FAIL - GetTenantsForAllLocationsAsync method doesn't exist yet
            _mockTenantValidationService.Setup(x => x.GetTenantsForAllLocationsAsync())
                .ReturnsAsync(allNullTenantList);

            // Act
            // THIS WILL FAIL - ValidationRoutine method doesn't exist yet
            await _tenantValidationService.ValidationRoutine();

            // Assert
            // Verify ValidateTenantsAsync was never called since all tenants are null
            _mockTenantValidationService.Verify(x => x.ValidateTenantsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<List<int>>()), 
                Times.Never, "Should not call ValidateTenantsAsync when all tenants are null");

            // Verify report capture was called for each null location with 'Fail' result
            var expectedFailedLocations = new[] { 1001, 1002, 1003, 2001, 2002 };
            foreach (var locationId in expectedFailedLocations)
            {
                _mockResultCapture.Verify(x => x.AddLocationFailure(locationId, "Fail", "No tenant found"), 
                    Times.Once, $"Should add location {locationId} to fail report");
            }
        }

        [TestMethod]
        [TestCategory("ValidationRoutine")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidationRoutine_WhenNoTenantsFound_CompletesSuccessfully()
        {
            // Arrange - Empty tenant list
            var emptyTenantList = new (string tenantId, string tenantName, List<int> locations)[0];

            // THIS WILL FAIL - GetTenantsForAllLocationsAsync method doesn't exist yet
            _mockTenantValidationService.Setup(x => x.GetTenantsForAllLocationsAsync())
                .ReturnsAsync(emptyTenantList);

            // Act
            // THIS WILL FAIL - ValidationRoutine method doesn't exist yet
            await _tenantValidationService.ValidationRoutine();

            // Assert
            // Should complete without errors and not call validation methods
            _mockTenantValidationService.Verify(x => x.ValidateTenantsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<List<int>>()), 
                Times.Never, "Should not call ValidateTenantsAsync when no tenants exist");
        }

        #endregion

        #region ValidateTenantsAsync Tests

        [TestMethod]
        [TestCategory("ValidateTenantsAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateTenantsAsync_WhenSkipConditionsMet_SkipsLoadManagerAndReturns()
        {
            // Arrange
            const string tenantId = "TenantA";
            const string tenantName = "Corporate HQ";
            var locations = new List<int> { 1001 };
            const int existingEmployeeCount = 75; // Above minimum to skip
            const int minimumCountToSkip = 50;
            const bool skipWhenPreexistingFound = true;

            var firstEmployee = new { Id = 1, Name = "John Doe" };

            // THIS WILL FAIL - GetEmployeesAsync method doesn't exist yet
            _mockTenantValidationService.Setup(x => x.GetEmployeesAsync(tenantId, 1001))
                .ReturnsAsync((existingEmployeeCount, firstEmployee));

            _mockConfigurationService.Setup(x => x.GetSkipWhenPreexistingFound())
                .Returns(skipWhenPreexistingFound);
            _mockConfigurationService.Setup(x => x.GetMinimumCountToSkip())
                .Returns(minimumCountToSkip);

            // Act
            // THIS WILL FAIL - ValidateTenantsAsync method doesn't exist yet
            await _tenantValidationService.ValidateTenantsAsync(tenantId, tenantName, locations);

            // Assert
            // Verify LoadManagerAsync was NOT called due to skip condition
            _mockTenantValidationService.Verify(x => x.LoadManagerAsync(It.IsAny<string>(), It.IsAny<int>(), 
                It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<int>()), 
                Times.Never, "Should skip LoadManagerAsync when existing employee count meets skip criteria");

            // Verify ReadEmployeeAsync was called with first employee (skipped = true)
            _mockTenantValidationService.Verify(x => x.ReadEmployeeAsync(tenantId, 1001, true, firstEmployee), 
                Times.Once, "Should read first employee when load manager is skipped");

            // Verify ValidatePositionsAsync was still called
            _mockTenantValidationService.Verify(x => x.ValidatePositionsAsync(tenantId, 1001), 
                Times.Once, "Should still validate positions even when load manager is skipped");
        }

        [TestMethod]
        [TestCategory("ValidateTenantsAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateTenantsAsync_WhenSkipConditionsNotMet_ExecutesLoadManager()
        {
            // Arrange
            const string tenantId = "TenantB";
            const string tenantName = "Franchise Beta";
            var locations = new List<int> { 2001 };
            const int existingEmployeeCount = 25; // Below minimum to skip
            const int minimumCountToSkip = 50;
            const bool skipWhenPreexistingFound = true;

            var firstEmployee = new { Id = 2, Name = "Jane Smith" };
            var loadedManager = new { Id = 3, Name = "Manager Bob", IsManager = true };

            // THIS WILL FAIL - GetEmployeesAsync method doesn't exist yet
            _mockTenantValidationService.Setup(x => x.GetEmployeesAsync(tenantId, 2001))
                .ReturnsAsync((existingEmployeeCount, firstEmployee));

            // THIS WILL FAIL - LoadManagerAsync method doesn't exist yet
            _mockTenantValidationService.Setup(x => x.LoadManagerAsync(tenantId, 2001, existingEmployeeCount, 
                skipWhenPreexistingFound, minimumCountToSkip))
                .ReturnsAsync(("Pass", "Manager loaded successfully"));

            _mockConfigurationService.Setup(x => x.GetSkipWhenPreexistingFound())
                .Returns(skipWhenPreexistingFound);
            _mockConfigurationService.Setup(x => x.GetMinimumCountToSkip())
                .Returns(minimumCountToSkip);

            // Act
            // THIS WILL FAIL - ValidateTenantsAsync method doesn't exist yet
            await _tenantValidationService.ValidateTenantsAsync(tenantId, tenantName, locations);

            // Assert
            // Verify LoadManagerAsync WAS called since skip conditions not met
            _mockTenantValidationService.Verify(x => x.LoadManagerAsync(tenantId, 2001, existingEmployeeCount, 
                skipWhenPreexistingFound, minimumCountToSkip), 
                Times.Once, "Should call LoadManagerAsync when existing employee count below skip threshold");

            // Verify ReadEmployeeAsync was called with loaded manager (skipped = false)
            _mockTenantValidationService.Verify(x => x.ReadEmployeeAsync(tenantId, 2001, false, It.IsAny<object>()), 
                Times.Once, "Should read loaded manager when load manager was executed");
        }

        [TestMethod]
        [TestCategory("ValidateTenantsAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidateTenantsAsync_WhenMultipleLocations_ProcessesEachLocation()
        {
            // Arrange
            const string tenantId = "TenantC";
            const string tenantName = "Multi-Location Corp";
            var locations = new List<int> { 3001, 3002, 3003 };

            foreach (var locationId in locations)
            {
                // THIS WILL FAIL - GetEmployeesAsync method doesn't exist yet
                _mockTenantValidationService.Setup(x => x.GetEmployeesAsync(tenantId, locationId))
                    .ReturnsAsync((10, new { Id = locationId, Name = $"Employee_{locationId}" }));

                // THIS WILL FAIL - LoadManagerAsync method doesn't exist yet
                _mockTenantValidationService.Setup(x => x.LoadManagerAsync(tenantId, locationId, 10, false, 50))
                    .ReturnsAsync(("Pass", $"Manager loaded for location {locationId}"));

                // THIS WILL FAIL - ReadEmployeeAsync method doesn't exist yet
                _mockTenantValidationService.Setup(x => x.ReadEmployeeAsync(tenantId, locationId, false, It.IsAny<object>()))
                    .ReturnsAsync(("Pass", $"Employee read for location {locationId}"));

                // THIS WILL FAIL - ValidatePositionsAsync method doesn't exist yet
                _mockTenantValidationService.Setup(x => x.ValidatePositionsAsync(tenantId, locationId))
                    .ReturnsAsync(("Pass", $"Positions validated for location {locationId}"));
            }

            _mockConfigurationService.Setup(x => x.GetSkipWhenPreexistingFound()).Returns(false);
            _mockConfigurationService.Setup(x => x.GetMinimumCountToSkip()).Returns(50);

            // Act
            // THIS WILL FAIL - ValidateTenantsAsync method doesn't exist yet
            await _tenantValidationService.ValidateTenantsAsync(tenantId, tenantName, locations);

            // Assert
            // Verify each location was processed through all validation steps
            foreach (var locationId in locations)
            {
                _mockTenantValidationService.Verify(x => x.GetEmployeesAsync(tenantId, locationId), 
                    Times.Once, $"Should get employees for location {locationId}");

                _mockTenantValidationService.Verify(x => x.LoadManagerAsync(tenantId, locationId, 10, false, 50), 
                    Times.Once, $"Should load manager for location {locationId}");

                _mockTenantValidationService.Verify(x => x.ReadEmployeeAsync(tenantId, locationId, false, It.IsAny<object>()), 
                    Times.Once, $"Should read employee for location {locationId}");

                _mockTenantValidationService.Verify(x => x.ValidatePositionsAsync(tenantId, locationId), 
                    Times.Once, $"Should validate positions for location {locationId}");
            }
        }

        #endregion

        #region GetTenantsForAllLocationsAsync Tests

        [TestMethod]
        [TestCategory("GetTenantsForAllLocationsAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task GetTenantsForAllLocationsAsync_WhenCalledWithValidData_ReturnsExpectedValueTuples()
        {
            // Arrange - This test will define the expected behavior of the method
            var expectedLocations = new[] { 1001, 1002, 2001, 3001 };
            
            _mockConfigurationService.Setup(x => x.GetProcessLocations())
                .Returns(expectedLocations);

            // Setup mock tenant data
            var mockTenantMappings = new[]
            {
                new HarriTenantByLocation { LocationId = 1001, TenantId = Guid.NewGuid(), TenantCode = "TenantA" },
                new HarriTenantByLocation { LocationId = 1002, TenantId = Guid.NewGuid(), TenantCode = "TenantA" },
                new HarriTenantByLocation { LocationId = 2001, TenantId = Guid.NewGuid(), TenantCode = "TenantB" },
                null // Location 3001 has no tenant
            };

            for (int i = 0; i < expectedLocations.Length; i++)
            {
                _mockHarriTenantByLocationRepository.Setup(x => x.GetTenantByLocation(expectedLocations[i]))
                    .Returns(mockTenantMappings[i]);
            }

            // Setup tenant names
            _mockHarriTenantRepository.Setup(x => x.FirstOrDefault(It.IsAny<Func<HarriTenant, bool>>()))
                .Returns<Func<HarriTenant, bool>>(predicate =>
                {
                    // Return different names based on tenant code
                    var tenantCode = mockTenantMappings.FirstOrDefault(t => t != null && predicate(new HarriTenant { TenantId = t.TenantId }))?.TenantCode;
                    return tenantCode switch
                    {
                        "TenantA" => new HarriTenant { Name = "Corporate HQ", TenantId = mockTenantMappings[0].TenantId },
                        "TenantB" => new HarriTenant { Name = "Franchise Beta", TenantId = mockTenantMappings[2].TenantId },
                        _ => null
                    };
                });

            // Act
            // THIS WILL FAIL - GetTenantsForAllLocationsAsync method doesn't exist yet
            var result = await _tenantValidationService.GetTenantsForAllLocationsAsync();

            // Assert
            // Verify the ValueTuple structure: (tenantId, tenantName, List<int> locations)
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(3, result.Length, "Should return 3 entries: 2 valid tenants + 1 null tenant group");

            // Verify TenantA entry
            var tenantA = result.FirstOrDefault(t => t.tenantId == "TenantA");
            Assert.IsNotNull(tenantA, "Should contain TenantA entry");
            Assert.AreEqual("Corporate HQ", tenantA.tenantName, "Should use actual tenant name");
            CollectionAssert.AreEquivalent(new[] { 1001, 1002 }, tenantA.locations, "TenantA should have locations 1001 and 1002");

            // Verify TenantB entry
            var tenantB = result.FirstOrDefault(t => t.tenantId == "TenantB");
            Assert.IsNotNull(tenantB, "Should contain TenantB entry");
            Assert.AreEqual("Franchise Beta", tenantB.tenantName, "Should use actual tenant name");
            CollectionAssert.AreEquivalent(new[] { 2001 }, tenantB.locations, "TenantB should have location 2001");

            // Verify null tenant entry
            var nullTenant = result.FirstOrDefault(t => t.tenantId == null);
            Assert.IsNotNull(nullTenant, "Should contain null tenant entry");
            Assert.IsNull(nullTenant.tenantName, "Null tenant should have null name");
            CollectionAssert.AreEquivalent(new[] { 3001 }, nullTenant.locations, "Null tenant should have location 3001");
        }

        [TestMethod]
        [TestCategory("GetTenantsForAllLocationsAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task GetTenantsForAllLocationsAsync_WhenNoLocationsConfigured_ReturnsEmptyArray()
        {
            // Arrange
            _mockConfigurationService.Setup(x => x.GetProcessLocations())
                .Returns(new int[0]);

            // Act
            // THIS WILL FAIL - GetTenantsForAllLocationsAsync method doesn't exist yet
            var result = await _tenantValidationService.GetTenantsForAllLocationsAsync();

            // Assert
            Assert.IsNotNull(result, "Result should not be null even when no locations configured");
            Assert.AreEqual(0, result.Length, "Should return empty array when no locations configured");
        }

        #endregion

        #region GetEmployeesAsync Tests

        [TestMethod]
        [TestCategory("GetEmployeesAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task GetEmployeesAsync_WhenEmployeesExist_ReturnsCountAndFirstEmployee()
        {
            // Arrange
            const string tenantId = "TenantA";
            const int locationId = 1001;
            const int expectedCount = 42;
            var expectedFirstEmployee = new { Id = 1, Name = "John Doe", Position = "Manager" };

            // Mock the Harri API call to return employees
            _mockCallHarriWebServiceProvider.Setup(x => x.GetEmployeesForLocation(tenantId, locationId, 50))
                .ReturnsAsync(new[] { expectedFirstEmployee, new { Id = 2, Name = "Jane Smith" } });

            // Act
            // THIS WILL FAIL - GetEmployeesAsync method doesn't exist yet
            var result = await _tenantValidationService.GetEmployeesAsync(tenantId, locationId);

            // Assert
            // Verify ValueTuple structure: (int count, object firstEmployee)
            Assert.AreEqual(expectedCount, result.count, "Should return correct employee count");
            Assert.AreEqual(expectedFirstEmployee, result.firstEmployee, "Should return first employee object");

            // Verify API was called with correct parameters (max 50 employees)
            _mockCallHarriWebServiceProvider.Verify(x => x.GetEmployeesForLocation(tenantId, locationId, 50), 
                Times.Once, "Should call Harri API with max 50 employees limit");
        }

        [TestMethod]
        [TestCategory("GetEmployeesAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task GetEmployeesAsync_WhenNoEmployeesFound_ReturnsZeroCountAndNull()
        {
            // Arrange
            const string tenantId = "TenantB";
            const int locationId = 2001;

            _mockCallHarriWebServiceProvider.Setup(x => x.GetEmployeesForLocation(tenantId, locationId, 50))
                .ReturnsAsync(new object[0]);

            // Act
            // THIS WILL FAIL - GetEmployeesAsync method doesn't exist yet
            var result = await _tenantValidationService.GetEmployeesAsync(tenantId, locationId);

            // Assert
            Assert.AreEqual(0, result.count, "Should return 0 when no employees found");
            Assert.IsNull(result.firstEmployee, "Should return null when no employees found");
        }

        [TestMethod]
        [TestCategory("GetEmployeesAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task GetEmployeesAsync_WhenApiThrowsException_PropagatesException()
        {
            // Arrange
            const string tenantId = "TenantC";
            const int locationId = 3001;
            var expectedException = new InvalidOperationException("Harri API failed");

            _mockCallHarriWebServiceProvider.Setup(x => x.GetEmployeesForLocation(tenantId, locationId, 50))
                .ThrowsAsync(expectedException);

            // Act & Assert
            // THIS WILL FAIL - GetEmployeesAsync method doesn't exist yet
            var actualException = await Assert.ThrowsExceptionAsync<InvalidOperationException>(
                () => _tenantValidationService.GetEmployeesAsync(tenantId, locationId));

            Assert.AreEqual(expectedException.Message, actualException.Message, 
                "Should propagate the exact exception from Harri API");
        }

        #endregion

        #region LoadManagerAsync Tests

        [TestMethod]
        [TestCategory("LoadManagerAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task LoadManagerAsync_WhenSkipConditionsMet_ReturnsSkippedResult()
        {
            // Arrange
            const string tenantId = "TenantA";
            const int locationId = 1001;
            const int existingCount = 75;
            const bool skipWhenPreexistingFound = true;
            const int minimumCountToSkip = 50;

            // Act
            // THIS WILL FAIL - LoadManagerAsync method doesn't exist yet
            var result = await _tenantValidationService.LoadManagerAsync(tenantId, locationId, 
                existingCount, skipWhenPreexistingFound, minimumCountToSkip);

            // Assert
            // Verify ValueTuple structure: (string result, string details)
            Assert.AreEqual("Skipped", result.result, "Should return 'Skipped' when skip conditions are met");
            Assert.IsTrue(result.details.Contains("existing employee count"), 
                "Details should explain why it was skipped");
        }

        [TestMethod]
        [TestCategory("LoadManagerAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task LoadManagerAsync_WhenSkipConditionsNotMet_LoadsManagerAndReturnsResult()
        {
            // Arrange
            const string tenantId = "TenantB";
            const int locationId = 2001;
            const int existingCount = 25;
            const bool skipWhenPreexistingFound = true;
            const int minimumCountToSkip = 50;
            var expectedManager = new { Id = 100, Name = "Manager Smith", IsManager = true };

            _mockCallHarriWebServiceProvider.Setup(x => x.LoadManagerForLocation(tenantId, locationId))
                .ReturnsAsync(expectedManager);

            // Act
            // THIS WILL FAIL - LoadManagerAsync method doesn't exist yet
            var result = await _tenantValidationService.LoadManagerAsync(tenantId, locationId, 
                existingCount, skipWhenPreexistingFound, minimumCountToSkip);

            // Assert
            Assert.AreEqual("Pass", result.result, "Should return 'Pass' when manager loaded successfully");
            Assert.IsTrue(result.details.Contains("Manager loaded"), 
                "Details should indicate successful manager loading");

            // Verify API was called to load manager
            _mockCallHarriWebServiceProvider.Verify(x => x.LoadManagerForLocation(tenantId, locationId), 
                Times.Once, "Should call Harri API to load manager when skip conditions not met");
        }

        [TestMethod]
        [TestCategory("LoadManagerAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task LoadManagerAsync_WhenManagerLoadFails_ReturnsFailResult()
        {
            // Arrange
            const string tenantId = "TenantC";
            const int locationId = 3001;
            const int existingCount = 10;
            const bool skipWhenPreexistingFound = false; // Force loading
            const int minimumCountToSkip = 50;

            var expectedException = new InvalidOperationException("Manager load failed");
            _mockCallHarriWebServiceProvider.Setup(x => x.LoadManagerForLocation(tenantId, locationId))
                .ThrowsAsync(expectedException);

            // Act
            // THIS WILL FAIL - LoadManagerAsync method doesn't exist yet
            var result = await _tenantValidationService.LoadManagerAsync(tenantId, locationId, 
                existingCount, skipWhenPreexistingFound, minimumCountToSkip);

            // Assert
            Assert.AreEqual("Fail", result.result, "Should return 'Fail' when manager load fails");
            Assert.IsTrue(result.details.Contains(expectedException.Message), 
                "Details should contain the exception message");
        }

        #endregion

        #region ReadEmployeeAsync Tests

        [TestMethod]
        [TestCategory("ReadEmployeeAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ReadEmployeeAsync_WhenWasSkipped_ReadsFirstEmployeeFromGetEmployees()
        {
            // Arrange
            const string tenantId = "TenantA";
            const int locationId = 1001;
            const bool wasSkipped = true;
            var employeeToRead = new { Id = 1, Name = "John Doe" };

            _mockCallHarriWebServiceProvider.Setup(x => x.ReadEmployee(tenantId, locationId, employeeToRead))
                .ReturnsAsync(new { Success = true, Details = "Employee read successfully" });

            // Act
            // THIS WILL FAIL - ReadEmployeeAsync method doesn't exist yet
            var result = await _tenantValidationService.ReadEmployeeAsync(tenantId, locationId, wasSkipped, employeeToRead);

            // Assert
            // Verify ValueTuple structure: (string result, string details)
            Assert.AreEqual("Pass", result.result, "Should return 'Pass' when employee read successfully");
            Assert.IsTrue(result.details.Contains("Employee read"), 
                "Details should indicate successful employee reading");

            // Verify API was called to read the first employee (from GetEmployeesAsync)
            _mockCallHarriWebServiceProvider.Verify(x => x.ReadEmployee(tenantId, locationId, employeeToRead), 
                Times.Once, "Should call Harri API to read the first employee when LoadManager was skipped");
        }

        [TestMethod]
        [TestCategory("ReadEmployeeAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ReadEmployeeAsync_WhenWasNotSkipped_ReadsLoadedManagerEmployee()
        {
            // Arrange
            const string tenantId = "TenantB";
            const int locationId = 2001;
            const bool wasSkipped = false;
            var employeeToRead = new { Id = 100, Name = "Manager Smith", IsManager = true };

            _mockCallHarriWebServiceProvider.Setup(x => x.ReadEmployee(tenantId, locationId, employeeToRead))
                .ReturnsAsync(new { Success = true, Details = "Manager read successfully" });

            // Act
            // THIS WILL FAIL - ReadEmployeeAsync method doesn't exist yet
            var result = await _tenantValidationService.ReadEmployeeAsync(tenantId, locationId, wasSkipped, employeeToRead);

            // Assert
            Assert.AreEqual("Pass", result.result, "Should return 'Pass' when manager read successfully");
            Assert.IsTrue(result.details.Contains("read successfully"), 
                "Details should indicate successful reading");

            // Verify API was called to read the loaded manager
            _mockCallHarriWebServiceProvider.Verify(x => x.ReadEmployee(tenantId, locationId, employeeToRead), 
                Times.Once, "Should call Harri API to read the loaded manager when LoadManager was executed");
        }

        [TestMethod]
        [TestCategory("ReadEmployeeAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ReadEmployeeAsync_WhenReadFails_ReturnsFailResult()
        {
            // Arrange
            const string tenantId = "TenantC";
            const int locationId = 3001;
            const bool wasSkipped = false;
            var employeeToRead = new { Id = 200, Name = "Employee Jones" };

            var expectedException = new InvalidOperationException("Employee read failed");
            _mockCallHarriWebServiceProvider.Setup(x => x.ReadEmployee(tenantId, locationId, employeeToRead))
                .ThrowsAsync(expectedException);

            // Act
            // THIS WILL FAIL - ReadEmployeeAsync method doesn't exist yet
            var result = await _tenantValidationService.ReadEmployeeAsync(tenantId, locationId, wasSkipped, employeeToRead);

            // Assert
            Assert.AreEqual("Fail", result.result, "Should return 'Fail' when employee read fails");
            Assert.IsTrue(result.details.Contains(expectedException.Message), 
                "Details should contain the exception message");
        }

        #endregion

        #region ValidatePositionsAsync Tests

        [TestMethod]
        [TestCategory("ValidatePositionsAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidatePositionsAsync_WhenPositionsValid_ReturnsPassResult()
        {
            // Arrange
            const string tenantId = "TenantA";
            const int locationId = 1001;

            _mockCallHarriWebServiceProvider.Setup(x => x.ValidatePositionsForLocation(tenantId, locationId))
                .ReturnsAsync(new { IsValid = true, ValidationDetails = "All positions valid" });

            // Act
            // THIS WILL FAIL - ValidatePositionsAsync method doesn't exist yet
            var result = await _tenantValidationService.ValidatePositionsAsync(tenantId, locationId);

            // Assert
            // Verify ValueTuple structure: (string result, string details)
            Assert.AreEqual("Pass", result.result, "Should return 'Pass' when positions are valid");
            Assert.IsTrue(result.details.Contains("valid"), 
                "Details should indicate positions are valid");

            // Verify API was called to validate positions
            _mockCallHarriWebServiceProvider.Verify(x => x.ValidatePositionsForLocation(tenantId, locationId), 
                Times.Once, "Should call Harri API to validate positions");
        }

        [TestMethod]
        [TestCategory("ValidatePositionsAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidatePositionsAsync_WhenPositionsInvalid_ReturnsFailResult()
        {
            // Arrange
            const string tenantId = "TenantB";
            const int locationId = 2001;

            _mockCallHarriWebServiceProvider.Setup(x => x.ValidatePositionsForLocation(tenantId, locationId))
                .ReturnsAsync(new { IsValid = false, ValidationDetails = "Invalid position mappings found" });

            // Act
            // THIS WILL FAIL - ValidatePositionsAsync method doesn't exist yet
            var result = await _tenantValidationService.ValidatePositionsAsync(tenantId, locationId);

            // Assert
            Assert.AreEqual("Fail", result.result, "Should return 'Fail' when positions are invalid");
            Assert.IsTrue(result.details.Contains("Invalid"), 
                "Details should indicate what failed");
        }

        [TestMethod]
        [TestCategory("ValidatePositionsAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task ValidatePositionsAsync_WhenValidationThrowsException_ReturnsFailResult()
        {
            // Arrange
            const string tenantId = "TenantC";
            const int locationId = 3001;

            var expectedException = new InvalidOperationException("Position validation service unavailable");
            _mockCallHarriWebServiceProvider.Setup(x => x.ValidatePositionsForLocation(tenantId, locationId))
                .ThrowsAsync(expectedException);

            // Act
            // THIS WILL FAIL - ValidatePositionsAsync method doesn't exist yet
            var result = await _tenantValidationService.ValidatePositionsAsync(tenantId, locationId);

            // Assert
            Assert.AreEqual("Fail", result.result, "Should return 'Fail' when validation throws exception");
            Assert.IsTrue(result.details.Contains(expectedException.Message), 
                "Details should contain the exception message");
        }

        #endregion

        #region Integration Tests

        [TestMethod]
        [TestCategory("Integration")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FullValidationWorkflow_WhenMixedScenarios_ProcessesCorrectly()
        {
            // Arrange - Complex scenario with multiple tenants, skip conditions, and various results
            var tenantList = new[]
            {
                ("TenantA", "Corp HQ", new List<int> { 1001 }), // Will be skipped
                ("TenantB", "Franchise", new List<int> { 2001 }), // Will process fully
                (null, null, new List<int> { 3001 }) // Null tenant - should fail
            };

            // Setup ValidationRoutine dependencies
            _mockTenantValidationService.Setup(x => x.GetTenantsForAllLocationsAsync())
                .ReturnsAsync(tenantList);

            // Setup TenantA (will be skipped)
            _mockTenantValidationService.Setup(x => x.GetEmployeesAsync("TenantA", 1001))
                .ReturnsAsync((100, new { Id = 1, Name = "Employee1" })); // High count triggers skip

            // Setup TenantB (will process fully)
            _mockTenantValidationService.Setup(x => x.GetEmployeesAsync("TenantB", 2001))
                .ReturnsAsync((5, new { Id = 2, Name = "Employee2" })); // Low count, no skip

            _mockTenantValidationService.Setup(x => x.LoadManagerAsync("TenantB", 2001, 5, true, 50))
                .ReturnsAsync(("Pass", "Manager loaded"));

            _mockTenantValidationService.Setup(x => x.ReadEmployeeAsync("TenantB", 2001, false, It.IsAny<object>()))
                .ReturnsAsync(("Pass", "Manager read"));

            _mockTenantValidationService.Setup(x => x.ValidatePositionsAsync("TenantB", 2001))
                .ReturnsAsync(("Pass", "Positions valid"));

            _mockConfigurationService.Setup(x => x.GetSkipWhenPreexistingFound()).Returns(true);
            _mockConfigurationService.Setup(x => x.GetMinimumCountToSkip()).Returns(50);

            // Act
            // THIS WILL FAIL - ValidationRoutine method doesn't exist yet
            await _tenantValidationService.ValidationRoutine();

            // Assert
            // Verify TenantA processing (should be skipped due to high employee count)
            _mockTenantValidationService.Verify(x => x.ValidateTenantsAsync("TenantA", "Corp HQ", 
                It.Is<List<int>>(list => list.SequenceEqual(new[] { 1001 }))), 
                Times.Once, "Should process TenantA");

            // Verify TenantB processing (should be fully processed)
            _mockTenantValidationService.Verify(x => x.ValidateTenantsAsync("TenantB", "Franchise", 
                It.Is<List<int>>(list => list.SequenceEqual(new[] { 2001 }))), 
                Times.Once, "Should process TenantB");

            // Verify null tenant handling (should be added to fail report)
            _mockResultCapture.Verify(x => x.AddLocationFailure(3001, "Fail", It.IsAny<string>()), 
                Times.Once, "Should add null tenant location to fail report");
        }

        #endregion

        #region FindConfigTenantForLocationAsync Tests - TDD Red Phase

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenLocationExistsInFirstConfigTenant_ReturnsFirstTenant()
        {
            // Arrange
            const int targetLocationId = 1001;
            var configuredTenants = new List<Configuration.ConfigTenant>
            {
                new Configuration.ConfigTenant
                {
                    Name = "CorporateHQ",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret1",
                    TokenUrl = "https://tenant1.com/token"
                },
                new Configuration.ConfigTenant
                {
                    Name = "FranchiseAlpha",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret2", 
                    TokenUrl = "https://tenant2.com/token"
                }
            };

            // Mock Harri API response for first tenant - location exists
            var firstTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 1001, Name = "Store A", Type = "Restaurant" },
                new HarriLocation2 { Id = 1002, Name = "Store B", Type = "Restaurant" }
            };

            var firstTenantGuid = Guid.Parse(configuredTenants[0].ClientId);
            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(firstTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(firstTenantLocations)
                });

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNotNull(result, "Should return a config tenant when location is found");
            Assert.AreEqual("CorporateHQ", result.Name, "Should return the first tenant containing the location");
            Assert.AreEqual(configuredTenants[0].ClientId, result.ClientId, "Should return the correct tenant details");

            // Verify only the first tenant was called (method should stop after finding location)
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(firstTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
        }

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenLocationExistsInSecondConfigTenant_ReturnsSecondTenant()
        {
            // Arrange
            const int targetLocationId = 2001;
            var configuredTenants = new List<Configuration.ConfigTenant>
            {
                new Configuration.ConfigTenant
                {
                    Name = "CorporateHQ",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret1",
                    TokenUrl = "https://tenant1.com/token"
                },
                new Configuration.ConfigTenant
                {
                    Name = "FranchiseAlpha",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret2",
                    TokenUrl = "https://tenant2.com/token"
                }
            };

            var firstTenantGuid = Guid.Parse(configuredTenants[0].ClientId);
            var secondTenantGuid = Guid.Parse(configuredTenants[1].ClientId);

            // Mock Harri API response for first tenant - location does NOT exist
            var firstTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 1001, Name = "Store A", Type = "Restaurant" },
                new HarriLocation2 { Id = 1002, Name = "Store B", Type = "Restaurant" }
            };

            // Mock Harri API response for second tenant - location EXISTS
            var secondTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 2001, Name = "Franchise Store", Type = "Restaurant" },
                new HarriLocation2 { Id = 2002, Name = "Franchise Store 2", Type = "Restaurant" }
            };

            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(firstTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(firstTenantLocations)
                });

            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(secondTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(secondTenantLocations)
                });

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNotNull(result, "Should return a config tenant when location is found");
            Assert.AreEqual("FranchiseAlpha", result.Name, "Should return the second tenant containing the location");
            Assert.AreEqual(configuredTenants[1].ClientId, result.ClientId, "Should return the correct tenant details");

            // Verify both tenants were called (first didn't contain location, so continued to second)
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(firstTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(secondTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
        }

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenLocationDoesNotExistInAnyTenant_ReturnsNull()
        {
            // Arrange
            const int targetLocationId = 9999; // Non-existent location
            var configuredTenants = new List<Configuration.ConfigTenant>
            {
                new Configuration.ConfigTenant
                {
                    Name = "CorporateHQ",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret1",
                    TokenUrl = "https://tenant1.com/token"
                },
                new Configuration.ConfigTenant
                {
                    Name = "FranchiseAlpha", 
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret2",
                    TokenUrl = "https://tenant2.com/token"
                }
            };

            var firstTenantGuid = Guid.Parse(configuredTenants[0].ClientId);
            var secondTenantGuid = Guid.Parse(configuredTenants[1].ClientId);

            // Mock Harri API responses - neither tenant contains the target location
            var firstTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 1001, Name = "Store A", Type = "Restaurant" },
                new HarriLocation2 { Id = 1002, Name = "Store B", Type = "Restaurant" }
            };

            var secondTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 2001, Name = "Franchise Store", Type = "Restaurant" },
                new HarriLocation2 { Id = 2002, Name = "Franchise Store 2", Type = "Restaurant" }
            };

            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(firstTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(firstTenantLocations)
                });

            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(secondTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(secondTenantLocations)
                });

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNull(result, "Should return null when location is not found in any config tenant");

            // Verify all tenants were checked
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(firstTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(secondTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
        }

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenNoConfigTenantsProvided_ReturnsNull()
        {
            // Arrange
            const int targetLocationId = 1001;
            var configuredTenants = new List<Configuration.ConfigTenant>(); // Empty list

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNull(result, "Should return null when no config tenants are provided");

            // Verify no API calls were made
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(It.IsAny<Guid>(), It.IsAny<RestSharp.Method>(), It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>(), It.IsAny<string>()), Times.Never);
        }

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenNullConfigTenantsProvided_ReturnsNull()
        {
            // Arrange
            const int targetLocationId = 1001;
            List<Configuration.ConfigTenant> configuredTenants = null; // Null list

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNull(result, "Should return null when config tenants list is null");

            // Verify no API calls were made
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(It.IsAny<Guid>(), It.IsAny<RestSharp.Method>(), It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>(), It.IsAny<string>()), Times.Never);
        }

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenApiCallFailsForTenant_HandlesGracefullyAndContinues()
        {
            // Arrange
            const int targetLocationId = 2001;
            var configuredTenants = new List<Configuration.ConfigTenant>
            {
                new Configuration.ConfigTenant
                {
                    Name = "FailingTenant",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret1",
                    TokenUrl = "https://failing-tenant.com/token"
                },
                new Configuration.ConfigTenant
                {
                    Name = "WorkingTenant",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret2",
                    TokenUrl = "https://working-tenant.com/token"
                }
            };

            var failingTenantGuid = Guid.Parse(configuredTenants[0].ClientId);
            var workingTenantGuid = Guid.Parse(configuredTenants[1].ClientId);

            // Mock first tenant API call to fail
            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(failingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = false,
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    Content = "Internal server error"
                });

            // Mock second tenant API call to succeed and contain the target location
            var workingTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 2001, Name = "Working Store", Type = "Restaurant" },
                new HarriLocation2 { Id = 2002, Name = "Another Store", Type = "Restaurant" }
            };

            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(workingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(workingTenantLocations)
                });

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNotNull(result, "Should return tenant even when other tenants fail");
            Assert.AreEqual("WorkingTenant", result.Name, "Should return the working tenant that contains the location");
            Assert.AreEqual(configuredTenants[1].ClientId, result.ClientId, "Should return the correct tenant details");

            // Verify both API calls were attempted
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(failingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(workingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
        }

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenApiCallThrowsExceptionForTenant_HandlesGracefullyAndContinues()
        {
            // Arrange
            const int targetLocationId = 2001;
            var configuredTenants = new List<Configuration.ConfigTenant>
            {
                new Configuration.ConfigTenant
                {
                    Name = "ExceptionTenant",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret1",
                    TokenUrl = "https://exception-tenant.com/token"
                },
                new Configuration.ConfigTenant
                {
                    Name = "WorkingTenant",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret2",
                    TokenUrl = "https://working-tenant.com/token"
                }
            };

            var exceptionTenantGuid = Guid.Parse(configuredTenants[0].ClientId);
            var workingTenantGuid = Guid.Parse(configuredTenants[1].ClientId);

            // Mock first tenant API call to throw exception
            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(exceptionTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ThrowsAsync(new InvalidOperationException("Network timeout"));

            // Mock second tenant API call to succeed and contain the target location
            var workingTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 2001, Name = "Working Store", Type = "Restaurant" },
                new HarriLocation2 { Id = 2002, Name = "Another Store", Type = "Restaurant" }
            };

            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(workingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(workingTenantLocations)
                });

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNotNull(result, "Should return tenant even when other tenants throw exceptions");
            Assert.AreEqual("WorkingTenant", result.Name, "Should return the working tenant that contains the location");
            Assert.AreEqual(configuredTenants[1].ClientId, result.ClientId, "Should return the correct tenant details");

            // Verify both API calls were attempted
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(exceptionTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(workingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
        }

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenApiReturnsInvalidJson_HandlesGracefullyAndContinues()
        {
            // Arrange
            const int targetLocationId = 2001;
            var configuredTenants = new List<Configuration.ConfigTenant>
            {
                new Configuration.ConfigTenant
                {
                    Name = "InvalidJsonTenant",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret1",
                    TokenUrl = "https://invalid-json-tenant.com/token"
                },
                new Configuration.ConfigTenant
                {
                    Name = "WorkingTenant",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret2", 
                    TokenUrl = "https://working-tenant.com/token"
                }
            };

            var invalidJsonTenantGuid = Guid.Parse(configuredTenants[0].ClientId);
            var workingTenantGuid = Guid.Parse(configuredTenants[1].ClientId);

            // Mock first tenant API call to return invalid JSON
            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(invalidJsonTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = "{ invalid json structure [" // Malformed JSON
                });

            // Mock second tenant API call to succeed and contain the target location
            var workingTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 2001, Name = "Working Store", Type = "Restaurant" },
                new HarriLocation2 { Id = 2002, Name = "Another Store", Type = "Restaurant" }
            };

            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(workingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(workingTenantLocations)
                });

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNotNull(result, "Should return tenant even when other tenants return invalid JSON");
            Assert.AreEqual("WorkingTenant", result.Name, "Should return the working tenant that contains the location");
            Assert.AreEqual(configuredTenants[1].ClientId, result.ClientId, "Should return the correct tenant details");

            // Verify both API calls were attempted
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(invalidJsonTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(workingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
        }

        [TestMethod]
        [TestCategory("FindConfigTenantForLocationAsync")]
        [TestCategory("TDD_Red_Phase")]
        public async Task FindConfigTenantForLocationAsync_WhenTenantHasInvalidClientId_SkipsTenantGracefully()
        {
            // Arrange
            const int targetLocationId = 2001;
            var configuredTenants = new List<Configuration.ConfigTenant>
            {
                new Configuration.ConfigTenant
                {
                    Name = "InvalidClientIdTenant",
                    ClientId = "not-a-valid-guid", // Invalid GUID format
                    Secret = "secret1",
                    TokenUrl = "https://invalid-clientid-tenant.com/token"
                },
                new Configuration.ConfigTenant
                {
                    Name = "WorkingTenant",
                    ClientId = Guid.NewGuid().ToString(),
                    Secret = "secret2",
                    TokenUrl = "https://working-tenant.com/token"
                }
            };

            var workingTenantGuid = Guid.Parse(configuredTenants[1].ClientId);

            // Mock second tenant API call to succeed and contain the target location
            var workingTenantLocations = new HarriLocations2List
            {
                new HarriLocation2 { Id = 2001, Name = "Working Store", Type = "Restaurant" },
                new HarriLocation2 { Id = 2002, Name = "Another Store", Type = "Restaurant" }
            };

            _mockCallHarriWebServiceProvider
                .Setup(x => x.Call(workingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"))
                .ReturnsAsync(new RestSharp.RestResponse
                {
                    IsSuccessful = true,
                    Content = JsonConvert.SerializeObject(workingTenantLocations)
                });

            // Act
            // THIS WILL FAIL - FindConfigTenantForLocationAsync method doesn't exist yet
            var result = await _refactoredTenantValidationService.FindConfigTenantForLocationAsync(targetLocationId, configuredTenants);

            // Assert
            Assert.IsNotNull(result, "Should return tenant even when other tenants have invalid client IDs");
            Assert.AreEqual("WorkingTenant", result.Name, "Should return the working tenant that contains the location");
            Assert.AreEqual(configuredTenants[1].ClientId, result.ClientId, "Should return the correct tenant details");

            // Verify only the valid tenant was called (invalid client ID tenant should be skipped)
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(workingTenantGuid, RestSharp.Method.Get, "locations", null, false, "v1"), Times.Once);
            _mockCallHarriWebServiceProvider.Verify(x => x.Call(It.IsAny<Guid>(), It.IsAny<RestSharp.Method>(), It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>(), It.IsAny<string>()), Times.Once, "Should only call API for the tenant with valid client ID");
        }

        #endregion

        // Mock interface for testing - this represents the expanded interface we need
        private Mock<ITenantValidationService> _mockTenantValidationService;

        [TestInitialize]
        public void SetupExpandedInterface()
        {
            // Create mock for the expanded interface that doesn't exist yet
            _mockTenantValidationService = new Mock<ITenantValidationService>();
        }
    }
}