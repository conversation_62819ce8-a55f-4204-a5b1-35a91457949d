using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using Jitb.Employment.HarriValidateTenant.Models;
using Jitb.Employment.HarriValidateTenant.Services;

namespace Jitb.Employment.HarriValidateTenant.Tests.Services
{
    /// <summary>
    /// Tests for TenantValidationResultCapture enhancements to support tenant names.
    /// These tests are written in TDD Red phase and WILL FAIL initially until implementation is updated.
    /// 
    /// User requirements:
    /// 1. TenantValidationResultCapture should accept and store tenant names
    /// 2. StartTenantValidation should have an overload that accepts tenantName parameter
    /// 3. Results should include tenant names when available
    /// 4. Interface should be updated to support tenant names
    /// </summary>
    [TestClass]
    public class TenantValidationResultCaptureTests
    {
        private ITenantValidationResultCapture _resultCapture;

        [TestInitialize]
        public void Setup()
        {
            _resultCapture = new TenantValidationResultCapture();
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_ShouldAcceptTenantName_WhenTenantNameProvided()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const string tenantName = "Jack in the Box Corporate";
            const int location = 3365;

            // Act
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, tenantName, location);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            Assert.IsNotNull(result, "Result should be created when starting tenant validation");
            Assert.AreEqual(tenantId, result.TenantId, "TenantId should be stored correctly");
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.AreEqual(tenantName, result.TenantName, "TenantName should be stored correctly");
            Assert.AreEqual(location, result.ProcessedLocation, "Location should be stored correctly");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_ShouldHandleNullTenantName()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const string tenantName = null;
            const int location = 3365;

            // Act
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, tenantName, location);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            Assert.IsNotNull(result, "Result should be created even with null tenant name");
            Assert.AreEqual(tenantId, result.TenantId, "TenantId should be stored correctly");
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.IsNull(result.TenantName, "TenantName should be null when not provided");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_ShouldHandleEmptyTenantName()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const string tenantName = "";
            const int location = 3365;

            // Act
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, tenantName, location);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            Assert.IsNotNull(result, "Result should be created with empty tenant name");
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.AreEqual(string.Empty, result.TenantName, "TenantName should be empty string when provided as empty");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_ShouldTrimTenantNameWhitespace()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const string tenantNameWithWhitespace = "  Jack in the Box Corporate  ";
            const string expectedTenantName = "Jack in the Box Corporate";
            const int location = 3365;

            // Act
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, tenantNameWithWhitespace, location);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.AreEqual(expectedTenantName, result.TenantName, 
                "TenantName should be trimmed of leading and trailing whitespace");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_ExistingOverload_ShouldStillWork()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const int location = 3365;

            // Act - Use existing overload without tenant name
            _resultCapture.StartTenantValidation(tenantId, location);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            Assert.IsNotNull(result, "Result should be created with existing overload");
            Assert.AreEqual(tenantId, result.TenantId, "TenantId should be stored correctly");
            Assert.AreEqual(location, result.ProcessedLocation, "Location should be stored correctly");
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.IsNull(result.TenantName, "TenantName should be null when using existing overload");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_ShouldOverwriteExistingResult_WhenTenantNameProvided()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const string originalTenantName = "Original Name";
            const string updatedTenantName = "Updated Corporate Name";
            const int location = 3365;

            // Act - Start validation first time
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, originalTenantName, location);

            // Act - Start validation again with different tenant name
            _resultCapture.StartTenantValidation(tenantId, updatedTenantName, location);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.AreEqual(updatedTenantName, result.TenantName, 
                "TenantName should be updated when validation is restarted");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void GetAllResults_ShouldReturnResultsWithTenantNames_WhenTenantNamesProvided()
        {
            // Arrange
            var tenantData = new[]
            {
                ("tenant-1", "Corporate Tenant", 1001),
                ("tenant-2", "Franchise Tenant", 2002),
                ("tenant-3", null, 3003) // No tenant name
            };

            // Act - Start validations with mixed tenant name availability
            foreach (var (tenantId, tenantName, location) in tenantData)
            {
                if (tenantName != null)
                {
                    // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
                    _resultCapture.StartTenantValidation(tenantId, tenantName, location);
                }
                else
                {
                    _resultCapture.StartTenantValidation(tenantId, location);
                }
            }

            var allResults = _resultCapture.GetAllResults();

            // Assert
            Assert.AreEqual(3, allResults.Count, "Should return all results");

            var corporateResult = allResults.FirstOrDefault(r => r.TenantId == "tenant-1");
            var franchiseResult = allResults.FirstOrDefault(r => r.TenantId == "tenant-2");
            var noNameResult = allResults.FirstOrDefault(r => r.TenantId == "tenant-3");

            Assert.IsNotNull(corporateResult, "Corporate tenant result should exist");
            Assert.IsNotNull(franchiseResult, "Franchise tenant result should exist");
            Assert.IsNotNull(noNameResult, "No-name tenant result should exist");

            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.AreEqual("Corporate Tenant", corporateResult.TenantName, 
                "Corporate tenant should have correct name");
            Assert.AreEqual("Franchise Tenant", franchiseResult.TenantName, 
                "Franchise tenant should have correct name");
            Assert.IsNull(noNameResult.TenantName, 
                "No-name tenant should have null tenant name");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void ITenantValidationResultCapture_Interface_ShouldIncludeTenantNameOverload()
        {
            // Arrange & Act
            var interfaceType = typeof(ITenantValidationResultCapture);
            
            // Assert
            // THIS WILL FAIL - Interface doesn't have the overload with tenant name yet
            var methods = interfaceType.GetMethods()
                .Where(m => m.Name == "StartTenantValidation")
                .ToList();

            Assert.IsTrue(methods.Count >= 2, 
                "Interface should have at least 2 StartTenantValidation overloads");

            var overloadWithTenantName = methods.FirstOrDefault(m => 
                m.GetParameters().Length == 3 &&
                m.GetParameters().Any(p => p.Name == "tenantName" && p.ParameterType == typeof(string)));

            Assert.IsNotNull(overloadWithTenantName, 
                "Interface should have StartTenantValidation overload with tenantName parameter");

            // Verify parameter order: tenantId, tenantName, location
            var parameters = overloadWithTenantName.GetParameters();
            Assert.AreEqual("tenantId", parameters[0].Name, "First parameter should be tenantId");
            Assert.AreEqual("tenantName", parameters[1].Name, "Second parameter should be tenantName");
            Assert.AreEqual("location", parameters[2].Name, "Third parameter should be location");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_ShouldHandleLongTenantNames()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const string longTenantName = "Very Long Corporate Tenant Name That Might Exceed Normal Display Limits - Regional Operations Division";
            const int location = 3365;

            // Act
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, longTenantName, location);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.AreEqual(longTenantName, result.TenantName, 
                "Long tenant names should be stored without truncation");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_ShouldHandleSpecialCharactersInTenantName()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const string specialTenantName = "Jack in the Box® Corporate - Headquarters & Regional Operations (2024)";
            const int location = 3365;

            // Act
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, specialTenantName, location);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.AreEqual(specialTenantName, result.TenantName, 
                "Tenant names with special characters should be stored correctly");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_WithTenantName_ShouldThrowException_WhenTenantIdIsNull()
        {
            // Arrange
            const string tenantId = null;
            const string tenantName = "Valid Tenant Name";
            const int location = 3365;

            // Act & Assert
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            Assert.ThrowsException<ArgumentException>(() => 
                _resultCapture.StartTenantValidation(tenantId, tenantName, location),
                "Should throw ArgumentException when tenantId is null");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_WithTenantName_ShouldThrowException_WhenTenantIdIsEmpty()
        {
            // Arrange
            const string tenantId = "";
            const string tenantName = "Valid Tenant Name";
            const int location = 3365;

            // Act & Assert
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            Assert.ThrowsException<ArgumentException>(() => 
                _resultCapture.StartTenantValidation(tenantId, tenantName, location),
                "Should throw ArgumentException when tenantId is empty");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void StartTenantValidation_WithTenantName_ShouldHandleInvalidLocation()
        {
            // Arrange
            const string tenantId = "valid-tenant-id";
            const string tenantName = "Valid Tenant Name";
            const int invalidLocation = 0;

            // Act
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, tenantName, invalidLocation);

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            Assert.IsNotNull(result, "Result should be created even with invalid location");
            Assert.AreEqual(invalidLocation, result.ProcessedLocation, 
                "Invalid location should be stored as-is (validation happens elsewhere)");
        }

        [TestMethod]
        [TestCategory("TenantValidation")]
        [TestCategory("ResultCapture")]
        [TestCategory("TDD_Red_Phase")]
        public void RecordComponentResult_ShouldWorkNormally_AfterStartingWithTenantName()
        {
            // Arrange
            const string tenantId = "test-tenant-id";
            const string tenantName = "Test Tenant";
            const int location = 3365;

            // Act
            // THIS WILL FAIL - StartTenantValidation overload with tenantName doesn't exist yet
            _resultCapture.StartTenantValidation(tenantId, tenantName, location);
            _resultCapture.RecordComponentResult(tenantId, "Tenant Lookup", "Pass", "Tenant found successfully");

            // Assert
            var result = _resultCapture.GetResult(tenantId);
            Assert.IsNotNull(result, "Result should exist");
            
            // THIS WILL FAIL - TenantName property doesn't exist on TenantValidationResult yet
            Assert.AreEqual(tenantName, result.TenantName, "TenantName should be preserved");
            
            Assert.AreEqual(1, result.ComponentResults.Count, "Should have one component result");
            Assert.AreEqual("Tenant Lookup", result.ComponentResults[0].ComponentName);
            Assert.AreEqual("Pass", result.ComponentResults[0].Status);
        }
    }
}