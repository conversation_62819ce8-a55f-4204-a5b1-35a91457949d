# TDD Red Phase Validation Guide

## Validating That Tests Will Fail

This guide explains how to verify that the comprehensive unit tests created for the refactored HarriValidateTenant system properly follow TDD Red phase principles.

## Expected Compilation and Test Failures

### 1. Interface Not Found Errors
The tests reference `IRefactoredTenantValidationService` interface that doesn't exist yet:

```csharp
// THIS WILL FAIL - Interface doesn't exist
private IRefactoredTenantValidationService _tenantValidationService;
```

### 2. Class Not Found Errors
The tests attempt to instantiate `RefactoredTenantValidationService` class:

```csharp
// THIS WILL FAIL - Class doesn't exist yet
_tenantValidationService = new RefactoredTenantValidationService(...);
```

### 3. Method Not Found Errors
All test methods call methods that don't exist:

```csharp
// THIS WILL FAIL - ValidationRoutine method doesn't exist yet
await _tenantValidationService.ValidationRoutine();

// THIS WILL FAIL - ValidateTenantsAsync method doesn't exist yet
await _tenantValidationService.ValidateTenantsAsync(tenantId, tenantName, locations);

// THIS WILL FAIL - GetTenantsForAllLocationsAsync method doesn't exist yet
var result = await _tenantValidationService.GetTenantsForAllLocationsAsync();
```

## Verification Steps

### Step 1: Build Test Project
```bash
# Navigate to test project directory
cd "Jitb.Employment/Jitb.Employment.HarriValidateTenant.Tests"

# Attempt to build (should fail)
msbuild Jitb.Employment.HarriValidateTenant.Tests.csproj

# Expected errors:
# - IRefactoredTenantValidationService not found
# - RefactoredTenantValidationService not found
```

### Step 2: Create Skeleton Implementation
```csharp
// Create RefactoredTenantValidationService.cs with empty methods
public class RefactoredTenantValidationService : IRefactoredTenantValidationService
{
    public Task ValidationRoutine() => throw new NotImplementedException();
    public Task ValidateTenantsAsync(string tenantId, string tenantName, List<int> locations) 
        => throw new NotImplementedException();
    // ... other methods throwing NotImplementedException
}
```

### Step 3: Run Tests (Should Fail with NotImplementedException)
```bash
# Run tests - should fail with NotImplementedException
dotnet test --logger "console;verbosity=detailed"
```

### Step 4: Implement Methods One by One
```csharp
// Implement ValidationRoutine first
public async Task ValidationRoutine()
{
    var tenantList = await GetTenantsForAllLocationsAsync();
    // Implementation here...
}

// Tests should start passing as each method is implemented
```

## Test Coverage Validation

### Core Functionality Tests (35+ test methods)

#### ValidationRoutine Tests - 3 methods
- ✅ Mixed tenants processing
- ✅ All null tenants handling  
- ✅ Empty tenant list handling

#### ValidateTenantsAsync Tests - 3 methods
- ✅ Skip conditions met scenario
- ✅ Skip conditions not met scenario
- ✅ Multiple locations processing

#### GetTenantsForAllLocationsAsync Tests - 2 methods
- ✅ Valid data ValueTuple return
- ✅ Empty locations handling

#### GetEmployeesAsync Tests - 3 methods
- ✅ Employees exist scenario
- ✅ No employees found scenario
- ✅ API exception handling

#### LoadManagerAsync Tests - 3 methods
- ✅ Skip conditions met
- ✅ Skip conditions not met
- ✅ Manager load failure

#### ReadEmployeeAsync Tests - 3 methods
- ✅ Skipped scenario (read first employee)
- ✅ Not skipped scenario (read loaded manager)
- ✅ Read failure handling

#### ValidatePositionsAsync Tests - 3 methods
- ✅ Valid positions
- ✅ Invalid positions
- ✅ Validation exception

#### Edge Case Tests - 12 methods
- ✅ Boundary conditions (exact thresholds)
- ✅ Null/empty data handling
- ✅ Configuration edge cases
- ✅ Concurrent processing
- ✅ Error propagation
- ✅ ValueTuple structure validation

#### Integration Tests - 2 methods
- ✅ Full workflow with mixed scenarios
- ✅ ValueTuple structure verification

## Mock Dependencies Verification

All tests use properly configured mocks:

```csharp
private Mock<IHarriTenantByLocationRepository> _mockHarriTenantByLocationRepository;
private Mock<IHarriTenantRepository> _mockHarriTenantRepository;
private Mock<ILocationValidationService> _mockLocationValidationService;
private Mock<IConfigurationService> _mockConfigurationService;
private Mock<ITenantValidationResultCapture> _mockResultCapture;
private Mock<ICallHarriWebServiceProvider> _mockCallHarriWebServiceProvider;
```

## Expected TDD Flow

### Red Phase (Current State)
- ✅ Tests written
- ❌ Interface doesn't exist → Compilation errors
- ❌ Class doesn't exist → Compilation errors
- ❌ Methods don't exist → Compilation errors

### Green Phase (Next Step)
- ✅ Create interface and skeleton class
- ✅ Implement methods to make tests pass
- ❌ Tests may fail with assertion errors until logic is correct

### Refactor Phase (Final Step)
- ✅ All tests passing
- ✅ Clean up implementation
- ✅ Optimize for maintainability

## ValueTuple Verification

Tests validate exact ValueTuple structures required:

```csharp
// GetTenantsForAllLocationsAsync
Task<(string tenantId, string tenantName, List<int> locations)[]>

// GetEmployeesAsync  
Task<(int count, object firstEmployee)>

// All result methods
Task<(string result, string details)>
```

## Success Criteria

The TDD Red phase is successful when:

1. ✅ **Tests compile** but reference non-existent implementations
2. ✅ **Test execution fails** with "not implemented" or "method not found" errors
3. ✅ **All pseudo code requirements** are captured in test assertions
4. ✅ **ValueTuple structures** are validated in test expectations
5. ✅ **Skip logic** is thoroughly tested with boundary conditions
6. ✅ **Error scenarios** are covered comprehensively
7. ✅ **Mock dependencies** ensure environment independence

## File Deliverables

1. **RefactoredTenantValidationServiceTests.cs** - Core functionality tests (672 lines)
2. **RefactoredTenantValidationServiceEdgeCaseTests.cs** - Edge cases and boundary conditions (420+ lines)
3. **IRefactoredTenantValidationService.cs** - Interface definition with complete method signatures
4. **Updated .csproj** - Includes new test files
5. **Documentation** - TDD validation guides and test summaries

Total: **1,100+ lines of comprehensive failing unit tests** ready to drive implementation of the exact pseudo code structure specified.