# Documentation Scope

## Purpose of This File
This Notes.md file contains **solution-wide insights** that benefit all developers working across the entire Jitb.Employment solution. 

### What SHOULD be documented here:
- Database patterns and best practices used across the solution
- Repository system architecture and configuration patterns
- API integration patterns (Harri, UltiPro, etc.) used by multiple projects
- Domain model relationships and business rules
- Common pitfalls and solutions discovered during development
- Solution-wide vocabulary and terminology clarifications

### What should NOT be documented here:
- Project-specific implementation details
- Individual tool command-line options or usage
- Project-specific configuration or setup instructions
- Implementation details specific to a single project/tool

### Project-Specific Documentation
Individual projects (like HarriCompare) should maintain their own documentation sections with project-specific details, usage instructions, and implementation specifics.

---

# Database
## Employee Table
- Do not use HomeLocationNumber.  Use table EmployeeLocation2
- Do not use JobCode.  Use table EmployeeJobCode

## EmployeeLocation  Table
- Do not use.  It is obsolete.  Use EmployeeLocation2

## EmployeeLocation2 Table
- Use this table instead of EmployeeLocation, which is obsolete
- Uses `DateRange` object with `Start` and `End` properties for date-based filtering
- Active relationships typically have End date set to year 3000 (far future)
- Inactive relationships have actual End dates
- Use `DateRange.Contains(date)` method to check if relationship is active on specific date

# Repository System (Afterman.nRepo Framework)

## Repository Configuration Pattern
The repository system requires proper configuration using the Afterman.nRepo framework. Key requirements:

1. **Configuration Chain**: Use `Configure.As.NHibernate()` configuration chain pattern
2. **Entity Mappings**: Must include `AddMappingsForNamespaceOf<EmployeeMap>()` for proper NHibernate mappings
3. **Configuration Startup**: Must call `configuration.Start()` BEFORE creating `MasterUnitOfWork`
4. **Required Settings**: DefaultSchema("dbo"), ImplicitTransactions(), ShowSql(true), Platform specification

## Repository Creation Order
Critical sequence for repository creation:
1. Create `nRepoConfiguration` instance
2. Configure with connection string and mappings using fluent API
3. Call `configuration.Start()` to initialize the repository system
4. Create `MasterUnitOfWork` instance
5. Create specific repository (e.g., `EmployeeRepository`)

## Example Working Configuration
```csharp
var configuration = new Afterman.nRepo.nRepoConfiguration();
configuration.With("Default",
    Afterman.nRepo.Configure
        .As
        .NHibernate()
        .AddMappingsForNamespaceOf<Jitb.Employment.Domain.Repositories.Employment.Maps.EmployeeMap>()
        .ConnectionString(connectionString)
        .DefaultSchema("dbo")
        .ImplicitTransactions()
        .ShowSql(true)
        .Platform<Afterman.nRepo.DbPlatforms.MsSqlServer.Server2012Platform>());
configuration.Start(); // Critical - must be called before creating UnitOfWork
var unitOfWork = new MasterUnitOfWork();
var repository = new EmployeeRepository(unitOfWork);
```

## Employee Repository Queries

### Location-Based Query Methods
- `GetAllByLocationNumber(int locationNumber)` - Retrieves ALL employees assigned to location (primary + secondary assignments)
- `GetAllByLocationNumber(int locationNumber, DateTime effectiveDate)` - Same as above for specific date
- `GetByPrimaryLocation(int locationNumber)` - Gets employees whose PRIMARY location is this location only
- `GetByPrimaryLocation(int locationNumber, DateTime effectiveDate)` - Same as above for specific date

### IMPORTANT: Primary vs All Location Assignments
**For store comparisons, use `GetByPrimaryLocation`** - this gets employees whose home/primary location is the specified store.

**Avoid `GetAllByLocationNumber`** for store comparisons as it includes:
- Primary location assignments (home store employees)
- Secondary location assignments (borrowed/temporary employees)  
- Multiple location assignments (employees working multiple stores)

**Business Rule**: Store comparisons should typically include only employees whose primary location matches the store being compared.

### CRITICAL: Location Filtering Implementation
**FIXED ISSUE**: Repository methods were incorrectly using obsolete `Employee.Locations` property instead of current `Employee.EmployeeLocations2`.

**Correct Implementation Pattern:**
```csharp
// Use EmployeeLocations2 with explicit Start/End date comparisons for active filtering
x.EmployeeLocations2.Any(y => 
    y.Location != null && 
    y.Location.LocationNumber == locationNumber && 
    y.DateRange.Start <= effectiveDate && 
    effectiveDate < y.DateRange.End)
```

**NHibernate Query Considerations:**
- Cannot use computed properties like `IsActive` in LINQ queries (causes QueryException)
- Cannot use `DateRange.Contains(date)` in LINQ queries (causes NotSupportedException)
- Must use explicit date comparisons: `DateRange.Start <= date && date < DateRange.End`
- `IsActive` is a computed property that calls `IsActiveForDate(DateTime.Today)` which uses `DateRange.Contains()`

### Query Performance Notes
- Query includes comprehensive employee data fields (60+ columns)
- Uses NHibernate SQL generation with parameterized queries
- Returns employees with CurrentStatus filtering for active employees

## Database Connection
- Uses SQL Server with integrated security
- Connection string format: `data source=.;initial catalog=dbNserviceBus;integrated security=SSPI;enlist=false;`
- Repository system handles connection pooling and transaction management
- ShowSql(true) enables SQL query logging for debugging

# Harri API Integration

## Architecture Overview
The Harri API integration follows a well-established pattern throughout the codebase:

### Core Components
- **`ICallHarriWebServiceProvider`**: Main interface for making Harri API calls
- **`ITenantByLocationProvider`**: Maps store locations to Harri tenants
- **`HarriInboundEmployee`**: Standard data model for Harri employee responses

### API Call Pattern
```csharp
// 1. Get tenant by location
var tenantId = await _tenantByLocationProvider.GetTenantIdByLocation(storeNumber);

// 2. Call Harri API
var response = await _callHarriWebServiceProvider.Call(
    tenantId,
    Method.Get,
    endpoint,
    payload,
    failIfNotFound,
    version
);

// 3. Parse response
var employees = JsonConvert.DeserializeObject<List<HarriInboundEmployee>>(response.Content);
```

### Common Employee Endpoints
- `employees/{badgeId}` - Get employee by badge ID (V1)
- `employees?location_id={locationId}&limit=500&status=ACTIVE` - Get employees by location (V2)
- `employees` - Create new employee (V3, POST)
- `employees/{badgeId}/basic_info` - Update employee info (V2, PUT)
- `employees/{badgeId}/locations/{locationId}` - Manage location assignments (V1)

### Error Handling Pattern
- Check `response.IsSuccessStatusCode` before processing
- Handle `JsonException` for parsing errors
- Log failures with context information
- Return empty collections or null for graceful degradation

### Authentication
- OAuth2 client credentials flow
- Automatic token caching with expiration handling
- Tenant-specific authentication configuration

## HarriCompare Integration
The HarriCompare project has access to the full Harri API infrastructure:
- `ICallHarriWebServiceProvider` already injected
- `ITenantByLocationProvider` already injected
- `HarriInboundEmployee` model available
- No additional dependencies needed for production API calls

### Location Validation Patterns
**Critical Insight**: Not all store locations have corresponding Harri tenant mappings.

**Tenant Lookup Failure Handling:**
```csharp
// Pattern for handling missing tenant mappings
var tenantId = await _tenantProvider.GetTenantIdByLocation(storeNumber);
if (!tenantId.HasValue)
{
    throw new TenantNotFoundForLocationException(storeNumber);
}
```

**Domain Exception Placement**: Location validation exceptions belong in `Jitb.Employment.HarriCaller.Domain.Exceptions`, not in consuming applications. The HarriCaller domain owns the access logic and tenant mapping responsibility.

**Error Differentiation Pattern**: Distinguish between:
- **Missing tenant mapping**: Location exists but not configured for Harri integration
- **API connectivity issues**: Cannot reach Harri API to validate tenant mapping
- **Invalid location format**: Location number is malformed or out of range

**Exception Enhancement**: When enhancing domain exceptions, add contextual properties (like `LocationNumber`) to provide debugging information while maintaining clean error messages.

# HarriCompare Project Insights

## Console Application Architecture Pattern
The HarriCompare project demonstrates an excellent pattern for building enterprise console applications:

### Outside-In Development Approach
- **User Story Driven**: Development starts from user requirements and works inward
- **Test-Driven Development**: Comprehensive TDD implementation with task-categorized tests
- **Interface-First Design**: All major components implement interfaces for testability
- **Dependency Injection**: Full IoC container integration for modular architecture

### Configuration Management Pattern
**Multi-Source Configuration Hierarchy:**
1. **App.config**: Primary configuration source with environment-specific settings
2. **Command-Line Parameters**: Override configuration for specific executions
3. **Validation Framework**: Comprehensive validation with specific exit codes

**Key Configuration Insights:**
- `ReportSpec` format: `FilterType,ReportType,LocationNumbers` (e.g., "L,B,168,175,3026")
- Dual database connections: "Default" for employee data, "Config" for Harri tenant mappings
- Environment-specific templates in `sample-configs/` directory

### Data Comparison Architecture
**Efficient Comparison Algorithm:**
```csharp
// Create lookup dictionaries for O(1) comparison performance
var eisLookup = eisEmployees.ToDictionary(e => e.Id, StringComparer.OrdinalIgnoreCase);
var harriLookup = harriEmployees.ToDictionary(e => e.Id, StringComparer.OrdinalIgnoreCase);

// Two-pass comparison for missing employees
// Pass 1: Find employees in EIS but not in Harri
// Pass 2: Find employees in Harri but not in EIS
```

### Report Generation Pattern
**CSV Pipe-Delimited Format:**
- Standardized format: `LocationNumber|TenantId|TenantName|EmployeeId|DiscrepancyType|Description|EisData|HarriData|Severity|Timestamp`
- Consolidated reporting across multiple locations
- Timestamped file naming: `comparison_report_YYYYMMDD_HHMMSS.csv`

### Error Handling Strategy
**Layered Exception Handling:**
- **Configuration Errors**: `ConfigurationException` with specific `ExitCode` values
- **Domain Errors**: `TenantNotFoundForLocationException` for business logic failures
- **Integration Errors**: `EisException` for database issues, API errors for Harri connectivity
- **Graceful Degradation**: Continue processing other locations when one fails

### Testing Patterns
**Comprehensive Test Architecture:**
- **Task Categorization**: All tests marked with `[Trait("Category", "TaskXX")]` for traceability
- **Test Data Builders**: Fluent builder pattern for creating test data
- **AutoFixture Integration**: Automated test data generation with Moq integration
- **Repository Mocking**: Clean separation of unit and integration tests

### Multi-Location Processing
**Scalable Location Processing:**
- Sequential processing with individual result collection
- Per-location error isolation
- Consolidated reporting across all processed locations
- Progress logging for operational visibility

## Integration Patterns Used

### Repository Pattern Enhancement
The HarriCompare project extends the standard repository pattern with:
- **Health Status Monitoring**: `CheckHealthAsync()` methods for operational diagnostics
- **Data Source Abstraction**: Common `EmployeeComparisonData` model for cross-system comparison
- **Error Recovery**: Graceful handling of repository connectivity issues

### Service-Oriented Architecture
**Clean Service Boundaries:**
- `IComparisonService`: Workflow orchestration
- `IEmployeeComparisonService`: Business logic for comparisons
- `IReportGeneratorService`: Output generation and formatting
- `ILoggingService`: Structured logging abstraction

## Lessons Learned

### Configuration Validation
**Comprehensive Validation Strategy:**
- Validate configuration format early in application startup
- Provide clear error messages with specific exit codes
- Support both file-based and command-line configuration sources

### Database Integration
**Repository Configuration Best Practices:**
- Always call `configuration.Start()` before creating UnitOfWork
- Use location-specific queries (`GetByPrimaryLocation`) for store comparisons
- Handle database connectivity gracefully with fallback strategies

### API Integration
**Rate Limiting and Error Handling:**
- Implement configurable rate limiting for external API calls
- Handle tenant mapping failures gracefully
- Provide detailed logging for API connectivity issues

### Performance Considerations
**Optimization Strategies:**
- Use dictionary lookups for O(1) employee comparison performance
- Process locations sequentially to avoid overwhelming external systems
- Implement streaming for large datasets in future enhancements

# Vocabulary
- 'Company employee' is ambiguous.  It could mean (1) an employee working at a company location, or (2) an above-store employee employed by Jack in the Box (as opposed to a franchisee).
I suggest Company employee refers to (1); corporate employee refers to (2).
- 'HarriCompare' and 'ComparisonReport' refer to the same project: `Jitb.Employment.HarriCompare`